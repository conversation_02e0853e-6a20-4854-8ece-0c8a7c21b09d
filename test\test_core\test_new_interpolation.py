"""
對重構後的 core.interpolation 套件進行單元測試
"""

import numpy as np
import pytest

# 從新的、重構後的路徑導入
from config.interpolation import InterpolationMethod
from core.interpolation.compat import create_interpolator, mode_to_order
from core.interpolation.interpolator import AdvancedInterpolator
from core.interpolation.kernels.advanced import (_lanczos_kernel_3,
                                                 _mitchell_kernel_default)
from core.interpolation.kernels.basic import _bilinear_core


@pytest.fixture
def sample_image():
    """創建一個簡單、已知的 4x4 灰度圖像用於測試。"""
    return np.array(
        [[10, 20, 30, 40], [50, 60, 70, 80], [90, 100, 110, 120], [130, 140, 150, 160]],
        dtype=np.float32,
    )


class TestKernels:
    """測試插值核函數的數學正確性"""

    def test_lanczos3_kernel_properties(self):
        """測試 Lanczos-3 核函數的關鍵屬性"""
        # 在中心點 (x=0)，值應為 1
        assert np.isclose(_lanczos_kernel_3(0.0), 1.0)
        # 在整數點 (x=1, 2, ...)，值應為 0
        assert np.isclose(_lanczos_kernel_3(1.0), 0.0)
        assert np.isclose(_lanczos_kernel_3(2.0), 0.0)
        # 在支撐範圍外 (a=3)，值應為 0
        assert np.isclose(_lanczos_kernel_3(3.0), 0.0)
        assert np.isclose(_lanczos_kernel_3(-3.0), 0.0)

    def test_mitchell_kernel_properties(self):
        """測試 Mitchell-Netravali 核函數的關鍵屬性"""
        # 測試一些已知屬性
        assert np.isclose(_mitchell_kernel_default(0.0), 0.88888888)
        assert np.isclose(_mitchell_kernel_default(1.0), 0.0)
        assert _mitchell_kernel_default(2.0) == 0.0


class TestInterpolator:
    """測試核心插值器 AdvancedInterpolator"""

    def test_bilinear_interpolation(self, sample_image):
        """測試雙線性插值的準確性"""
        interpolator = create_interpolator(InterpolationMethod.BILINEAR)

        # 測試點 1: 圖像中心點 (1.5, 1.5)
        # 預期結果 = (60+70+100+110) / 4 = 85
        x_coords = np.array([[1.5]])
        y_coords = np.array([[1.5]])
        result = interpolator.interpolate(sample_image, x_coords, y_coords)
        assert np.isclose(result[0, 0], 85.0)

        # 測試點 2: 點 (0.5, 1.5)
        # 預期結果 = (20+30+60+70) / 4 = 45
        x_coords = np.array([[0.5]])
        y_coords = np.array([[1.5]])
        result = interpolator.interpolate(sample_image, x_coords, y_coords)
        assert np.isclose(result[0, 0], 45.0)

    def test_nearest_interpolation(self, sample_image):
        """測試最近鄰插值的準確性"""
        interpolator = create_interpolator(InterpolationMethod.NEAREST)

        # 測試點 (1.6, 1.6) -> 應捨入到 (2, 2) -> 值為 110
        x_coords = np.array([[1.6]])
        y_coords = np.array([[1.6]])
        result = interpolator.interpolate(sample_image, x_coords, y_coords)
        assert np.isclose(result[0, 0], 110.0)

        # 測試點 (1.4, 0.4) -> 應捨入到 (1, 0) -> 值為 20
        x_coords = np.array([[1.4]])
        y_coords = np.array([[0.4]])
        result = interpolator.interpolate(sample_image, x_coords, y_coords)
        assert np.isclose(result[0, 0], 20.0)

    def test_rgb_image_interpolation(self):
        """測試多通道 (RGB) 圖像的插值"""
        interpolator = create_interpolator(InterpolationMethod.BILINEAR)
        rgb_image = np.stack(
            [np.zeros((4, 4)), np.ones((4, 4)) * 100, np.ones((4, 4)) * 200], axis=-1
        ).astype(np.float32)

        x_coords = np.array([[1.5]])
        y_coords = np.array([[1.5]])
        result = interpolator.interpolate(rgb_image, x_coords, y_coords)

        assert result.shape == (1, 1, 3)
        # 預期結果: [0, 100, 200]
        assert np.allclose(result[0, 0], [0.0, 100.0, 200.0])


class TestCompatibilityLayer:
    """測試向後兼容層"""

    def test_create_interpolator_factory(self):
        """測試 create_interpolator 工廠函數"""
        interpolator = create_interpolator("bicubic", use_gpu=False)
        assert isinstance(interpolator, AdvancedInterpolator)
        assert interpolator.config.method == InterpolationMethod.BICUBIC
        assert interpolator.config.use_gpu is False

    def test_mode_to_order(self):
        """測試 mode_to_order 函數"""
        assert mode_to_order("nearest") == 0
        assert mode_to_order("BILINEAR") == 1  # 測試不區分大小寫
        assert mode_to_order("cubic") == 3
        with pytest.raises(ValueError):
            mode_to_order("unknown_mode")
