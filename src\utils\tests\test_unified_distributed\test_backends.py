import pytest
import time
import asyncio
import sys
from pathlib import Path
from concurrent.futures import Future

# Import project modules directly (using pip install -e .)

from utils.unified_distributed_processor import UnifiedDistributedProcessor

# ==== Test Functions ====
def sample_sync_task(x, y):
    """一個簡單的同步任務"""
    return x + y

async def sample_async_task(x, y):
    """一個簡單的異步任務"""
    await asyncio.sleep(0.05)
    return x * y

# ==== Test Cases ====
def test_singleton_instance(distributed_processor: UnifiedDistributedProcessor):
    """測試分散式處理器是否為單例"""
    another_processor = UnifiedDistributedProcessor()
    assert distributed_processor is another_processor
    distributed_processor.shutdown()

def test_threading_backend_sync(distributed_processor: UnifiedDistributedProcessor):
    """測試線程後端的同步提交"""
    future = distributed_processor.submit(sample_sync_task, 2, 3)
    assert isinstance(future, Future)
    result = future.result(timeout=1)
    assert result == 5
    distributed_processor.shutdown()

@pytest.mark.asyncio
async def test_threading_backend_async(distributed_processor: UnifiedDistributedProcessor):
    """測試線程後端的異步提交"""
    result = await distributed_processor.submit_async(sample_async_task, 10, 5)
    assert result == 50
    distributed_processor.shutdown()

def test_map_operation(distributed_processor: UnifiedDistributedProcessor):
    """測試並行映射操作"""
    data = [1, 2, 3, 4, 5]
    def square(x):
        return x * x
        
    results = distributed_processor.map(square, data)
    assert results == [1, 4, 9, 16, 25]
    distributed_processor.shutdown()

# 注意: Multiprocessing 在某些測試環境下可能會有問題
# 這裡提供一個可選的測試
@pytest.mark.skip(reason="Multiprocessing can be tricky in some test runners")
def test_multiprocessing_backend():
    """測試多進程後端"""
    processor = UnifiedDistributedProcessor(backend="MULTIPROCESSING", max_workers=2)
    future = processor.submit(sample_sync_task, 10, 20)
    result = future.result(timeout=2)
    assert result == 30
    processor.shutdown()

def test_shutdown_behavior(distributed_processor: UnifiedDistributedProcessor):
    """測試關閉行為"""
    processor = distributed_processor
    processor.submit(time.sleep, 0.1)
    processor.shutdown()
    
    # 關閉後再提交應該會引發異常
    with pytest.raises(Exception):
        processor.submit(sample_sync_task, 1, 1)