"""
Tests for core.projection module
"""

from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import numpy as np
import pytest

# Mock GPU libraries
mock_torch = Mock()
mock_cupy = Mock()
mock_pyopencl = Mock()

# Patch imports before importing the module
with patch.dict(
    "sys.modules", {"torch": mock_torch, "cupy": mock_cupy, "pyopencl": mock_pyopencl}
):
    with patch("cv2.resize") as mock_cv2_resize:
        mock_cv2_resize.return_value = np.random.randint(
            0, 255, (512, 512, 3), dtype=np.uint8
        )
        from core.projection import ProjectionCore


class TestProjectionCore:
    """Test ProjectionCore class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.projection = ProjectionCore()
        self.equirect_image = np.random.randint(0, 255, (512, 1024, 3), dtype=np.uint8)
        self.cube_size = 512

    def test_init(self):
        """Test ProjectionCore initialization"""
        assert isinstance(self.projection, ProjectionCore)
        assert hasattr(self.projection, "enable_gpu")
        assert hasattr(self.projection, "gpu_backend")

    def test_init_with_parameters(self):
        """Test ProjectionCore initialization with parameters"""
        projection = ProjectionCore(
            enable_gpu=False, gpu_backend="cpu", cache_enabled=True
        )
        assert projection.enable_gpu is False
        assert projection.gpu_backend == "cpu"

    @patch("cv2.resize")
    def test_equirect_to_cubemap_basic(self, mock_resize):
        """Test basic equirectangular to cubemap conversion"""
        # Mock cv2.resize to return appropriate sized images
        mock_resize.return_value = np.random.randint(
            0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
        )

        cube_faces = self.projection.equirect_to_cubemap(
            self.equirect_image, self.cube_size
        )

        assert isinstance(cube_faces, dict)
        assert len(cube_faces) == 6

        expected_faces = ["front", "right", "back", "left", "up", "down"]
        for face_name in expected_faces:
            assert face_name in cube_faces
            assert cube_faces[face_name].shape == (self.cube_size, self.cube_size, 3)

    @patch("cv2.resize")
    def test_cubemap_to_equirect_basic(self, mock_resize):
        """Test basic cubemap to equirectangular conversion"""
        # Create cube faces
        cube_faces = {}
        face_names = ["front", "right", "back", "left", "up", "down"]
        for face in face_names:
            cube_faces[face] = np.random.randint(
                0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
            )

        # Mock cv2.resize
        mock_resize.return_value = np.random.randint(
            0, 255, (512, 1024, 3), dtype=np.uint8
        )

        equirect_image = self.projection.cubemap_to_equirect(
            cube_faces, output_width=1024, output_height=512
        )

        assert equirect_image.shape == (512, 1024, 3)

    def test_round_trip_conversion(self):
        """Test round-trip conversion: equirect -> cube -> equirect"""
        with patch("cv2.resize") as mock_resize:
            # First conversion: equirect to cube
            mock_resize.return_value = np.random.randint(
                0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
            )
            cube_faces = self.projection.equirect_to_cubemap(
                self.equirect_image, self.cube_size
            )

            # Second conversion: cube to equirect
            mock_resize.return_value = np.random.randint(
                0, 255, (512, 1024, 3), dtype=np.uint8
            )
            reconstructed = self.projection.cubemap_to_equirect(cube_faces, 1024, 512)

            # Should have same dimensions as original
            assert reconstructed.shape == self.equirect_image.shape

    def test_different_cube_sizes(self):
        """Test conversion with different cube sizes"""
        sizes = [256, 512, 1024]

        for size in sizes:
            with patch("cv2.resize") as mock_resize:
                mock_resize.return_value = np.random.randint(
                    0, 255, (size, size, 3), dtype=np.uint8
                )

                cube_faces = self.projection.equirect_to_cubemap(
                    self.equirect_image, size
                )

                assert len(cube_faces) == 6
                for face in cube_faces.values():
                    assert face.shape == (size, size, 3)

    def test_different_output_formats(self):
        """Test conversion with different output formats"""
        # Test list output
        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
            )

            cube_list = self.projection.equirect_to_cubemap(
                self.equirect_image, self.cube_size, output_format="list"
            )

            assert isinstance(cube_list, list)
            assert len(cube_list) == 6
            for face in cube_list:
                assert face.shape == (self.cube_size, self.cube_size, 3)

        # Test horizon output
        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
            )

            horizon_image = self.projection.equirect_to_cubemap(
                self.equirect_image, self.cube_size, output_format="horizon"
            )

            assert horizon_image.shape == (self.cube_size, self.cube_size * 6, 3)

    def test_grayscale_conversion(self):
        """Test conversion with grayscale images"""
        gray_equirect = np.random.randint(0, 255, (512, 1024), dtype=np.uint8)

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (self.cube_size, self.cube_size), dtype=np.uint8
            )

            cube_faces = self.projection.equirect_to_cubemap(
                gray_equirect, self.cube_size
            )

            # Should handle grayscale appropriately
            assert len(cube_faces) == 6

    def test_gpu_acceleration_cuda(self):
        """Test CUDA GPU acceleration (mocked)"""
        with patch.object(
            self.projection, "_check_gpu_availability", return_value=True
        ):
            with patch.object(
                self.projection, "_equirect_to_cubemap_cuda"
            ) as mock_cuda:
                mock_cuda.return_value = {
                    "front": np.random.randint(
                        0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
                    ),
                    "right": np.random.randint(
                        0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
                    ),
                    "back": np.random.randint(
                        0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
                    ),
                    "left": np.random.randint(
                        0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
                    ),
                    "up": np.random.randint(
                        0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
                    ),
                    "down": np.random.randint(
                        0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
                    ),
                }

                projection = ProjectionCore(enable_gpu=True, gpu_backend="cuda")
                cube_faces = projection.equirect_to_cubemap(
                    self.equirect_image, self.cube_size
                )

                assert len(cube_faces) == 6

    def test_performance_monitoring(self):
        """Test performance monitoring functionality"""
        projection = ProjectionCore(enable_performance_monitoring=True)

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
            )

            cube_faces = projection.equirect_to_cubemap(
                self.equirect_image, self.cube_size
            )

            # Should have performance stats
            if hasattr(projection, "get_performance_stats"):
                stats = projection.get_performance_stats()
                assert isinstance(stats, dict)

    def test_coordinate_generation(self):
        """Test coordinate generation methods"""
        # Test cube coordinate generation
        coords = self.projection._generate_cube_coords(self.cube_size)

        if coords is not None:
            assert isinstance(coords, dict)
            assert len(coords) == 6

    def test_coordinate_mapping(self):
        """Test coordinate mapping functions"""
        # Test XYZ to UV mapping
        x, y, z = 1.0, 0.0, 0.0  # Point on unit sphere
        u, v = self.projection._xyz_to_uv(x, y, z)

        assert 0 <= u <= 1
        assert 0 <= v <= 1

    def test_edge_cases(self):
        """Test edge cases and boundary conditions"""
        # Very small equirectangular image
        tiny_equirect = np.random.randint(0, 255, (16, 32, 3), dtype=np.uint8)

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (64, 64, 3), dtype=np.uint8
            )

            cube_faces = self.projection.equirect_to_cubemap(tiny_equirect, 64)
            assert len(cube_faces) == 6

        # Very small cube size
        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (8, 8, 3), dtype=np.uint8
            )

            cube_faces = self.projection.equirect_to_cubemap(self.equirect_image, 8)
            assert len(cube_faces) == 6

    def test_invalid_inputs(self):
        """Test handling of invalid inputs"""
        # Invalid cube size
        with pytest.raises((ValueError, AssertionError)):
            self.projection.equirect_to_cubemap(self.equirect_image, 0)

        # Invalid image
        with pytest.raises((ValueError, IndexError)):
            self.projection.equirect_to_cubemap(np.array([]), self.cube_size)

    def test_caching_functionality(self):
        """Test caching functionality"""
        projection = ProjectionCore(cache_enabled=True)

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
            )

            # First call
            cube_faces1 = projection.equirect_to_cubemap(
                self.equirect_image, self.cube_size
            )

            # Second call with same parameters (should use cache if implemented)
            cube_faces2 = projection.equirect_to_cubemap(
                self.equirect_image, self.cube_size
            )

            assert len(cube_faces1) == 6
            assert len(cube_faces2) == 6

    def test_memory_management(self):
        """Test memory management during conversion"""
        # Process multiple conversions to test memory cleanup
        for i in range(5):
            test_image = np.random.randint(0, 255, (256, 512, 3), dtype=np.uint8)

            with patch("cv2.resize") as mock_resize:
                mock_resize.return_value = np.random.randint(
                    0, 255, (256, 256, 3), dtype=np.uint8
                )

                cube_faces = self.projection.equirect_to_cubemap(test_image, 256)
                assert len(cube_faces) == 6

                # Cleanup
                del cube_faces

    def test_different_backends(self):
        """Test different processing backends"""
        backends = ["cpu", "cuda", "opencl"]

        for backend in backends:
            projection = ProjectionCore(
                gpu_backend=backend, enable_gpu=False
            )  # Force CPU for testing

            with patch("cv2.resize") as mock_resize:
                mock_resize.return_value = np.random.randint(
                    0, 255, (self.cube_size, self.cube_size, 3), dtype=np.uint8
                )

                cube_faces = projection.equirect_to_cubemap(
                    self.equirect_image, self.cube_size
                )
                assert len(cube_faces) == 6


class TestProjectionMathematics:
    """Test mathematical correctness of projection algorithms"""

    def setup_method(self):
        """Setup test fixtures"""
        self.projection = ProjectionCore()

    def test_spherical_to_cartesian_conversion(self):
        """Test spherical to Cartesian coordinate conversion"""
        # Test known points
        test_points = [
            (0, 0),  # Front center
            (np.pi, 0),  # Back center
            (np.pi / 2, 0),  # Right center
            (-np.pi / 2, 0),  # Left center
        ]

        for theta, phi in test_points:
            x, y, z = self.projection._spherical_to_cartesian(theta, phi)

            # Should be on unit sphere
            radius = np.sqrt(x * x + y * y + z * z)
            np.testing.assert_almost_equal(radius, 1.0, decimal=6)

    def test_coordinate_bounds(self):
        """Test that coordinate transformations maintain proper bounds"""
        # Generate grid of UV coordinates
        u_coords = np.linspace(0, 1, 100)
        v_coords = np.linspace(0, 1, 100)

        for u in u_coords:
            for v in v_coords:
                # Convert UV to spherical
                theta, phi = self.projection._uv_to_spherical(u, v)

                # Check bounds
                assert -np.pi <= theta <= np.pi
                assert -np.pi / 2 <= phi <= np.pi / 2

    def test_projection_consistency(self):
        """Test consistency of projection mappings"""
        # Test that projection mappings are consistent
        projection = ProjectionCore()

        # Create a simple test pattern
        test_pattern = np.zeros((100, 200, 3), dtype=np.uint8)
        test_pattern[40:60, 90:110] = 255  # White square in center

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (100, 100, 3), dtype=np.uint8
            )

            cube_faces = projection.equirect_to_cubemap(test_pattern, 100)

            # All faces should be generated
            assert len(cube_faces) == 6

            # Each face should have expected dimensions
            for face in cube_faces.values():
                assert face.shape == (100, 100, 3)


if __name__ == "__main__":
    pytest.main([__file__])


