"""
統一警報系統模組
標準化警報處理，避免重複警報邏輯
"""
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable

from .metric_collector import Metric, MetricType
from .thresholds import SystemThresholds, DEFAULT_THRESHOLDS

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """警報級別"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


@dataclass
class Alert:
    """警報數據結構"""
    level: AlertLevel
    message: str
    metric: Metric
    threshold_value: float
    timestamp: float = field(default_factory=time.time)
    tags: dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> dict[str, Any]:
        """轉換為字典格式"""
        return {
            "level": self.level.value,
            "message": self.message,
            "metric": self.metric.to_dict(),
            "threshold_value": self.threshold_value,
            "timestamp": self.timestamp,
            "tags": self.tags
        }


class AlertHandler(ABC):
    """抽象警報處理器"""
    
    @abstractmethod
    def handle(self, alert: Alert):
        """處理警報"""
        pass


class LoggingAlertHandler(AlertHandler):
    """日誌警報處理器"""
    
    def handle(self, alert: Alert):
        level_map = {
            AlertLevel.INFO: logger.info,
            AlertLevel.WARNING: logger.warning,
            AlertLevel.CRITICAL: logger.error
        }
        log_func = level_map.get(alert.level, logger.info)
        log_func(f"[{alert.level.value.upper()}] {alert.message}")


class CallbackAlertHandler(AlertHandler):
    """回調警報處理器"""
    
    def __init__(self, callback: Callable[[Alert], None]):
        self.callback = callback
    
    def handle(self, alert: Alert):
        try:
            self.callback(alert)
        except Exception as e:
            logger.error(f"Alert callback failed: {e}")


class AlertSystem:
    """統一警報系統"""
    
    def __init__(self, thresholds: SystemThresholds | None = None):
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self.handlers: list[AlertHandler] = []
        self.active_alerts: dict[str, Alert] = {}
        
        # 預設添加日誌處理器
        self.add_handler(LoggingAlertHandler())
    
    def add_handler(self, handler: AlertHandler):
        """添加警報處理器"""
        self.handlers.append(handler)
    
    def remove_handler(self, handler: AlertHandler):
        """移除警報處理器"""
        if handler in self.handlers:
            self.handlers.remove(handler)
    
    def check_metric(self, metric: Metric) -> Alert | None:
        """檢查指標是否觸發警報"""
        alert = None
        
        # 根據指標類型檢查閾值
        if metric.metric_type == MetricType.CPU_USAGE:
            alert = self._check_threshold(
                metric, 
                self.thresholds.cpu_warning, 
                self.thresholds.cpu_critical,
                "CPU使用率"
            )
        elif metric.metric_type == MetricType.MEMORY_USAGE:
            alert = self._check_threshold(
                metric,
                self.thresholds.memory_warning,
                self.thresholds.memory_critical,
                "記憶體使用率"
            )
        elif metric.metric_type == MetricType.GPU_USAGE:
            alert = self._check_threshold(
                metric,
                self.thresholds.gpu_warning,
                self.thresholds.gpu_critical,
                "GPU使用率"
            )
        elif metric.metric_type == MetricType.DISK_USAGE:
            alert = self._check_threshold(
                metric,
                self.thresholds.disk_warning,
                self.thresholds.disk_critical,
                "磁碟使用率"
            )
        
        if alert:
            self._trigger_alert(alert)
        
        return alert
    
    def _check_threshold(self, metric: Metric, warning: float, critical: float, resource_name: str) -> Alert | None:
        """檢查單一閾值"""
        if metric.value >= critical:
            return Alert(
                level=AlertLevel.CRITICAL,
                message=f"{resource_name} {metric.value:.1f}% 已達到危險水平 (閾值: {critical}%)",
                metric=metric,
                threshold_value=critical
            )
        elif metric.value >= warning:
            return Alert(
                level=AlertLevel.WARNING,
                message=f"{resource_name} {metric.value:.1f}% 較高 (閾值: {warning}%)",
                metric=metric,
                threshold_value=warning
            )
        return None
    
    def _trigger_alert(self, alert: Alert):
        """觸發警報"""
        alert_key = f"{alert.metric.name}_{alert.level.value}"
        
        # 避免重複警報
        if alert_key in self.active_alerts:
            last_alert = self.active_alerts[alert_key]
            if time.time() - last_alert.timestamp < 60:  # 1分鐘內不重複
                return
        
        self.active_alerts[alert_key] = alert
        
        # 通知所有處理器
        for handler in self.handlers:
            try:
                handler.handle(alert)
            except Exception as e:
                logger.error(f"Alert handler failed: {e}")
    
    def clear_alerts(self):
        """清除所有警報"""
        self.active_alerts.clear()
    
    def get_active_alerts(self) -> list[Alert]:
        """獲取當前活躍警報"""
        return list(self.active_alerts.values())
