import pytest
import time
import sys
from pathlib import Path
import asyncio

# Import project modules directly (using pip install -e .)

from utils.unified_performance_monitor import UnifiedPerformanceMonitor

def test_singleton_instance(performance_monitor: UnifiedPerformanceMonitor):
    """測試性能監控器是否為單例"""
    another_monitor = UnifiedPerformanceMonitor()
    assert performance_monitor is another_monitor

def test_monitoring_lifecycle(performance_monitor: UnifiedPerformanceMonitor):
    """測試監控的啟動和停止生命週期"""
    assert not performance_monitor.monitoring
    performance_monitor.start_monitoring()
    assert performance_monitor.monitoring
    assert performance_monitor.monitor_thread is not None
    assert performance_monitor.monitor_thread.is_alive()
    
    # 讓監控運行一小段時間
    time.sleep(0.2)
    
    performance_monitor.stop_monitoring()
    assert not performance_monitor.monitoring
    # 由於線程可能需要一點時間完全終止，這裡不直接斷言 is_alive()
    
def test_collect_system_metrics(performance_monitor: UnifiedPerformanceMonitor):
    """測試系統指標的收集"""
    performance_monitor._collect_system_metrics()
    metrics = performance_monitor.current_metrics
    assert 'system.cpu.usage' in metrics
    assert 'system.memory.usage' in metrics
    assert metrics['system.cpu.usage'].unit == '%'
    assert 0 <= metrics['system.cpu.usage'].value <= 100

@pytest.mark.asyncio
async def test_measure_async_context(performance_monitor: UnifiedPerformanceMonitor):
    """測試異步測量上下文"""
    task_name = "async_test_task"
    
    async with performance_monitor.measure_async(task_name):
        await asyncio.sleep(0.1)
        
    assert task_name in performance_monitor.metrics_history
    metric = performance_monitor.metrics_history[task_name][0]
    assert metric.name == task_name
    assert metric.unit == "ms"
    # 預期執行時間約為 100ms
    assert 90 < metric.value < 150

def test_measure_sync_context(performance_monitor: UnifiedPerformanceMonitor):
    """測試同步測量上下文"""
    task_name = "sync_test_task"
    
    with performance_monitor.measure(task_name):
        time.sleep(0.1)
        
    assert task_name in performance_monitor.metrics_history
    metric = performance_monitor.metrics_history[task_name][0]
    assert metric.name == task_name
    assert metric.unit == "ms"
    # 預期執行時間約為 100ms
    assert 90 < metric.value < 150

def test_generate_report(performance_monitor: UnifiedPerformanceMonitor):
    """測試報告生成"""
    with performance_monitor.measure("report_generation_task"):
        time.sleep(0.05)
    
    report = performance_monitor.generate_report()
    assert isinstance(report, dict)
    assert 'summary' in report
    assert 'metrics' in report
    assert 'report_generation_task' in report['metrics']
    assert 'avg_cpu_usage' in report['summary']