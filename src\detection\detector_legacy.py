"""
舊版偵測器介面 (相容性層) - 已棄用

本檔案旨在為舊有的、單體式的 `Detector` 類別提供一個向後相容的介面。
其主要目的是確保依賴舊版 `detection.detector` 路徑的程式碼在升級後仍能繼續運作，
同時在內部巧妙地將呼叫轉發至重構後的高度模組化架構。

**重要提示：**
- **請勿在此檔案中撰寫新功能。** 所有新功能都應在 `detection/core`、`detection/strategies` 和 `detection/postprocessing` 中實現。
- **此檔案應被視為「唯讀」**，僅作為過渡時期的相容性解決方案。
- 所有來自此模組的匯入操作都會觸發一個 `DeprecationWarning`，強烈建議開發者盡快遷移至新的 API。

新的、建議的匯入方式是：
`from detection.core.detector import Detector`
`from detection.core.config import DetectionConfig`
"""

import warnings
from typing import (Dict, List, Optional,  # 舊版 typing 以求相容性
                    Tuple)

# 從新的核心模組匯入重構後的元件
from .core.config import DetectionConfig as ModernDetectionConfig
from .core.data_structures import DetectionBox as ModernDetectionBox
from .core.detector import Detector as ModernDetector

# --- 棄用警告 ---
# 在模組被匯入時立即發出一個明確的棄用警告，
# 引導開發者使用新的、更優雅的 API。
warnings.warn(
    "從 `detection.detector_legacy` (或舊的 `detection.detector`) 匯入已被棄用，並將在未來版本中移除。"
    "請更新您的程式碼，改用 `from detection.core import Detector, DetectionConfig`。"
    "舊的單體偵測器已被重構為一個採用策略和管線模式的現代化模組化架構。",
    DeprecationWarning,
    stacklevel=2,
)

# --- 舊版介面匯出 ---
# 為了讓舊的 `from detection.detector import DetectionBox, DetectionConfig` 語法繼續工作，
# 我們將新的資料結構重新命名並匯出。
DetectionBox = ModernDetectionBox
DetectionConfig = ModernDetectionConfig


class Detector(ModernDetector):
    """
    用於向後相容的舊版偵測器包裝器 (Legacy Detector Wrapper)

    這個類別繼承自新的 `ModernDetector`，但其公開介面 (public API)
    完全模仿了原始的、擁有 762 行程式碼的單體偵測器。
    它的存在是為了確保舊的程式碼庫無需修改即可執行，
    同時所有核心邏輯都已由新的模組化架構在內部處理。
    """

    def __init__(self, config: DetectionConfig):
        """
        以與舊版介面完全相容的方式初始化偵測器。

        Args:
            config (DetectionConfig): 偵測設定物件。
        """
        super().__init__(config)

        # --- 為了相容性而保留的舊版屬性 ---
        # 這些屬性在舊版中是公開的，為了避免 `AttributeError`，在此重新賦值。
        self.use_amp = "cuda" in self.device
        self.primary_model = self.model_manager.get_model("primary")
        self.secondary_model = self.model_manager.get_model("secondary")
        self.blur_processor = self.post_processor.blur_processor # 假設 blur_processor 在 post_processor 中

    def detect(self, image, model, conf_threshold: float) -> List[DetectionBox]:
        """
        【已棄用】為相容性而保留的舊版單一模型偵測方法。

        注意：此方法在新架構中已不再使用。新的 `detect` 方法是基於策略的，
        並且不直接接收模型或信賴度閾值作為參數。
        """
        warnings.warn(
            "`detector.detect(image, model, conf_threshold)` 已棄用。"
            "請改用 `detector.detect(image, face_id)`。",
            DeprecationWarning,
            stacklevel=2,
        )

        # 使用標準策略來模擬舊的行為
        from .strategies.standard import StandardStrategy
        strategy = StandardStrategy()

        # 建立一個暫時的模型字典以符合新策略的介面
        models = {"primary": model} if model else {}

        # 建立一個暫時的設定物件
        # 這是一個模擬 `DetectionConfig` 的匿名型別
        temp_config = type(
            "TempConfig",
            (),
            {
                "conf_threshold": conf_threshold,
                "iou_threshold": self.config.iou_threshold,
                "device": self.device,
            },
        )()

        # 呼叫新策略的 detect 方法
        return strategy.detect(image, models, temp_config)

    def detect_dual_model(self, image) -> List[DetectionBox]:
        """【已棄用】為相容性而保留的舊版雙模型偵測。"""
        warnings.warn(
            "`detect_dual_model()` 已棄用。請改用 `detect(image, face_id)`。"
            "新架構會自動根據設定處理單/雙模型。",
            DeprecationWarning,
            stacklevel=2,
        )

        # 使用標準策略並傳入所有模型來模擬雙模型偵測
        from .strategies.standard import StandardStrategy
        strategy = StandardStrategy()
        return strategy.detect(image, self.model_manager.get_models(), self.config)

    def detect_face5_with_rotation(self, image) -> List[DetectionBox]:
        """【已棄用】為相容性而保留的舊版面 5 旋轉偵測。"""
        warnings.warn(
            "`detect_face5_with_rotation()` 已棄用。請改用 `detect(image, face_id=5)`。",
            DeprecationWarning,
            stacklevel=2,
        )

        # 直接使用旋轉策略來模擬此行為
        from .strategies.rotation import RotationStrategy
        strategy = RotationStrategy()
        return strategy.detect(image, self.model_manager.get_models(), self.config)

    def calculate_iou(self, box1: DetectionBox, box2: DetectionBox) -> float:
        """【已棄用】為相容性而保留的舊版 IoU 計算。"""
        # 將呼叫轉發到新的後處理元件
        from .postprocessing.merger import MergerProcessor
        merger = MergerProcessor(iou_threshold=0.5) # iou_threshold 在此無關緊要
        return merger._calculate_iou(box1, box2)

    def merge_overlapping_boxes(
        self, detections: List[DetectionBox]
    ) -> List[DetectionBox]:
        """【已棄用】為相容性而保留的舊版偵測框合併。"""
        warnings.warn(
            "`merge_overlapping_boxes()` 已棄用。新架構的後處理管線 "
            "會自動處理合併邏輯。",
            DeprecationWarning,
            stacklevel=2,
        )
        # 將呼叫轉發到新的後處理元件
        from .postprocessing.merger import MergerProcessor
        merger = MergerProcessor(self.config.iou_threshold)
        context = {"iou_threshold": self.config.iou_threshold}
        return merger.process(detections, context)

    def filter_large_boxes(
        self, detections: List[DetectionBox], image_shape: Tuple[int, int]
    ) -> List[DetectionBox]:
        """【已棄用】為相容性而保留的舊版大型偵測框過濾。"""
        warnings.warn(
            "`filter_large_boxes()` 已棄用。新架構的後處理管線 "
            "會自動處理過濾邏輯。",
            DeprecationWarning,
            stacklevel=2,
        )
        # 將呼叫轉發到新的後處理元件
        from .postprocessing.filters import LargeBoxFilter
        filter_proc = LargeBoxFilter(self.config.max_area_ratio)
        context = {
            "image_shape": image_shape,
            "max_area_ratio": self.config.max_area_ratio,
        }
        return filter_proc.process(detections, context)

    def filter_center_region(
        self, detections: List[DetectionBox], image_shape: Tuple[int, int]
    ) -> List[DetectionBox]:
        """【已棄用】為相容性而保留的舊版中央區域過濾。"""
        warnings.warn(
            "`filter_center_region()` 已棄用。新架構的後處理管線 "
            "會根據 face_id=5 自動處理此過濾。",
            DeprecationWarning,
            stacklevel=2,
        )
        # 將呼叫轉發到新的後處理元件
        from .postprocessing.filters import CenterRegionFilter
        filter_proc = CenterRegionFilter()
        context = {"image_shape": image_shape, "face_id": 5}
        return filter_proc.process(detections, context)

    def apply_gaussian_blur(self, image, kernel_size=None):
        """【已棄用】為相容性而保留的舊版模糊應用。"""
        # 假設模糊處理現在是後處理管線的一部分
        return self.post_processor.blur_processor.apply_gaussian_blur(image, kernel_size)


# --- 全域匯出 ---
# 為確保 `from detection.detector import *` 這種舊式寫法仍能運作，
# 定義 `__all__` 變數。
__all__ = ["Detector", "DetectionConfig", "DetectionBox"]
