"""
跳過偵測策略

為面 4 (天空面) 實作跳過策略，此情況下不需要偵測。
"""

from typing import Any

import numpy as np

from log_utils import get_logger

from .base import DetectionStrategy

logger = get_logger(__name__)


class SkipStrategy(DetectionStrategy):
    """面 4 (天空面) 的跳過偵測策略"""

    def detect(self, image: np.ndarray, models: dict[str, Any], config: Any) -> list:
        """跳過偵測並回傳空列表

        :param image: 輸入影像 (未使用)
        :param models: 可用模型 (未使用)
        :param config: 偵測設定 (未使用)
        :return: 空列表 (無偵測結果)
        """
        logger.debug("跳過天空面 (面 4) 的偵測")
        return []

    def get_name(self) -> str:
        """取得策略名稱"""
        return "skip"

    def can_handle_face(self, face_id: int) -> bool:
        """跳過策略只處理面 4 (天空)"""
        return face_id == 4

    def get_description(self) -> str:
        """取得策略描述"""
        return "為天空面跳過偵測 (預期無隱私問題)"
