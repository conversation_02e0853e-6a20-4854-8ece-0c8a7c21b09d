# 🧪 Test 模組 - 品質保障與測試框架

> **一個專業、全面的自動化測試套件，是確保整個系統穩定、可靠與可維護的堅實後盾。**

---

## 📖 模組概述

`test` 模組不僅僅是一堆測試腳本的集合，它是一個經過精心設計的、多層次的品質保障系統。本模組使用 `pytest` 作為核心測試框架，並整合了 `pytest-cov`、`pytest-xdist` 和 `pytest-mock` 等多種強大的外掛程式，為專案的每一個核心功能提供了全面的測試覆蓋。

我們的測試哲學是：**任何沒有經過測試的程式碼都是不可信的**。因此，本模組的結構與主程式碼庫完全鏡像，確保了每一個功能模組都有其對應的測試案例。

## 🏛️ 架構與設計

- **當前狀態**: ✅ 重構完成
- **核心框架**: `pytest`
- **設計哲學**:
    - **測試結構與原始碼同步**: `test/test_core` 對應 `core`，`test/test_detection` 對應 `detection`，結構清晰，易於查找。
    - **分層測試策略**:
        - **單元測試**: 專注於單一函數或類別的邏輯正確性。
        - **整合測試**: 驗證多個模組之間的協作是否順暢。
    - **依賴隔離**: 大量使用 `mock` 技術來隔離外部依賴（如硬體、檔案系統、網路），確保測試的穩定性和速度。

### 檔案結構

```
test/
├── conftest.py                # Pytest設定與共享的Fixtures
├── run_tests.py               # 強大的命令列測試執行器
├── test_helpers.py            # 測試輔助函式
├── fixtures/                  # 測試所需的靜態資源 (圖片、設定檔)
├── test_config/               # `config`模組的測試
├── test_core/                 # `core`模組的測試
├── test_detection/            # `detection`模組的測試
├── test_processing/           # `processing`模組的測試
└── README.md                  # (本文件)
```

---

## ✨ 主要特色

### 1. `run_tests.py`: 統一的測試入口
提供一個簡單而強大的命令列介面來執行所有測試任務。

```bash
# 執行所有測試
python test/run_tests.py

# 只執行 core 模組的測試
python test/run_tests.py --module core

# 執行測試並生成覆蓋率報告
python test/run_tests.py --coverage --html
```

### 2. `conftest.py`: 可重用的測試資源 (Fixtures)
這是 `pytest` 的核心。我們定義了大量可重用的 `fixtures`，為測試案例提供標準化的測試數據和模擬物件。
- **`sample_image`**: 提供一個標準尺寸的 Numpy 陣列用於影像處理測試。
- **`mock_detector`**: 提供一個模擬的 `Detector` 物件，使其在不實際載入AI模型的情況下返回預設的偵測結果。
- **`temp_dir`**: 為需要進行檔案操作的測試提供一個臨時、乾淨的目錄，並在測試結束後自動清理。

### 3. 全面的模擬 (Mocking)
我們廣泛使用 `unittest.mock` 來模擬所有外部和耗時的依賴，包括：
- **AI模型**: 避免在測試中實際載入龐大的YOLO模型。
- **GPU操作**: 確保測試可以在沒有GPU的環境下順利運行。
- **檔案系統**: 使用臨時目錄和模擬的檔案讀寫，避免測試污染開發環境。

### 4. 整合測試
在 `test/test_processing/test_processor_integration.py` 中，我們不關心單個元件的實現細節，而是驗證**整個處理管線**能否順利執行。透過模擬底層服務的輸入和輸出來確認高層協調者的邏輯是否正確。

---

## 🚀 如何執行測試

### 推薦方式
使用我們提供的 `run_tests.py` 腳本。

```bash
# 執行所有單元測試，並行執行以提高速度
python test/run_tests.py --parallel

# 執行 detection 模組的測試，並顯示詳細輸出
python test/run_tests.py --module detection --verbose
```

### 使用 `pytest` 直接執行
對於更進階的開發者，可以直接使用 `pytest` 命令。

```bash
# 執行所有測試
pytest

# 執行特定檔案的測試
pytest test/test_core/test_projection.py

# 執行特定標記的測試 (例如，只跑單元測試，跳過慢速測試)
pytest -m "unit and not slow"
```

---

## 🤝 如何為新功能添加測試

為專案貢獻程式碼時，提供相應的測試是**必要**的。

1.  **找到對應目錄**: 如果您修改了 `core/projection.py`，請在 `test/test_core/test_projection.py` 中添加或修改測試。如果檔案不存在，請創建它。
2.  **利用Fixtures**: 在 `conftest.py` 中尋找可用的測試資源。如果需要新的共享資源，請添加到 `conftest.py` 中。
3.  **編寫測試案例**: 遵循 `Arrange-Act-Assert` (準備-執行-斷言) 的模式編寫清晰的測試。
4.  **隔離依賴**: 對於所有外部依賴（檔案、網路、硬體），請使用 `@patch` 進行模擬。
5.  **執行測試**: 在提交您的程式碼之前，請務必執行相關的測試，確保您的變更沒有破壞現有功能。

```python
# 範例: test/test_new_feature.py
import pytest
from unittest.mock import patch

# 使用 conftest.py 中定義的 fixture
def test_new_feature_with_image(sample_image):
    # Arrange: 準備輸入和預期輸出
    input_image = sample_image
    expected_result = "some_value"

    # Act: 執行被測函數
    from my_module import new_feature
    result = new_feature(input_image)

    # Assert: 斷言結果是否符合預期
    assert result == expected_result

# 測試需要模擬外部依賴的函數
@patch("my_module.external_service")
def test_feature_with_mock(mock_service):
    # Arrange
    mock_service.get_data.return_value = "mocked_data"

    # Act
    from my_module import feature_using_service
    result = feature_using_service()

    # Assert
    assert result == "processed_mocked_data"
    mock_service.get_data.assert_called_once()