"""
模糊處理工具

處理高斯模糊的應用和區域建立。
"""

from typing import Any

import cv2
import numpy as np

from config.constants import DEFAULT_BLUR_KERNEL_SIZE
from log_utils import get_logger

from ..core.data_structures import DetectionBox

logger = get_logger(__name__)


class BlurProcessor:
    """處理偵測區域的模糊化"""

    def __init__(self, kernel_size: tuple[int, int] = DEFAULT_BLUR_KERNEL_SIZE):
        """初始化模糊處理器

        :param kernel_size: 高斯模糊核心大小
        """
        self.kernel_size = kernel_size

    def apply_gaussian_blur(
        self, image: np.ndarray, kernel_size: tuple[int, int] | None = None
    ) -> np.ndarray:
        """對影像應用高斯模糊

        :param image: 輸入影像
        :param kernel_size: 模糊核心大小 (可選)
        :return: 模糊後的影像
        """
        if kernel_size is None:
            kernel_size = self.kernel_size

        try:
            return cv2.GaussianBlur(image, kernel_size, 0)
        except Exception as e:
            logger.error(f"高斯模糊失敗: {e}")
            return image

    def process_detections(
        self,
        image: np.ndarray,
        detections: list[DetectionBox],
        enable_draw: bool = False,
    ) -> dict[str, dict[str, Any]]:
        """處理偵測結果並建立模糊區域

        :param image: 輸入影像 (將被就地修改)
        :param detections: 偵測框列表
        :param enable_draw: 是否繪製偵測框
        :return: 模糊區域的字典
        """
        blur_regions = {}

        for i, detection in enumerate(detections):
            try:
                # 確保座標在影像邊界內
                h, w = image.shape[:2]
                x1 = max(0, min(detection.x1, w))
                y1 = max(0, min(detection.y1, h))
                x2 = max(x1, min(detection.x2, w))
                y2 = max(y1, min(detection.y2, h))

                # 跳過無效區域
                if x2 <= x1 or y2 <= y1:
                    continue

                # 提取感興趣的區域
                roi = image[y1:y2, x1:x2]
                if roi.size == 0:
                    continue

                # 應用模糊
                blurred_roi = self.apply_gaussian_blur(roi)

                # 將模糊應用於原始影像 (就地修改)
                image[y1:y2, x1:x2] = blurred_roi

                # 如果啟用，則繪製偵測框
                if enable_draw:
                    self._draw_detection_box(image, detection, (x1, y1, x2, y2))

                # 儲存模糊區域資訊
                region_name = f"region{i+1}"
                blur_regions[region_name] = {
                    "box": [x1, y1, x2, y2, detection.confidence, detection.class_id],
                    "image": blurred_roi,
                }

            except Exception as e:
                logger.error(f"處理第 {i} 個偵測時失敗: {e}")
                continue

        return blur_regions

    def _draw_detection_box(
        self,
        image: np.ndarray,
        detection: DetectionBox,
        coords: tuple[int, int, int, int],
    ):
        """在影像上繪製偵測框

        :param image: 要繪製的影像
        :param detection: 偵測框
        :param coords: 正規化後的座標 (x1, y1, x2, y2)
        """
        x1, y1, x2, y2 = coords

        # 繪製矩形
        cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), thickness=3)

        # 繪製信賴度分數
        score_text = f"Score: {detection.confidence:.3f}"
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(image, score_text, (x1, y1 - 10), font, 0.7, (255, 255, 255), 2)

    def create_blur_mask(
        self, image_shape: tuple[int, int], detections: list[DetectionBox]
    ) -> np.ndarray:
        """為模糊區域建立二元遮罩

        :param image_shape: 影像形狀 (高, 寬)
        :param detections: 偵測框列表
        :return: 二元遮罩 (模糊區域為 255，正常區域為 0)
        """
        mask = np.zeros(image_shape, dtype=np.uint8)

        for detection in detections:
            x1 = max(0, min(detection.x1, image_shape[1]))
            y1 = max(0, min(detection.y1, image_shape[0]))
            x2 = max(x1, min(detection.x2, image_shape[1]))
            y2 = max(y1, min(detection.y2, image_shape[0]))

            if x2 > x1 and y2 > y1:
                mask[y1:y2, x1:x2] = 255

        return mask

    def apply_selective_blur(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """僅對遮罩區域應用模糊

        :param image: 輸入影像
        :param mask: 二元遮罩 (模糊區域為 255)
        :return: 選擇性模糊後的影像
        """
        # 建立整個影像的模糊版本
        blurred = self.apply_gaussian_blur(image)

        # 僅在遮罩為非零的地方應用模糊
        result = image.copy()
        blur_mask = mask > 0
        result[blur_mask] = blurred[blur_mask]

        return result
