"""
Labelme 標註處理器 - 獨立模組
支援 Labelme 格式標註檔案的載入、儲存、解析和座標轉換
"""
import os
import json
import base64
import logging
from pathlib import Path
from typing import Any

logger = logging.getLogger(__name__)


class LabelmeProcessor:
    """Labelme 標註處理器 - 獨立版本"""

    def __init__(self, coord_transformer=None):
        """初始化處理器
        
        Args:
            coord_transformer: 座標轉換器實例，用於標註座標轉換
        """
        self.coord_transformer = coord_transformer
        self.face_names = ['F', 'R', 'B', 'L', 'U', 'D']

    @staticmethod
    def load_labelme_file(labelme_path: str) -> dict | None:
        """載入 labelme 標註檔案"""
        try:
            with open(labelme_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功載入標註檔案: {labelme_path}")
            return data
        except Exception as e:
            logger.error(f"無法載入標註檔案 {labelme_path}: {e}")
            return None

    @staticmethod
    def save_labelme_file(shapes: list[dict], output_path: str,
                          img_width: int, img_height: int,
                          image_data: str | None = None,
                          image_path: str = "") -> bool:
        """儲存 labelme 格式檔案"""
        try:
            labelme_data = {
                "version": "5.0.1",
                "flags": {},
                "shapes": shapes,
                "imagePath": os.path.basename(image_path),
                "imageData": image_data,
                "imageHeight": img_height,
                "imageWidth": img_width
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, indent=2, ensure_ascii=False)

            logger.info(f"儲存標註檔案: {output_path}")
            return True
        except Exception as e:
            logger.error(f"儲存標註檔案失敗 {output_path}: {e}")
            return False

    @staticmethod
    def image_to_base64(image_path: str) -> str | None:
        """
        🚨 已棄用 - 請使用 utils.image_utils.ImageEncoder.image_to_base64
        將圖像檔案轉換為 base64 編碼
        """
        try:
            from utils.image_utils import ImageEncoder
            return ImageEncoder.image_to_base64(image_path)
        except Exception as e:
            logger.error(f"轉換圖像為 base64 失敗 {image_path}: {e}")
            return None

    @staticmethod
    def parse_labelme_annotations(labelme_data: dict) -> list[dict]:
        """解析 labelme 標註為統一格式"""
        annotations = []

        for shape in labelme_data.get('shapes', []):
            if shape['shape_type'] == 'rectangle':
                points = shape['points']
                x1, y1 = points[0]
                x2, y2 = points[1]

                annotation = {
                    'label': shape['label'],
                    'x1': min(x1, x2),
                    'y1': min(y1, y2),
                    'x2': max(x1, x2),
                    'y2': max(y1, y2),
                    'shape_type': 'rectangle',
                    'group_id': shape.get('group_id'),
                    'flags': shape.get('flags', {})
                }
                annotations.append(annotation)

            elif shape['shape_type'] == 'polygon':
                points = shape['points']
                xs = [p[0] for p in points]
                ys = [p[1] for p in points]

                annotation = {
                    'label': shape['label'],
                    'x1': min(xs),
                    'y1': min(ys),
                    'x2': max(xs),
                    'y2': max(ys),
                    'points': points,
                    'shape_type': 'polygon',
                    'group_id': shape.get('group_id'),
                    'flags': shape.get('flags', {})
                }
                annotations.append(annotation)

        return annotations

    def convert_panorama_to_cube_annotations(self, annotations: list[dict],
                                             pano_width: int, pano_height: int,
                                             cube_size: int) -> dict[str, list[dict]]:
        """將全景圖標註轉換為立方體面標註
        
        注意：此方法需要 coord_transformer 已設置
        """
        if not self.coord_transformer:
            raise ValueError("需要設置 coord_transformer 才能進行座標轉換")
            
        cube_annotations = {face: [] for face in self.face_names}

        for annotation in annotations:
            if annotation['shape_type'] == 'rectangle':
                face_boxes = self.coord_transformer.convert_panorama_bbox_to_cube_faces(
                    annotation, pano_width, pano_height, cube_size)

                for face_name, boxes in face_boxes.items():
                    cube_annotations[face_name].extend(boxes)

            elif annotation['shape_type'] == 'polygon':
                face_boxes = self._convert_polygon_to_cube_faces(
                    annotation, pano_width, pano_height, cube_size)

                for face_name, boxes in face_boxes.items():
                    cube_annotations[face_name].extend(boxes)

        return cube_annotations

    def convert_cube_to_panorama_annotations(self, cube_annotations: dict[str, list[dict]],
                                             cube_size: int, pano_width: int,
                                             pano_height: int) -> list[dict]:
        """將立方體面標註轉換為全景圖標註
        
        注意：此方法需要 coord_transformer 已設置
        """
        if not self.coord_transformer:
            raise ValueError("需要設置 coord_transformer 才能進行座標轉換")
            
        panorama_annotations = []

        for face_name, annotations in cube_annotations.items():
            for ann in annotations:
                if 'points' in ann and len(ann['points']) == 2:
                    bbox = {
                        'label': ann['label'],
                        'x1': ann['points'][0][0],
                        'y1': ann['points'][0][1],
                        'x2': ann['points'][1][0],
                        'y2': ann['points'][1][1],
                        'group_id': ann.get('group_id'),
                        'flags': ann.get('flags', {})
                    }

                    converted_bbox = self.coord_transformer.convert_cube_face_bbox_to_panorama(
                        face_name, bbox, cube_size, pano_width, pano_height)

                    if converted_bbox:
                        labelme_shape = {
                            'label': converted_bbox['label'],
                            'points': converted_bbox['points'],
                            'group_id': converted_bbox.get('group_id'),
                            'shape_type': 'rectangle',
                            'flags': converted_bbox.get('flags', {})
                        }
                        panorama_annotations.append(labelme_shape)

        return panorama_annotations

    def _convert_polygon_to_cube_faces(self, polygon: dict, pano_width: int,
                                       pano_height: int, cube_size: int) -> dict[str, list[dict]]:
        """將多邊形標註轉換為立方體面標註"""
        if not self.coord_transformer:
            raise ValueError("需要設置 coord_transformer 才能進行座標轉換")
            
        face_annotations = {face: [] for face in self.face_names}

        x1, y1, x2, y2 = polygon['x1'], polygon['y1'], polygon['x2'], polygon['y2']
        polygon_points = polygon.get('points', [])

        # 密集採樣多邊形內部的點
        sample_points = []
        grid_size = 25

        for i in range(grid_size):
            for j in range(grid_size):
                x = x1 + (x2 - x1) * i / (grid_size - 1)
                y = y1 + (y2 - y1) * j / (grid_size - 1)

                if self.coord_transformer.point_in_polygon(x, y, polygon_points):
                    sample_points.append((x, y))

        # 添加多邊形頂點
        sample_points.extend([(p[0], p[1]) for p in polygon_points])

        # 轉換到立方體面
        face_points = {face: [] for face in self.face_names}

        for pano_u, pano_v in sample_points:
            face_name, face_x, face_y = self.coord_transformer.panorama_point_to_cube_face(
                pano_u, pano_v, pano_width, pano_height, cube_size)

            if face_name:
                face_points[face_name].append((face_x, face_y))

        # 為每個面創建邊界框
        for face_name, points_list in face_points.items():
            if len(points_list) >= 4:
                xs = [p[0] for p in points_list]
                ys = [p[1] for p in points_list]

                face_x1 = max(0, min(xs) - 3)
                face_y1 = max(0, min(ys) - 3)
                face_x2 = min(cube_size, max(xs) + 3)
                face_y2 = min(cube_size, max(ys) + 3)

                if face_x2 > face_x1 + 5 and face_y2 > face_y1 + 5:
                    face_annotations[face_name].append({
                        'label': polygon['label'],
                        'points': [[face_x1, face_y1], [face_x2, face_y2]],
                        'shape_type': 'rectangle',
                        'group_id': polygon.get('group_id'),
                        'flags': polygon.get('flags', {}),
                        'original_shape_type': 'polygon'
                    })

        return face_annotations

    @classmethod
    def create_with_coordinate_transformer(cls, coord_transformer):
        """工廠方法：創建帶有座標轉換器的處理器"""
        return cls(coord_transformer)

    @classmethod
    def create_basic(cls):
        """工廠方法：創建基礎處理器（僅支援檔案操作，不支援座標轉換）"""
        return cls(None)