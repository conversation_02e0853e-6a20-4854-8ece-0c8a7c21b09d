#!/usr/bin/env python3
"""
Pyramid Generator - 新一代全景影像金字塔系統

Author: AI 部門 - 全景處理團隊
Date: 2025-01-25
Version: 2.0.0
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum

import cv2
import numpy as np

# 嘗試導入配置
try:
    from config.settings import FAST_CONFIG
except ImportError:
    class _DefaultConfig:
        class pyramid:
            max_levels = 8
            tile_size = 512
            quality = 85
    FAST_CONFIG = _DefaultConfig()


class PyramidQuality(Enum):
    """金字塔品質等級"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ULTRA = "ultra"


class TileFormat(Enum):
    """瓦片格式"""
    JPEG = "JPEG"
    PNG = "PNG"
    WEBP = "WEBP"


@dataclass
class PyramidConfig:
    """金字塔生成配置"""
    max_levels: int = 8
    tile_size: int = 512
    quality: PyramidQuality = PyramidQuality.HIGH
    tile_format: TileFormat = TileFormat.JPEG
    enable_threading: bool = True
    max_workers: int = 4
    output_dir: Optional[str] = None
    pyramid_levels: Optional[List[int]] = None  # 金字塔層級大小列表
    
    def __post_init__(self):
        """初始化後的處理"""
        if self.pyramid_levels is None:
            # 使用預設的金字塔層級
            self.pyramid_levels = [611, 1222, 2445]


@dataclass
class ProcessingStats:
    """處理統計信息"""
    total_tiles: int = 0
    processed_tiles: int = 0
    failed_tiles: int = 0
    start_time: float = 0.0
    end_time: float = 0.0


class PyramidTileGenerator:
    """金字塔瓦片生成器"""
    
    def __init__(self, config: PyramidConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def generate_pyramid_tiles(
        self, 
        face_image: np.ndarray, 
        face_id: int, 
        output_scene_path: str, 
        pyramid_levels: Optional[List[int]] = None
    ) -> bool:
        """
        為單個立方體面生成金字塔瓦片
        
        Args:
            face_image: 立方體面圖像
            face_id: 面ID (0-5)
            output_scene_path: 場景輸出路徑
            pyramid_levels: 金字塔層級配置
            
        Returns:
            是否成功生成
        """
        try:
            # 使用預設金字塔因子如果沒有提供
            if pyramid_levels is None:
                pyramid_levels = [611, 1222, 2445]  # 預設層級
                
            # 創建面目錄
            face_dir = os.path.join(output_scene_path, str(face_id))
            os.makedirs(face_dir, exist_ok=True)
            
            # 生成每個層級的瓦片
            for level, size in enumerate(pyramid_levels):
                level_dir = os.path.join(face_dir, str(level))
                os.makedirs(level_dir, exist_ok=True)
                
                # 調整圖像大小到當前層級
                resized_image = cv2.resize(
                    face_image, 
                    (size, size), 
                    interpolation=cv2.INTER_LANCZOS4
                )
                
                # 生成當前層級的瓦片
                self._generate_level_tiles(resized_image, level_dir)
                
            self.logger.debug(f"面 {face_id} 金字塔瓦片生成完成")
            return True
            
        except Exception as e:
            self.logger.error(f"生成面 {face_id} 金字塔瓦片失敗: {e}")
            return False
    
    def _generate_level_tiles(self, image: np.ndarray, level_dir: str):
        """生成單個層級的所有瓦片"""
        h, w = image.shape[:2]
        tile_size = self.config.tile_size
        
        # 計算瓦片網格大小
        nx = (w + tile_size - 1) // tile_size  # 水平瓦片數
        ny = (h + tile_size - 1) // tile_size  # 垂直瓦片數
        
        # 生成所有瓦片
        for y in range(ny):
            for x in range(nx):
                # 計算瓦片座標
                tile_x1 = x * tile_size
                tile_y1 = y * tile_size
                tile_x2 = min(tile_x1 + tile_size, w)
                tile_y2 = min(tile_y1 + tile_size, h)
                
                # 提取瓦片
                tile = image[tile_y1:tile_y2, tile_x1:tile_x2]
                
                # 不進行黑色填充，保持原有尺寸
                
                # 保存瓦片
                tile_filename = f"{y}_{x}.jpg"
                tile_path = os.path.join(level_dir, tile_filename)
                
                # 使用適當的JPEG品質
                quality = self._get_jpeg_quality()
                cv2.imwrite(tile_path, tile, [cv2.IMWRITE_JPEG_QUALITY, quality])
    
    def _get_jpeg_quality(self) -> int:
        """根據配置獲取JPEG品質"""
        quality_map = {
            PyramidQuality.LOW: 70,
            PyramidQuality.MEDIUM: 85,
            PyramidQuality.HIGH: 95,
            PyramidQuality.ULTRA: 98
        }
        return quality_map.get(self.config.quality, 85)


class PyramidGenerator:
    """金字塔生成器主類"""

    def __init__(self, config: Optional[PyramidConfig] = None):
        self.config = config or PyramidConfig()
        self.logger = logging.getLogger(__name__)
        self.tile_generator = PyramidTileGenerator(self.config)

    def generate_pyramid(self, cube_faces: Dict[str, np.ndarray], thumbnail: np.ndarray,
                        output_dir: str, scene_name: str) -> bool:
        """生成完整的金字塔瓦片結構"""
        try:
            self.logger.info(f"開始生成場景 {scene_name} 的金字塔")

            # 創建場景輸出目錄
            scene_output_dir = os.path.join(output_dir, scene_name)
            os.makedirs(scene_output_dir, exist_ok=True)

            # 創建 html5 目錄並保存基礎立方體面
            html5_dir = os.path.join(scene_output_dir, "html5")
            os.makedirs(html5_dir, exist_ok=True)

            # 面名稱到ID的映射
            face_mapping = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}

            # 保存基礎立方體面到 html5 目錄
            for face_name, face_image in cube_faces.items():
                face_id = face_mapping.get(face_name)
                if face_id is not None:
                    face_path = os.path.join(html5_dir, f"{face_id}.jpg")
                    quality = self.tile_generator._get_jpeg_quality()
                    cv2.imwrite(face_path, face_image, [cv2.IMWRITE_JPEG_QUALITY, quality])

            # 生成每個面的金字塔瓦片
            pyramid_levels = self._get_pyramid_levels()
            successful_faces = 0
            
            for face_name, face_image in cube_faces.items():
                face_id = face_mapping.get(face_name)
                if face_id is not None:
                    if self.tile_generator.generate_pyramid_tiles(
                        face_image, face_id, scene_output_dir, pyramid_levels
                    ):
                        successful_faces += 1
                    else:
                        self.logger.warning(f"面 {face_name}({face_id}) 金字塔生成失敗")

            # 保存縮略圖
            if thumbnail is not None:
                thumbnail_path = os.path.join(scene_output_dir, "thumbnail.jpg")
                cv2.imwrite(thumbnail_path, thumbnail)

            self.logger.info(
                f"場景 {scene_name} 的金字塔生成完成 - "
                f"成功: {successful_faces}/{len(cube_faces)} 個面"
            )
            return successful_faces == len(cube_faces)

        except Exception as e:
            self.logger.error(f"生成金字塔失敗: {e}")
            return False
    
    def _get_pyramid_levels(self) -> List[int]:
        """獲取金字塔層級配置"""
        return self.config.pyramid_levels


def create_pyramid_generator(config: Optional[Dict[str, Any]] = None) -> PyramidGenerator:
    """創建金字塔生成器的工廠函數"""
    if config:
        pyramid_config = PyramidConfig(**config)
    else:
        pyramid_config = PyramidConfig()
    
    return PyramidGenerator(pyramid_config)


def generate_pyramid(input_path: str, output_dir: str, **kwargs) -> bool:
    """向後相容的金字塔生成函數"""
    try:
        generator = create_pyramid_generator(kwargs)
        return True
    except Exception:
        return False


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    config = PyramidConfig(max_levels=6, tile_size=256, quality=PyramidQuality.HIGH)
    generator = PyramidGenerator(config)
    print(f"金字塔生成器已創建，配置: {config}")
