#!/usr/bin/env python3
"""
統一圖像緩存管理模組
Unified Image Cache Management Module

提供一個統一的圖像和中間處理結果的緩存機制，以減少重複的 I/O 和計算。
Provides a unified caching mechanism for images and intermediate processing results to reduce redundant I/O and computation.

Author: Roo
Date: 2025-07-19
"""

# 1. 標準庫
import logging
import time
from collections import OrderedDict
from functools import lru_cache
from pathlib import Path
from typing import Any

# 2. 第三方庫
import cv2
import numpy as np

class ImageCache:
    """
    統一的圖像和數據緩存管理，避免重複載入和計算。

    特性:
    - 使用 LRU (Least Recently Used) 策略管理內存中的圖像對象。
    - 提供基於文件路徑的磁盤緩存（可選，未來擴展）。
    - 智能清理機制，以防止內存溢出。
    """
    
    def __init__(self, max_size_mb: int = 1024):
        """
        初始化圖像緩存

        Args:
            max_size_mb: 緩存的最大內存限制（MB）
        """
        self.max_size_mb = max_size_mb
        self._cache = OrderedDict()  # 鍵: cache_key, 值: (data, size_mb, timestamp)
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"圖像緩存已初始化，最大大小: {max_size_mb} MB")

    @lru_cache(maxsize=128)
    def load_image(self, image_path: str | Path) -> np.ndarray | None:
        """
        從文件路徑加載圖像，並利用 lru_cache 進行緩存。
        這主要緩存函數調用本身，適合路徑相對固定的場景。

        Args:
            image_path: 圖像文件路徑

        Returns:
            加載的圖像 (np.ndarray)，如果失敗則返回 None。
        """
        try:
            image = cv2.imread(str(image_path))
            if image is None:
                self.logger.warning(f"無法從路徑加載圖像: {image_path}")
                return None
            self.logger.debug(f"圖像已從磁盤加載: {image_path}")
            return image
        except Exception as e:
            self.logger.error(f"加載圖像時發生錯誤 {image_path}: {e}")
            return None

    def get(self, key: str) -> Any | None:
        """從緩存中獲取數據"""
        if key in self._cache:
            # 更新訪問時間
            self._cache[key] = (self._cache[key][0], self._cache[key][1], time.time())
            self._cache.move_to_end(key)
            return self._cache[key][0]
        return None

    def put(self, key: str, data: Any, size_mb: float | None = None):
        """
        將數據放入緩存

        Args:
            key: 緩存鍵
            data: 要緩存的數據
            size_mb: 數據的大小（MB）。如果為 None，會嘗試自動計算。
        """
        if size_mb is None:
            if isinstance(data, np.ndarray):
                size_mb = data.nbytes / (1024 * 1024)
            else:
                # 對於非 numpy 數組，這是一個粗略估計
                size_mb = 1 

        # 清理緩存以騰出空間
        while self._current_size_mb() + size_mb > self.max_size_mb and self._cache:
            self._evict_one()

        self._cache[key] = (data, size_mb, time.time())
        self.logger.debug(f"已緩存數據 '{key}' (大小: {size_mb:.2f} MB)。當前緩存大小: {self._current_size_mb():.2f} MB")

    def _evict_one(self):
        """驅逐最久未使用的項目"""
        key, (data, size, _) = self._cache.popitem(last=False)
        self.logger.info(f"緩存已滿，正在驅逐最久未使用的項目: '{key}' (大小: {size:.2f} MB)")
        del data # 輔助垃圾回收

    def _current_size_mb(self) -> float:
        """計算當前緩存的總大小"""
        return sum(size for _, size, _ in self._cache.values())

    def clear(self):
        """清空整個緩存"""
        self._cache.clear()
        self.load_image.cache_clear()
        self.logger.info("圖像緩存已完全清空。")
