"""
pytest configuration and shared fixtures
"""

import json
import os
import sys
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, Mock

import numpy as np
import pytest



@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture
def sample_config():
    """Sample configuration for testing"""
    return {
        "processing": {"cube_size": 1024, "image_quality": 95, "max_workers": 2},
        "model": {"conf_threshold": 0.5, "iou_threshold": 0.4},
        "system": {"enable_gpu": False, "log_level": "INFO"},
    }


@pytest.fixture
def sample_image():
    """Create a small sample image for testing"""
    # Create a simple 64x32 RGB image (simulating a panoramic aspect ratio)
    return np.random.randint(0, 255, (32, 64, 3), dtype=np.uint8)


@pytest.fixture
def sample_cube_faces():
    """Create sample cube face images for testing"""
    faces = {}
    face_names = ["front", "back", "left", "right", "up", "down"]
    for face in face_names:
        faces[face] = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
    return faces


@pytest.fixture
def mock_detector():
    """Mock YOLO detector for testing"""
    detector = Mock()
    detector.detect.return_value = [
        {"bbox": [100, 100, 50, 50], "confidence": 0.8, "class": "face"}
    ]
    detector.is_available.return_value = True
    return detector


@pytest.fixture
def mock_gpu_manager():
    """Mock GPU manager for testing"""
    gpu_manager = Mock()
    gpu_manager.is_available.return_value = False
    gpu_manager.get_memory_info.return_value = {"free": 1000, "total": 8000}
    gpu_manager.get_device_count.return_value = 0
    return gpu_manager


@pytest.fixture
def test_config_file(temp_dir, sample_config):
    """Create a temporary configuration file"""
    config_file = temp_dir / "test_config.json"
    with open(config_file, "w", encoding="utf-8") as f:
        json.dump(sample_config, f, indent=2)
    return config_file


@pytest.fixture
def sample_coordinates():
    """Sample coordinate data for testing"""
    return {
        "theta": np.linspace(0, 2 * np.pi, 10),
        "phi": np.linspace(-np.pi / 2, np.pi / 2, 5),
        "x": np.random.uniform(-1, 1, 50),
        "y": np.random.uniform(-1, 1, 50),
        "z": np.random.uniform(-1, 1, 50),
    }


@pytest.fixture
def mock_cv2():
    """Mock OpenCV for testing"""
    cv2_mock = Mock()
    cv2_mock.imread.return_value = np.random.randint(
        0, 255, (480, 640, 3), dtype=np.uint8
    )
    cv2_mock.imwrite.return_value = True
    cv2_mock.resize.return_value = np.random.randint(
        0, 255, (256, 256, 3), dtype=np.uint8
    )
    return cv2_mock


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Setup test environment variables"""
    monkeypatch.setenv("TESTING", "1")
    monkeypatch.setenv("DISABLE_GPU", "1")
    monkeypatch.setenv("LOG_LEVEL", "ERROR")


@pytest.fixture
def sample_detection_results():
    """Sample detection results for testing"""
    return [
        {
            "bbox": [100, 100, 50, 50],
            "confidence": 0.85,
            "class": "face",
            "class_id": 0,
        },
        {
            "bbox": [200, 150, 80, 40],
            "confidence": 0.72,
            "class": "license_plate",
            "class_id": 1,
        },
    ]


@pytest.fixture
def mock_memory_manager():
    """Mock memory manager for testing"""
    memory_manager = Mock()
    memory_manager.get_available_memory.return_value = 4000  # MB
    memory_manager.cleanup.return_value = None
    memory_manager.set_memory_limit.return_value = None
    return memory_manager


# Test data paths
@pytest.fixture
def test_data_dir():
    """Test data directory path"""
    return Path(__file__).parent / "fixtures"


@pytest.fixture
def test_images_dir(test_data_dir):
    """Test images directory path"""
    return test_data_dir / "test_images"


@pytest.fixture
def test_configs_dir(test_data_dir):
    """Test configs directory path"""
    return test_data_dir / "test_configs"


# Cleanup fixture
@pytest.fixture(autouse=True)
def cleanup_temp_files():
    """Cleanup temporary files after each test"""
    yield
    # Cleanup code here if needed
    import gc

    gc.collect()


