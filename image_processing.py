import cv2
import numpy as np
import os
import torch
import torch.nn.functional as F
import time
import logging
from PIL import Image, UnidentifiedImageError
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import List, Dict, Tuple, Optional, Union, Iterator
from functools import lru_cache, wraps
from dataclasses import dataclass, field
from pathlib import Path
import threading
from queue import Queue, Empty
import weakref
import gc
import mmap
from collections import defaultdict
import multiprocessing as mp

# 從配置和其他模組導入
from config import (
    SUPPORTED_IMAGE_EXTS, Face, SaveMode,
    SYSTEM_CONFIG, PERFORMANCE_CONFIG
)

logger = logging.getLogger('panorama_processor')

# 性能監控裝飾器


def processing_timer(func):
    """圖像處理性能計時裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        logger.debug(f"{func.__name__} 處理耗時: {elapsed:.3f}秒")
        return result
    return wrapper

# 圖像驗證結果快取


@lru_cache(maxsize=1000)
def is_valid_image_cached(file_path: str) -> bool:
    """快取版本的圖像驗證"""
    try:
        ext = Path(file_path).suffix.lower()
        if ext not in SUPPORTED_IMAGE_EXTS:
            return False

        # 快速檔案大小檢查
        if not os.path.exists(file_path) or os.path.getsize(file_path) < 100:
            return False

        # 使用PIL進行快速驗證
        with Image.open(file_path) as img:
            img.verify()
        return True
    except (OSError, UnidentifiedImageError, ValueError):
        return False
    except Exception as e:
        logger.warning(f"檢查圖像 {file_path} 時發生未知錯誤: {e}")
        return False

# 高效能圖像讀寫類別


class OptimizedImageIO:
    """最佳化的圖像I/O處理器"""

    def __init__(self):
        self.read_cache = weakref.WeakValueDictionary()
        self.write_queue = Queue(maxsize=100)
        self.lock = threading.Lock()
        self._start_write_worker()

    def _start_write_worker(self):
        """啟動寫入工作執行緒"""
        if PERFORMANCE_CONFIG.use_async_io:
            self.write_thread = threading.Thread(
                target=self._write_worker, daemon=True)
            self.write_thread.start()

    def _write_worker(self):
        """非同步寫入工作執行緒"""
        while True:
            try:
                write_task = self.write_queue.get(timeout=1.0)
                if write_task is None:  # 終止信號
                    break

                file_path, image, quality = write_task
                self._write_image_sync(file_path, image, quality)
                self.write_queue.task_done()

            except Empty:
                continue
            except Exception as e:
                logger.error(f"非同步寫入失敗: {e}")

    @processing_timer
    def read_image_fast(self, file_path: str) -> Optional[np.ndarray]:
        """高效能圖像讀取"""
        # 檢查快取
        with self.lock:
            if file_path in self.read_cache:
                return self.read_cache[file_path]

        try:
            # 快速檔案格式檢查
            ext = Path(file_path).suffix.lower()
            if ext not in SUPPORTED_IMAGE_EXTS:
                logger.warning(f"不支援的圖像格式: {file_path}")
                return None

            # 根據檔案大小選擇讀取策略
            file_size = os.path.getsize(file_path)

            if file_size > 100 * 1024 * 1024:  # 大於100MB使用記憶體映射
                img = self._read_with_mmap(file_path)
            elif file_size > 10 * 1024 * 1024:  # 大於10MB使用優化讀取
                img = self._read_optimized(file_path)
            else:
                img = self._read_standard(file_path)

            # 加入快取（小圖像）
            if img is not None and file_size < 50 * 1024 * 1024:
                with self.lock:
                    self.read_cache[file_path] = img

            return img

        except Exception as e:
            logger.warning(f"讀取圖像 {file_path} 時發生錯誤: {e}")
            return None

    def _read_with_mmap(self, file_path: str) -> Optional[np.ndarray]:
        """使用記憶體映射讀取大檔案"""
        try:
            with open(file_path, 'rb') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                    img_data = np.frombuffer(mm, dtype=np.uint8)
                    return cv2.imdecode(img_data, cv2.IMREAD_UNCHANGED)
        except Exception as e:
            logger.warning(f"記憶體映射讀取失敗 {file_path}: {e}")
            return self._read_optimized(file_path)

    def _read_optimized(self, file_path: str) -> Optional[np.ndarray]:
        """優化讀取中等大小檔案"""
        try:
            img_data = np.fromfile(file_path, dtype=np.uint8)
            return cv2.imdecode(img_data, cv2.IMREAD_UNCHANGED)
        except Exception:
            return self._read_standard(file_path)

    def _read_standard(self, file_path: str) -> Optional[np.ndarray]:
        """標準讀取小檔案"""
        try:
            return cv2.imread(file_path, cv2.IMREAD_UNCHANGED)
        except Exception:
            return None

    def write_image_async(self, file_path: str, image: np.ndarray, quality: int = SYSTEM_CONFIG.image_quality):
        """非同步圖像寫入"""
        if PERFORMANCE_CONFIG.use_async_io:
            try:
                self.write_queue.put_nowait((file_path, image.copy(), quality))
                return True
            except:
                # 佇列滿時回退到同步寫入
                return self._write_image_sync(file_path, image, quality)
        else:
            return self._write_image_sync(file_path, image, quality)

    def _write_image_sync(self, file_path: str, image: np.ndarray, quality: int) -> bool:
        """同步圖像寫入"""
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(
                os.path.abspath(file_path)), exist_ok=True)

            ext = Path(file_path).suffix.lower()

            # 選擇最佳編碼參數
            if ext in ['.jpg', '.jpeg']:
                params = [cv2.IMWRITE_JPEG_QUALITY, quality,
                          cv2.IMWRITE_JPEG_OPTIMIZE, 1]
            elif ext == '.png':
                compress_level = min(9, max(0, int(9 - quality / 10)))
                params = [cv2.IMWRITE_PNG_COMPRESSION, compress_level]
            else:
                params = []

            # 多執行緒安全的寫入
            success = cv2.imwrite(file_path, image, params)

            if not success:
                logger.error(f"OpenCV寫入失敗: {file_path}")
                # 嘗試使用PIL作為備用
                try:
                    pil_image = Image.fromarray(
                        cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                    pil_image.save(file_path, quality=quality, optimize=True)
                    success = True
                except Exception as e:
                    logger.error(f"PIL寫入也失敗: {e}")

            return success

        except Exception as e:
            logger.error(f"保存圖像 {file_path} 時發生錯誤: {e}")
            return False

    def __del__(self):
        """清理資源"""
        if hasattr(self, 'write_queue'):
            try:
                self.write_queue.put_nowait(None)  # 終止信號
                if hasattr(self, 'write_thread'):
                    self.write_thread.join(timeout=5.0)
            except:
                pass


# 全域圖像I/O實例
IMAGE_IO = OptimizedImageIO()

# 快捷函數（向後相容）


def cv_imread(file_path: str) -> Optional[np.ndarray]:
    """最佳化的圖像讀取函數"""
    return IMAGE_IO.read_image_fast(file_path)


def cv_imwrite(file_path: str, image: np.ndarray, quality: int = SYSTEM_CONFIG.image_quality) -> bool:
    """最佳化的圖像寫入函數"""
    return IMAGE_IO.write_image_async(file_path, image, quality)

# GPU加速圖像縮放


class GPUImageResizer:
    """GPU加速的圖像縮放器"""

    def __init__(self):
        self.device = torch.device(
            'cuda' if torch.cuda.is_available() else 'cpu')
        self.use_gpu = torch.cuda.is_available() and SYSTEM_CONFIG.enable_gpu_acceleration
        self.tensor_cache = {}
        self.lock = threading.Lock()

    def resize_image(self, image: np.ndarray, target_size: Tuple[int, int],
                     scale_factor: Optional[float] = None) -> np.ndarray:
        """智慧圖像縮放"""
        if scale_factor is not None:
            target_size = (int(image.shape[0] * scale_factor),
                           int(image.shape[1] * scale_factor))

        # 選擇最佳縮放方法
        if self.use_gpu and image.size > 1000000:  # 大圖像使用GPU
            return self._resize_gpu(image, target_size)
        else:
            return self._resize_cpu(image, target_size, scale_factor)

    def _resize_gpu(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """GPU縮放"""
        try:
            # 轉換為PyTorch張量
            if len(image.shape) == 3:
                img_tensor = torch.from_numpy(
                    image.transpose(2, 0, 1)).unsqueeze(0).float()
            else:
                img_tensor = torch.from_numpy(
                    image).unsqueeze(0).unsqueeze(0).float()

            img_tensor = img_tensor.to(self.device)

            # 執行縮放
            with torch.no_grad():
                resized = F.interpolate(img_tensor, size=target_size,
                                        mode='bilinear', align_corners=False)

            # 轉回numpy
            if len(image.shape) == 3:
                result = resized.squeeze(0).permute(
                    1, 2, 0).cpu().numpy().astype(image.dtype)
            else:
                result = resized.squeeze(0).squeeze(
                    0).cpu().numpy().astype(image.dtype)

            return result

        except Exception as e:
            logger.warning(f"GPU縮放失敗，回退到CPU: {e}")
            return self._resize_cpu(image, target_size)

    def _resize_cpu(self, image: np.ndarray, target_size: Tuple[int, int],
                    scale_factor: Optional[float] = None) -> np.ndarray:
        """CPU縮放"""
        # 智慧選擇插值方法
        if scale_factor is not None:
            interpolation = cv2.INTER_AREA if scale_factor < 1.0 else cv2.INTER_LINEAR
        else:
            current_area = image.shape[0] * image.shape[1]
            target_area = target_size[0] * target_size[1]
            interpolation = cv2.INTER_AREA if target_area < current_area else cv2.INTER_LINEAR

        return cv2.resize(image, (target_size[1], target_size[0]), interpolation=interpolation)


# 全域GPU縮放器
GPU_RESIZER = GPUImageResizer()


def torch_resize_image(image: np.ndarray, target_size: Tuple[int, int],
                       device: str = None) -> np.ndarray:
    """GPU加速圖像縮放（向後相容函數）"""
    return GPU_RESIZER.resize_image(image, target_size)

# Logo疊加優化器


class OptimizedLogoOverlay:
    """最佳化的Logo疊加處理器"""

    def __init__(self):
        self.logo_cache = {}
        self.alpha_cache = {}
        self.lock = threading.Lock()

    @lru_cache(maxsize=32)
    def get_processed_logo(self, logo_path: str, target_size: Tuple[int, int]) -> Optional[np.ndarray]:
        """獲取處理過的logo"""
        cache_key = f"{logo_path}_{target_size}"

        with self.lock:
            if cache_key in self.logo_cache:
                return self.logo_cache[cache_key]

        try:
            logo = cv2.imread(logo_path, cv2.IMREAD_UNCHANGED)
            if logo is None:
                return None

            # 縮放logo
            resized_logo = GPU_RESIZER.resize_image(logo, target_size)

            # 預處理alpha通道
            if resized_logo.shape[2] < 4:
                alpha = np.ones(resized_logo.shape[:2], dtype=np.uint8) * 255
                resized_logo = np.dstack([resized_logo, alpha])

            # 正規化alpha通道
            resized_logo = resized_logo.astype(np.float32)
            resized_logo[:, :, 3] /= 255.0

            with self.lock:
                self.logo_cache[cache_key] = resized_logo

            return resized_logo

        except Exception as e:
            logger.error(f"處理logo失敗: {e}")
            return None

    def overlay_logo_vectorized(self, face: np.ndarray, logo: np.ndarray,
                                position: Tuple[int, int]) -> np.ndarray:
        """向量化logo疊加"""
        x_offset, y_offset = position
        logo_h, logo_w = logo.shape[:2]

        # 確保logo在圖像範圍內
        face_h, face_w = face.shape[:2]
        if x_offset + logo_w > face_w or y_offset + logo_h > face_h:
            return face

        # 獲取ROI
        roi = face[y_offset:y_offset + logo_h, x_offset:x_offset + logo_w]

        # 向量化alpha混合
        if logo.shape[2] == 4:
            alpha = logo[:, :, 3:4]
            logo_rgb = logo[:, :, :3]

            # 一次性向量化計算
            blended = roi * (1 - alpha) + logo_rgb * alpha
            face[y_offset:y_offset + logo_h, x_offset:x_offset +
                 logo_w] = blended.astype(face.dtype)
        else:
            face[y_offset:y_offset + logo_h,
                 x_offset:x_offset + logo_w] = logo[:, :, :3]

        return face


# 全域logo疊加器
LOGO_OVERLAY = OptimizedLogoOverlay()

# 並行切片處理器


class ParallelSliceProcessor:
    """並行切片處理器"""

    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or SYSTEM_CONFIG.max_threads
        self.slice_cache = {}
        self.lock = threading.Lock()

    @processing_timer
    def save_image_slices_parallel(self, image: np.ndarray, folder: str,
                                   face_id: Optional[int] = None, selective: bool = False,
                                   slice_size: int = 512) -> int:
        """並行保存圖像切片"""
        height, width = image.shape[:2]
        num_rows = (height + slice_size - 1) // slice_size
        num_cols = (width + slice_size - 1) // slice_size

        # 生成切片資訊
        slice_infos = self._generate_slice_infos(
            height, width, num_rows, num_cols, slice_size, face_id, selective)

        if not slice_infos:
            return 0

        # 確保輸出目錄存在
        os.makedirs(folder, exist_ok=True)

        # 選擇處理策略
        if len(slice_infos) > 50 and PERFORMANCE_CONFIG.enable_batch_processing:
            return self._save_slices_batch(image, folder, slice_infos)
        else:
            return self._save_slices_parallel(image, folder, slice_infos)

    def _generate_slice_infos(self, height: int, width: int, num_rows: int, num_cols: int,
                              slice_size: int, face_id: Optional[int], selective: bool) -> List[Tuple]:
        """生成切片資訊"""
        slice_infos = []

        for row in range(num_rows):
            for col in range(num_cols):
                y1 = row * slice_size
                x1 = col * slice_size
                y2 = min(y1 + slice_size, height)
                x2 = min(x1 + slice_size, width)

                # 選擇性保存檢查
                if selective and face_id is not None:
                    if not self._should_save_slice(x1, y1, x2, y2, face_id):
                        continue

                slice_infos.append((row, col, y1, x1, y2, x2))

        return slice_infos

    def _should_save_slice(self, x1: int, y1: int, x2: int, y2: int, face_id: int) -> bool:
        """判斷是否應該保存此切片（基於模糊區域）"""
        # 這裡需要與模糊區域資訊整合，暫時返回True
        return True

    def _save_slices_parallel(self, image: np.ndarray, folder: str,
                              slice_infos: List[Tuple]) -> int:
        """並行保存切片"""
        def save_single_slice(info):
            row, col, y1, x1, y2, x2 = info
            try:
                tile = image[y1:y2, x1:x2]
                tile_path = os.path.join(folder, f"{row}_{col}.jpg")
                return IMAGE_IO.write_image_async(tile_path, tile)
            except Exception as e:
                logger.error(f"保存切片失敗 {row}_{col}: {e}")
                return False

        # 使用執行緒池並行處理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(save_single_slice, info)
                       for info in slice_infos]
            results = [future.result() for future in as_completed(futures)]

        success_count = sum(1 for r in results if r)
        logger.debug(f"成功保存 {success_count}/{len(slice_infos)} 個切片")
        return success_count

    def _save_slices_batch(self, image: np.ndarray, folder: str,
                           slice_infos: List[Tuple]) -> int:
        """批次保存切片（適用於大量切片）"""
        batch_size = SYSTEM_CONFIG.batch_size
        success_count = 0

        for i in range(0, len(slice_infos), batch_size):
            batch = slice_infos[i:i + batch_size]
            batch_count = self._save_slices_parallel(image, folder, batch)
            success_count += batch_count

            # 定期垃圾回收
            if i % (batch_size * 5) == 0:
                gc.collect()

        return success_count


# 全域切片處理器
SLICE_PROCESSOR = ParallelSliceProcessor()

# 主圖像處理類別


class ImageCutterWithLogo:
    """高效能圖像切割器，支援logo疊加和模糊處理"""

    def __init__(self, cube_dict: Dict[str, np.ndarray], preview: np.ndarray,
                 thumbnail: np.ndarray, output_scene_folder: str,
                 detector=None, logo_path: str = None, save_mode: SaveMode = SaveMode.ALL):

        # 基本設定
        self.cube_index_map = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}
        self.cubemaps = {
            self.cube_index_map[key]: value for key, value in cube_dict.items()}
        self.output_scene_folder = output_scene_folder
        self.save_mode = save_mode

        # 圖像資源
        self.preview = preview
        self.thumbnail = thumbnail

        # 檢測器和模糊區域
        self.detector = detector
        self.blurred_regions = {}

        # Logo處理
        self.logo_path = logo_path
        self.logo_img = None
        self._initialize_logo()

        # 效能監控
        self.processing_stats = defaultdict(float)

        # 記憶體管理
        self.memory_threshold = SYSTEM_CONFIG.memory_limit_mb * 1024 * 1024 * 0.8  # 80%閾值

    def _initialize_logo(self):
        """初始化logo"""
        if self.logo_path and os.path.exists(self.logo_path):
            try:
                self.logo_img = cv2.imread(
                    self.logo_path, cv2.IMREAD_UNCHANGED)
                if self.logo_img is not None:
                    logger.debug(f"成功載入logo: {self.logo_path}")

                    # 預處理logo
                    if self.logo_img.shape[2] < 4:
                        alpha = np.ones(
                            self.logo_img.shape[:2], dtype=np.uint8) * 255
                        self.logo_img = cv2.merge([
                            self.logo_img[:, :, 0],
                            self.logo_img[:, :, 1],
                            self.logo_img[:, :, 2],
                            alpha
                        ])
                else:
                    logger.warning(f"無法載入logo: {self.logo_path}")
                    self.logo_path = None
            except Exception as e:
                logger.error(f"初始化logo失敗: {e}")
                self.logo_path = None
                self.logo_img = None

    def create_directory(self, directory: str):
        """快速創建目錄"""
        Path(directory).mkdir(parents=True, exist_ok=True)

    @processing_timer
    def save_preview(self):
        """保存預覽圖像，確保包含logo"""
        start_time = time.time()

        try:
            # 在preview中添加logo（如果有）
            if 5 in self.cubemaps and self.logo_img is not None:
                self._add_logo_to_preview()

            # 縮放並保存預覽
            resized_preview = GPU_RESIZER.resize_image(
                self.preview, (256, 1536))
            preview_path = os.path.join(
                self.output_scene_folder, 'preview.jpg')

            success = IMAGE_IO.write_image_async(preview_path, resized_preview)
            if success:
                logger.debug(f"預覽圖像已保存: {preview_path}")

        except Exception as e:
            logger.error(f"保存預覽圖像失敗: {e}")
        finally:
            self.processing_stats['preview_time'] += time.time() - start_time

    def _add_logo_to_preview(self):
        """在預覽圖中添加logo"""
        try:
            face_w = self.preview.shape[0]
            start_col = 5 * face_w
            end_col = start_col + face_w

            if self.preview.shape[1] >= end_col:
                face = self.preview[:, start_col:end_col].copy()
                face_with_logo = self._overlay_logo_optimized(
                    face, scale=0.741)
                self.preview[:, start_col:end_col] = face_with_logo
        except Exception as e:
            logger.error(f"添加logo到預覽失敗: {e}")

    @processing_timer
    def save_thumbnail(self):
        """保存縮略圖"""
        start_time = time.time()

        try:
            resized_thumbnail = GPU_RESIZER.resize_image(
                self.thumbnail, (200, 400))
            thumbnail_path = os.path.join(
                self.output_scene_folder, 'thumbnail.jpg')

            success = IMAGE_IO.write_image_async(
                thumbnail_path, resized_thumbnail)
            if success:
                logger.debug(f"縮略圖已保存: {thumbnail_path}")

        except Exception as e:
            logger.error(f"保存縮略圖失敗: {e}")
        finally:
            self.processing_stats['thumbnail_time'] += time.time() - start_time

    def _overlay_logo_optimized(self, face: np.ndarray, scale: float = 0.741) -> np.ndarray:
        """最佳化的logo疊加"""
        if self.logo_img is None:
            return face

        face_copy = face.copy() if len(face.shape) == 2 else face
        if len(face.shape) == 2:
            face_copy = cv2.cvtColor(face_copy, cv2.COLOR_GRAY2BGR)

        face_h, face_w = face_copy.shape[:2]
        logo_width = int(face_w * scale)
        logo_height = int(
            self.logo_img.shape[0] * (logo_width / self.logo_img.shape[1]))

        # 使用最佳化的logo處理
        processed_logo = LOGO_OVERLAY.get_processed_logo(
            self.logo_path, (logo_height, logo_width))

        if processed_logo is not None:
            x_offset = (face_w - logo_width) // 2
            y_offset = (face_h - logo_height) // 2

            face_copy = LOGO_OVERLAY.overlay_logo_vectorized(
                face_copy, processed_logo, (x_offset, y_offset))

        return face_copy

    @processing_timer
    def save_image(self):
        """根據儲存模式保存圖像"""
        start_time = time.time()

        try:
            # 保存預覽和縮略圖
            self.save_preview()
            self.save_thumbnail()

            # 根據儲存模式決定保存策略
            if self.save_mode == SaveMode.ALL:
                self._save_all_images()
            elif self.save_mode == SaveMode.LOGO_ONLY:
                self._save_logo_only()
            elif self.save_mode == SaveMode.BLUR_ONLY:
                self._save_blur_only()
            elif self.save_mode == SaveMode.LOGO_AND_BLUR:
                self._save_logo_and_blur()

        except Exception as e:
            logger.error(f"保存圖像失敗: {e}")
        finally:
            total_time = time.time() - start_time
            self.processing_stats['total_save_time'] = total_time
            logger.debug(f"圖像保存總耗時: {total_time:.2f}秒")

    def _save_all_images(self):
        """全部儲存模式"""
        html5_folder = os.path.join(self.output_scene_folder, 'html5')
        self.create_directory(html5_folder)

        # 並行保存html5面
        self._save_html5_faces_parallel(html5_folder)

        # 並行處理金字塔
        self._process_pyramids_parallel()

    def _save_html5_faces_parallel(self, html5_folder: str):
        """並行保存html5面"""
        def save_html5_face(face_id):
            if face_id not in self.cubemaps:
                return False

            new_cubemap = self.cubemaps[face_id].copy()

            # 應用模糊處理
            if face_id in self.blurred_regions:
                new_cubemap = self._apply_blur_regions(new_cubemap, face_id)

            # 添加logo（面5）
            if face_id == 5 and self.logo_img is not None:
                new_cubemap = self._overlay_logo_optimized(
                    new_cubemap, scale=0.741)

            # 縮放並保存
            resized_cubemap = GPU_RESIZER.resize_image(
                new_cubemap, (2048, 2048))
            output_path = os.path.join(html5_folder, f'{face_id}.jpg')
            return IMAGE_IO.write_image_async(output_path, resized_cubemap)

        # 並行處理所有面
        with ThreadPoolExecutor(max_workers=min(6, SYSTEM_CONFIG.max_threads)) as executor:
            futures = [executor.submit(save_html5_face, i) for i in range(6)]
            results = [future.result() for future in as_completed(futures)]

        success_count = sum(1 for r in results if r)
        logger.debug(f"成功保存 {success_count}/6 個html5面")

    def _apply_blur_regions(self, face: np.ndarray, face_id: int) -> np.ndarray:
        """應用模糊區域到面"""
        if face_id not in self.blurred_regions:
            return face

        for region_name, region_data in self.blurred_regions[face_id].items():
            if 'image' in region_data and region_data['image'] is not None:
                box = region_data['box']
                x1, y1, x2, y2 = box[0], box[1], box[2], box[3]
                try:
                    face[y1:y2, x1:x2] = region_data['image']
                except Exception as e:
                    logger.warning(f"應用模糊區域失敗: {e}")

        return face

    def _process_pyramids_parallel(self):
        """並行處理金字塔圖像"""
        if not PERFORMANCE_CONFIG.enable_batch_processing:
            # 順序處理
            for i in range(6):
                if i in self.cubemaps:
                    self._process_pyramid_face(i, selective_save=False)
            return

        # 並行處理
        with ThreadPoolExecutor(max_workers=min(6, SYSTEM_CONFIG.max_threads)) as executor:
            futures = []
            for i in range(6):
                if i in self.cubemaps:
                    futures.append(executor.submit(
                        self._process_pyramid_face, i, False))

            # 等待完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"金字塔處理失敗: {e}")

    def _save_logo_only(self):
        """只儲存有標誌的面"""
        if self.logo_img is None:
            logger.warning("沒有標誌可保存")
            return

        html5_folder = os.path.join(self.output_scene_folder, 'html5')
        self.create_directory(html5_folder)

        # 只處理面5
        face_id = 5
        if face_id in self.cubemaps:
            self._process_single_face_with_logo(face_id, html5_folder)

    def _save_blur_only(self):
        """只儲存有模糊區域的面"""
        if not self.blurred_regions:
            logger.warning("沒有模糊區域可保存")
            return

        html5_folder = os.path.join(self.output_scene_folder, 'html5')
        self.create_directory(html5_folder)

        # 並行處理有模糊的面
        with ThreadPoolExecutor(max_workers=SYSTEM_CONFIG.max_threads) as executor:
            futures = []
            for face_id in self.blurred_regions:
                if face_id in self.cubemaps:
                    futures.append(executor.submit(
                        self._process_single_face_with_blur, face_id, html5_folder))

            for future in as_completed(futures):
                future.result()

    def _save_logo_and_blur(self):
        """儲存有標誌或模糊區域的面"""
        html5_folder = os.path.join(self.output_scene_folder, 'html5')
        self.create_directory(html5_folder)

        # 收集需要處理的面
        faces_to_process = set()

        # 添加有logo的面
        if self.logo_img is not None and 5 in self.cubemaps:
            faces_to_process.add(5)

        # 添加有模糊的面
        faces_to_process.update(self.blurred_regions.keys())

        # 並行處理
        with ThreadPoolExecutor(max_workers=SYSTEM_CONFIG.max_threads) as executor:
            futures = []
            for face_id in faces_to_process:
                if face_id in self.cubemaps and face_id != 4:  # 排除天空面
                    futures.append(executor.submit(
                        self._process_mixed_face, face_id, html5_folder))

            for future in as_completed(futures):
                future.result()

    def _process_single_face_with_logo(self, face_id: int, html5_folder: str):
        """處理單個有logo的面"""
        new_cubemap = self.cubemaps[face_id].copy()

        # 應用模糊（如果有）
        if face_id in self.blurred_regions:
            new_cubemap = self._apply_blur_regions(new_cubemap, face_id)

        # 添加logo
        new_cubemap = self._overlay_logo_optimized(new_cubemap, scale=0.741)

        # 保存html5
        resized_cubemap = GPU_RESIZER.resize_image(new_cubemap, (2048, 2048))
        output_path = os.path.join(html5_folder, f'{face_id}.jpg')
        IMAGE_IO.write_image_async(output_path, resized_cubemap)

        # 處理金字塔
        self._process_pyramid_face(face_id, selective_save=False)

    def _process_single_face_with_blur(self, face_id: int, html5_folder: str):
        """處理單個有模糊的面"""
        new_cubemap = self.cubemaps[face_id].copy()

        # 應用模糊
        new_cubemap = self._apply_blur_regions(new_cubemap, face_id)

        # 保存html5
        resized_cubemap = GPU_RESIZER.resize_image(new_cubemap, (2048, 2048))
        output_path = os.path.join(html5_folder, f'{face_id}.jpg')
        IMAGE_IO.write_image_async(output_path, resized_cubemap)

        # 處理金字塔（選擇性保存）
        self._process_pyramid_face(face_id, selective_save=True)

    def _process_mixed_face(self, face_id: int, html5_folder: str):
        """處理混合模式的面"""
        new_cubemap = self.cubemaps[face_id].copy()

        # 應用模糊
        if face_id in self.blurred_regions:
            new_cubemap = self._apply_blur_regions(new_cubemap, face_id)

        # 添加logo（如果是面5）
        if face_id == 5 and self.logo_img is not None:
            new_cubemap = self._overlay_logo_optimized(
                new_cubemap, scale=0.741)

        # 保存html5
        resized_cubemap = GPU_RESIZER.resize_image(new_cubemap, (2048, 2048))
        output_path = os.path.join(html5_folder, f'{face_id}.jpg')
        IMAGE_IO.write_image_async(output_path, resized_cubemap)

        # 處理金字塔
        selective = face_id != 5 or self.logo_img is None  # logo面保存所有切片
        self._process_pyramid_face(face_id, selective_save=selective)

    @processing_timer
    def _process_pyramid_face(self, face_id: int, selective_save: bool = False):
        """處理單個面的金字塔圖像結構"""
        if face_id not in self.cubemaps:
            return

        start_time = time.time()

        try:
            # 準備基底圖像
            base_image = self._prepare_base_image(face_id)

            # 生成多尺度圖像
            images = self._generate_multi_scale_images(base_image)

            # 創建目錄結構
            base_dir = os.path.join(self.output_scene_folder, str(face_id))
            self.create_directory(base_dir)

            dirs = [os.path.join(base_dir, str(i)) for i in range(3)]
            for d in dirs:
                self.create_directory(d)

            # 並行保存所有層級
            self._save_pyramid_levels_parallel(
                images, dirs, face_id, selective_save)

        except Exception as e:
            logger.error(f"處理金字塔面 {face_id} 失敗: {e}")
        finally:
            elapsed = time.time() - start_time
            self.processing_stats[f'pyramid_face_{face_id}'] = elapsed

    def _prepare_base_image(self, face_id: int) -> np.ndarray:
        """準備基底圖像"""
        base_image = self.cubemaps[face_id].copy()

        # 應用模糊
        if face_id in self.blurred_regions:
            base_image = self._apply_blur_regions(base_image, face_id)

        # 添加logo
        if face_id == 5 and self.logo_img is not None:
            base_image = self._overlay_logo_optimized(base_image, scale=0.741)

        return base_image

    def _generate_multi_scale_images(self, base_image: np.ndarray) -> List[np.ndarray]:
        """生成多尺度圖像"""
        scale_factors = [1.194, 1.194 / 2, 1.194 / 4]

        if PERFORMANCE_CONFIG.enable_batch_processing:
            # 並行生成
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = [executor.submit(GPU_RESIZER.resize_image, base_image, None, scale)
                           for scale in scale_factors]
                images = [future.result() for future in futures]
        else:
            # 順序生成
            images = [GPU_RESIZER.resize_image(base_image, None, scale)
                      for scale in scale_factors]

        return images

    def _save_pyramid_levels_parallel(self, images: List[np.ndarray], dirs: List[str],
                                      face_id: int, selective_save: bool):
        """並行保存金字塔層級"""
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = []
            for i, (image, folder) in enumerate(zip(images, dirs)):
                futures.append(executor.submit(
                    SLICE_PROCESSOR.save_image_slices_parallel,
                    image, folder, face_id, selective_save
                ))

            # 等待完成並收集結果
            for i, future in enumerate(futures):
                try:
                    slice_count = future.result()
                    logger.debug(f"面 {face_id} 層級 {i}: {slice_count} 個切片")
                except Exception as e:
                    logger.error(f"保存金字塔層級 {i} 失敗: {e}")

    @processing_timer
    def process_and_store_blurred_regions(self) -> Dict[int, int]:
        """處理並儲存檢測到的模糊區域"""
        if self.detector is None:
            logger.warning("未初始化檢測器")
            return {}

        blur_stats = {}
        start_time = time.time()

        # 並行處理所有面
        if PERFORMANCE_CONFIG.enable_batch_processing and len(self.cubemaps) > 2:
            blur_stats = self._process_faces_parallel()
        else:
            blur_stats = self._process_faces_sequential()

        processing_time = time.time() - start_time
        self.processing_stats['blur_detection_time'] = processing_time

        logger.debug(f"模糊檢測總耗時: {processing_time:.2f}秒")
        return blur_stats

    def _process_faces_parallel(self) -> Dict[int, int]:
        """並行處理所有面的模糊檢測"""
        blur_stats = {}

        with ThreadPoolExecutor(max_workers=SYSTEM_CONFIG.max_threads) as executor:
            # 提交所有檢測任務
            futures = {}
            for face_id, face_image in self.cubemaps.items():
                futures[face_id] = executor.submit(
                    self._process_single_face_blur, face_id, face_image)

            # 收集結果
            for face_id, future in futures.items():
                try:
                    blurred_regions, count = future.result()
                    if blurred_regions:
                        self.blurred_regions[face_id] = blurred_regions
                    blur_stats[face_id] = count
                except Exception as e:
                    logger.error(f"處理面 {face_id} 模糊檢測失敗: {e}")
                    blur_stats[face_id] = 0

        return blur_stats

    def _process_faces_sequential(self) -> Dict[int, int]:
        """順序處理面的模糊檢測"""
        blur_stats = {}

        for face_id, face_image in self.cubemaps.items():
            try:
                blurred_regions, count = self._process_single_face_blur(
                    face_id, face_image)
                if blurred_regions:
                    self.blurred_regions[face_id] = blurred_regions
                blur_stats[face_id] = count
            except Exception as e:
                logger.error(f"處理面 {face_id} 模糊檢測失敗: {e}")
                blur_stats[face_id] = 0

        return blur_stats

    def _process_single_face_blur(self, face_id: int, face_image: np.ndarray) -> Tuple[Dict, int]:
        """處理單個面的模糊檢測"""
        logger.debug(f"處理立方體面 {face_id} 的檢測...")

        try:
            blurred_regions = self.detector.process_and_get_blurred_regions(
                face_image, face_id=face_id)
            count = len(blurred_regions) if blurred_regions else 0
            return blurred_regions, count
        except Exception as e:
            logger.error(f"面 {face_id} 檢測失敗: {e}")
            return {}, 0

    def get_processing_stats(self) -> Dict[str, float]:
        """獲取處理統計資訊"""
        return dict(self.processing_stats)

    def __del__(self):
        """清理資源"""
        try:
            # 清理大型陣列
            if hasattr(self, 'cubemaps'):
                self.cubemaps.clear()
            if hasattr(self, 'blurred_regions'):
                self.blurred_regions.clear()

            # 強制垃圾回收
            gc.collect()

        except Exception:
            pass

# 模組清理函數


def cleanup_image_processing():
    """清理圖像處理模組的快取和資源"""
    # 清理全域快取
    is_valid_image_cached.cache_clear()
    LOGO_OVERLAY.get_processed_logo.cache_clear()

    # 清理物件快取
    LOGO_OVERLAY.logo_cache.clear()
    LOGO_OVERLAY.alpha_cache.clear()

    # 清理GPU快取
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # 強制垃圾回收
    gc.collect()

    logger.info("圖像處理模組快取已清理")

# 批次圖像處理工具函數


def batch_process_images(images: List[np.ndarray],
                         process_func: callable,
                         batch_size: int = None) -> List:
    """批次處理圖像的通用函數"""
    batch_size = batch_size or SYSTEM_CONFIG.batch_size
    results = []

    for i in range(0, len(images), batch_size):
        batch = images[i:i + batch_size]

        if PERFORMANCE_CONFIG.enable_batch_processing and len(batch) > 1:
            # 並行處理批次
            with ThreadPoolExecutor(max_workers=SYSTEM_CONFIG.max_threads) as executor:
                futures = [executor.submit(process_func, img) for img in batch]
                batch_results = [future.result()
                                 for future in as_completed(futures)]
        else:
            # 順序處理
            batch_results = [process_func(img) for img in batch]

        results.extend(batch_results)

    return results
