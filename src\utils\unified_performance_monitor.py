#!/usr/bin/env python3
"""
統一性能監控器 - 整合所有性能監控功能
"""
import time
import logging
import threading
from contextlib import contextmanager, asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable
from collections import defaultdict, deque
import statistics
import json

import psutil
try:
    import pynvml
    HAS_NVML = True
except ImportError:
    HAS_NVML = False

from .core.config import PerformanceConfig
from .core.metric_collector import Metric, MetricType, MetricRegistry
from .core.alert_system import AlertSystem
from .core.thresholds import DEFAULT_THRESHOLDS

logger = logging.getLogger(__name__)


class MonitoringLevel(Enum):
    """監控等級"""
    BASIC = "basic"      # 基本監控 - CPU、記憶體
    DETAILED = "detailed"  # 詳細監控 - 包含磁碟、網路
    ADVANCED = "advanced"  # 進階監控 - 包含GPU、自定義指標


# 移除重複定義，使用核心模組


class UnifiedPerformanceMonitor:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, config: PerformanceConfig | None = None):
        if hasattr(self, "_initialized"):
            return
        self.config = config or PerformanceConfig()
        self.metrics_history: dict[str, deque] = defaultdict(lambda: deque(maxlen=3600))
        self.current_metrics: dict[str, Metric] = {}
        self.monitoring = False
        self.monitor_thread = None
        
        # 整合核心模組
        self.metric_registry = MetricRegistry()
        self.alert_system = AlertSystem(DEFAULT_THRESHOLDS)
        
        if self.config.enable_gpu_monitoring and HAS_NVML:
            pynvml.nvmlInit()
            self.gpu_count = pynvml.nvmlDeviceGetCount()
        self._initialized = True
        logger.info("UnifiedPerformanceMonitor initialized.")

    def start_monitoring(self):
        if self.monitoring:
            return
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Performance monitoring started.")

    def stop_monitoring(self):
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        logger.info("Performance monitoring stopped.")

    def _monitoring_loop(self):
        while self.monitoring:
            self._collect_system_metrics()
            if self.config.enable_gpu_monitoring:
                self._collect_gpu_metrics()
            time.sleep(self.config.interval_sec)

    def _collect_system_metrics(self):
        cpu = psutil.cpu_percent(interval=None)
        mem = psutil.virtual_memory().percent
        self._add_metric("system.cpu.usage", cpu, "%", MetricType.CPU_USAGE)
        self._add_metric("system.memory.usage", mem, "%", MetricType.MEMORY_USAGE)

    def _collect_gpu_metrics(self):
        if not (self.config.enable_gpu_monitoring and HAS_NVML):
            return
        for i in range(self.gpu_count):
            handle = pynvml.nvmlDeviceGetHandleByIndex(i)
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            self._add_metric(f"gpu.{i}.usage", util.gpu, "%", MetricType.GPU_USAGE)
            self._add_metric(f"gpu.{i}.memory", (mem_info.used / mem_info.total) * 100, "%", MetricType.GPU_MEMORY)

    def _add_metric(self, name: str, value: float, unit: str, metric_type: MetricType = MetricType.CUSTOM, tags: dict | None = None):
        metric = Metric(name=name, value=value, unit=unit, metric_type=metric_type, tags=tags or {})
        self.metrics_history[name].append(metric)
        self.current_metrics[name] = metric
        
        # 檢查警報
        self.alert_system.check_metric(metric)

    @contextmanager
    def measure(self, name: str, tags: dict | None = None):
        """同步測量上下文"""
        start_time = time.perf_counter()
        try:
            yield
        finally:
            duration = (time.perf_counter() - start_time) * 1000  # ms
            self._add_metric(name, duration, "ms", tags)

    @asynccontextmanager
    async def measure_async(self, name: str, tags: dict | None = None):
        """異步測量上下文"""
        start_time = time.perf_counter()
        try:
            yield
        finally:
            duration = (time.perf_counter() - start_time) * 1000  # ms
            self._add_metric(name, duration, "ms", tags)

    def get_metric_summary(self, name: str) -> dict | None:
        history = self.metrics_history.get(name)
        if not history:
            return None
        values = [m.value for m in history]
        return {
            "name": name,
            "count": len(values),
            "mean": statistics.mean(values),
            "median": statistics.median(values),
            "stdev": statistics.stdev(values) if len(values) > 1 else 0,
            "min": min(values),
            "max": max(values),
        }

    def generate_report(self) -> dict:
        report = {"summary": {}, "metrics": {}}
        for name in self.metrics_history.keys():
            summary = self.get_metric_summary(name)
            if summary:
                report["metrics"][name] = summary
        
        cpu_summary = report.get("metrics", {}).get("system.cpu.usage", {})
        mem_summary = report.get("metrics", {}).get("system.memory.usage", {})
        report["summary"] = {
            "avg_cpu_usage": cpu_summary.get("mean"),
            "max_cpu_usage": cpu_summary.get("max"),
            "avg_mem_usage": mem_summary.get("mean"),
            "max_mem_usage": mem_summary.get("max"),
        }
        return report

    def save_report(self, filepath: str):
        report = self.generate_report()
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        logger.info(f"Performance report saved to {filepath}")