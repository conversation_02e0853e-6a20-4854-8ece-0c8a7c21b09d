"""
簡化的日誌器工廠模組

提供統一、簡潔的日誌器創建接口，替代複雜的舊版工廠模式。
使用現代的工廠設計模式，減少代碼重複和複雜度。
"""

import logging
from functools import partial
from typing import Optional, Dict, Any

from .core.config import LogConfig, create_config, LogConfigPresets
from .core.manager import LogManager
from .exceptions import ConfigurationError, LogManagerError
from .error_handler import ErrorHandler
from .messages import ErrorMessages, InfoMessages


def _create_config_from_preset(base_config: LogConfig, **overrides) -> LogConfig:
    """
    從基礎配置創建新配置，應用覆蓋參數
    
    :param base_config: 基礎配置
    :param overrides: 覆蓋參數
    :return: 新的配置對象
    """
    # 創建基礎配置的副本
    config_dict = {
        'name': base_config.name,
        'level': base_config.level,
        'console_output': base_config.console_output,
        'file_output': base_config.file_output,
        'base_output_path': getattr(base_config, 'base_output_path', None),
        'simple_console': base_config.simple_console,
        'use_colors': base_config.use_colors,
        'max_bytes': getattr(base_config, 'max_bytes', 10 * 1024 * 1024),
        'backup_count': getattr(base_config, 'backup_count', 5),
    }
    
    # 應用覆蓋參數
    config_dict.update(overrides)
    
    # 創建新的配置對象
    return LogConfig(**config_dict)


class ConfigPresets:
    """
    統一的配置預設管理器
    
    集中管理所有配置預設，提供清晰的配置創建接口。
    """
    
    # 預設配置映射
    _presets = {
        "default": lambda **kwargs: LogConfig(
            console_output=True,
            file_output=True,
            use_colors=True,
            simple_console=False,
            level=logging.INFO,
            **kwargs
        ),
        
        "development": lambda **kwargs: _create_config_from_preset(LogConfigPresets.development(), **kwargs),
        
        "production": lambda **kwargs: _create_config_from_preset(LogConfigPresets.production(), **kwargs),
        
        "debug": lambda **kwargs: _create_config_from_preset(LogConfigPresets.debug(), **kwargs),
        
        "minimal": lambda **kwargs: _create_config_from_preset(LogConfigPresets.minimal(), **kwargs),
        
        "console_only": lambda **kwargs: _create_config_from_preset(LogConfigPresets.console_only(), **kwargs),
        
        "file_only": lambda **kwargs: _create_config_from_preset(LogConfigPresets.file_only(), **kwargs),
        
        "simple": lambda **kwargs: LogConfig(
            console_output=True,
            file_output=True,
            use_colors=False,
            simple_console=True,
            level=logging.INFO,
            **kwargs
        ),
        
        "tool": lambda **kwargs: LogConfig(
            console_output=True,
            file_output=True,
            use_colors=True,  # 自動偵測環境
            simple_console=False,
            level=logging.INFO,
            **kwargs
        ),
    }
    
    @classmethod
    def get(cls, preset: str, name: str = "logger", **overrides) -> LogConfig:
        """
        獲取配置預設
        
        :param preset: 預設名稱
        :param name: 日誌器名稱
        :param overrides: 覆蓋參數
        :return: 配置對象
        :raises ConfigurationError: 當預設不存在時
        """
        if preset not in cls._presets:
            available = list(cls._presets.keys())
            raise ConfigurationError(
                ErrorMessages.format_error(
                    ErrorMessages.CONFIG_PRESET_UNKNOWN,
                    preset=preset,
                    available_presets=available
                )
            )
        
        try:
            # 創建基礎配置
            config = cls._presets[preset](name=name, **overrides)
            return config
        except Exception as e:
            raise ConfigurationError(
                ErrorMessages.format_error(
                    ErrorMessages.CONFIG_VALIDATION_FAILED,
                    errors=str(e)
                )
            ) from e
    
    @classmethod
    def list_presets(cls) -> list[str]:
        """列出所有可用的預設"""
        return list(cls._presets.keys())
    
    @classmethod
    def add_preset(cls, name: str, config_factory):
        """
        添加自定義預設
        
        :param name: 預設名稱
        :param config_factory: 配置工廠函數
        """
        cls._presets[name] = config_factory


class LoggerFactory:
    """
    統一的日誌器工廠
    
    提供簡潔、一致的日誌器創建接口，替代複雜的舊版工廠函數。
    使用配置預設系統，大幅簡化日誌器創建流程。
    """
    
    def __init__(self, manager: LogManager):
        """
        初始化工廠
        
        :param manager: 日誌管理器實例
        """
        self.manager = manager
    
    def create(self, name: str, preset: str = "default", **overrides) -> logging.Logger:
        """
        統一的日誌器創建方法
        
        替代所有舊版專用創建函數，如：
        - setup_basic_logger
        - setup_simple_logger  
        - create_tool_logger
        - create_console_only_logger
        - create_file_only_logger
        - create_debug_logger
        - create_production_logger
        
        :param name: 日誌器名稱
        :param preset: 配置預設名稱
        :param overrides: 覆蓋參數
        :return: 配置好的日誌器
        :raises ConfigurationError: 當配置創建失敗時
        """
        def _create_config():
            return ConfigPresets.get(preset, name=name, **overrides)
        
        def _fallback_config():
            # 如果預設配置失敗，使用最基本的配置
            return LogConfig(
                name=name,
                level=logging.INFO,
                console_output=True,
                file_output=False
            )
        
        config = ErrorHandler.handle_configuration_error(
            config_factory=_create_config,
            fallback_factory=_fallback_config,
            config_name=f"{preset}:{name}"
        )
        
        return self.manager.setup_logger(name, config)
    
    def get_or_create(self, name: str, preset: str = "default", **overrides) -> logging.Logger:
        """
        獲取現有日誌器或創建新的
        
        :param name: 日誌器名稱
        :param preset: 配置預設（僅在創建時使用）
        :param overrides: 覆蓋參數（僅在創建時使用）
        :return: 日誌器實例
        """
        # 嘗試獲取現有日誌器
        existing_loggers = self.manager.list_loggers()
        if name in existing_loggers:
            return self.manager.get_logger(name)
        
        # 創建新日誌器
        return self.create(name, preset, **overrides)
    
    def reconfigure(self, name: str, preset: str = "default", **overrides) -> logging.Logger:
        """
        重新配置現有日誌器
        
        :param name: 日誌器名稱
        :param preset: 新的配置預設
        :param overrides: 覆蓋參數
        :return: 重新配置的日誌器
        """
        # 移除現有日誌器
        self.manager.remove_logger(name)
        
        # 創建新的配置
        return self.create(name, preset, **overrides)
    
    def batch_create(self, logger_configs: Dict[str, Dict[str, Any]]) -> Dict[str, logging.Logger]:
        """
        批量創建日誌器
        
        :param logger_configs: 日誌器配置字典 {name: {preset: str, **overrides}}
        :return: 創建的日誌器字典
        """
        loggers = {}
        for name, config in logger_configs.items():
            preset = config.pop('preset', 'default')
            try:
                loggers[name] = self.create(name, preset, **config)
            except Exception as e:
                # 記錄錯誤但繼續處理其他日誌器
                if loggers:  # 如果已經有日誌器創建成功，用它來記錄錯誤
                    first_logger = next(iter(loggers.values()))
                    first_logger.error(f"批量創建日誌器 '{name}' 失敗: {e}")
        
        return loggers
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取工廠統計信息
        
        :return: 統計信息字典
        """
        manager_stats = self.manager.get_statistics()
        return {
            **manager_stats,
            "factory_type": "LoggerFactory",
            "available_presets": ConfigPresets.list_presets(),
            "factory_features": [
                "統一創建接口",
                "配置預設系統", 
                "批量創建",
                "自動降級",
                "錯誤恢復"
            ]
        }


# 全域工廠實例
_global_factory: Optional[LoggerFactory] = None


def get_factory() -> LoggerFactory:
    """
    獲取全域日誌器工廠實例
    
    :return: LoggerFactory 實例
    """
    global _global_factory
    
    if _global_factory is None:
        # 需要導入以避免循環依賴
        from .factory import get_global_log_manager
        
        manager = get_global_log_manager()
        _global_factory = LoggerFactory(manager)
    
    return _global_factory


# ============================================================================
# 簡化的公共 API
# ============================================================================

def create_logger(name: str, preset: str = "default", **overrides) -> logging.Logger:
    """
    統一的日誌器創建函數
    
    這是新的主要創建函數，替代所有舊版專用函數：
    - setup_basic_logger → create_logger(name, "default")
    - setup_simple_logger → create_logger(name, "simple")  
    - create_tool_logger → create_logger(name, "tool")
    - create_console_only_logger → create_logger(name, "console_only")
    - create_file_only_logger → create_logger(name, "file_only")
    - create_debug_logger → create_logger(name, "debug")
    - create_production_logger → create_logger(name, "production")
    
    :param name: 日誌器名稱
    :param preset: 配置預設名稱
    :param overrides: 覆蓋參數
    :return: 配置好的日誌器
    """
    return get_factory().create(name, preset, **overrides)


def get_or_create_logger(name: str, preset: str = "default", **overrides) -> logging.Logger:
    """
    獲取現有日誌器或創建新的
    
    :param name: 日誌器名稱  
    :param preset: 配置預設（僅在創建時使用）
    :param overrides: 覆蓋參數（僅在創建時使用）
    :return: 日誌器實例
    """
    return get_factory().get_or_create(name, preset, **overrides)


# ============================================================================
# 便利函數 - 使用 partial 創建特定用途的簡化函數
# ============================================================================

# 這些函數提供了簡潔的接口，同時保持向後相容性
console_logger = partial(create_logger, preset="console_only")
console_logger.__doc__ = "創建僅輸出到控制台的日誌器"

file_logger = partial(create_logger, preset="file_only") 
file_logger.__doc__ = "創建僅輸出到檔案的日誌器"

debug_logger = partial(create_logger, preset="debug")
debug_logger.__doc__ = "創建調試模式日誌器"

production_logger = partial(create_logger, preset="production")
production_logger.__doc__ = "創建生產模式日誌器"

simple_logger = partial(create_logger, preset="simple")
simple_logger.__doc__ = "創建簡單格式日誌器"

tool_logger = partial(create_logger, preset="tool")
tool_logger.__doc__ = "創建工具專用日誌器"


# ============================================================================
# 配置管理
# ============================================================================

def configure_global_logging(preset: str = "default", **overrides):
    """
    全域日誌配置
    
    :param preset: 配置預設名稱
    :param overrides: 覆蓋參數  
    """
    config = ConfigPresets.get(preset, name="global", **overrides)
    from .factory import get_global_log_manager
    get_global_log_manager().reload_config(config)


def list_available_presets() -> list[str]:
    """列出所有可用的配置預設"""
    return ConfigPresets.list_presets()


def get_factory_statistics() -> Dict[str, Any]:
    """獲取工廠統計信息"""
    return get_factory().get_statistics()