#!/usr/bin/env python3
"""
對 processing.components.detection_service 的單元測試
"""

import unittest
from unittest.mock import Mock, patch
import numpy as np

# Import project modules directly (using pip install -e .)
from pathlib import Path

from processing.components.detection_service import DetectionService, DetectionResult
from detection.core.detector import Detector
from detection.core.data_structures import DetectionBox

class TestDetectionService(unittest.TestCase):

    def setUp(self):
        """為每個測試設置模擬的檢測器"""
        self.mock_primary_detector = Mock(spec=Detector)
        self.mock_secondary_detector = Mock(spec=Detector)
        
        # 模擬返回的檢測框和模糊後的圖像
        self.test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        self.blurred_image_mock = np.ones((100, 100, 3), dtype=np.uint8)
        
        self.primary_detection_result = DetectionResult(
            regions=[DetectionBox(x1=10, y1=10, x2=50, y2=50, class_id=0, confidence=0.9)],
            blurred_image=self.blurred_image_mock
        )
        self.secondary_detection_result = DetectionResult(
            regions=[DetectionBox(x1=60, y1=60, x2=90, y2=90, class_id=1, confidence=0.8)],
            blurred_image=self.blurred_image_mock
        )

        self.mock_primary_detector.process_and_get_blurred_regions.return_value = self.primary_detection_result
        self.mock_secondary_detector.process_and_get_blurred_regions.return_value = self.secondary_detection_result

    @patch('processing.components.detection_service.hashlib.sha256')
    def test_process_face_caching(self, mock_sha256):
        """測試檢測結果的緩存功能"""
        mock_sha256.return_value.hexdigest.return_value = "test_hash"
        
        service = DetectionService(self.mock_primary_detector)
        
        # 第一次調用
        result1 = service.process_face(self.test_image, 0)
        
        # 第二次調用
        result2 = service.process_face(self.test_image, 0)
        
        # 斷言檢測器只被調用了一次
        self.mock_primary_detector.process_and_get_blurred_regions.assert_called_once()
        self.assertEqual(result1, result2)
        self.assertIsNot(result1.blurred_image, self.test_image) # 確保返回的是副本

    def test_batch_process_faces(self):
        """測試批次處理多個面"""
        service = DetectionService(self.mock_primary_detector)
        faces = {0: self.test_image, 1: self.test_image + 1}
        
        results = service.batch_process_faces(faces)
        
        self.assertEqual(len(results), 2)
        self.assertIn(0, results)
        self.assertIn(1, results)
        self.assertEqual(self.mock_primary_detector.process_and_get_blurred_regions.call_count, 2)

    def test_dual_detector_logic(self):
        """測試雙模型協同工作"""
        service = DetectionService(self.mock_primary_detector, self.mock_secondary_detector)
        
        result = service.process_face(self.test_image, 0)
        
        # 斷言兩個檢測器都被調用
        self.mock_primary_detector.process_and_get_blurred_regions.assert_called_once()
        self.mock_secondary_detector.process_and_get_blurred_regions.assert_called_once()
        
        # 斷言結果合併了兩個檢測器的結果
        self.assertEqual(len(result.regions), 2)

    def test_primary_detector_failure(self):
        """測試主檢測器失敗時，副檢測器仍能工作"""
        self.mock_primary_detector.process_and_get_blurred_regions.side_effect = Exception("Primary detector failed")
        
        service = DetectionService(self.mock_primary_detector, self.mock_secondary_detector)
        
        result = service.process_face(self.test_image, 0)
        
        # 主檢測器被調用（並失敗）
        self.mock_primary_detector.process_and_get_blurred_regions.assert_called_once()
        # 副檢測器應該被調用
        self.mock_secondary_detector.process_and_get_blurred_regions.assert_called_once()
        
        # 結果應該只包含副檢測器的結果
        self.assertEqual(len(result.regions), 1)
        self.assertEqual(result.regions[0].class_id, 1)

if __name__ == '__main__':
    unittest.main()