"""
偵測設定

用於偵測系統的設定類別與工具程式。
"""

from dataclasses import dataclass
from typing import Optional

# 使用延遲載入來避免循環 import
def _get_constants():
    """延遲載入 config.constants"""
    try:
        from config.constants import (DEFAULT_CONF_THRESHOLD, DEFAULT_FACE5_TEST_CONF,
                                      DEFAULT_IOU_THRESHOLD, DEFAULT_MAX_AREA_RATIO)
        return DEFAULT_CONF_THRESHOLD, DEFAULT_FACE5_TEST_CONF, DEFAULT_IOU_THRESHOLD, DEFAULT_MAX_AREA_RATIO
    except ImportError:
        # 如果無法載入，使用預設值
        return 0.25, 0.5, 0.45, 0.8

# 載入常數
DEFAULT_CONF_THRESHOLD, DEFAULT_FACE5_TEST_CONF, DEFAULT_IOU_THRESHOLD, DEFAULT_MAX_AREA_RATIO = _get_constants()


@dataclass
class DetectionConfig:
    """使用現代化型別提示的偵測設定"""

    primary_model_path: str
    secondary_model_path: Optional[str] = None
    conf_threshold: float = DEFAULT_CONF_THRESHOLD
    iou_threshold: float = DEFAULT_IOU_THRESHOLD
    max_area_ratio: float = DEFAULT_MAX_AREA_RATIO
    face5_test_conf: float = DEFAULT_FACE5_TEST_CONF
    device: Optional[str] = None
    enable_draw: bool = False

    def to_dict(self) -> dict[str, str | float | bool | None]:
        """轉換為字典表示"""
        return {
            "primary_model_path": self.primary_model_path,
            "secondary_model_path": self.secondary_model_path,
            "conf_threshold": self.conf_threshold,
            "iou_threshold": self.iou_threshold,
            "max_area_ratio": self.max_area_ratio,
            "face5_test_conf": self.face5_test_conf,
            "device": self.device,
            "enable_draw": self.enable_draw,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "DetectionConfig":
        """從字典建立"""
        return cls(**data)

    def validate(self) -> list[str]:
        """驗證設定並回傳問題列表"""
        issues = []

        if not self.primary_model_path:
            issues.append("必須提供 primary_model_path")

        if not 0.0 <= self.conf_threshold <= 1.0:
            issues.append("conf_threshold 必須介於 0.0 和 1.0 之間")

        if not 0.0 <= self.iou_threshold <= 1.0:
            issues.append("iou_threshold 必須介於 0.0 和 1.0 之間")

        if not 0.0 <= self.max_area_ratio <= 1.0:
            issues.append("max_area_ratio 必須介於 0.0 和 1.0 之間")

        if not 0.0 <= self.face5_test_conf <= 1.0:
            issues.append("face5_test_conf 必須介於 0.0 和 1.0 之間")

        return issues
