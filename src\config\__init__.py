"""
配置模組 - 系統設定與常數

此模組為整個系統的配置核心，集中管理所有配置參數、常數定義及設定管理功能。
透過此模組，可以方便地存取和修改系統的各項設定。

主要組件:
- constants: 定義系統中使用的所有靜態常數，如立方體面（Face）、儲存模式（SaveMode）、支援的檔案格式等。
- settings: 提供一個全局的、多層級的配置管理系統，允許動態載入和儲存設定。
- interpolation: 管理與影像插值算法相關的詳細設定。
- detection_classes.txt: 一個外部設定檔，用於定義AI模型可檢測的物件類別。
"""

# 為 PyInstaller 提供明確的套件標識，確保在打包為可執行檔時能正確找到模組。
__package__ = "config"
__version__ = "1.0.0"

# 定義此模組公開的子模組列表，當使用 'from config import *' 時，只有這些模組會被匯入。
__all__ = [
    # 子模組
    "constants", 
    "settings", 
    "interpolation",
    # 主要類別和枚舉 (將在下方動態添加)
]


# 確保此模組被 Python 解譯器正確識別為一個套件。
def _ensure_package():
    """確保此模組被正確識別為一個套件。"""
    return True


_package_initialized = _ensure_package()

# 嘗試匯入便捷的項目，即使失敗也不會中斷程式執行。
# 這種設計使得模組在不同環境下更具彈性。
try:
    from . import constants, interpolation, settings

    # 嘗試從子模組中匯入常用的類別和常數，以便於從頂層模組直接存取。
    # 這是一種提供便捷API的常見模式。
    try:
        # 從 constants 匯入主要常數和枚舉
        from .constants import (
            DETECTION_CLASSES, 
            DETECTION_COLORS,
            SUPPORTED_IMAGE_EXTS, 
            SUPPORTED_LABEL_EXTS,
            DEFAULT_CONF_THRESHOLD,
            DEFAULT_IOU_THRESHOLD,
            DEFAULT_CUBE_SIZE,
            DEFAULT_PYRAMID_LEVELS,
            DEFAULT_TILE_SIZE,
            Face, 
            ProcessMode,
            SaveMode,
            InterpolationMode,
            ErrorCode,
            ProcessingStatus,
        )
        
        # 從 interpolation 匯入插值相關配置
        from .interpolation import (
            InterpolationConfig, 
            InterpolationMethod,
            MultipleInterpolationStrategy,
            MultipleInterpolationConfig,
        )
        
        # 從 settings 匯入配置類別
        from .settings import (
            PyramidConfig,
            Config,
            PathConfig,
            ModelConfig,
            SystemConfig,
            ProcessingSettings,
            UtilsSettings,
            get_config,
        )

        # 將這些便捷匯入的名稱加入 __all__ 列表，使其可以被外部 'from config import *' 匯入。
        __all__.extend([
            # 常數和枚舉
            "Face",
            "SaveMode", 
            "ProcessMode",
            "InterpolationMode",
            "ErrorCode",
            "ProcessingStatus",
            "SUPPORTED_IMAGE_EXTS",
            "SUPPORTED_LABEL_EXTS", 
            "DEFAULT_CONF_THRESHOLD",
            "DEFAULT_IOU_THRESHOLD",
            "DEFAULT_CUBE_SIZE",
            "DEFAULT_PYRAMID_LEVELS",
            "DEFAULT_TILE_SIZE",
            "DETECTION_CLASSES",
            "DETECTION_COLORS",
            # 插值配置
            "InterpolationConfig",
            "InterpolationMethod",
            "MultipleInterpolationStrategy", 
            "MultipleInterpolationConfig",
            # 設定配置
            "PyramidConfig",
            "Config",
            "PathConfig",
            "ModelConfig", 
            "SystemConfig",
            "ProcessingSettings",
            "UtilsSettings",
            "get_config",
        ])
    except ImportError:
        # 如果子模組中的特定常數或類別不存在，則靜默忽略。
        # 這有助於保持向後相容性或處理可選依賴。
        pass

except ImportError as e:
    import sys

    # 處理模組匯入失敗的異常情況。
    # 如果在 PyInstaller 打包的環境中，可能是正常現象，僅打印警告。
    if hasattr(sys, "_MEIPASS"):  # 檢查是否在 PyInstaller 執行環境
        print(f"配置模組匯入警告 (PyInstaller 環境): {e}")
    else:
        # 在標準 Python 環境中，打印更通用的警告訊息。
        print(f"配置模組匯入警告: {e}")


def main():
    """
    測試整個 config 模組的功能
    """
    print("=" * 60)
    print("測試 config 模組 - 系統配置核心")
    print("=" * 60)

    # 測試模組基本信息
    print(f"\n1. 模組基本信息:")
    print(f"   套件名稱: {__package__}")
    print(f"   版本: {__version__}")
    print(f"   套件初始化: {'成功' if _package_initialized else '失敗'}")

    # 測試可用的公開 API
    print(f"\n2. 可用的公開 API:")
    print(f"   總共 {len(__all__)} 個公開項目")
    print(f"   前10個項目: {__all__[:10]}")

    # 測試枚舉和常數
    print(f"\n3. 測試主要枚舉和常數:")
    try:
        print(f"   Face.FRONT = {Face.FRONT}")
        print(f"   ProcessMode.PANO_CUBE_DETECT_PYRAMID = {ProcessMode.PANO_CUBE_DETECT_PYRAMID}")
        print(f"   SaveMode.ALL = {SaveMode.ALL}")
        print(f"   支援的圖像格式: {len(SUPPORTED_IMAGE_EXTS)} 種")
        print(f"   檢測類別數量: {len(DETECTION_CLASSES)}")
    except NameError as e:
        print(f"   部分枚舉不可用: {e}")
    except AttributeError as e:
        print(f"   部分屬性不可用: {e}")

    # 測試配置功能
    print(f"\n4. 測試配置功能:")
    try:
        config = get_config()
        print(f"   配置創建: 成功 ({type(config)})")
        print(f"   輸入目錄: {config.paths.input_dir}")
        print(f"   模型置信度: {config.model.conf_threshold}")
    except Exception as e:
        print(f"   配置功能測試失敗: {e}")

    # 測試插值配置
    print(f"\n5. 測試插值配置:")
    try:
        interp_config = InterpolationConfig(method=InterpolationMethod.BICUBIC)
        print(f"   插值配置創建: 成功")
        print(f"   插值方法: {interp_config.method}")
        print(f"   可用插值方法數量: {len(list(InterpolationMethod))}")
    except Exception as e:
        print(f"   插值配置測試失敗: {e}")

    # 測試子模組
    print(f"\n6. 測試子模組:")
    submodules = ['constants', 'settings', 'interpolation']
    for module_name in submodules:
        try:
            module = globals().get(module_name)
            if module:
                print(f"   {module_name}: 可用 ✓")
            else:
                print(f"   {module_name}: 不可用 ✗")
        except Exception as e:
            print(f"   {module_name}: 錯誤 - {e}")

    print(f"\n✅ config 模組整體測試完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
