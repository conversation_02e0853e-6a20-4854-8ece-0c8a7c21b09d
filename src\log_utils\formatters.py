"""
日誌格式器模組 (log_utils.formatters)

本模組提供多種日誌格式器 (Formatter)，用於將日誌記錄 (LogRecord) 轉換為
最終輸出的字串形式。這是實現結構化日誌、彩色輸出以及客製化日誌外觀的關鍵。

從原始的 `logger.py` 中分離出來後，此模組專注於日誌的「外觀」呈現。
"""

import json
import logging
import os
import sys
from datetime import datetime
from typing import Optional


class ColoredFormatter(logging.Formatter):
    """
    彩色日誌格式器

    為終端機主控台提供易於閱讀的彩色日誌輸出。它會根據日誌級別 (如 INFO, WARNING, ERROR)
    自動套用不同的顏色，並能智慧地檢測終端機是否支援顏色，支援跨平台（包括 Windows 10+）。
    """

    # 定義 ANSI 顏色代碼
    COLORS = {
        "DEBUG": "\033[36m",    # 青色
        "INFO": "\033[32m",     # 綠色
        "WARNING": "\033[33m",  # 黃色
        "ERROR": "\033[31m",    # 紅色
        "CRITICAL": "\033[35m", # 紫色
        "RESET": "\033[0m",     # 重設所有屬性
    }

    # 提供多種擴充的顏色主題，以適應不同背景的終端機
    THEMES = {
        "default": COLORS,
        "dark": {
            "DEBUG": "\033[90m",    # 深灰色
            "INFO": "\033[94m",     # 亮藍色
            "WARNING": "\033[93m",  # 亮黃色
            "ERROR": "\033[91m",    # 亮紅色
            "CRITICAL": "\033[95m", # 亮紫色
            "RESET": "\033[0m",
        },
        "light": {
            "DEBUG": "\033[96m",    # 亮青色
            "INFO": "\033[92m",     # 亮綠色
            "WARNING": "\033[33m",  # 黃色
            "ERROR": "\033[31m",    # 紅色
            "CRITICAL": "\033[35m", # 紫色
            "RESET": "\033[0m",
        },
    }

    def __init__(self, fmt: str, use_colors: bool = True, theme: str = "default"):
        """
        初始化彩色格式器。

        :param fmt: 日誌格式字串，與標準 `logging.Formatter` 相同。
        :param use_colors: 是否啟用顏色輸出。預設為 True。
        :param theme: 顏色主題名稱 ('default', 'dark', 'light')。
        """
        super().__init__(fmt)
        self.use_colors = use_colors and self._supports_color()
        self.theme = self.THEMES.get(theme, self.THEMES["default"])

    def _supports_color(self) -> bool:
        """
        檢查當前終端環境是否支援 ANSI 顏色。

        :return: 如果支援則返回 `True`，否則返回 `False`。
        """
        # 透過環境變數強制啟用或停用顏色
        if os.environ.get("NO_COLOR"):
            return False
        if os.environ.get("FORCE_COLOR"):
            return True

        # 對於 Windows 系統，嘗試啟用虛擬終端處理 (Virtual Terminal Processing)
        if os.name == "nt":
            try:
                import ctypes
                kernel32 = ctypes.windll.kernel32
                handle = kernel32.GetStdHandle(-11)  # STD_OUTPUT_HANDLE
                mode = ctypes.c_ulong()
                if kernel32.GetConsoleMode(handle, ctypes.byref(mode)) == 0:
                    return False
                ENABLE_VIRTUAL_TERMINAL_PROCESSING = 0x0004
                kernel32.SetConsoleMode(handle, mode.value | ENABLE_VIRTUAL_TERMINAL_PROCESSING)
                return True
            except (ImportError, AttributeError, OSError):
                return False

        # 對於 Unix/Linux 系統，檢查是否為互動式終端
        return hasattr(sys.stderr, "isatty") and sys.stderr.isatty() and os.environ.get("TERM") != "dumb"

    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日誌記錄，並為其添加顏色。

        :param record: `logging.LogRecord` 物件。
        :return: 格式化並上色後的日誌字串。
        """
        formatted_message = super().format(record)
        if self.use_colors and record.levelname in self.theme:
            color_code = self.theme[record.levelname]
            reset_code = self.theme["RESET"]
            # 僅對日誌級別名稱本身進行著色，以保持格式整潔
            formatted_message = formatted_message.replace(
                record.levelname, f"{color_code}{record.levelname}{reset_code}"
            )
        return formatted_message

    def set_theme(self, theme: str):
        """
        在執行期間動態設定顏色主題。

        :param theme: 主題名稱 ('default', 'dark', 'light')。
        :raises ValueError: 如果提供的主題名稱不存在。
        """
        if theme in self.THEMES:
            self.theme = self.THEMES[theme]
        else:
            raise ValueError(f"未知的顏色主題: '{theme}'. 可用主題: {list(self.THEMES.keys())}")


class JSONFormatter(logging.Formatter):
    """
    JSON 格式器

    將日誌記錄輸出為結構化的 JSON 字串。這對於將日誌傳送到日誌聚合系統
    （如 ELK Stack, Splunk, Datadog）進行後續分析、查詢和視覺化非常有用。
    """
    def __init__(self, include_extra: bool = True, ensure_ascii: bool = False):
        """
        初始化 JSON 格式器。

        :param include_extra: 是否在 JSON 輸出中包含 `extra` 字典中的額外欄位。
        :param ensure_ascii: 傳遞給 `json.dumps` 的參數，若為 `False`，可正確處理非 ASCII 字元（如中文）。
        """
        super().__init__()
        self.include_extra = include_extra
        self.ensure_ascii = ensure_ascii

    def format(self, record: logging.LogRecord) -> str:
        """
        將 `LogRecord` 物件格式化為 JSON 字串。

        :param record: `logging.LogRecord` 物件。
        :return: 代表該日誌記錄的 JSON 字串。
        """
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger_name": record.name,
            "message": record.getMessage(),
            "source": {
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno,
            },
            "process": {"id": record.process, "name": record.processName},
            "thread": {"id": record.thread, "name": record.threadName},
        }

        # 如果有異常資訊，也將其格式化並加入
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # 如果 `logging.info("...", extra={...})` 中有額外欄位，則將其加入
        if self.include_extra:
            extra_data = {
                key: value
                for key, value in record.__dict__.items()
                if key not in log_data and not key.startswith("_") and key not in ["args", "msg", "exc_info", "exc_text", "stack_info", "created", "msecs", "relativeCreated", "levelno", "levelname", "name", "module", "funcName", "lineno", "process", "processName", "thread", "threadName"]
            }
            if extra_data:
                log_data["extra"] = extra_data

        return json.dumps(log_data, ensure_ascii=self.ensure_ascii, default=str)


class CompactFormatter(logging.Formatter):
    """
    緊湊格式器

    提供一種非常簡潔的日誌格式，適合在主控台進行大量日誌輸出，
    或用於生產環境中，以減少日誌的視覺雜訊。
    """
    def __init__(self, show_time: bool = True, show_level: bool = True, show_logger: bool = False):
        """
        初始化緊湊格式器。

        :param show_time: 是否在日誌中顯示時間 (HH:MM:SS)。
        :param show_level: 是否顯示日誌級別。
        :param show_logger: 是否顯示 logger 的名稱。
        """
        fmt_parts = []
        if show_time:
            fmt_parts.append("%(asctime)s")
        if show_level:
            fmt_parts.append("[%(levelname)s]")
        if show_logger:
            fmt_parts.append("%(name)s:")
        fmt_parts.append("%(message)s")

        fmt = " ".join(fmt_parts)
        super().__init__(fmt, datefmt="%H:%M:%S")


class DetailedFormatter(logging.Formatter):
    """
    詳細格式器

    提供非常詳細的日誌資訊，包含時間戳、日誌級別、logger 名稱、原始檔案、
    行號、函式名稱等。主要用於偵錯和開發環境，幫助快速定位問題來源。
    """
    def __init__(self, show_thread: bool = True, show_process: bool = False):
        """
        初始化詳細格式器。

        :param show_thread: 是否顯示執行緒 ID。
        :param show_process: 是否顯示行程 ID。
        """
        fmt_parts = [
            "%(asctime)s",
            "[%(levelname)s]",
            "%(name)s",
            "(%(filename)s:%(lineno)d)",
            "%(funcName)s():",
        ]
        if show_thread:
            fmt_parts.append("[Thread-%(thread)d]")
        if show_process:
            fmt_parts.append("[PID-%(process)d]")
        fmt_parts.append("%(message)s")

        fmt = " ".join(fmt_parts)
        super().__init__(fmt, datefmt="%Y-%m-%d %H:%M:%S")


class ProgressFormatter(logging.Formatter):
    """
    進度格式器

    專門用於在主控台顯示進度條或進度百分比。它利用回車符 `\r` 來覆寫
    同一行，從而產生動態更新的效果，而不會被大量的日誌淹沒。
    """
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化進度資訊。

        它會從 `LogRecord` 的 `extra` 字典中尋找 `progress_current` 和 `progress_total`。

        :param record: `logging.LogRecord` 物件。
        :return: 格式化後的進度字串。
        """
        message = record.getMessage()
        progress_parts = []

        if hasattr(record, "progress_current") and hasattr(record, "progress_total"):
            current = record.progress_current
            total = record.progress_total
            percentage = (current / total) * 100 if total > 0 else 0
            progress_parts.append(f"[{'#' * int(percentage // 5):<20}]") # 簡單的進度條
            progress_parts.append(f"{percentage:6.2f}%")
            progress_parts.append(f"({current}/{total})")

        if progress_parts:
            message = f"{message.ljust(20)} {' '.join(progress_parts)}"

        # 使用回車符 `\r` 讓游標回到行首，以便下次輸出可以覆寫本行
        return f"\r{message}"


# --- 工廠函式與預定義字串 ---

def create_formatter(formatter_type: str = "colored", **kwargs) -> logging.Formatter:
    """
    建立格式器的工廠函式。

    :param formatter_type: 格式器的類型名稱 (例如 "colored", "json", "compact")。
    :param kwargs: 傳遞給對應格式器建構函式的參數。
    :return: 一個 `logging.Formatter` 的實例。
    :raises ValueError: 如果提供了未知的格式器類型。
    """
    formatters = {
        "colored": ColoredFormatter,
        "json": JSONFormatter,
        "compact": CompactFormatter,
        "detailed": DetailedFormatter,
        "progress": ProgressFormatter,
    }
    formatter_class = formatters.get(formatter_type)
    if formatter_class is None:
        raise ValueError(f"未知的格式器類型: '{formatter_type}'. 可用類型: {list(formatters.keys())}")
    return formatter_class(**kwargs)


# 提供一系列預先定義好的格式字串，方便快速取用
FORMAT_STRINGS = {
    "simple": "%(levelname)s: %(message)s",
    "basic": "%(asctime)s [%(levelname)s] %(message)s",
    "detailed": "%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d) - %(message)s",
    "debug": "%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d) - %(funcName)s() - %(message)s",
    "thread": "%(asctime)s [%(levelname)s] [Thread-%(thread)d] %(name)s - %(message)s",
    "process": "%(asctime)s [%(levelname)s] [PID-%(process)d] %(name)s - %(message)s",
    "full": "%(asctime)s [%(levelname)s] [PID-%(process)d, Thread-%(thread)d] %(name)s (%(filename)s:%(lineno)d) - %(funcName)s() - %(message)s",
}


def get_format_string(format_name: str) -> str:
    """
    根據名稱獲取一個預先定義的格式字串。

    :param format_name: 預定義格式的名稱 (例如 "simple", "detailed")。
    :return: 對應的格式字串。如果名稱不存在，則返回 "basic" 格式。
    """
    return FORMAT_STRINGS.get(format_name, FORMAT_STRINGS["basic"])


def main():
    """
    測試 log_utils.formatters 模組的主要功能
    """
    print("=" * 60)
    print("測試 log_utils.formatters 模組")
    print("=" * 60)

    # 創建測試日誌記錄
    logger = logging.getLogger("test_formatter")
    logger.setLevel(logging.DEBUG)

    # 測試彩色格式器
    print("\n1. 測試彩色格式器:")
    try:
        fmt = "%(asctime)s [%(levelname)s] %(message)s"
        colored_formatter = ColoredFormatter(fmt)
        print(f"   彩色格式器創建成功")
        print(f"   顏色支援: {colored_formatter.supports_color}")
        print(f"   當前主題: {colored_formatter.theme}")

        # 測試不同主題
        themes = ["default", "dark", "light", "minimal"]
        for theme in themes:
            try:
                theme_formatter = ColoredFormatter(fmt, theme=theme)
                print(f"   主題 '{theme}': 創建成功")
            except Exception as e:
                print(f"   主題 '{theme}': 創建失敗 - {e}")
    except Exception as e:
        print(f"   彩色格式器測試失敗: {e}")

    # 測試 JSON 格式器
    print("\n2. 測試 JSON 格式器:")
    try:
        json_formatter = JSONFormatter()
        print(f"   JSON 格式器創建成功")

        # 創建測試記錄
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="test.py", lineno=42,
            msg="測試訊息", args=(), exc_info=None
        )

        formatted = json_formatter.format(record)
        parsed = json.loads(formatted)
        print(f"   JSON 格式化成功: {len(parsed)} 個欄位")
        print(f"   包含欄位: {list(parsed.keys())[:5]}...")
    except Exception as e:
        print(f"   JSON 格式器測試失敗: {e}")

    # 測試緊湊格式器
    print("\n3. 測試緊湊格式器:")
    try:
        compact_formatter = CompactFormatter()
        print(f"   緊湊格式器創建成功")

        # 測試格式化
        record = logging.LogRecord(
            name="test", level=logging.WARNING, pathname="test.py", lineno=42,
            msg="警告訊息", args=(), exc_info=None
        )

        formatted = compact_formatter.format(record)
        print(f"   緊湊格式化結果: {formatted}")
    except Exception as e:
        print(f"   緊湊格式器測試失敗: {e}")

    # 測試詳細格式器
    print("\n4. 測試詳細格式器:")
    try:
        detailed_formatter = DetailedFormatter()
        print(f"   詳細格式器創建成功")

        # 測試格式化
        record = logging.LogRecord(
            name="test.module", level=logging.ERROR, pathname="/path/to/test.py", lineno=123,
            msg="錯誤訊息", args=(), exc_info=None
        )
        record.funcName = "test_function"
        record.process = 12345
        record.thread = 67890

        formatted = detailed_formatter.format(record)
        print(f"   詳細格式化結果: {formatted[:100]}...")
    except Exception as e:
        print(f"   詳細格式器測試失敗: {e}")

    # 測試格式器工廠
    print("\n5. 測試格式器工廠:")
    try:
        formatters = [
            ("colored", {"fmt": "%(asctime)s [%(levelname)s] %(message)s"}),
            ("json", {}),
            ("compact", {}),
            ("detailed", {})
        ]
        for formatter_type, kwargs in formatters:
            try:
                formatter = create_formatter(formatter_type, **kwargs)
                print(f"   {formatter_type} 格式器: 創建成功 ({type(formatter).__name__})")
            except Exception as e:
                print(f"   {formatter_type} 格式器: 創建失敗 - {e}")
    except Exception as e:
        print(f"   格式器工廠測試失敗: {e}")

    # 測試預定義格式字串
    print("\n6. 測試預定義格式字串:")
    try:
        format_names = list(FORMAT_STRINGS.keys())
        print(f"   可用格式數量: {len(format_names)}")
        print(f"   格式名稱: {format_names}")

        for name in format_names[:3]:  # 只測試前3個
            format_str = get_format_string(name)
            print(f"   {name}: {format_str[:50]}...")
    except Exception as e:
        print(f"   格式字串測試失敗: {e}")

    # 測試實際日誌輸出
    print("\n7. 測試實際日誌輸出:")
    try:
        # 創建測試日誌器
        test_logger = logging.getLogger("formatter_test")
        test_logger.setLevel(logging.DEBUG)

        # 清除現有處理器
        for handler in test_logger.handlers[:]:
            test_logger.removeHandler(handler)

        # 添加彩色處理器
        handler = logging.StreamHandler()
        fmt = "%(asctime)s [%(levelname)s] %(message)s"
        handler.setFormatter(ColoredFormatter(fmt))
        test_logger.addHandler(handler)

        print("   測試不同級別的日誌輸出:")
        test_logger.debug("這是一條調試訊息")
        test_logger.info("這是一條信息訊息")
        test_logger.warning("這是一條警告訊息")
        test_logger.error("這是一條錯誤訊息")
        test_logger.critical("這是一條嚴重錯誤訊息")

        print("   實際日誌輸出測試完成")
    except Exception as e:
        print(f"   實際日誌輸出測試失敗: {e}")

    print("\n✅ log_utils.formatters 模組測試完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
