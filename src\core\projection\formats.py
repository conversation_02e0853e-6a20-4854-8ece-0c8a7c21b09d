"""
立方體映射格式化工具模組 (Cubemap Formatting Utilities)

本模組提供了一組高階輔助函數，專門用於處理立方體映射（Cubemap）在不同資料
格式之間的轉換。它作為 `core.projection` 模組的配套工具，封裝了對底層
`CubeMapper` 的呼叫，為上層邏輯提供更簡潔、更具語意化的介面。
"""

from typing import Union, Dict, List, Any

import numpy as np
from numpy.typing import NDArray

from ..cube_mapping import CubeMapper


def standardize_cube_input(
    cube_faces: Union[Dict[str, NDArray], List[NDArray], NDArray],
    face_w: int
) -> NDArray:
    """
    將各種常見格式的立方體輸入，標準化為一個統一的 NumPy 陣列格式。

    標準格式為一個 shape 為 (6, H, W, C) 或 (6, H, W) 的陣列，
    其中第一個維度代表六個面，順序為 F, R, B, L, U, D。
    這個函數是 `cubemap_to_equirect` 等函數的預處理步驟，確保輸入的統一性。

    Args:
        cube_faces: 輸入的立方體面，可以是字典、列表或水平/骰子排列的陣列。
        face_w: 每個立方體面的邊長（像素）。

    Returns:
        一個標準化的 NumPy 陣列，代表立方體六個面。
    """
    mapper = CubeMapper(face_w)

    if isinstance(cube_faces, dict):
        # 從字典格式轉換
        horizon = mapper.dict_to_horizon(cube_faces)
        return np.array(mapper.horizon_to_list(horizon))
    
    elif isinstance(cube_faces, list):
        # 從列表格式轉換
        if len(cube_faces) != 6:
            raise ValueError(f"列表格式的輸入必須包含6個面，實際得到 {len(cube_faces)} 個。")
        return np.array(cube_faces)

    elif isinstance(cube_faces, np.ndarray):
        # 根據陣列形狀判斷是水平排列還是骰子排列
        if cube_faces.ndim >= 2 and cube_faces.shape[1] == face_w * 6: # 水平排列
            return np.array(mapper.horizon_to_list(cube_faces))
        elif cube_faces.ndim >= 2 and cube_faces.shape[1] == face_w * 4: # 骰子排列
            return np.array(mapper.dice_to_list(cube_faces))
        elif cube_faces.shape[0] == 6: # 已經是標準格式
             return cube_faces
        else:
            raise ValueError(f"無法識別的 NumPy 陣列格式，形狀為: {cube_faces.shape}")

    else:
        raise TypeError(f"不支援的輸入類型: {type(cube_faces)}")


def format_cubemap(
    cubemap: NDArray, format_type: str, face_w: int, squeeze: bool
) -> Union[NDArray, List[NDArray], Dict[str, NDArray]]:
    """
    將一個標準的水平排列立方體圖，轉換為使用者指定的目標格式。

    Args:
        cubemap: 來源，一個水平排列的立方體圖 NumPy 陣列。
        format_type: 目標輸出格式，可選 "horizon", "list", "dict", "dice"。
        face_w: 每個立方體面的邊長（像素）。
        squeeze: 對於單通道圖像，是否移除最後的通道維度。

    Returns:
        一個符合 `format_type` 指定格式的立方體圖資料。
    """
    mapper = CubeMapper(face_w)
    return mapper.format_output(cubemap, format_type, squeeze)
