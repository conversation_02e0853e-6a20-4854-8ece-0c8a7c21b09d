#!/usr/bin/env python3
"""
測試顯示模式和恢復功能
Test script for display modes and resume functionality
"""

import os
import sys
import time
import tempfile
import datetime
from pathlib import Path

# Add src path
sys.path.insert(0, 'src')

def test_display_modes():
    """測試顯示模式功能"""
    print("=== 測試顯示模式功能 ===")
    
    # 測試安靜模式的時間戳格式
    import datetime
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    
    # 模擬安靜模式輸出
    print("\n安靜模式輸出示例:")
    for i in range(1, 4):
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"({i}/3) test_scene_{i} {current_time}", flush=True)
        time.sleep(1)
    
    print("\n詳細模式輸出示例:")
    # 詳細模式輸出會通過 logger，這裡只模擬格式
    for i in range(1, 4):
        print(f"進度: ({i}/3) - 正在處理 test_scene_{i}")
        time.sleep(0.5)
    
    print("✅ 顯示模式測試完成")

def test_progress_manager():
    """測試進度管理器功能"""
    print("\n=== 測試進度管理器恢復功能 ===")
    
    try:
        from processing.progress_manager import ProgressManager
        
        # 創建臨時目錄
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"使用臨時目錄: {temp_dir}")
            
            # 初始化進度管理器
            pm = ProgressManager(output_path=temp_dir)
            
            # 模擬保存一些進度
            test_scenes = [
                ("district1", "scene1", "完成", 1.5),
                ("district1", "scene2", "完成", 2.1),
                ("district1", "scene3", "失敗", 0.8),
                ("district2", "scene4", "完成", 1.9),
            ]
            
            print("保存測試進度...")
            for district, scene, status, time_cost in test_scenes:
                pm.save_progress(district, scene, status, time_cost)
            
            # 測試加載已完成場景
            print("測試恢復功能...")
            completed_scenes = pm.load_completed_scenes(verify_output=False)
            print(f"找到已完成場景: {completed_scenes}")
            
            # 測試跳過邏輯
            print("測試跳過邏輯...")
            should_skip1 = pm.should_skip_scene("district1", "scene1", completed_scenes)
            should_skip2 = pm.should_skip_scene("district1", "scene3", completed_scenes)  # 失敗的場景
            should_skip3 = pm.should_skip_scene("district1", "scene_new", completed_scenes)  # 新場景
            
            print(f"scene1 (已完成) 應該跳過: {should_skip1}")
            print(f"scene3 (失敗) 應該跳過: {should_skip2}")
            print(f"scene_new (新場景) 應該跳過: {should_skip3}")
            
            # 獲取進度摘要
            summary = pm.get_progress_summary()
            print(f"進度摘要: {summary}")
            
            print("✅ 進度管理器測試完成")
            
    except ImportError as e:
        print(f"❌ 無法載入進度管理器: {e}")
        return False
    except Exception as e:
        print(f"❌ 進度管理器測試失敗: {e}")
        return False
    
    return True

def test_command_line_args():
    """測試命令行參數處理"""
    print("\n=== 測試命令行參數處理 ===")
    
    # 模擬命令行參數處理
    class MockArgs:
        def __init__(self, quiet=False, verbose=False):
            self.quiet = quiet
            self.verbose = verbose
    
    # 模擬處理器配置
    config = {}
    
    # 測試安靜模式
    args = MockArgs(quiet=True)
    if args.quiet:
        config["display_mode"] = "quiet"
    elif args.verbose:
        config["display_mode"] = "verbose"
    else:
        config.setdefault("display_mode", "verbose")
    
    print(f"安靜模式參數 -> display_mode: {config['display_mode']}")
    
    # 測試詳細模式
    config = {}
    args = MockArgs(verbose=True)
    if args.quiet:
        config["display_mode"] = "quiet"
    elif args.verbose:
        config["display_mode"] = "verbose"
    else:
        config.setdefault("display_mode", "verbose")
    
    print(f"詳細模式參數 -> display_mode: {config['display_mode']}")
    
    # 測試預設模式
    config = {}
    args = MockArgs()
    if args.quiet:
        config["display_mode"] = "quiet"
    elif args.verbose:
        config["display_mode"] = "verbose"
    else:
        config.setdefault("display_mode", "verbose")
    
    print(f"預設參數 -> display_mode: {config['display_mode']}")
    
    print("✅ 命令行參數測試完成")

def test_integration():
    """整合測試"""
    print("\n=== 整合測試 ===")
    
    # 模擬完整的處理流程
    print("模擬處理流程...")
    
    # 1. 載入已完成場景
    print("1. 載入已完成場景...")
    completed_scenes = {"district1/scene1", "district1/scene2"}
    
    # 2. 模擬處理場景列表
    all_scenes = [
        ("district1", "scene1"),  # 已完成，應跳過
        ("district1", "scene2"),  # 已完成，應跳過
        ("district1", "scene3"),  # 新場景，需處理
        ("district2", "scene4"),  # 新場景，需處理
    ]
    
    # 3. 處理邏輯
    total_skipped = 0
    for i, (district, scene) in enumerate(all_scenes, 1):
        scene_key = f"{district}/{scene}"
        
        if scene_key in completed_scenes:
            print(f"⏭️  {scene_key} 已確認完成，跳過處理")
            total_skipped += 1
            continue
        
        # 模擬顯示進度（安靜模式）
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"({i}/{len(all_scenes)}) {scene_key} {current_time}")
        
        # 模擬處理
        print(f"正在處理 {scene_key}...")
        time.sleep(0.5)
        
        # 模擬完成
        processing_time = 0.5
        print(f"處理完成，處理時間: {processing_time:.2f} 秒")
    
    print(f"整合測試完成 - 跳過已完成: {total_skipped} 個")
    print("✅ 整合測試完成")

if __name__ == "__main__":
    print("🚀 顯示模式和恢復功能測試開始")
    print("=" * 50)
    
    # 執行所有測試
    test_display_modes()
    
    pm_success = test_progress_manager()
    
    test_command_line_args()
    
    test_integration()
    
    print("\n" + "=" * 50)
    if pm_success:
        print("🎉 所有測試完成！功能正常運作")
    else:
        print("⚠️ 部分測試失敗，請檢查模組是否正確安裝")
    print("=" * 50)