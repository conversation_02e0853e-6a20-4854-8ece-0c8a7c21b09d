"""
邊界框 (Bbox) 座標轉換工具

這個模組專門處理與檢測結果 (bounding boxes) 相關的座標轉換。
它依賴於 core.coordinate.core 中的通用座標轉換器來實現底層的點對點轉換。
"""

import time
from typing import Optional

import numpy as np

from core.coordinate.core import CoordinateTransformer

try:
    from utils.unified_performance_monitor import UnifiedPerformanceMonitor
    HAS_PERFORMANCE_MONITOR = True
except ImportError:
    HAS_PERFORMANCE_MONITOR = False


class BboxConverter:
    """
    邊界框轉換器

    將全景圖上的邊界框轉換到立方體面，或反之。
    """

    def __init__(self, coord_transformer: CoordinateTransformer):
        self.coord_transformer = coord_transformer
        self.face_names = self.coord_transformer.face_names
        self.performance_monitor = (
            UnifiedPerformanceMonitor() if HAS_PERFORMANCE_MONITOR else None
        )

    def convert_panorama_bbox_to_cube_faces(
        self,
        bbox: dict,
        pano_width: int,
        pano_height: int,
        cube_size: int,
        num_samples: int = 20,
    ) -> dict[str, list[dict]]:
        """將單個全景圖邊界框轉換為可能橫跨的多個立方體面上的邊界框"""
        start_time = time.time()
        face_annotations = {face: [] for face in self.face_names}
        x1, y1, x2, y2 = bbox["x1"], bbox["y1"], bbox["x2"], bbox["y2"]

        sample_points = []
        for i in range(num_samples):
            t = i / (num_samples - 1) if num_samples > 1 else 0
            sample_points.extend(
                [
                    (x1 + t * (x2 - x1), y1),
                    (x1 + t * (x2 - x1), y2),
                    (x1, y1 + t * (y2 - y1)),
                    (x2, y1 + t * (y2 - y1)),
                ]
            )
        for i in range(5):  # 內部取樣
            for j in range(5):
                sample_points.append((x1 + (x2 - x1) * i / 4, y1 + (y2 - y1) * j / 4))

        face_points = {face: [] for face in self.face_names}
        for pano_u, pano_v in sample_points:
            face_name, face_x, face_y = (
                self.coord_transformer.panorama_point_to_cube_face(
                    pano_u, pano_v, pano_width, pano_height, cube_size
                )
            )
            if face_name:
                face_points[face_name].append((face_x, face_y))

        for face_name, points_list in face_points.items():
            if len(points_list) >= 4:
                xs, ys = [p[0] for p in points_list], [p[1] for p in points_list]
                face_x1, face_y1, face_x2, face_y2 = (
                    max(0, min(xs)),
                    max(0, min(ys)),
                    min(cube_size, max(xs)),
                    min(cube_size, max(ys)),
                )
                if face_x2 > face_x1 + 5 and face_y2 > face_y1 + 5:
                    face_annotations[face_name].append(
                        {
                            "label": bbox.get("label", "unknown"),
                            "x1": face_x1,
                            "y1": face_y1,
                            "x2": face_x2,
                            "y2": face_y2,
                            "points": [[face_x1, face_y1], [face_x2, face_y2]],
                            "shape_type": "rectangle",
                            "group_id": bbox.get("group_id"),
                            "flags": bbox.get("flags", {}),
                        }
                    )

        if self.performance_monitor:
            duration_ms = (time.time() - start_time) * 1000
            self.performance_monitor._add_metric(
                name="panorama_bbox_to_cube", value=duration_ms, unit="ms"
            )
        return face_annotations

    def convert_cube_face_bbox_to_panorama(
        self,
        face_name: str,
        bbox: dict,
        cube_size: int,
        pano_width: int,
        pano_height: int,
        num_samples: int = 16,
    ) -> Optional[dict]:
        """將單個立方體面上的邊界框轉換回全景圖上的單個邊界框"""
        x1, y1, x2, y2 = bbox["x1"], bbox["y1"], bbox["x2"], bbox["y2"]
        sample_points = [((x1 + x2) / 2, (y1 + y2) / 2)]
        for i in range(num_samples):
            t = i / (num_samples - 1) if num_samples > 1 else 0
            sample_points.extend(
                [
                    (x1 + t * (x2 - x1), y1),
                    (x1 + t * (x2 - x1), y2),
                    (x1, y1 + t * (y2 - y1)),
                    (x2, y1 + t * (y2 - y1)),
                ]
            )

        pano_points = [
            self.coord_transformer.cube_face_point_to_panorama(
                face_name, fx, fy, cube_size, pano_width, pano_height
            )
            for fx, fy in sample_points
        ]
        pano_points = [
            (np.clip(u, 0, pano_width - 1), np.clip(v, 0, pano_height - 1))
            for u, v in pano_points
        ]

        if pano_points:
            xs, ys = [p[0] for p in pano_points], [p[1] for p in pano_points]
            if max(xs) - min(xs) > pano_width * 0.7:
                left_xs, right_xs = [x for x in xs if x < pano_width * 0.5], [
                    x for x in xs if x > pano_width * 0.5
                ]
                if left_xs and right_xs:
                    xs = left_xs if len(left_xs) >= len(right_xs) else right_xs

            pano_x1, pano_y1, pano_x2, pano_y2 = (
                max(0, min(xs)),
                max(0, min(ys)),
                min(pano_width, max(xs)),
                min(pano_height, max(ys)),
            )
            if pano_x2 > pano_x1 + 5 and pano_y2 > pano_y1 + 5:
                return {
                    "label": bbox.get("label", "unknown"),
                    "x1": pano_x1,
                    "y1": pano_y1,
                    "x2": pano_x2,
                    "y2": pano_y2,
                    "points": [[pano_x1, pano_y1], [pano_x2, pano_y2]],
                    "shape_type": "rectangle",
                    "group_id": bbox.get("group_id"),
                    "flags": bbox.get("flags", {}),
                }
        return None
