"""
偵測框合併處理器

使用基於 IoU 的聚類來合併重疊的偵測框。
"""

from typing import Any

from config.constants import DEFAULT_IOU_THRESHOLD
from log_utils.factory import get_logger

from ..core.data_structures import DetectionBox
from .base import PostProcessor

logger = get_logger(__name__)


class MergerProcessor(PostProcessor):
    """合併重疊的偵測框"""

    def __init__(self, iou_threshold: float = DEFAULT_IOU_THRESHOLD):
        """初始化合併處理器

        :param iou_threshold: 用於合併偵測框的 IoU 閾值
        """
        self.iou_threshold = iou_threshold

    def process(
        self, detections: list[DetectionBox], context: dict[str, Any]
    ) -> list[DetectionBox]:
        """合併重疊的偵測框

        :param detections: 偵測框列表
        :param context: 處理上下文
        :return: 合併後的偵測框列表
        """
        if not detections:
            return []

        # 從上下文取得 IoU 閾值，或使用預設值
        iou_threshold = context.get("iou_threshold", self.iou_threshold)

        # 按信賴度降序排序
        detections = sorted(detections, key=lambda x: x.confidence, reverse=True)
        merged = []
        used = set()

        for i, best in enumerate(detections):
            if i in used:
                continue

            group = [best]

            for j, detection in enumerate(detections):
                if j == i or j in used:
                    continue

                # 僅合併相同類別的偵測框
                if (
                    detection.class_id == best.class_id
                    and self._calculate_iou(best, detection) > iou_threshold
                ):
                    group.append(detection)
                    used.add(j)

            # 合併群組中的偵測框
            if len(group) > 1:
                merged_box = self._merge_box_group(group)
                merged.append(merged_box)
                used.add(i)
            else:
                merged.append(best)

        logger.debug(f"將 {len(detections)} 個偵測框合併為 {len(merged)} 個")
        return merged

    def _calculate_iou(self, box1: DetectionBox, box2: DetectionBox) -> float:
        """計算兩個偵測框之間的 IoU

        :param box1: 第一個偵測框
        :param box2: 第二個偵測框
        :return: IoU 值 (0-1)
        """
        # 計算交集
        inter_x1 = max(box1.x1, box2.x1)
        inter_y1 = max(box1.y1, box2.y1)
        inter_x2 = min(box1.x2, box2.x2)
        inter_y2 = min(box1.y2, box2.y2)

        inter_area = max(0, inter_x2 - inter_x1) * max(0, inter_y2 - inter_y1)

        # 計算聯集
        box1_area = box1.area
        box2_area = box2.area
        union_area = box1_area + box2_area - inter_area

        return inter_area / (union_area + 1e-6)

    def _merge_box_group(self, group: list[DetectionBox]) -> DetectionBox:
        """將一組偵測框合併為單一偵測框

        :param group: 要合併的偵測框列表
        :return: 合併後的偵測框
        """
        # 使用所有偵測框的邊界框
        x1 = min(box.x1 for box in group)
        y1 = min(box.y1 for box in group)
        x2 = max(box.x2 for box in group)
        y2 = max(box.y2 for box in group)

        # 使用最高的信賴度
        confidence = max(box.confidence for box in group)

        # 使用最高信賴度偵測框的類別
        best_box = max(group, key=lambda x: x.confidence)
        class_id = best_box.class_id

        return DetectionBox(x1, y1, x2, y2, confidence, class_id)

    def get_name(self) -> str:
        """取得處理器名稱"""
        return "merger"

    def get_description(self) -> str:
        """取得處理器描述"""
        return f"合併重疊的偵測框 (IoU > {self.iou_threshold})"
