# 🧠 核心模組 (Core Module) - 高效能演算法引擎

> **360° 全景影像處理系統的數學與計算核心，提供從座標轉換到影像投影的全套底層演算法支援。**

[![Core Module Status](https://img.shields.io/badge/Core-Production_Ready-brightgreen.svg)](https://github.com/user/repo)
[![Performance Status](https://img.shields.io/badge/Performance-Optimized-blue.svg)](https://github.com/user/repo)
[![GPU Support](https://img.shields.io/badge/GPU-Accelerated-orange.svg)](https://github.com/user/repo)
[![Numba JIT](https://img.shields.io/badge/Numba-JIT-red.svg)](https://github.com/user/repo)
[![Interpolation Methods](https://img.shields.io/badge/Interpolation-38%2B_Methods-purple.svg)](https://github.com/user/repo)

---

## 📖 模組概覽

`core` 模組是整個全景影像處理系統的**引擎室**。它包含了所有關鍵的、與性能密切相關的底層演算法。經過徹底的重構，此模組被劃分為多個高度專業化、職責單一的子套件，確保了程式碼的清晰性、可測試性和極致的性能。

### 核心設計理念

本模組專注於解決三大核心問題：
1.  **🎯 幾何正確性**：精確地處理不同座標系統（球體、笛卡爾、立方體、全景圖）之間的數學轉換。
2.  **⚡ 計算效率**：利用現代硬體（多核心 CPU、GPU）和先進的編譯技術（Numba JIT）來加速計算密集型任務。
3.  **🔧 模組化設計**：每個子模組職責單一，易于擴展和維護。

### 技術亮點

-   **🚀 性能優先**: 廣泛應用向量化、JIT 編譯和 GPU 加速。
-   **🎨 演算法豐富**: 提供超過 38 種插值演算法，滿足不同品質與速度的需求。
-   **🔄 動態後端**: 智慧檢測硬體環境，自動選擇最佳運算後端（NumPy, CuPy, PyTorch）。
-   **📚 快取優化**: 使用 LRU 快取機制，顯著減少重複計算。
-   **🏗️ 企業級架構**: 遵循單一職責原則，採用高度模組化的設計。

---

## 🏛️ 檔案結構

```
core/
├── __init__.py                      # 模組化封裝與 PyInstaller 相容性處理
├── README.md                        # 本說明文件
│
├── coordinate/                      # 🌐 座標轉換系統
│   ├── __init__.py                  # - 導出 CoordinateTransformer
│   ├── core.py                      # - CoordinateTransformer 主類別
│   ├── numba_kernels.py             # - Numba JIT 加速的核心函式
│   └── utils.py                     # - 座標轉換輔助工具
│
├── interpolation/                   # 🎭 高性能插值引擎
│   ├── __init__.py                  # - 統一導出介面 (38+ 種方法)
│   ├── interpolator.py              # - AdvancedInterpolator 核心類別
│   ├── gpu_backend.py               # - GPU 加速實現
│   ├── cache.py                     # - 插值快取管理
│   ├── compat.py                    # - 向後相容層
│   └── kernels/                     # - 插值核函式套件
│       ├── __init__.py              # -- 統一導出 26+ 種核函式
│       ├── basic.py                 # -- 基礎插值 (雙線性, 三次等)
│       ├── advanced.py              # -- 進階插值 (Lanczos, Gaussian等)
│       └── experimental.py          # -- 實驗性演算法 (預留)
│
├── projection/                      # 🔄 投影變換引擎
│   ├── __init__.py                  # - 導出 ProjectionCore
│   ├── core.py                      # - ProjectionCore 主類別
│   ├── gpu_backends.py              # - 多種 GPU 後端實現
│   ├── formats.py                   # - 投影格式定義
│   └── utils.py                     # - 投影輔助工具
│
├── samplers/                        # 📐 智慧採樣器
│   ├── __init__.py                  # - 導出採樣器工廠
│   └── core.py                      # - 增強型採樣器實現
│
└── cube_mapping/                    # 🎲 立方體面格式轉換工具
    ├── __init__.py                  # - 導出 CubeMapper
    └── core.py                      # - CubeMapper 核心類別
```

---

## 🧩 核心元件詳解

### 1. 🌐 Coordinate System (座標轉換系統)

**職責**: 處理不同座標系統之間的精確數學轉換。

#### 主要類別
-   **`CoordinateTransformer`**: 統一的座標轉換介面。

#### 核心功能
```python
from core.coordinate import CoordinateTransformer

transformer = CoordinateTransformer()
# 實現 球面座標 ↔ 笛卡爾座標 ↔ 立方體面座標 ↔ 全景圖像素 之間的無縫轉換
```

#### 技術特點
-   **⚡ Numba JIT 優化**: 核心轉換邏輯使用即時編譯 (JIT)，性能遠超純 Python 實現。
-   **📊 性能監控**: 內建統計系統，可追蹤轉換次數和處理時間。
-   **🔄 LRU 快取**: `generate_cube_coordinates()` 方法使用快取機制，避免重複的密集計算。

#### 支援的轉換類型
-   `uv_to_xyz()`: UV 座標 → 3D 笛卡爾座標
-   `xyz_to_uv()`: 3D 座標 → UV 座標
-   `coor_to_uv()`: 座標索引 → UV 座標
-   `uv_to_coor()`: UV 座標 → 座標索引
-   `compute_cube_coords_fast()`: 快速計算立方體座標

---

### 2. 🎭 Interpolation Engine (插值引擎)

**職責**: 提供高品質、多樣化的影像插值演算法。

#### 主要類別
-   **`AdvancedInterpolator`**: 核心插值器，支援超過 38 種插值方法。
-   **`MultipleInterpolator`**: 多重插值器，可組合多種插值策略以達到特殊效果。

#### 插值方法分類

##### 基礎插值 (Basic Kernels)
```python
# basic.py - 4 種基礎方法
- bilinear_core      # 雙線性插值
- box_kernel         # 盒狀核函式
- cubic_convolution  # 三次捲積
- triangular_kernel  # 三角形核函式
```

##### 進階插值 (Advanced Kernels)
```python
# advanced.py - 26 種進階方法
# Lanczos 系列 (5 種)
- lanczos_kernel, lanczos_kernel_2/3/4/8

# 高斯系列 (2 種)
- gaussian_kernel, gaussian_kernel_default

# Mitchell 系列 (2 種)
- mitchell_kernel, mitchell_kernel_default

# ... 以及其他 17 種專業核函式
```

#### 使用範例
```python
from core.interpolation import create_interpolator, InterpolationMethod

# 建立一個高品質的 Lanczos3 插值器
interpolator = create_interpolator(InterpolationMethod.LANCZOS3)

# 執行插值
result = interpolator.interpolate(image, x_coords, y_coords)

# 使用多重插值策略（例如，銳化後再進行高斯平滑）
multi_interpolator = create_multiple_interpolator([
    InterpolationMethod.LANCZOS3,
    InterpolationMethod.GAUSSIAN
])
```

#### 效能特點
-   **🔥 GPU 加速**: 完整支援 CuPy (NVIDIA) 和 PyTorch (通用) 後端。
-   **📈 快取機制**: 內建插值結果快取，加速重複操作。
-   **⚙️ 設定驅動**: 透過 `InterpolationConfig` 精確控制插值行為。
-   **🔄 策略模式**: 易於擴展新的自訂插值演算法。

---

### 3. 🔄 Projection Engine (投影變換引擎)

**職責**: 實現全景圖與立方體貼圖之間的雙向轉換。

#### 主要類別
-   **`ProjectionCore`**: 核心投影轉換的協調器。

#### 核心功能
```python
from core.projection import create_projection_core

# 建立投影引擎
projection_core = create_projection_core(
    height=2048,        # 全景圖高度
    width=4096,         # 全景圖寬度
    face_size=1024      # 立方體面尺寸
)

# 全景圖 → 立方體貼圖
cubemap = projection_core.equirect_to_cubemap(
    e_img=panorama_image,
    cube_format='dict'  # 支援 'dict', 'list', 'horizon', 'dice' 等格式
)

# 立方體貼圖 → 全景圖
panorama = projection_core.cubemap_to_equirect(cubemap)
```

#### 智慧後端選擇
```python
# 根據硬體環境自動選擇最佳後端
Available Backends:
- CPU: NumPy (預設)
- GPU: CuPy (NVIDIA CUDA)
- GPU: PyTorch (通用 GPU)
```

#### 技術亮點
-   **🎯 高精度轉換**: 結合 `coordinate` 和 `interpolation` 模組的能力，確保轉換精度。
-   **🧠 智慧後端**: 自動偵測並選擇可用的最佳運算後端。
-   **📊 格式支援**: 支援多種立方體面格式輸出，方便與不同工具鏈整合。
-   **⚡ 平行處理**: 充分利用多核心 CPU 和 GPU 資源進行加速。

---

### 4. 📐 Smart Samplers (智慧採樣器)

**職責**: 根據投影座標，從來源影像中精確地採樣像素值。

#### 主要類別
-   **`EnhancedEquirectSampler`**: 增強型全景圖採樣器。
-   **`EnhancedCubeFaceSampler`**: 增強型立方體面採樣器。

#### 工廠函式
```python
from core.samplers import create_equirect_sampler, create_cube_face_sampler

# 建立全景圖採樣器
equirect_sampler = create_equirect_sampler(interpolation_method="lanczos3")

# 建立立方體面採樣器
cube_sampler = create_cube_face_sampler(interpolation_method="gaussian")
```

#### 特殊處理能力
-   **🌍 環繞邊界**: 完美處理全景圖在 180°/-180° 經度線上的邊界縫合問題。
-   **🔗 鄰接處理**: 平滑處理立方體各面之間的邊界，避免產生接縫。
-   **🎯 邊緣優化**: 確保影像邊緣和角落的採樣正確無誤。

---

### 5. 🎲 Cube Mapping Tools (立方體貼圖工具)

**職責**: 提供立方體面在不同表示格式之間的轉換。

#### 主要類別
-   **`CubeMapper`**: 立方體格式轉換器。

#### 支援格式
```python
from core.cube_mapping import CubeMapper

mapper = CubeMapper(face_size=1024)

# 格式轉換範例
cubemap_dict = {"F": front, "R": right, "B": back, "L": left, "U": up, "D": down}

# dict → horizon (水平排列): [F][R][B][L][U][D]
horizon_format = mapper.dict_to_horizon(cubemap_dict)

# dict → list (列表格式): [front, right, back, left, up, down]
list_format = mapper.dict_to_list(cubemap_dict)

# dict → dice (骰子展開格式)
dice_format = mapper.dict_to_dice(cubemap_dict)

# 同時也支援反向轉換
dict_from_horizon = mapper.horizon_to_dict(horizon_format)
```

#### 應用場景
-   **💾 儲存優化**: `horizon` 格式適合直接儲存為單一圖片。
-   **🔄 資料處理**: `list` 格式便於進行批次處理或機器學習訓練。
-   **🎮 遊戲引擎**: `dice` 格式適合導入 Unity, Unreal 等遊戲引擎。
-   **🌐 Web 顯示**: `dict` 格式適合在前端進行互動式展示。

---

## 🚀 使用工作流程

### 基本投影轉換流程

```python
import numpy as np
from core.projection import create_projection_core
from core.cube_mapping import CubeMapper
from config.interpolation import InterpolationMethod

# 1. 初始化核心組件
projection_core = create_projection_core(
    height=2048,
    width=4096,
    face_size=1024,
    interpolation_method=InterpolationMethod.LANCZOS3
)
cube_mapper = CubeMapper(face_size=1024)

# 2. 載入全景圖
panorama_image = np.random.rand(2048, 4096, 3)  # 範例資料

# 3. 執行投影轉換
cubemap_dict = projection_core.equirect_to_cubemap(
    e_img=panorama_image,
    cube_format='dict'
)

# 4. 根據需求進行格式轉換
if save_as_single_image:
    horizontal_cubemap = cube_mapper.dict_to_horizon(cubemap_dict)
    # cv2.imwrite("cubemap_horizontal.jpg", horizontal_cubemap)
elif use_in_game_engine:
    dice_cubemap = cube_mapper.dict_to_dice(cubemap_dict)
elif batch_processing:
    cube_list = cube_mapper.dict_to_list(cubemap_dict)

# 5. 反向轉換 (立方體 → 全景圖)
reconstructed_panorama = projection_core.cubemap_to_equirect(cubemap_dict)
```

### 高性能插值工作流程

```python
from core.interpolation import (
    create_interpolator,
    create_multiple_interpolator,
    InterpolationMethod,
    clear_interpolation_cache,
    get_cache_stats
)

# 1. 建立高品質插值器
interpolator = create_interpolator(
    method=InterpolationMethod.LANCZOS3,
    enable_gpu=True,  # 啟用 GPU 加速
    cache_size=128    # 設定快取大小
)

# 2. 或使用多重插值策略
multi_interpolator = create_multiple_interpolator([
    InterpolationMethod.LANCZOS3,  # 主要方法
    InterpolationMethod.GAUSSIAN   # 輔助平滑
])

# 3. 執行插值操作
result = interpolator.interpolate(
    image=source_image,
    x_coords=target_x_coordinates,
    y_coords=target_y_coordinates
)

# 4. 監控快取效能
cache_stats = get_cache_stats()
print(f"快取命中率: {cache_stats['hit_rate']:.2%}")

# 5. 在需要時清理快取
clear_interpolation_cache()
```

---

## 🎯 性能優化指南

### 1. 插值方法選擇策略

| 需求 | 建議方法 | 特性 |
|:---|:---|:---|
| 最快速度 | `InterpolationMethod.BILINEAR` | 速度極快，但可能模糊 |
| 平衡選擇 | `InterpolationMethod.BICUBIC` | 速度與品質的良好平衡 |
| 高品質 | `InterpolationMethod.LANCZOS3` | 清晰，細節保留佳 |
| 最佳品質 | `InterpolationMethod.LANCZOS8` | 極高品質，但計算成本高 |
| 特殊效果 | `CATMULL_ROM` (銳化), `GAUSSIAN` (平滑) | |

### 2. 快取優化策略

```python
from core.interpolation import get_cache_stats, clear_interpolation_cache
from config.constants import _CACHE_SIZE

# 監控快取效率
def optimize_cache():
    stats = get_cache_stats()
    hit_rate = stats.get('hit_rate', 0)
    if hit_rate < 0.5:  # 命中率低於 50%
        print("快取效率低，考慮調整快取大小或清理快取。")
        clear_interpolation_cache()
    return hit_rate

# 可在 config/constants.py 中調整 _CACHE_SIZE 來影響座標轉換的快取
```

### 3. GPU 使用最佳實踐

```python
import psutil
import GPUtil

def smart_backend_selection(image_size):
    """根據影像大小和系統資源智慧選擇後端"""
    # ... (實作如前)
```

### 4. 記憶體管理最佳實踐

```python
import gc
from utils.memory_utils import get_memory_usage

def memory_efficient_processing(large_image_batch):
    """記憶體效率的大批次處理"""
    for i, image in enumerate(large_image_batch):
        memory_before = get_memory_usage()
        result = process_single_image(image)
        # 每處理 10 張圖片，強制進行一次垃圾回收
        if i % 10 == 9:
            gc.collect()
            memory_after = get_memory_usage()
            print(f"記憶體回收: {memory_before - memory_after:.1f}MB")
        yield result
```

---

## 🤝 與其他模組關係

### 依賴關係圖

```mermaid
graph TD
    subgraph "外部模組"
        D[Processing Module]
        E[Detection Module]
        F[Utils Module]
    end
    subgraph "核心模組 (Core)"
        A[Core Module]
    end
    subgraph "設定與日誌"
        B[Config Module]
        C[Log Utils Module]
    end
    
    A --> B;
    A --> C;
    D --> A;
    E -- "間接使用" --> A;
    F --> A;
```

### 關係說明

-   **`Config` 模組**: `core` 模組的**設定提供者**。所有運算參數和行為都由 `config` 模組中的設定類別驅動。
-   **`Processing` 模組**: `core` 模組的**主要消費者**。`processing` 負責業務流程，並呼叫 `core` 來執行底層的計算任務。
-   **`Detection` 模組**: **間接使用者**。雖然不直接匯入，但通常在 `processing` 流程中，會先使用 `core` 將影像轉換為適當的格式，再交給 `detection` 模組處理。
-   **`Utils` 模組**: **協作者**。`utils` 提供如 GPU 管理、記憶體監控等工具，與 `core` 模組協同工作以優化性能。
-   **`Log Utils` 模組**: **通用服務**。`core` 模組使用 `log_utils` 來記錄詳細的運算過程、性能指標和錯誤資訊。

---

## 🧪 測試與驗證

### 測試覆蓋範圍

| 測試檔案 | 主要測試內容 |
|:---|:---|
| `test_coordinate.py` | 座標轉換的數學精度 |
| `test_cube_mapping.py` | 立方體貼圖格式轉換的正確性 |
| `test_interpolation.py`| 各種插值演算法的輸出品質 |
| `test_projection.py` | 投影轉換的幾何正確性（往返測試） |
| `test_samplers.py` | 採樣器在邊界條件下的行為 |

### 執行測試指令

```bash
# 執行所有 core 模組的性能基準測試
python test/run_tests.py --module core --benchmark

# 比較 GPU 與 CPU 後端的性能差異
python test/test_core/test_projection.py --compare-backends

# 執行座標轉換的往返精度驗證
python test/test_core/test_coordinate.py --precision-test
```

---

## 🔧 設定與自訂

### 自訂插值方法

```python
# 在 `core/interpolation/kernels/experimental.py` 中註冊自訂核函式
from .experimental import register_custom_kernel

@register_custom_kernel("my_sinc")
def my_custom_sinc_kernel(x, a=3.0):
    import numpy as np
    return np.sinc(a * x)

# 之後便可在設定中直接使用 "my_sinc"
```

### 擴展座標系統

```python
# 透過繼承擴展座標轉換系統
from core.coordinate import CoordinateTransformer

class ExtendedCoordinateTransformer(CoordinateTransformer):
    def spherical_to_cylindrical(self, theta, phi, r=1.0):
        # ... 實作球面到柱面座標的轉換 ...
        pass
```

---

## ⚠️ 常見問題與解決方案

-   **記憶體溢出**: 處理超高解析度影像時，在建立 `ProjectionCore` 時設定 `streaming_mode=True`。
-   **GPU 記憶體不足**: 實作一個安全的處理迴圈，在 `try-except` 區塊中捕捉 `out of memory` 錯誤，並自動降級到 CPU 處理。
-   **插值結果有鋸齒或模糊**: 嘗試更高品質的插值方法（如 `LANCZOS3`），或使用 `MultipleInterpolator` 組合銳化與平滑策略。
-   **立方體面接縫問題**: 確保在建立採樣器時啟用 `boundary_blending=True` 和 `seamless_mode=True`。

---

## 📈 版本資訊與更新日誌

### 當前版本: 1.0.1

-   **主要特性**: 完整的座標轉換、插值、投影、採樣和立方體貼圖格式化功能。
-   **重構亮點**: 模組化設計、統一的工廠函式介面、向後相容層、企業級錯誤處理。

### 未來發展計畫

-   **v1.1.0**: 正式化實驗性插值演算法，增加 OpenCL GPU 後端支援。
-   **v1.2.0**: 引入機器學習增強的插值方法，支援 WebAssembly 匯出。

---

**🎯 Core Module - 360° 全景影像處理系統的演算法引擎**

> 透過高度模組化的設計和極致的性能優化，為企業級全景影像處理提供穩定、快速、精確的底層演算法支援。