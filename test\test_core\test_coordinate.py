"""
Tests for core.coordinate module
"""

import math
from unittest.mock import <PERSON><PERSON>ock, patch

import numpy as np
import pytest


# Mock numba decorator to avoid compilation during testing
def mock_jit(*args, **kwargs):
    def decorator(func):
        return func

    return decorator


# Patch numba.jit before importing the module
with patch("numba.jit", side_effect=mock_jit):
    from core.coordinate import \
        CoordinateTransformer  # , uv_to_coor, xyz_to_uv


class TestCoordinateTransformer:
    """Test CoordinateTransformer class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.transformer = CoordinateTransformer()
        self.cube_size = 512
        self.equirect_width = 1024
        self.equirect_height = 512

    def test_init(self):
        """Test CoordinateTransformer initialization"""
        assert isinstance(self.transformer, CoordinateTransformer)
        # Check that cache is initialized
        assert hasattr(self.transformer, "_coord_cache")

    def test_generate_cube_coordinates_basic(self):
        """Test basic cube coordinates generation"""
        coords = self.transformer.generate_cube_coordinates(self.cube_size)

        assert isinstance(coords, dict)
        assert len(coords) == 6  # Six faces

        # Check that all faces are present
        expected_faces = ["front", "back", "left", "right", "up", "down"]
        for face in expected_faces:
            assert face in coords
            assert "x" in coords[face]
            assert "y" in coords[face]
            assert "z" in coords[face]

            # Check dimensions
            assert coords[face]["x"].shape == (self.cube_size, self.cube_size)
            assert coords[face]["y"].shape == (self.cube_size, self.cube_size)
            assert coords[face]["z"].shape == (self.cube_size, self.cube_size)

    def test_generate_cube_coordinates_caching(self):
        """Test that cube coordinates are cached"""
        # First call
        coords1 = self.transformer.generate_cube_coordinates(self.cube_size)

        # Second call should return cached result
        coords2 = self.transformer.generate_cube_coordinates(self.cube_size)

        # Should be the same object (cached)
        assert coords1 is coords2

    def test_generate_equirect_grid(self):
        """Test equirectangular grid generation"""
        theta, phi = self.transformer.generate_equirect_grid(
            self.equirect_width, self.equirect_height
        )

        assert theta.shape == (self.equirect_height, self.equirect_width)
        assert phi.shape == (self.equirect_height, self.equirect_width)

        # Check theta range [0, 2π]
        assert np.min(theta) >= 0
        assert np.max(theta) <= 2 * np.pi

        # Check phi range [-π/2, π/2]
        assert np.min(phi) >= -np.pi / 2
        assert np.max(phi) <= np.pi / 2

    def test_compute_face_type(self):
        """Test face type computation from 3D coordinates"""
        # Test points that should clearly belong to specific faces
        test_cases = [
            # (x, y, z, expected_face)
            (1.0, 0.0, 0.0, 0),  # front face
            (0.0, 1.0, 0.0, 1),  # right face
            (-1.0, 0.0, 0.0, 2),  # back face
            (0.0, -1.0, 0.0, 3),  # left face
            (0.0, 0.0, 1.0, 4),  # up face
            (0.0, 0.0, -1.0, 5),  # down face
        ]

        for x, y, z, expected_face in test_cases:
            face_type = self.transformer.compute_face_type(
                np.array([x]), np.array([y]), np.array([z])
            )
            assert face_type[0] == expected_face

    def test_panorama_to_sphere_basic(self):
        """Test panorama to sphere coordinate conversion"""
        # Test with simple coordinates
        u = np.array([0, 512, 1023])  # left, center, right
        v = np.array([0, 256, 511])  # top, center, bottom

        x, y, z = self.transformer.panorama_to_sphere(
            u, v, self.equirect_width, self.equirect_height
        )

        assert len(x) == len(u)
        assert len(y) == len(v)
        assert len(z) == len(u)

        # Check that coordinates are on unit sphere
        radius = np.sqrt(x**2 + y**2 + z**2)
        np.testing.assert_array_almost_equal(radius, 1.0, decimal=6)

    def test_sphere_to_panorama_basic(self):
        """Test sphere to panorama coordinate conversion"""
        # Test with unit sphere points
        x = np.array([1.0, 0.0, -1.0])
        y = np.array([0.0, 1.0, 0.0])
        z = np.array([0.0, 0.0, 0.0])

        u, v = self.transformer.sphere_to_panorama(
            x, y, z, self.equirect_width, self.equirect_height
        )

        assert len(u) == len(x)
        assert len(v) == len(y)

        # Check bounds
        assert np.all(u >= 0)
        assert np.all(u < self.equirect_width)
        assert np.all(v >= 0)
        assert np.all(v < self.equirect_height)

    def test_round_trip_conversion(self):
        """Test round-trip conversion: panorama -> sphere -> panorama"""
        # Original panorama coordinates
        u_orig = np.array([100, 300, 700])
        v_orig = np.array([50, 150, 400])

        # Convert to sphere
        x, y, z = self.transformer.panorama_to_sphere(
            u_orig, v_orig, self.equirect_width, self.equirect_height
        )

        # Convert back to panorama
        u_back, v_back = self.transformer.sphere_to_panorama(
            x, y, z, self.equirect_width, self.equirect_height
        )

        # Should get back original coordinates (within floating point precision)
        np.testing.assert_array_almost_equal(u_orig, u_back, decimal=1)
        np.testing.assert_array_almost_equal(v_orig, v_back, decimal=1)

    def test_convert_panorama_bbox_to_cube_faces(self):
        """Test panorama bounding box to cube face conversion"""
        # Test bounding box (x, y, width, height)
        bbox = [100, 50, 200, 100]

        face_bboxes = self.transformer.convert_panorama_bbox_to_cube_faces(
            bbox, self.equirect_width, self.equirect_height, self.cube_size
        )

        assert isinstance(face_bboxes, dict)

        # Check that each face bbox has correct format
        for face_name, face_bbox in face_bboxes.items():
            if face_bbox is not None:
                assert len(face_bbox) == 4  # [x, y, width, height]
                assert all(isinstance(coord, (int, float)) for coord in face_bbox)

                # Check bounds
                x, y, w, h = face_bbox
                assert x >= 0
                assert y >= 0
                assert x + w <= self.cube_size
                assert y + h <= self.cube_size

    def test_label_conversion_methods(self):
        """Test label conversion methods"""
        # Test panorama to cube label conversion
        labels = [
            {"category": "face", "bbox": [100, 50, 200, 100]},
            {"category": "license_plate", "bbox": [300, 200, 150, 80]},
        ]

        cube_labels = self.transformer.panorama_labels_to_cube_labels(
            labels, self.equirect_width, self.equirect_height, self.cube_size
        )

        assert isinstance(cube_labels, dict)
        assert len(cube_labels) == 6  # Six faces

        # Check structure
        for face_name, face_labels in cube_labels.items():
            assert isinstance(face_labels, list)
            for label in face_labels:
                assert "category" in label
                assert "bbox" in label
                assert len(label["bbox"]) == 4

    def test_edge_cases(self):
        """Test edge cases and boundary conditions"""
        # Test with very small cube size
        small_coords = self.transformer.generate_cube_coordinates(64)
        assert len(small_coords) == 6

        # Test with boundary coordinates
        u_edge = np.array([0, self.equirect_width - 1])
        v_edge = np.array([0, self.equirect_height - 1])

        x, y, z = self.transformer.panorama_to_sphere(
            u_edge, v_edge, self.equirect_width, self.equirect_height
        )

        # Should not raise exceptions
        assert len(x) == 2
        assert len(y) == 2
        assert len(z) == 2

    def test_invalid_inputs(self):
        """Test handling of invalid inputs"""
        # Test with negative cube size
        with pytest.raises((ValueError, AssertionError)):
            self.transformer.generate_cube_coordinates(-1)

        # Test with mismatched array lengths
        u = np.array([1, 2, 3])
        v = np.array([1, 2])  # Different length

        with pytest.raises((ValueError, IndexError)):
            self.transformer.panorama_to_sphere(
                u, v, self.equirect_width, self.equirect_height
            )


# class TestStaticFunctions:
#     """Test static utility functions"""
#
#     def test_uv_to_coor_basic(self):
#         """Test uv_to_coor function with basic inputs"""
#         # Test center point
#         coor = uv_to_coor(0.5, 0.5)
#         assert len(coor) == 3
#
#         # Test corners
#         corners = [(0.0, 0.0), (1.0, 0.0), (0.0, 1.0), (1.0, 1.0)]
#
#         for u, v in corners:
#             coor = uv_to_coor(u, v)
#             assert len(coor) == 3
#             assert all(isinstance(x, (int, float)) for x in coor)
#
#     def test_xyz_to_uv_basic(self):
#         """Test xyz_to_uv function with basic inputs"""
#         # Test with unit vectors
#         test_vectors = [
#             (1.0, 0.0, 0.0),
#             (0.0, 1.0, 0.0),
#             (0.0, 0.0, 1.0),
#             (-1.0, 0.0, 0.0),
#             (0.0, -1.0, 0.0),
#             (0.0, 0.0, -1.0),
#         ]
#
#         for x, y, z in test_vectors:
#             u, v = xyz_to_uv(x, y, z)
#             assert 0 <= u <= 1
#             assert 0 <= v <= 1
#
#     def test_uv_xyz_round_trip(self):
#         """Test round-trip conversion between UV and XYZ"""
#         # Test several UV points
#         uv_points = [(0.0, 0.0), (0.5, 0.5), (1.0, 1.0), (0.25, 0.75), (0.75, 0.25)]
#
#         for u_orig, v_orig in uv_points:
#             # UV -> XYZ
#             x, y, z = uv_to_coor(u_orig, v_orig)
#
#             # XYZ -> UV
#             u_back, v_back = xyz_to_uv(x, y, z)
#
#             # Should get back original coordinates (within precision)
#             np.testing.assert_almost_equal(u_orig, u_back, decimal=6)
#             np.testing.assert_almost_equal(v_orig, v_back, decimal=6)
#
#     def test_mathematical_properties(self):
#         """Test mathematical properties of coordinate transformations"""
#         # Test that UV coordinates map to unit sphere
#         u_test = np.random.uniform(0, 1, 100)
#         v_test = np.random.uniform(0, 1, 100)
#
#         for u, v in zip(u_test, v_test):
#             x, y, z = uv_to_coor(u, v)
#             radius = math.sqrt(x * x + y * y + z * z)
#             np.testing.assert_almost_equal(radius, 1.0, decimal=6)


class TestPerformanceAndMemory:
    """Test performance and memory aspects"""

    def test_large_coordinate_generation(self):
        """Test coordinate generation with large sizes"""
        transformer = CoordinateTransformer()

        # Test with larger size (but not too large for CI)
        large_size = 1024
        coords = transformer.generate_cube_coordinates(large_size)

        assert len(coords) == 6
        for face_coords in coords.values():
            assert face_coords["x"].shape == (large_size, large_size)

    def test_vectorized_operations(self):
        """Test that vectorized operations work correctly"""
        transformer = CoordinateTransformer()

        # Test with arrays of coordinates
        u_array = np.random.uniform(0, 1024, 1000)
        v_array = np.random.uniform(0, 512, 1000)

        x, y, z = transformer.panorama_to_sphere(u_array, v_array, 1024, 512)

        assert len(x) == 1000
        assert len(y) == 1000
        assert len(z) == 1000

    def test_cache_effectiveness(self):
        """Test that caching is working effectively"""
        transformer = CoordinateTransformer()

        # Multiple calls with same size should be fast (cached)
        size = 256

        # First call
        coords1 = transformer.generate_cube_coordinates(size)

        # Second call should use cache
        coords2 = transformer.generate_cube_coordinates(size)

        # Should be identical objects (cached)
        assert coords1 is coords2

        # Different size should create new coordinates
        coords3 = transformer.generate_cube_coordinates(size * 2)
        assert coords3 is not coords1


if __name__ == "__main__":
    pytest.main([__file__])
