"""
全景圖處理核心模組
從舊版本 main.py 重構而來，負責核心全景圖處理邏輯

主要功能：
- 單張全景圖處理
- 立方體面處理
- HTML5資料夾處理
- 模糊區域檢測與應用
- 批次處理基礎功能
"""

# 1. 標準庫
import gc
import os
import shutil
import sys
import time
from pathlib import Path

# 2. 延遲載入重型庫
def _import_heavy_libs():
    """延遲載入重型庫"""
    global cv2, np
    try:
        import cv2
        import numpy as np
        return True
    except ImportError as e:
        import warnings
        warnings.warn(f"無法載入必要的重型庫: {e}", ImportWarning)
        return False

# Import project modules directly (using pip install -e .)
from utils import import_helper

# 3. 本地模組
try:
    from log_utils import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

# 延遲載入其他模組
def _import_processing_deps():
    """延遲載入處理相關依賴"""
    try:
        from processing.factory import ProcessingFactory
        from config.settings import get_config
        return ProcessingFactory, get_config
    except ImportError as e:
        import warnings
        warnings.warn(f"無法載入處理依賴: {e}", ImportWarning)
        return None, None

logger = get_logger("panorama_processor")

class PanoramaProcessor:
    """
    全景圖處理器 (純協調者)
    Refactored Panorama Processor (Pure Coordinator)

    職責：作為處理管線的高級接口，將所有工作委派給 ProcessingPipeline。
    """

    def __init__(self, config=None):
        """
        初始化全景圖處理器

        Args:
            config: 應用程序配置對象 (可選)
        """
        self._initialized = False
        self._config = config
        self.logger = get_logger("PanoramaProcessor")

        self.logger.info("全景圖處理器 (延遲載入模式) 基本初始化完成")

    def _ensure_initialized(self):
        """確保處理器已完全初始化"""
        if not self._initialized:
            # 載入處理依賴
            ProcessingFactory, get_config = _import_processing_deps()
            if ProcessingFactory is None or get_config is None:
                raise ImportError("無法載入處理依賴")

            self.config = self._config or get_config()
            self.pipeline = ProcessingFactory.create_pipeline(self.config)
            self._initialized = True

            self.logger.info("全景圖處理器完全初始化完成")

    def process_single_panorama(
        self,
        img_path: str,
        output_folder: str,
    ) -> tuple[bool, dict]:
        """
        處理單張全景圖像，將工作完全委派給處理管線。
        """
        self._ensure_initialized()
        self.logger.info(f"委派處理任務給管線: {img_path}")
        result = self.pipeline.process_panorama(img_path, output_folder)
        return result.success, result.context.get_summary()

    def process_cube_structure(
        self,
        cube_path: str,
        output_folder: str,
    ) -> tuple[bool, dict]:
        """
        處理已有的立方體面結構，將工作完全委派給處理管線。
        """
        self._ensure_initialized()
        self.logger.info(f"委派立方體處理任務給管線: {cube_path}")
        result = self.pipeline.process_cube_faces(cube_path, output_folder)
        return result.success, result.context.get_summary()



def main():
    """測試用主函數，增加了調試信息和預設路徑"""
    print("--- 開始執行 panorama_processor.py 主函數 ---")
    
    # import sys
    # from config.settings import get_config
    print(11)
    # --- 修改部分：增加預設路徑以便於測試 ---
    # 如果提供了命令列參數，則使用它們
    if len(sys.argv) >= 3:
        img_path = sys.argv[1]
        output_path = sys.argv[2]
        print(f"從命令列讀取路徑: 圖像='{img_path}', 輸出='{output_path}'")
    else:
        # 否則，使用預設的佔位符路徑
        # !!! 重要：請將下面的 'path/to/your/image.jpg' 替換為一個真實有效的圖像路徑
        img_path = r"D:\image\1_test_image\pano_img_test\panotpv8790154.jpg"
        output_path = r"D:\image\1_test_image\test "
        print("未提供命令列參數，使用預設測試路徑。")
        print(f"預設圖像路徑: '{img_path}'")
        print(f"預設輸出路徑: '{output_path}'")
        print("警告: 如果 'path/to/your/image.jpg' 不是有效文件，處理將會失敗。")

    print("\n步驟 1: 初始化配置...")
    config = get_config()
    processor = PanoramaProcessor(config)
    print("配置與處理器初始化完成。")

    print(f"\n步驟 2: 檢查輸入文件是否存在: '{img_path}'")
    if os.path.isfile(img_path):
        print(f"文件存在。準備處理...")
        os.makedirs(output_path, exist_ok=True)
        print(f"輸出目錄 '{output_path}' 已確認存在。")
        
        print("\n步驟 3: 開始調用 process_single_panorama 進行處理...")
        try:
            result, stats = processor.process_single_panorama(img_path, output_path)
            print("處理函式執行完畢。")
            print(f"處理結果: {result}, 統計: {stats}")
        except Exception as e:
            print(f"!!! 處理過程中發生錯誤: {e}")
            logger.exception("在 process_single_panorama 中捕獲到未處理的異常")

    else:
        print(f"!!! 錯誤: 輸入文件不存在或不是一個文件: '{img_path}'")
        print("請檢查路徑或修改腳本中的預設路徑。")
    
    print("\n--- panorama_processor.py 主函數執行完畢 ---")


if __name__ == "__main__":
    main()
