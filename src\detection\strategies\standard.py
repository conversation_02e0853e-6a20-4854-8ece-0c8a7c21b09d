"""
標準偵測策略

為大多數立方體面實作標準的雙模型偵測。
"""

from typing import Any

import numpy as np
import torch

from log_utils import get_logger

from ..core.data_structures import DetectionBox
from .base import DetectionStrategy

logger = get_logger(__name__)


class StandardStrategy(DetectionStrategy):
    """標準雙模型偵測策略"""

    def detect(
        self, image: np.ndarray, models: dict[str, Any], config: Any
    ) -> list[DetectionBox]:
        """使用主要和次要模型執行標準偵測

        :param image: 輸入影像
        :param models: 可用模型的字典
        :param config: 偵測設定
        :return: 偵測框列表
        """
        all_detections = []

        # 主要模型偵測
        primary_model = models.get("primary")
        if primary_model:
            primary_detections = self._detect_with_model(
                image, primary_model, config.conf_threshold, config
            )
            all_detections.extend(primary_detections)

        # 次要模型偵測 (如果可用)
        secondary_model = models.get("secondary")
        if secondary_model:
            secondary_detections = self._detect_with_model(
                image, secondary_model, config.conf_threshold, config
            )
            all_detections.extend(secondary_detections)

        return all_detections

    def _detect_with_model(
        self, image: np.ndarray, model: Any, conf_threshold: float, config: Any
    ) -> list[DetectionBox]:
        """使用單一模型執行偵測

        :param image: 輸入影像
        :param model: YOLO 模型實例
        :param conf_threshold: 信賴度閾值
        :param config: 偵測設定
        :return: 偵測框列表
        """
        try:
            # 如果 CUDA 可用，則使用 AMP
            use_amp = (
                "cuda" in str(config.device) if hasattr(config, "device") else False
            )

            if use_amp:
                with torch.amp.autocast(device_type="cuda"):
                    results = model.predict(
                        image,
                        conf=conf_threshold,
                        iou=config.iou_threshold,
                        verbose=False,
                        device=config.device,
                    )
            else:
                results = model.predict(
                    image,
                    conf=conf_threshold,
                    iou=config.iou_threshold,
                    verbose=False,
                    device=config.device,
                )

            detections = []
            if results and len(results) > 0:
                for box in results[0].boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    conf_val = box.conf[0].cpu().item()
                    cls = int(box.cls[0].cpu().item())

                    detections.append(
                        DetectionBox(
                            x1=int(x1),
                            y1=int(y1),
                            x2=int(x2),
                            y2=int(y2),
                            confidence=conf_val,
                            class_id=cls,
                        )
                    )

            return detections

        except Exception as e:
            logger.error(f"標準偵測失敗: {e}")
            return []

    def get_name(self) -> str:
        """取得策略名稱"""
        return "standard"

    def can_handle_face(self, face_id: int) -> bool:
        """標準策略處理面 0, 1, 2, 3"""
        return face_id in [0, 1, 2, 3]

    def get_description(self) -> str:
        """取得策略描述"""
        return "用於一般立方體面的標準雙模型偵測"
