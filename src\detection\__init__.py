"""
Detection 模組 v2.0 - 現代化AI物件偵測系統

本 `__init__.py` 檔案是 `detection` 模組的統一入口點，負責定義其公開API。
此模組為全景影像處理流程中的隱私保護（如人臉、車牌偵測）提供了一個
高度模組化、可擴展且易於維護的YOLO物件偵測解決方案。

## 核心設計理念

- **模組化架構 (Modular Architecture)**: 將原先龐大的單體類別拆分為多個各司其職的小模組
- **關注點分離 (Separation of Concerns)**: 每個模組只專注於一項任務，提升程式碼清晰度
- **可擴展性 (Extensibility)**: 透過策略模式和管線模式，可輕易新增新的偵測演算法或後處理步驟
- **向後相容 (Backward Compatibility)**: 透過相容性層確保舊版程式碼無縫接軌
- **效能優化 (Performance Optimization)**: 整合GPU加速、批次處理、記憶體最佳化等特性

## 主要架構元件

### 核心模組 (core/)
- **Detector**: 整個偵測流程的總協調器，整合所有子系統
- **DetectionConfig**: 強型別配置類別，支援多種偵測模式配置
- **DetectionBox**: 標準化的偵測結果資料結構，包含位置、信心度、標籤等

### 策略模組 (strategies/)
- **StrategyFactory**: 根據情境動態建立偵測策略的工廠類別
- **DetectionStrategy**: 偵測策略的抽象基類，定義統一介面
- **StandardStrategy**: 標準偵測策略，適用於大多數情境
- **RotationStrategy**: 旋轉偵測策略，處理不同角度的物件
- **SkipStrategy**: 跳過策略，用於特定條件下的效能最佳化

### 後處理模組 (postprocessing/)
- **PostProcessingPipeline**: 可組合的後處理管線，支援多階段處理
- **MergerProcessor**: 重疊檢測結果合併處理器
- **LargeBoxFilter**: 大型邊界框過濾器，移除異常檢測結果
- **CenterRegionFilter**: 中心區域過濾器，專注於影像中心區域

### 模型管理模組 (models/)
- **ModelManager**: 統一的模型生命週期管理器
- **ModelLoader**: 智慧型模型載入器，支援多種模型格式
- **預訓練模型**: 支援YOLOv8、YOLOv9等多種預訓練模型

### 工具模組 (utils/)
- **BlurProcessor**: 基於檢測結果的模糊處理工具
- **StatisticsCollector**: 偵測統計資料收集器
- **可視化工具**: 檢測結果的可視化和標註工具

## 使用範例

### 基本偵測流程
```python
# 1. 匯入核心元件
from detection import Detector, DetectionConfig
from detection.strategies import StrategyFactory
from detection.postprocessing import PostProcessingPipeline, MergerProcessor

# 2. 建立配置
config = DetectionConfig(
    primary_model_path='models/yolov8_face.pt',
    conf_threshold=0.25,
    iou_threshold=0.45,
    device='cuda',
    batch_size=4,
    enable_preprocessing=True,
    enable_postprocessing=True
)

# 3. 初始化偵測器
detector = Detector(config)

# 4. 執行偵測
import numpy as np
image = np.array(...)  # 載入影像
detections = detector.detect(image, face_id=0)

# 5. 處理結果
for detection in detections:
    print(f"檢測到 {detection.label}")
    print(f"位置: {detection.xyxy}")
    print(f"信心度: {detection.confidence:.3f}")
    print(f"面積: {detection.area}")

# 6. 清理資源
detector.cleanup()
```

### 自定義偵測策略
```python
from detection.strategies import DetectionStrategy, StrategyFactory
from detection.core.data_structures import DetectionBox

class CustomStrategy(DetectionStrategy):
    def detect(self, image, model, **kwargs):
        # 實作自定義偵測邏輯
        results = model.predict(image)
        
        # 轉換結果格式
        detections = []
        for result in results:
            detection = DetectionBox(
                xyxy=result.bbox,
                confidence=result.conf,
                label=result.label,
                class_id=result.class_id
            )
            detections.append(detection)
        
        return detections

# 註冊自定義策略
StrategyFactory.register_strategy("custom", CustomStrategy)

# 使用自定義策略
config = DetectionConfig(detection_strategy="custom")
detector = Detector(config)
```

### 構建後處理管線
```python
from detection.postprocessing import (
    PostProcessingPipeline, 
    MergerProcessor, 
    LargeBoxFilter,
    CenterRegionFilter
)

# 建立後處理管線
pipeline = PostProcessingPipeline()

# 添加後處理步驟
pipeline.add_processor(MergerProcessor(iou_threshold=0.3))
pipeline.add_processor(LargeBoxFilter(max_area_ratio=0.8))
pipeline.add_processor(CenterRegionFilter(center_weight=1.5))

# 在配置中使用自定義管線
config = DetectionConfig(
    postprocessing_pipeline=pipeline,
    enable_postprocessing=True
)
```

### 批次處理
```python
# 批次處理多張影像
images = [image1, image2, image3, image4]
batch_results = detector.detect_batch(images)

for i, detections in enumerate(batch_results):
    print(f"影像 {i+1} 檢測到 {len(detections)} 個物件")
```

### 效能監控整合
```python
from utils_re.monitoring import PerformanceMonitor

with PerformanceMonitor("detection_process") as monitor:
    # 執行偵測
    detections = detector.detect(image)
    
    # 取得效能統計
    stats = monitor.get_stats()
    print(f"偵測時間: {stats.execution_time:.3f}s")
    print(f"GPU使用率: {stats.gpu_utilization:.1f}%")
    print(f"記憶體使用: {stats.memory_usage_mb:.1f}MB")
```

## 支援的偵測模型

### YOLO系列模型
- **YOLOv8**: 最新的YOLO架構，平衡速度和精度
- **YOLOv9**: 最新版本，進一步提升檢測精度
- **YOLOv5**: 成熟穩定的版本，廣泛支援
- **自定義模型**: 支援用戶訓練的自定義YOLO模型

### 預訓練模型類別
- **人臉檢測 (Face Detection)**: 高精度人臉識別模型
- **車牌檢測 (License Plate Detection)**: 專門的車牌識別模型
- **多類別檢測 (Multi-class Detection)**: 支援多種物件類別
- **隱私物件檢測**: 專門針對隱私保護的綜合檢測模型

## 效能最佳化特性

### GPU加速支援
- **CUDA加速**: 支援NVIDIA GPU的CUDA加速
- **批次處理**: 多影像同時處理，最大化GPU利用率
- **記憶體管理**: 智慧型GPU記憶體分配和回收
- **動態模型載入**: 根據GPU記憶體動態調整模型精度

### 處理最佳化
- **多尺度檢測**: 支援不同解析度的自適應檢測
- **NMS最佳化**: 高效的非極大值抑制演算法
- **預處理加速**: 影像預處理的向量化運算
- **結果快取**: 重複影像的檢測結果快取機制

### 記憶體最佳化
- **串流處理**: 大影像的分塊處理，減少記憶體佔用
- **物件池**: 檢測結果物件的重用，減少GC壓力
- **延遲載入**: 模型的按需載入機制
- **記憶體監控**: 即時記憶體使用監控和告警

## 與其他模組的整合

### core模組整合
- **座標轉換**: 使用core.coordinate進行精確的座標映射
- **影像處理**: 整合core.interpolation進行高品質影像縮放
- **投影變換**: 支援全景圖和立方體格式的檢測結果轉換

### processing_re整合
- **管線支援**: 作為processing_re管線中的DetectionStep
- **批次處理**: 支援processing_re的批次處理需求
- **錯誤處理**: 統一的錯誤處理和恢復機制

### utils_re整合
- **資源管理**: 使用utils_re.resource進行GPU和記憶體管理
- **效能監控**: 整合utils_re.monitoring進行詳細效能分析
- **並行處理**: 支援utils_re.parallel的多執行緒檢測

## 品質保證與測試

### 單元測試覆蓋
- **檢測精度測試**: 驗證不同模型的檢測精度
- **效能基準測試**: 各種配置下的效能基準
- **記憶體洩漏測試**: 長時間運行的記憶體穩定性
- **GPU相容性測試**: 不同GPU環境的相容性驗證

### 回歸測試
- **模型升級測試**: 確保新模型不會降低檢測品質
- **API相容性測試**: 確保向後相容性
- **效能回歸測試**: 防止效能下降
- **整合測試**: 與其他模組的整合穩定性

## 配置選項詳解

### 基本配置
- **model_path**: 模型檔案路徑，支援本地和遠端URL
- **conf_threshold**: 信心度閾值，過濾低信心度檢測
- **iou_threshold**: IoU閾值，用於NMS後處理
- **device**: 運算裝置選擇('cpu', 'cuda', 'auto')

### 進階配置
- **batch_size**: 批次處理大小，影響GPU記憶體使用
- **max_det**: 最大檢測數量限制
- **classes**: 指定檢測的類別ID列表
- **agnostic_nms**: 是否使用類別無關的NMS

### 最佳化配置
- **half_precision**: 是否使用半精度浮點運算
- **cache_models**: 是否快取載入的模型
- **enable_tensorrt**: 是否啟用TensorRT最佳化
- **enable_onnx**: 是否使用ONNX運行時最佳化

## 故障排除指南

### 常見問題
1. **CUDA記憶體不足**: 減少batch_size或使用model分片
2. **模型載入失敗**: 檢查模型路徑和格式相容性
3. **檢測結果異常**: 調整conf_threshold和iou_threshold
4. **效能問題**: 啟用GPU加速和批次處理

### 除錯工具
- **verbose模式**: 詳細的執行日誌輸出
- **可視化工具**: 檢測結果的即時可視化
- **統計收集器**: 詳細的效能和精度統計
- **記憶體分析器**: GPU和CPU記憶體使用分析
"""

# --- 套件元資料 ---
__package__ = "detection"
__version__ = "2.0.1"
__author__ = "AI 部門 - 全景處理團隊"
__docformat__ = "restructuredtext"

# --- 公開 API 定義 ---
__all__ = [
    # 核心元件
    "Detector",
    "DetectionConfig", 
    "DetectionBox",
    
    # 策略模式相關
    "DetectionStrategy",
    "StandardStrategy",
    "RotationStrategy", 
    "SkipStrategy",
    "StrategyFactory",
    
    # 後處理管線相關
    "PostProcessor",
    "PostProcessingPipeline",
    "MergerProcessor",
    "LargeBoxFilter",
    "CenterRegionFilter",
    
    # 模型管理
    "ModelManager",
    "ModelLoader",
    
    # 工具程式
    "BlurProcessor",
    "StatisticsCollector",
]


# --- 確保套件正確初始化 ---
def _ensure_package() -> bool:
    """確保此模組被Python解譯器正確識別為一個套件。"""
    return True

_package_initialized = _ensure_package()


# --- 延遲載入的偵測系統元件匯入與匯出 ---
# 使用延遲載入來避免長時間的 import 過程

# 先嘗試載入最基本的核心元件
_core_loaded = False
_models_loaded = False
_postprocessing_loaded = False
_strategies_loaded = False
_utils_loaded = False

def _load_core():
    """延遲載入核心元件"""
    global _core_loaded
    if not _core_loaded:
        try:
            from .core.config import DetectionConfig
            from .core.data_structures import DetectionBox
            from .core.detector import Detector

            # 將元件添加到全域命名空間
            globals()['DetectionConfig'] = DetectionConfig
            globals()['DetectionBox'] = DetectionBox
            globals()['Detector'] = Detector

            _core_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"核心元件載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_models():
    """延遲載入模型管理元件"""
    global _models_loaded
    if not _models_loaded:
        try:
            from .models import ModelLoader, ModelManager

            globals()['ModelLoader'] = ModelLoader
            globals()['ModelManager'] = ModelManager

            _models_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"模型管理元件載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_postprocessing():
    """延遲載入後處理管線元件"""
    global _postprocessing_loaded
    if not _postprocessing_loaded:
        try:
            from .postprocessing import (
                CenterRegionFilter,
                LargeBoxFilter,
                MergerProcessor,
                PostProcessingPipeline,
                PostProcessor
            )

            globals()['CenterRegionFilter'] = CenterRegionFilter
            globals()['LargeBoxFilter'] = LargeBoxFilter
            globals()['MergerProcessor'] = MergerProcessor
            globals()['PostProcessingPipeline'] = PostProcessingPipeline
            globals()['PostProcessor'] = PostProcessor

            _postprocessing_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"後處理管線元件載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_strategies():
    """延遲載入偵測策略元件"""
    global _strategies_loaded
    if not _strategies_loaded:
        try:
            from .strategies import (
                DetectionStrategy,
                RotationStrategy,
                SkipStrategy,
                StandardStrategy,
                StrategyFactory
            )

            globals()['DetectionStrategy'] = DetectionStrategy
            globals()['RotationStrategy'] = RotationStrategy
            globals()['SkipStrategy'] = SkipStrategy
            globals()['StandardStrategy'] = StandardStrategy
            globals()['StrategyFactory'] = StrategyFactory

            _strategies_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"偵測策略元件載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_utils():
    """延遲載入工具程式元件"""
    global _utils_loaded
    if not _utils_loaded:
        try:
            from .utils import BlurProcessor, StatisticsCollector

            globals()['BlurProcessor'] = BlurProcessor
            globals()['StatisticsCollector'] = StatisticsCollector

            _utils_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"工具程式元件載入失敗: {e}", ImportWarning)
            return False
    return True

# 不在模組載入時立即載入任何元件，完全使用延遲載入
__all__ = [
    # 核心元件
    "Detector",
    "DetectionConfig",
    "DetectionBox",

    # 策略模式相關
    "DetectionStrategy",
    "StandardStrategy",
    "RotationStrategy",
    "SkipStrategy",
    "StrategyFactory",

    # 後處理管線相關
    "PostProcessor",
    "PostProcessingPipeline",
    "MergerProcessor",
    "LargeBoxFilter",
    "CenterRegionFilter",

    # 模型管理
    "ModelManager",
    "ModelLoader",

    # 工具程式
    "BlurProcessor",
    "StatisticsCollector",
]

# 定義 __getattr__ 來實現延遲載入
def __getattr__(name: str):
    """實現延遲載入的 __getattr__ 方法"""
    # 核心元件
    if name in ["Detector", "DetectionConfig", "DetectionBox"]:
        if _load_core():
            return globals().get(name)
        raise AttributeError(f"無法載入核心元件 '{name}'")

    # 策略相關元件
    elif name in ["DetectionStrategy", "StandardStrategy", "RotationStrategy", "SkipStrategy", "StrategyFactory"]:
        if _load_strategies():
            return globals().get(name)
        raise AttributeError(f"無法載入策略元件 '{name}'")

    # 後處理相關元件
    elif name in ["PostProcessor", "PostProcessingPipeline", "MergerProcessor", "LargeBoxFilter", "CenterRegionFilter"]:
        if _load_postprocessing():
            return globals().get(name)
        raise AttributeError(f"無法載入後處理元件 '{name}'")

    # 模型管理相關元件
    elif name in ["ModelManager", "ModelLoader"]:
        if _load_models():
            return globals().get(name)
        raise AttributeError(f"無法載入模型管理元件 '{name}'")

    # 工具程式相關元件
    elif name in ["BlurProcessor", "StatisticsCollector"]:
        if _load_utils():
            return globals().get(name)
        raise AttributeError(f"無法載入工具程式元件 '{name}'")

    # 如果不是已知的延遲載入元件，拋出標準錯誤
    raise AttributeError(f"模組 '{__name__}' 沒有屬性 '{name}'")


# --- 模組輔助函式 ---

def get_module_info() -> dict[str, str]:
    """
    取得關於偵測模組的詳細架構資訊。
    
    :return: 包含模組元資料的字典。
    """
    return {
        "version": __version__,
        "author": __author__,
        "description": "現代化AI物件偵測系統",
        "architecture": "模組化 (核心、策略、後處理、模型、工具)",
        "design_patterns": "策略模式、管線模式、工廠模式、觀察者模式",
        "key_features": "情境化偵測策略、可組合後處理管線、GPU加速、批次處理",
        "supported_models": "YOLOv8, YOLOv9, YOLOv5, 自定義YOLO模型",
        "detection_types": "人臉檢測、車牌檢測、多類別物件檢測",
        "refactored_from": "762行單體類別重構為模組化架構",
        "maintainability": "高 (High) - 清晰的模組分離",
        "extensibility": "高 (High) - 策略和管線模式支援",
        "performance": "GPU加速、批次處理、記憶體最佳化",
        "integration_modules": "core, processing_re, utils_re",
    }


def check_dependencies() -> dict[str, bool]:
    """
    檢查偵測模組相依性的可用性。
    
    :return: 包含相依性檢查結果的字典。
    """
    dependencies = {}
    
    # 檢查深度學習框架
    try:
        import torch
        dependencies["pytorch"] = True
        dependencies["pytorch_version"] = torch.__version__
        dependencies["cuda_available"] = torch.cuda.is_available()
        if dependencies["cuda_available"]:
            dependencies["cuda_version"] = torch.version.cuda
            dependencies["gpu_count"] = torch.cuda.device_count()
        else:
            dependencies["cuda_version"] = "N/A"
            dependencies["gpu_count"] = 0
    except ImportError:
        dependencies["pytorch"] = False
        dependencies["pytorch_version"] = "N/A"
        dependencies["cuda_available"] = False
        dependencies["cuda_version"] = "N/A"
        dependencies["gpu_count"] = 0
    
    # 檢查YOLO框架
    try:
        import ultralytics
        dependencies["ultralytics"] = True
        dependencies["ultralytics_version"] = ultralytics.__version__
    except ImportError:
        dependencies["ultralytics"] = False
        dependencies["ultralytics_version"] = "N/A"
    
    # 檢查影像處理庫
    try:
        import cv2
        dependencies["opencv"] = True
        dependencies["opencv_version"] = cv2.__version__
    except ImportError:
        dependencies["opencv"] = False
        dependencies["opencv_version"] = "N/A"
    
    # 檢查數值計算庫
    try:
        import numpy
        dependencies["numpy"] = True
        dependencies["numpy_version"] = numpy.__version__
    except ImportError:
        dependencies["numpy"] = False
        dependencies["numpy_version"] = "N/A"
    
    return dependencies


def get_available_strategies() -> list[str]:
    """
    取得當前可用的檢測策略列表。
    
    :return: 可用策略的名稱列表。
    """
    available_strategies = []
    
    # 檢查各個策略是否可用
    try:
        from .strategies import StrategyFactory
        available_strategies = StrategyFactory.get_registered_strategies()
    except ImportError:
        # 手動檢查基本策略
        strategy_modules = [
            ("StandardStrategy", ".strategies"),
            ("RotationStrategy", ".strategies"),
            ("SkipStrategy", ".strategies")
        ]
        
        for strategy_name, module_path in strategy_modules:
            try:
                __import__(f"detection{module_path}")
                available_strategies.append(strategy_name)
            except ImportError:
                pass
    
    return available_strategies


def get_available_models() -> dict[str, list[str]]:
    """
    取得當前支援的模型類型和版本。
    
    :return: 包含模型資訊的字典。
    """
    models = {
        "yolo_versions": ["YOLOv8", "YOLOv9", "YOLOv5"],
        "detection_types": ["face", "license_plate", "multi_object"],
        "model_formats": [".pt", ".onnx", ".engine"],
        "precision_modes": ["fp32", "fp16", "int8"]
    }
    
    # 檢查預設模型是否存在
    import os
    model_dir = "models"
    if os.path.exists(model_dir):
        available_models = []
        for file in os.listdir(model_dir):
            if file.endswith(('.pt', '.onnx', '.engine')):
                available_models.append(file)
        models["available_files"] = available_models
    else:
        models["available_files"] = []
    
    return models


def validate_environment() -> bool:
    """
    驗證偵測模組執行環境是否正確設置。
    
    :return: 如果環境驗證通過則回傳 True。
    """
    dependencies = check_dependencies()
    
    # 檢查關鍵相依性
    critical_deps = ["pytorch", "ultralytics", "opencv", "numpy"]
    
    for dep in critical_deps:
        if not dependencies.get(dep, False):
            import warnings
            warnings.warn(
                f"關鍵相依性 '{dep}' 不可用，detection 模組功能將受限",
                ImportWarning
            )
            return False
    
    # 檢查GPU支援（非必須但建議）
    if not dependencies.get("cuda_available", False):
        import warnings
        warnings.warn(
            "CUDA GPU 不可用，檢測速度將受限於CPU處理",
            ResourceWarning
        )
    
    return True


def get_performance_recommendations() -> dict[str, str]:
    """
    基於當前環境提供效能最佳化建議。
    
    :return: 包含最佳化建議的字典。
    """
    dependencies = check_dependencies()
    recommendations = {}
    
    # GPU相關建議
    if dependencies.get("cuda_available", False):
        gpu_count = dependencies.get("gpu_count", 0)
        if gpu_count > 1:
            recommendations["gpu"] = f"檢測到 {gpu_count} 個GPU，建議啟用多GPU並行處理"
        else:
            recommendations["gpu"] = "單GPU環境，建議使用批次處理提升效率"
    else:
        recommendations["gpu"] = "未檢測到GPU，建議使用較小的模型以提升CPU處理速度"
    
    # 記憶體相關建議
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 8:
            recommendations["memory"] = "記憶體較少，建議減少batch_size和使用較小模型"
        elif memory_gb >= 16:
            recommendations["memory"] = "記憶體充足，可使用較大batch_size和高精度模型"
        else:
            recommendations["memory"] = "記憶體適中，建議平衡batch_size和模型大小"
    except ImportError:
        recommendations["memory"] = "無法檢測記憶體狀況，請根據系統配置調整參數"
    
    # 模型選擇建議
    if dependencies.get("cuda_available", False):
        recommendations["model"] = "GPU環境建議使用YOLOv8或YOLOv9獲得最佳精度"
    else:
        recommendations["model"] = "CPU環境建議使用YOLOv5n或YOLOv8n獲得更快速度"
    
    return recommendations


def get_detection_summary() -> dict[str, any]:
    """
    取得偵測模組的完整摘要資訊。
    
    :return: 包含偵測模組摘要的字典。
    """
    module_info = get_module_info()
    dependencies = check_dependencies()
    available_strategies = get_available_strategies()
    available_models = get_available_models()
    performance_recommendations = get_performance_recommendations()
    
    return {
        "module_info": module_info,
        "dependencies": dependencies,
        "available_strategies": available_strategies,
        "available_models": available_models,
        "performance_recommendations": performance_recommendations,
        "environment_valid": validate_environment(),
        "initialization_status": _package_initialized
    }


# 在模組載入時執行一次環境驗證
_environment_validated = validate_environment()