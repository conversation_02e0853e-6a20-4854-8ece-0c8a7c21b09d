#!/usr/bin/env python3
"""
智能並行協調器模組
Intelligent Parallel Coordinator <PERSON><PERSON><PERSON>

根據系統資源和任務特性智能優化處理策略，提供動態並行調度和資源管理。
Intelligently optimizes processing strategies based on system resources and task characteristics, providing dynamic parallel scheduling and resource management.

Author: Claude Code Assistant
Date: 2025-01-19
"""

# 1. 標準庫
import logging
import os
import threading
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any

# 2. 第三方庫
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

# 3. 本地模組
try:
    from .memory_pool import get_global_memory_pool
    HAS_MEMORY_POOL = True
except ImportError:
    HAS_MEMORY_POOL = False


class ProcessingStrategy(Enum):
    """處理策略枚舉"""
    CONSERVATIVE = "conservative"  # 保守策略，最小資源使用
    BALANCED = "balanced"         # 平衡策略，性能與資源的平衡
    AGGRESSIVE = "aggressive"     # 激進策略，最大性能
    SMART = "smart"              # 智能策略，AI驅動優化
    STREAMING = "streaming"       # 流式策略，大檔案處理


class MemoryStrategy(Enum):
    """記憶體策略枚舉"""
    MINIMAL = "minimal"           # 最小記憶體使用
    STANDARD = "standard"         # 標準記憶體使用
    AGGRESSIVE = "aggressive"     # 激進記憶體使用
    ADAPTIVE = "adaptive"         # 自適應記憶體使用


@dataclass
class SystemResources:
    """系統資源信息"""
    cpu_count: int
    cpu_usage: float
    memory_total_gb: float
    memory_available_gb: float
    memory_usage_percent: float
    gpu_available: bool
    gpu_memory_gb: float = 0.0


@dataclass
class ProcessingConfig:
    """處理配置"""
    strategy: ProcessingStrategy
    memory_strategy: MemoryStrategy
    max_workers: int
    batch_size: int
    use_gpu: bool
    memory_limit_gb: float
    enable_caching: bool = True
    chunk_size: int = 8


class ParallelCoordinator:
    """
    智能並行協調器。
    
    特性:
    - 動態資源監控
    - 智能策略選擇
    - 自適應並行調度
    - 性能監控和調優
    """
    
    def __init__(self):
        """初始化並行協調器"""
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self._resource_cache_time = 0
        self._cached_resources: SystemResources | None = None
        self._cache_timeout = 30  # 30秒緩存
        
        # 性能統計
        self._strategy_performance: dict[str, list[float]] = {}
        
    def get_system_resources(self, force_refresh: bool = False) -> SystemResources:
        """
        獲取系統資源信息
        
        Args:
            force_refresh: 是否強制刷新緩存
            
        Returns:
            系統資源信息
        """
        current_time = time.time()
        
        if (not force_refresh and 
            self._cached_resources and 
            current_time - self._resource_cache_time < self._cache_timeout):
            return self._cached_resources
        
        with self._lock:
            # 雙重檢查
            if (not force_refresh and 
                self._cached_resources and 
                current_time - self._resource_cache_time < self._cache_timeout):
                return self._cached_resources
            
            resources = SystemResources(
                cpu_count=os.cpu_count() or 4,
                cpu_usage=0.0,
                memory_total_gb=8.0,  # 默認值
                memory_available_gb=4.0,
                memory_usage_percent=50.0,
                gpu_available=False
            )
            
            if HAS_PSUTIL:
                # CPU信息
                resources.cpu_usage = psutil.cpu_percent(interval=1)
                
                # 記憶體信息
                memory = psutil.virtual_memory()
                resources.memory_total_gb = memory.total / (1024 ** 3)
                resources.memory_available_gb = memory.available / (1024 ** 3)
                resources.memory_usage_percent = memory.percent
                
                # GPU檢測（簡化版）
                try:
                    import subprocess
                    result = subprocess.run(['nvidia-smi'], 
                                          capture_output=True, 
                                          timeout=5)
                    resources.gpu_available = result.returncode == 0
                except:
                    resources.gpu_available = False
            
            self._cached_resources = resources
            self._resource_cache_time = current_time
            
        return resources
    
    def optimize_processing_strategy(
        self, 
        task_count: int,
        estimated_memory_per_task_mb: float = 100,
        task_complexity: str = "medium"
    ) -> ProcessingConfig:
        """
        根據系統資源和任務特性優化處理策略
        
        Args:
            task_count: 任務數量
            estimated_memory_per_task_mb: 每個任務預估記憶體使用（MB）
            task_complexity: 任務複雜度 ("low", "medium", "high")
            
        Returns:
            優化的處理配置
        """
        resources = self.get_system_resources()
        
        # 基於系統資源選擇策略
        if resources.memory_usage_percent > 85:
            strategy = ProcessingStrategy.CONSERVATIVE
            memory_strategy = MemoryStrategy.MINIMAL
        elif resources.memory_usage_percent > 70:
            strategy = ProcessingStrategy.BALANCED
            memory_strategy = MemoryStrategy.STANDARD
        elif resources.cpu_usage > 80:
            strategy = ProcessingStrategy.BALANCED
            memory_strategy = MemoryStrategy.STANDARD
        else:
            strategy = ProcessingStrategy.AGGRESSIVE
            memory_strategy = MemoryStrategy.AGGRESSIVE
        
        # 計算並行參數
        config = self._calculate_parallel_params(
            resources, task_count, estimated_memory_per_task_mb, 
            task_complexity, strategy, memory_strategy
        )
        
        self.logger.info(
            f"優化策略: {strategy.value}, "
            f"並行度: {config.max_workers}, "
            f"批次大小: {config.batch_size}, "
            f"記憶體限制: {config.memory_limit_gb:.1f}GB"
        )
        
        return config
    
    def _calculate_parallel_params(
        self,
        resources: SystemResources,
        task_count: int,
        memory_per_task_mb: float,
        complexity: str,
        strategy: ProcessingStrategy,
        memory_strategy: MemoryStrategy
    ) -> ProcessingConfig:
        """計算並行參數"""
        
        # 基礎並行度計算
        base_workers = resources.cpu_count
        
        # 根據策略調整
        if strategy == ProcessingStrategy.CONSERVATIVE:
            max_workers = max(1, base_workers // 2)
            batch_size = min(4, task_count)
        elif strategy == ProcessingStrategy.BALANCED:
            max_workers = max(2, int(base_workers * 0.75))
            batch_size = min(8, task_count)
        elif strategy == ProcessingStrategy.AGGRESSIVE:
            max_workers = min(base_workers * 2, task_count, 16)
            batch_size = min(16, task_count)
        else:  # SMART 或 STREAMING
            max_workers = self._smart_worker_calculation(resources, complexity)
            batch_size = self._smart_batch_calculation(task_count, memory_per_task_mb)
        
        # 記憶體限制計算
        if memory_strategy == MemoryStrategy.MINIMAL:
            memory_limit = resources.memory_available_gb * 0.3
        elif memory_strategy == MemoryStrategy.STANDARD:
            memory_limit = resources.memory_available_gb * 0.6
        elif memory_strategy == MemoryStrategy.AGGRESSIVE:
            memory_limit = resources.memory_available_gb * 0.8
        else:  # ADAPTIVE 策略
            memory_limit = self._adaptive_memory_limit(resources, memory_per_task_mb, max_workers)
        
        # GPU使用決策
        use_gpu = (resources.gpu_available and 
                  task_count > 10 and 
                  complexity in ["medium", "high"])
        
        return ProcessingConfig(
            strategy=strategy,
            memory_strategy=memory_strategy,
            max_workers=max_workers,
            batch_size=batch_size,
            use_gpu=use_gpu,
            memory_limit_gb=memory_limit,
            chunk_size=max(1, batch_size // 2)
        )
    
    def _smart_worker_calculation(self, resources: SystemResources, complexity: str) -> int:
        """智能工作線程數計算"""
        base = resources.cpu_count
        
        # 根據複雜度調整
        if complexity == "low":
            return min(base * 2, 12)
        elif complexity == "high":
            return max(1, base // 2)
        else:  # 中等複雜度
            return base
    
    def _smart_batch_calculation(self, task_count: int, memory_per_task_mb: float) -> int:
        """智能批次大小計算"""
        # 基於記憶體使用預估
        if memory_per_task_mb > 500:  # 大記憶體任務
            return min(4, task_count)
        elif memory_per_task_mb > 100:  # 中等記憶體任務
            return min(8, task_count)
        else:  # 小記憶體任務
            return min(16, task_count)
    
    def _adaptive_memory_limit(
        self, 
        resources: SystemResources, 
        memory_per_task_mb: float, 
        max_workers: int
    ) -> float:
        """自適應記憶體限制計算"""
        # 預估總記憶體需求
        estimated_usage_gb = (memory_per_task_mb * max_workers) / 1024
        
        # 留出安全邊際
        safe_limit = resources.memory_available_gb * 0.7
        
        return min(safe_limit, estimated_usage_gb * 1.5)
    
    def record_strategy_performance(
        self, 
        strategy: ProcessingStrategy, 
        execution_time: float,
        success_rate: float
    ):
        """記錄策略性能"""
        strategy_key = strategy.value
        performance_score = success_rate / max(execution_time, 0.1)  # 避免除零
        
        if strategy_key not in self._strategy_performance:
            self._strategy_performance[strategy_key] = []
        
        self._strategy_performance[strategy_key].append(performance_score)
        
        # 限制歷史記錄長度
        if len(self._strategy_performance[strategy_key]) > 50:
            self._strategy_performance[strategy_key].pop(0)
    
    def get_strategy_recommendations(self) -> dict[str, Any]:
        """獲取策略建議"""
        recommendations = {}
        
        for strategy, scores in self._strategy_performance.items():
            if scores:
                avg_score = sum(scores) / len(scores)
                recommendations[strategy] = {
                    "average_performance": avg_score,
                    "sample_count": len(scores),
                    "trend": "improving" if len(scores) > 1 and scores[-1] > avg_score else "stable"
                }
        
        # 找出最佳策略
        if recommendations:
            best_strategy = max(recommendations.keys(), 
                              key=lambda k: recommendations[k]["average_performance"])
            recommendations["recommended"] = best_strategy
        
        return recommendations
    
    def monitor_system_health(self) -> dict[str, Any]:
        """監控系統健康狀況"""
        resources = self.get_system_resources(force_refresh=True)
        
        health_status = {
            "overall": "good",
            "cpu_load": "normal",
            "memory_usage": "normal", 
            "warnings": []
        }
        
        # CPU檢查
        if resources.cpu_usage > 90:
            health_status["cpu_load"] = "high"
            health_status["warnings"].append("CPU使用率過高")
            health_status["overall"] = "warning"
        elif resources.cpu_usage > 70:
            health_status["cpu_load"] = "elevated"
        
        # 記憶體檢查
        if resources.memory_usage_percent > 90:
            health_status["memory_usage"] = "critical"
            health_status["warnings"].append("記憶體使用率危險")
            health_status["overall"] = "critical"
        elif resources.memory_usage_percent > 80:
            health_status["memory_usage"] = "high"
            health_status["warnings"].append("記憶體使用率偏高")
            if health_status["overall"] == "good":
                health_status["overall"] = "warning"
        
        health_status["resources"] = resources
        return health_status


def create_parallel_coordinator() -> ParallelCoordinator:
    """
    創建並行協調器的工廠函數
    
    Returns:
        ParallelCoordinator實例
    """
    return ParallelCoordinator()