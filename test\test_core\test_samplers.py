"""
Tests for core.samplers module
"""

from unittest.mock import MagicMock, patch

import numpy as np
import pytest

# Mock cv2 and warnings
with patch("cv2.resize") as mock_cv2_resize:
    with patch("warnings.warn") as mock_warn:
        mock_cv2_resize.return_value = np.random.randint(
            0, 255, (100, 100, 3), dtype=np.uint8
        )
        from core.samplers import (EnhancedCubeFaceSampler,
                                   EnhancedEquirectSampler,
                                   create_cube_face_sampler,
                                   create_equirect_sampler)


class TestEnhancedEquirectSampler:
    """Test EnhancedEquirectSampler class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.sampler = EnhancedEquirectSampler()
        self.test_image = np.random.randint(0, 255, (512, 1024, 3), dtype=np.uint8)
        self.target_size = (256, 256)

    def test_init(self):
        """Test EnhancedEquirectSampler initialization"""
        assert isinstance(self.sampler, EnhancedEquirectSampler)
        assert hasattr(self.sampler, "interpolation_method")
        assert hasattr(self.sampler, "boundary_mode")

    def test_init_with_parameters(self):
        """Test initialization with custom parameters"""
        from core.interpolation import InterpolationMethod

        sampler = EnhancedEquirectSampler(
            interpolation_method=InterpolationMethod.CUBIC, boundary_mode="reflect"
        )
        assert sampler.interpolation_method == InterpolationMethod.CUBIC
        assert sampler.boundary_mode == "reflect"

    @patch("cv2.resize")
    def test_sample_basic(self, mock_resize):
        """Test basic sampling functionality"""
        mock_resize.return_value = np.random.randint(
            0, 255, self.target_size + (3,), dtype=np.uint8
        )

        result = self.sampler.sample(self.test_image, self.target_size)

        assert result.shape == self.target_size + (3,)
        assert result.dtype == np.uint8

    def test_sample_coordinates(self):
        """Test sampling with specific coordinates"""
        # Test coordinate-based sampling
        u_coords = np.array([0.0, 0.5, 1.0])
        v_coords = np.array([0.0, 0.5, 1.0])

        sampled_values = self.sampler.sample_at_coordinates(
            self.test_image, u_coords, v_coords
        )

        assert len(sampled_values) == len(u_coords)

    def test_boundary_handling(self):
        """Test boundary condition handling"""
        # Test with coordinates outside [0,1] range
        u_coords = np.array([-0.1, 0.5, 1.1])
        v_coords = np.array([-0.1, 0.5, 1.1])

        # Should handle gracefully with boundary mode
        try:
            sampled_values = self.sampler.sample_at_coordinates(
                self.test_image, u_coords, v_coords
            )
            assert len(sampled_values) == len(u_coords)
        except (ValueError, IndexError):
            # Acceptable if boundary handling raises errors for out-of-bounds
            pass

    def test_different_interpolation_methods(self):
        """Test different interpolation methods"""
        from core.interpolation import InterpolationMethod

        methods = [
            InterpolationMethod.NEAREST,
            InterpolationMethod.LINEAR,
            InterpolationMethod.CUBIC,
        ]

        for method in methods:
            sampler = EnhancedEquirectSampler(interpolation_method=method)

            with patch("cv2.resize") as mock_resize:
                mock_resize.return_value = np.random.randint(
                    0, 255, self.target_size + (3,), dtype=np.uint8
                )
                result = sampler.sample(self.test_image, self.target_size)
                assert result.shape == self.target_size + (3,)

    def test_grayscale_sampling(self):
        """Test sampling with grayscale images"""
        gray_image = np.random.randint(0, 255, (512, 1024), dtype=np.uint8)

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, self.target_size, dtype=np.uint8
            )
            result = self.sampler.sample(gray_image, self.target_size)
            assert result.shape == self.target_size


class TestEnhancedCubeFaceSampler:
    """Test EnhancedCubeFaceSampler class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.sampler = EnhancedCubeFaceSampler()
        self.test_cube_face = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        self.target_size = (256, 256)

    def test_init(self):
        """Test EnhancedCubeFaceSampler initialization"""
        assert isinstance(self.sampler, EnhancedCubeFaceSampler)
        assert hasattr(self.sampler, "interpolation_method")
        assert hasattr(self.sampler, "boundary_mode")

    @patch("cv2.resize")
    def test_sample_basic(self, mock_resize):
        """Test basic cube face sampling"""
        mock_resize.return_value = np.random.randint(
            0, 255, self.target_size + (3,), dtype=np.uint8
        )

        result = self.sampler.sample(self.test_cube_face, self.target_size)

        assert result.shape == self.target_size + (3,)
        assert result.dtype == np.uint8

    def test_sample_multiple_faces(self):
        """Test sampling from multiple cube faces"""
        cube_faces = {
            "front": np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
            "back": np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
            "left": np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
            "right": np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
            "up": np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
            "down": np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
        }

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, self.target_size + (3,), dtype=np.uint8
            )

            for face_name, face_image in cube_faces.items():
                result = self.sampler.sample(face_image, self.target_size)
                assert result.shape == self.target_size + (3,)

    def test_face_coordinate_mapping(self):
        """Test coordinate mapping for cube faces"""
        # Test UV coordinates within face
        u_coords = np.array([0.0, 0.5, 1.0])
        v_coords = np.array([0.0, 0.5, 1.0])

        sampled_values = self.sampler.sample_at_coordinates(
            self.test_cube_face, u_coords, v_coords
        )

        assert len(sampled_values) == len(u_coords)

    def test_edge_sampling(self):
        """Test sampling at cube face edges"""
        # Test coordinates at edges (important for cube mapping)
        edge_coords = [
            (0.0, 0.5),  # Left edge
            (1.0, 0.5),  # Right edge
            (0.5, 0.0),  # Top edge
            (0.5, 1.0),  # Bottom edge
        ]

        for u, v in edge_coords:
            sampled_value = self.sampler.sample_at_coordinates(
                self.test_cube_face, np.array([u]), np.array([v])
            )
            assert len(sampled_value) == 1


class TestFactoryFunctions:
    """Test factory functions for creating samplers"""

    def test_create_equirect_sampler(self):
        """Test equirectangular sampler factory function"""
        sampler = create_equirect_sampler()
        assert isinstance(sampler, EnhancedEquirectSampler)

    def test_create_equirect_sampler_with_params(self):
        """Test equirectangular sampler factory with parameters"""
        from core.interpolation import InterpolationMethod

        sampler = create_equirect_sampler(
            interpolation_method=InterpolationMethod.CUBIC, boundary_mode="reflect"
        )
        assert isinstance(sampler, EnhancedEquirectSampler)
        assert sampler.interpolation_method == InterpolationMethod.CUBIC

    def test_create_cube_face_sampler(self):
        """Test cube face sampler factory function"""
        sampler = create_cube_face_sampler()
        assert isinstance(sampler, EnhancedCubeFaceSampler)

    def test_create_cube_face_sampler_with_params(self):
        """Test cube face sampler factory with parameters"""
        from core.interpolation import InterpolationMethod

        sampler = create_cube_face_sampler(
            interpolation_method=InterpolationMethod.LANCZOS, boundary_mode="clamp"
        )
        assert isinstance(sampler, EnhancedCubeFaceSampler)
        assert sampler.interpolation_method == InterpolationMethod.LANCZOS


class TestBackwardCompatibility:
    """Test backward compatibility features"""

    def test_opencv_fallback(self):
        """Test OpenCV backend fallback"""
        # Test that samplers fall back to OpenCV when advanced interpolation fails
        sampler = EnhancedEquirectSampler()
        test_image = np.random.randint(0, 255, (100, 200, 3), dtype=np.uint8)

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (50, 50, 3), dtype=np.uint8
            )

            # Should not raise exceptions even if advanced interpolation fails
            result = sampler.sample(test_image, (50, 50))
            assert result.shape == (50, 50, 3)

    def test_legacy_interface_compatibility(self):
        """Test compatibility with legacy sampler interfaces"""
        # Test that new samplers work with legacy code patterns
        sampler = EnhancedEquirectSampler()
        test_image = np.random.randint(0, 255, (100, 200, 3), dtype=np.uint8)

        # Test basic sampling (should work like old sampler)
        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (50, 50, 3), dtype=np.uint8
            )
            result = sampler.sample(test_image, (50, 50))
            assert result.shape == (50, 50, 3)


class TestErrorHandling:
    """Test error handling and edge cases"""

    def test_invalid_image_input(self):
        """Test handling of invalid image inputs"""
        sampler = EnhancedEquirectSampler()

        # Empty image
        with pytest.raises((ValueError, IndexError)):
            sampler.sample(np.array([]), (100, 100))

        # Wrong dimensions
        with pytest.raises((ValueError, IndexError)):
            sampler.sample(np.random.random((10,)), (100, 100))

    def test_invalid_target_size(self):
        """Test handling of invalid target sizes"""
        sampler = EnhancedEquirectSampler()
        test_image = np.random.randint(0, 255, (100, 200, 3), dtype=np.uint8)

        # Negative size
        with pytest.raises((ValueError, TypeError)):
            sampler.sample(test_image, (-10, -10))

        # Zero size
        with pytest.raises((ValueError, TypeError)):
            sampler.sample(test_image, (0, 0))

    def test_boundary_coordinate_handling(self):
        """Test handling of boundary coordinates"""
        sampler = EnhancedEquirectSampler()
        test_image = np.random.randint(0, 255, (100, 200, 3), dtype=np.uint8)

        # Coordinates exactly at boundaries
        boundary_coords = [(0.0, 0.0), (1.0, 1.0), (0.0, 1.0), (1.0, 0.0)]

        for u, v in boundary_coords:
            try:
                result = sampler.sample_at_coordinates(
                    test_image, np.array([u]), np.array([v])
                )
                assert len(result) == 1
            except (ValueError, IndexError):
                # Acceptable if boundary handling has specific requirements
                pass


class TestPerformanceAndMemory:
    """Test performance and memory aspects"""

    def test_large_image_sampling(self):
        """Test sampling with large images"""
        # Test with reasonably large image
        large_image = np.random.randint(0, 255, (2048, 4096, 3), dtype=np.uint8)
        sampler = EnhancedEquirectSampler()

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (512, 512, 3), dtype=np.uint8
            )

            result = sampler.sample(large_image, (512, 512))
            assert result.shape == (512, 512, 3)

    def test_memory_efficiency(self):
        """Test memory efficiency of sampling operations"""
        sampler = EnhancedEquirectSampler()

        # Process multiple images to test memory cleanup
        for i in range(10):
            test_image = np.random.randint(0, 255, (256, 512, 3), dtype=np.uint8)

            with patch("cv2.resize") as mock_resize:
                mock_resize.return_value = np.random.randint(
                    0, 255, (128, 128, 3), dtype=np.uint8
                )

                result = sampler.sample(test_image, (128, 128))
                assert result.shape == (128, 128, 3)

                # Cleanup
                del result

    def test_coordinate_array_efficiency(self):
        """Test efficiency with large coordinate arrays"""
        sampler = EnhancedEquirectSampler()
        test_image = np.random.randint(0, 255, (256, 512, 3), dtype=np.uint8)

        # Large coordinate arrays
        u_coords = np.random.uniform(0, 1, 1000)
        v_coords = np.random.uniform(0, 1, 1000)

        # Should handle efficiently
        try:
            sampled_values = sampler.sample_at_coordinates(
                test_image, u_coords, v_coords
            )
            assert len(sampled_values) == 1000
        except (MemoryError, ValueError):
            # Acceptable if implementation has memory limits
            pass


if __name__ == "__main__":
    pytest.main([__file__])


