"""
統一指標收集模組
標準化指標收集接口，避免重複實現
"""
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any


class MetricType(Enum):
    """指標類型枚舉"""
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    GPU_USAGE = "gpu_usage"
    GPU_MEMORY = "gpu_memory"
    PROCESSING_TIME = "processing_time"
    DISK_USAGE = "disk_usage"
    NETWORK_IO = "network_io"
    CUSTOM = "custom"


@dataclass
class Metric:
    """標準指標數據結構"""
    name: str
    value: float
    unit: str
    metric_type: MetricType = MetricType.CUSTOM
    timestamp: float = field(default_factory=time.time)
    tags: dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> dict[str, Any]:
        """轉換為字典格式"""
        return {
            "name": self.name,
            "value": self.value,
            "unit": self.unit,
            "type": self.metric_type.value,
            "timestamp": self.timestamp,
            "tags": self.tags
        }


class MetricCollector(ABC):
    """抽象指標收集器"""
    
    @abstractmethod
    def collect(self) -> list[Metric]:
        """收集指標"""
        pass
    
    @abstractmethod
    def get_collector_name(self) -> str:
        """獲取收集器名稱"""
        pass


class MetricRegistry:
    """指標注册表 - 管理所有收集器"""
    
    def __init__(self):
        self.collectors: dict[str, MetricCollector] = {}
    
    def register(self, collector: MetricCollector):
        """註冊收集器"""
        name = collector.get_collector_name()
        self.collectors[name] = collector
    
    def unregister(self, name: str):
        """取消註冊收集器"""
        self.collectors.pop(name, None)
    
    def collect_all(self) -> dict[str, list[Metric]]:
        """收集所有指標"""
        results = {}
        for name, collector in self.collectors.items():
            try:
                results[name] = collector.collect()
            except Exception as e:
                results[name] = [
                    Metric(
                        name=f"{name}.error",
                        value=1.0,
                        unit="count",
                        tags={"error": str(e)}
                    )
                ]
        return results
