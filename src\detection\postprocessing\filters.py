"""
偵測框過濾器

根據不同標準移除不需要的偵測框的各種過濾器。
"""

from typing import Any

from config.constants import DEFAULT_MAX_AREA_RATIO
from log_utils.factory import get_logger

from ..core.data_structures import DetectionBox
from .base import PostProcessor

logger = get_logger(__name__)


class LargeBoxFilter(PostProcessor):
    """過濾掉相對於影像尺寸過大的偵測框"""

    def __init__(self, max_area_ratio: float = DEFAULT_MAX_AREA_RATIO):
        """初始化大型偵測框過濾器

        :param max_area_ratio: 最大面積比例 (偵測框面積 / 影像面積)
        """
        self.max_area_ratio = max_area_ratio

    def process(
        self, detections: list[DetectionBox], context: dict[str, Any]
    ) -> list[DetectionBox]:
        """過濾掉大型偵測框

        :param detections: 偵測框列表
        :param context: 處理上下文 (必須包含 'image_shape')
        :return: 過濾後的偵測列表
        """
        if not detections:
            return []

        image_shape = context.get("image_shape")
        if not image_shape:
            logger.warning("上下文中無 'image_shape'，跳過大型偵測框過濾器")
            return detections

        # 從上下文取得最大面積比例，或使用預設值
        max_area_ratio = context.get("max_area_ratio", self.max_area_ratio)

        filtered = []
        total_area = image_shape[0] * image_shape[1]

        for detection in detections:
            area_ratio = detection.area / total_area

            if area_ratio <= max_area_ratio:
                filtered.append(detection)
            else:
                logger.debug(
                    f"過濾掉大型偵測框: 面積比例 {area_ratio:.3f} > {max_area_ratio}"
                )

        logger.debug(f"大型偵測框過濾器: {len(detections)} -> {len(filtered)} 個偵測框")
        return filtered

    def get_name(self) -> str:
        """取得處理器名稱"""
        return "large_box_filter"

    def get_description(self) -> str:
        """取得處理器描述"""
        return f"過濾面積比例 > {self.max_area_ratio} 的偵測框"


class CenterRegionFilter(PostProcessor):
    """過濾掉中央圓形區域的偵測框 (用於面 5)"""

    def __init__(self, center_radius_ratio: float = 0.35):
        """初始化中央區域過濾器

        :param center_radius_ratio: 半徑比例，相對於 min(寬, 高)
        """
        self.center_radius_ratio = center_radius_ratio

    def process(
        self, detections: list[DetectionBox], context: dict[str, Any]
    ) -> list[DetectionBox]:
        """過濾掉中央圓形區域的偵測框

        :param detections: 偵測框列表
        :param context: 處理上下文 (必須包含 'image_shape')
        :return: 過濾後的偵測列表
        """
        if not detections:
            return []

        image_shape = context.get("image_shape")
        if not image_shape:
            logger.warning("上下文中無 'image_shape'，跳過中央區域過濾器")
            return detections

        filtered = []
        h, w = image_shape
        center_x, center_y = w // 2, h // 2
        radius = int(min(w, h) * self.center_radius_ratio)

        for detection in detections:
            box_center_x, box_center_y = detection.center
            distance = (
                (box_center_x - center_x) ** 2 + (box_center_y - center_y) ** 2
            ) ** 0.5

            if distance > radius:
                filtered.append(detection)
            else:
                logger.debug(
                    f"過濾掉中央區域的偵測框: 距離 {distance:.1f} <= {radius}"
                )

        logger.debug(
            f"中央區域過濾器: {len(detections)} -> {len(filtered)} 個偵測框"
        )
        return filtered

    def is_applicable(self, context: dict[str, Any]) -> bool:
        """僅適用於面 5 (地面)"""
        return context.get("face_id") == 5

    def get_name(self) -> str:
        """取得處理器名稱"""
        return "center_region_filter"

    def get_description(self) -> str:
        """取得處理器描述"""
        return (
            f"過濾中央區域的偵測框 (半徑比例 = {self.center_radius_ratio})"
        )


class ConfidenceFilter(PostProcessor):
    """根據信賴度閾值過濾偵測框"""

    def __init__(self, min_confidence: float = 0.1):
        """初始化信賴度過濾器

        :param min_confidence: 最小信賴度閾值
        """
        self.min_confidence = min_confidence

    def process(
        self, detections: list[DetectionBox], context: dict[str, Any]
    ) -> list[DetectionBox]:
        """按信賴度過濾偵測框

        :param detections: 偵測框列表
        :param context: 處理上下文
        :return: 過濾後的偵測列表
        """
        if not detections:
            return []

        # 從上下文取得信賴度閾值，或使用預設值
        min_confidence = context.get("min_confidence", self.min_confidence)

        filtered = [
            detection
            for detection in detections
            if detection.confidence >= min_confidence
        ]

        logger.debug(f"信賴度過濾器: {len(detections)} -> {len(filtered)} 個偵測框")
        return filtered

    def get_name(self) -> str:
        """取得處理器名稱"""
        return "confidence_filter"

    def get_description(self) -> str:
        """取得處理器描述"""
        return f"過濾信賴度 < {self.min_confidence} 的偵測框"


class SizeFilter(PostProcessor):
    """根據最小尺寸過濾偵測框"""

    def __init__(self, min_width: int = 5, min_height: int = 5):
        """初始化尺寸過濾器

        :param min_width: 最小偵測框寬度 (像素)
        :param min_height: 最小偵測框高度 (像素)
        """
        self.min_width = min_width
        self.min_height = min_height

    def process(
        self, detections: list[DetectionBox], context: dict[str, Any]
    ) -> list[DetectionBox]:
        """按最小尺寸過濾偵測框

        :param detections: 偵測框列表
        :param context: 處理上下文
        :return: 過濾後的偵測列表
        """
        if not detections:
            return []

        # 從上下文取得尺寸閾值，或使用預設值
        min_width = context.get("min_width", self.min_width)
        min_height = context.get("min_height", self.min_height)

        filtered = [
            detection
            for detection in detections
            if detection.width >= min_width and detection.height >= min_height
        ]

        logger.debug(f"尺寸過濾器: {len(detections)} -> {len(filtered)} 個偵測框")
        return filtered

    def get_name(self) -> str:
        """Get processor name"""
        return "size_filter"

    def get_description(self) -> str:
        """Get processor description"""
        return f"過濾小於 {self.min_width}x{self.min_height} 的偵測框"
