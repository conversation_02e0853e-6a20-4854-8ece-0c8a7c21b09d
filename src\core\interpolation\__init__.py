import sys
from pathlib import Path

# Import project modules directly (using pip install -e .)

"""
高性能插值模組 (core.interpolation)

本模組提供一個統一、高效能的影像插值框架，支援多種演算法和 GPU 加速。
它是影像幾何變換（如投影）中，決定最終成像品質的關鍵。

主要組件:
- `AdvancedInterpolator`: 核心插值器，支援超過 38 種插值方法。
- `MultipleInterpolator`: 多重插值器，可組合多種插值策略以實現複雜效果。
- `InterpolationMethod`: 插值方法的列舉類型。
- `InterpolationConfig`: 用於精確控制插值行為的設定類別。
- `kernels`: 包含所有底層插值核函式的子套件。

快速入門:
```python
from core.interpolation import create_interpolator, InterpolationMethod

# 建立一個高品質的 Lanczos3 插值器
interpolator = create_interpolator(InterpolationMethod.LANCZOS3)

# 執行插值
result = interpolator.interpolate(image, x_coords, y_coords)
```
此 `__init__.py` 檔案負責將各個子模組的核心元件整合並提升至本模組的頂層命名空間，
從而為使用者提供一個乾淨、一致的公開 API。
"""

# 從設定模組匯入與插值相關的設定類別
from config.interpolation import (InterpolationConfig, InterpolationMethod,
                                  MultipleInterpolationStrategy)

# 從本地子模組匯入核心功能
from .cache import clear_interpolation_cache, get_cache_stats
from .compat import (CubeFaceSampler, EquirectSampler, ImageInterpolator,
                     create_interpolator, create_multiple_interpolator,
                     mode_to_order)
from .interpolator import AdvancedInterpolator, MultipleInterpolator

# `__all__` 定義了本模組的公開 API，明確指出哪些是可以被外部安全使用的元件。
__all__ = [
    # --- 核心 API ---
    "AdvancedInterpolator",
    "MultipleInterpolator",
    
    # --- 設定與列舉 ---
    "InterpolationMethod",
    "InterpolationConfig",
    "MultipleInterpolationStrategy",
    
    # --- 快取管理 ---
    "clear_interpolation_cache",
    "get_cache_stats",
    
    # --- 工廠函式 ---
    "create_interpolator",
    "create_multiple_interpolator",
    
    # --- 向後相容層 ---
    "EquirectSampler",
    "CubeFaceSampler",
    "ImageInterpolator",
    "mode_to_order",
]
