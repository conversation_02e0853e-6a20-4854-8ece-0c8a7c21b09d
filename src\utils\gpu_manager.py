#!/usr/bin/env python3
"""
GPU設備管理器 - 方案一性能優化
GPU Device Manager - Plan 1 Performance Optimization

支援CUDA和OpenCL設備檢測、管理和智能分配
Supports CUDA and OpenCL device detection, management and intelligent allocation

Author: Claude Code Assistant  
Date: 2025-01-16
"""

import os
import sys
import warnings
from typing import Any
from dataclasses import dataclass
from enum import Enum
import logging
from pathlib import Path

# Import project modules directly (using pip install -e .)

logger = logging.getLogger('gpu_manager')

class DeviceType(Enum):
    """設備類型枚舉"""
    CPU = "cpu"
    CUDA = "cuda"
    OPENCL = "opencl"
    MPS = "mps"  # Apple Metal Performance Shaders

@dataclass
class DeviceInfo:
    """設備信息"""
    device_id: int
    device_type: DeviceType
    name: str
    total_memory: int  # MB
    free_memory: int   # MB
    compute_capability: str | None = None
    is_available: bool = True
    utilization: float = 0.0  # 0-100%

class GPUManager:
    """GPU設備管理器 - 高性能計算設備管理"""
    
    def __init__(self):
        """初始化GPU管理器"""
        self.available_devices: list[DeviceInfo] = []
        self.cuda_available = False
        self.opencl_available = False
        self.preferred_device: DeviceInfo | None = None
        
        # 檢測可用設備
        self._detect_devices()
        
        logger.info(f"GPU管理器初始化完成，檢測到 {len(self.available_devices)} 個設備")
    
    def _detect_devices(self):
        """檢測所有可用的計算設備"""
        self._detect_cuda_devices()
        self._detect_opencl_devices()
        self._detect_cpu_device()
        
        # 設置默認首選設備
        if self.available_devices:
            # 優先選擇CUDA設備，然後OpenCL，最後CPU
            cuda_devices = [d for d in self.available_devices if d.device_type == DeviceType.CUDA]
            opencl_devices = [d for d in self.available_devices if d.device_type == DeviceType.OPENCL]
            
            if cuda_devices:
                self.preferred_device = cuda_devices[0]
            elif opencl_devices:
                self.preferred_device = opencl_devices[0]
            else:
                self.preferred_device = self.available_devices[0]
    
    def _detect_cuda_devices(self):
        """檢測CUDA設備"""
        try:
            import torch
            if torch.cuda.is_available():
                self.cuda_available = True
                for i in range(torch.cuda.device_count()):
                    props = torch.cuda.get_device_properties(i)
                    free_mem, total_mem = torch.cuda.mem_get_info(i)
                    
                    device_info = DeviceInfo(
                        device_id=i,
                        device_type=DeviceType.CUDA,
                        name=props.name,
                        total_memory=total_mem // (1024 * 1024),  # Convert to MB
                        free_memory=free_mem // (1024 * 1024),
                        compute_capability=f"{props.major}.{props.minor}"
                    )
                    self.available_devices.append(device_info)
                    logger.info(f"檢測到CUDA設備 {i}: {props.name}")
        except ImportError:
            logger.debug("PyTorch不可用，跳過CUDA檢測")
        except Exception as e:
            logger.warning(f"CUDA檢測失敗: {e}")
    
    def _detect_opencl_devices(self):
        """檢測OpenCL設備"""
        try:
            import pyopencl as cl
            self.opencl_available = True
            
            for platform in cl.get_platforms():
                for i, device in enumerate(platform.get_devices()):
                    # 獲取設備內存信息
                    try:
                        total_mem = device.global_mem_size // (1024 * 1024)  # MB
                        # OpenCL沒有直接的空閒內存查詢，假設70%可用
                        free_mem = int(total_mem * 0.7)
                    except Exception:
                        total_mem = 0
                        free_mem = 0
                    
                    device_info = DeviceInfo(
                        device_id=len(self.available_devices),
                        device_type=DeviceType.OPENCL,
                        name=device.name.strip(),
                        total_memory=total_mem,
                        free_memory=free_mem
                    )
                    self.available_devices.append(device_info)
                    logger.info(f"檢測到OpenCL設備: {device.name.strip()}")
        except ImportError:
            logger.debug("PyOpenCL不可用，跳過OpenCL檢測")
        except Exception as e:
            logger.warning(f"OpenCL檢測失敗: {e}")
    
    def _detect_cpu_device(self):
        """檢測CPU設備"""
        try:
            import psutil
            cpu_count = os.cpu_count() or 1
            # 估算可用內存
            memory = psutil.virtual_memory()
            total_mem = memory.total // (1024 * 1024)  # MB
            free_mem = memory.available // (1024 * 1024)  # MB
            
            device_info = DeviceInfo(
                device_id=len(self.available_devices),
                device_type=DeviceType.CPU,
                name=f"CPU ({cpu_count} cores)",
                total_memory=total_mem,
                free_memory=free_mem
            )
            self.available_devices.append(device_info)
            logger.info(f"檢測到CPU設備: {cpu_count} 核心")
        except ImportError:
            # 如果psutil不可用，使用基本信息
            cpu_count = os.cpu_count() or 1
            device_info = DeviceInfo(
                device_id=len(self.available_devices),
                device_type=DeviceType.CPU,
                name=f"CPU ({cpu_count} cores)",
                total_memory=8192,  # 假設8GB
                free_memory=4096    # 假設4GB可用
            )
            self.available_devices.append(device_info)
    
    def get_best_device(self, memory_required: int = 0,
                       preferred_type: DeviceType | None = None) -> DeviceInfo | None:
        """
        根據需求獲取最佳設備
        
        Args:
            memory_required: 所需內存 (MB)
            preferred_type: 偏好設備類型
            
        Returns:
            最佳設備信息，如果沒有合適設備則返回None
        """
        suitable_devices = []
        
        for device in self.available_devices:
            if not device.is_available:
                continue
            
            # 檢查內存需求
            if memory_required > 0 and device.free_memory < memory_required:
                continue
            
            # 檢查類型偏好
            if preferred_type and device.device_type != preferred_type:
                continue
            
            suitable_devices.append(device)
        
        if not suitable_devices:
            return None
        
        # 按優先級排序：CUDA > OpenCL > CPU
        type_priority = {DeviceType.CUDA: 0, DeviceType.OPENCL: 1, DeviceType.CPU: 2}
        suitable_devices.sort(
            key=lambda d: (type_priority.get(d.device_type, 3), -d.free_memory)
        )
        
        return suitable_devices[0]
    
    def allocate_device(self, memory_required: int = 0) -> DeviceInfo | None:
        """分配設備資源"""
        device = self.get_best_device(memory_required)
        if device:
            # 更新設備使用狀態
            device.utilization = min(100.0, device.utilization + 10.0)
            logger.info(f"分配設備: {device.name} (類型: {device.device_type.value})")
        return device
    
    def release_device(self, device: DeviceInfo):
        """釋放設備資源"""
        if device in self.available_devices:
            device.utilization = max(0.0, device.utilization - 10.0)
            logger.debug(f"釋放設備: {device.name}")
    
    def get_device_stats(self) -> dict[str, Any]:
        """獲取設備統計信息"""
        stats = {
            'total_devices': len(self.available_devices),
            'cuda_devices': len([d for d in self.available_devices if d.device_type == DeviceType.CUDA]),
            'opencl_devices': len([d for d in self.available_devices if d.device_type == DeviceType.OPENCL]),
            'cpu_devices': len([d for d in self.available_devices if d.device_type == DeviceType.CPU]),
            'total_memory': sum(d.total_memory for d in self.available_devices),
            'free_memory': sum(d.free_memory for d in self.available_devices),
            'cuda_available': self.cuda_available,
            'opencl_available': self.opencl_available
        }
        return stats
    
    def print_device_info(self):
        """打印設備信息"""
        print("=== GPU設備管理器 - 設備信息 ===")
        for i, device in enumerate(self.available_devices):
            print(f"設備 {i}: {device.name}")
            print(f"  類型: {device.device_type.value}")
            print(f"  內存: {device.free_memory}/{device.total_memory} MB")
            if device.compute_capability:
                print(f"  計算能力: {device.compute_capability}")
            print(f"  使用率: {device.utilization:.1f}%")
            print()
        
        if self.preferred_device:
            print(f"首選設備: {self.preferred_device.name} ({self.preferred_device.device_type.value})")

# 全局GPU管理器實例
_gpu_manager = None

def get_gpu_manager() -> GPUManager:
    """獲取全局GPU管理器實例"""
    global _gpu_manager
    if _gpu_manager is None:
        _gpu_manager = GPUManager()
    return _gpu_manager

def create_gpu_manager() -> GPUManager:
    """創建新的GPU管理器實例"""
    return GPUManager()

# 便捷函數
def get_best_device(memory_required: int = 0,
                   preferred_type: DeviceType | None = None) -> DeviceInfo | None:
    """獲取最佳計算設備"""
    return get_gpu_manager().get_best_device(memory_required, preferred_type)

def is_cuda_available() -> bool:
    """檢查CUDA是否可用"""
    return get_gpu_manager().cuda_available

def is_opencl_available() -> bool:
    """檢查OpenCL是否可用"""
    return get_gpu_manager().opencl_available

def get_device_count() -> int:
    """獲取設備總數"""
    return len(get_gpu_manager().available_devices)

if __name__ == "__main__":
    # 測試GPU管理器
    manager = GPUManager()
    manager.print_device_info()
    
    # 測試設備分配
    device = manager.allocate_device(1024)  # 需要1GB內存
    if device:
        print(f"成功分配設備: {device.name}")
        manager.release_device(device)
    else:
        print("沒有合適的設備可用")