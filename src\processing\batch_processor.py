#!/usr/bin/env python3
"""
批次處理器模組 (已重構)
Batch Processor Module (Refactored)

從舊版本main.py中提取的高級批次處理功能，並根據新的協調者架構進行了重構。
"""

import csv
import gc
import os
import sys
import time
import uuid
from datetime import datetime
from pathlib import Path
# 現代類型提示語法 - 基本類型無需額外匯入

# Import project modules directly (using pip install -e .)

from config.settings import Config
from log_utils import get_logger
from .factory import ProcessingFactory
from .input_analyzer import InputAnalyzer
from utils.memory_utils import MemoryManager

logger = get_logger("batch_processor")

class AdvancedBatchProcessor:
    """
    高級批次處理器 - 重構為頂層協調者
    """

    def __init__(self, config: Config):
        """
        初始化高級批次處理器
        
        Args:
            config: 應用程序配置對象
        """
        self.config = config
        self.memory_manager = MemoryManager()
        self.input_analyzer = InputAnalyzer(config)
        
        # 使用工廠創建處理器鏈
        self.scene_processor = ProcessingFactory.create_scene_processor(config)

        self.current_progress_file: str | None = None
        self.task_id: str | None = None
        self.task_start_time: datetime | None = None

    def process_with_resume(self, **kwargs) -> bool:
        """
        處理所有區域目錄，支援中斷恢復。
        """
        base_path = self.config.io.input_path
        output_path = self.config.io.output_path
        
        self._initialize_environment(output_path, self.config.io.secondary_output_path, kwargs.get("specific_progress_file"))

        district_folders, specified_scenes = self._get_districts_and_scenes(
            base_path, self.config.io.scenes_csv_file, self.config.pipeline.process_csv_include
        )

        if not district_folders:
            return False

        processed_districts = 0
        try:
            for district_idx, district_path in enumerate(district_folders):
                processed_scenes = self._process_district(
                    district_path, district_idx, len(district_folders), output_path, specified_scenes
                )
                if processed_scenes > 0:
                    processed_districts += 1
            
            self._finalize_processing(processed_districts, len(district_folders), output_path)
            return True
        except KeyboardInterrupt:
            self._handle_interruption(processed_districts, len(district_folders))
            return False
        except Exception as e:
            self._handle_error(e, processed_districts, len(district_folders))
            return False

    def _initialize_environment(self, output_path: str, second_output_path: str | None, specific_progress_file: str | None):
        """初始化環境與資源"""
        if specific_progress_file:
            self.current_progress_file = specific_progress_file
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.current_progress_file = f"progress_{timestamp}.csv"

        self.task_id = str(uuid.uuid4())[:8]
        self.task_start_time = datetime.now()

        if second_output_path:
            os.makedirs(second_output_path, exist_ok=True)

    def _get_districts_and_scenes(self, base_path: str, scenes_csv_file: str | None, process_csv_include: bool) -> tuple[list[str], dict]:
        """獲取要處理的區域與場景"""
        district_folders = [os.path.join(base_path, d) for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d)) and "區" in d]
        if not district_folders:
            logger.warning(f"在 {base_path} 中找不到包含「區」字的目錄")
            return [], {}

        specified_scenes = {}
        if scenes_csv_file and os.path.exists(scenes_csv_file):
            try:
                with open(scenes_csv_file, "r", encoding="utf-8-sig") as f:
                    reader = csv.reader(f)
                    next(reader, None)
                    for row in reader:
                        if len(row) >= 2:
                            district, scene = row[0].strip(), row[1].strip()
                            specified_scenes.setdefault(district, []).append(scene)
                if process_csv_include:
                    district_folders = [d for d in district_folders if os.path.basename(d) in specified_scenes]
            except Exception as e:
                logger.error(f"解析 CSV 文件時發生錯誤: {e}")
        
        logger.info(f"將處理 {len(district_folders)} 個區域")
        return district_folders, specified_scenes

    def _process_district(self, district_path: str, district_idx: int, total_districts: int, output_path: str, specified_scenes: dict) -> int:
        """處理單個區域"""
        district_name = os.path.basename(district_path)
        logger.info(f"開始處理區域 [{district_idx+1}/{total_districts}]: {district_name}")
        self._save_progress(output_path, district_name, "全區", "待處理")

        all_scenes = [f for f in os.listdir(district_path) if os.path.isdir(os.path.join(district_path, f))]
        
        scenes_to_process = self._filter_scenes_by_csv(all_scenes, district_name, specified_scenes, self.config.pipeline.process_csv_include)
        
        resume_district, resume_scene, resume_status = self._load_progress(output_path)
        start_processing = self._should_start_processing(district_name, resume_district, resume_status)

        processed_scenes_count = 0
        for scene_idx, scene_name in enumerate(scenes_to_process):
            if not self._should_process_scene(district_name, scene_name, resume_district, resume_scene, start_processing):
                continue

            logger.info(f"處理場景: {scene_name} ({scene_idx+1}/{len(scenes_to_process)})")
            
            scene_path = os.path.join(district_path, scene_name)
            scene_data = self.input_analyzer.analyze_scene_content(scene_path)
            
            if not scene_data.get("has_content"):
                logger.warning(f"場景 {scene_name} 沒有可處理的內容，跳過。")
                continue

            success = self.scene_processor.process_single_scene(district_name, scene_name, scene_data)

            if success:
                processed_scenes_count += 1
            
            self.memory_manager.force_cleanup()

        self._save_progress(output_path, district_name, "全區", "完成")
        logger.info(f"完成處理區域: {district_name}，處理了 {processed_scenes_count}/{len(scenes_to_process)} 個場景")
        return processed_scenes_count

    def _filter_scenes_by_csv(self, all_scenes: list[str], district_name: str, specified_scenes: dict, process_csv_include: bool) -> list[str]:
        if not specified_scenes or district_name not in specified_scenes:
            return all_scenes
        
        specified_scene_list = specified_scenes[district_name]
        return [s for s in all_scenes if (s in specified_scene_list) == process_csv_include]

    def _should_start_processing(self, district_name: str, resume_district: str | None, resume_status: str | None) -> bool:
        return not (resume_district and resume_status != "完成") or district_name == resume_district

    def _should_process_scene(self, district_name: str, scene_name: str, resume_district: str | None, resume_scene: str | None, start_processing: bool) -> bool:
        if resume_district and district_name == resume_district and resume_scene and scene_name != resume_scene and not start_processing:
            logger.info(f"跳過場景 {scene_name}，等待恢復到 {resume_scene}")
            return False
        return True

    def _save_progress(self, output_path: str, district: str, scene: str, status: str):
        if not self.current_progress_file: return
        progress_file = os.path.join(output_path, self.current_progress_file)
        if not os.path.exists(progress_file):
            with open(progress_file, "w", encoding="utf-8", newline='') as f:
                writer = csv.writer(f)
                writer.writerow(["時間", "區域", "場景", "狀態"])
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(progress_file, "a", encoding="utf-8", newline='') as f:
            writer = csv.writer(f)
            writer.writerow([timestamp, district, scene, status])

    def _load_progress(self, output_path: str) -> tuple[str | None, str | None, str | None]:
        if not self.current_progress_file: return None, None, None
        progress_file = os.path.join(output_path, self.current_progress_file)
        if not os.path.exists(progress_file): return None, None, None
        try:
            with open(progress_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
                if len(lines) > 1:
                    last_line = lines[-1].strip()
                    if last_line:
                        parts = last_line.split(",")
                        if len(parts) >= 4:
                            return parts[1], parts[2], parts[3]
        except Exception as e:
            logger.error(f"讀取進度檔案時發生錯誤: {e}")
        return None, None, None

    def _finalize_processing(self, processed_districts: int, total_districts: int, output_path: str):
        logger.info(f"處理完成：{processed_districts}/{total_districts} 個區域")

    def _handle_interruption(self, processed_districts: int, total_districts: int):
        logger.info("使用者中斷處理，下次執行時可以選擇繼續處理")

    def _handle_error(self, e: Exception, processed_districts: int, total_districts: int):
        logger.error(f"處理過程中發生錯誤: {e}", exc_info=True)
