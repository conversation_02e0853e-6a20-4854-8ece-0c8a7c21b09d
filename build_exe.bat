@echo off
echo === generate_pyramid.py EXE 打包工具 ===
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 錯誤: 找不到 Python，請先安裝 Python
    pause
    exit /b 1
)

echo 正在安裝 PyInstaller...
pip install pyinstaller

echo.
echo 開始打包 generate_pyramid.py...

pyinstaller ^
    --onefile ^
    --console ^
    --name generate_pyramid ^
    --distpath dist ^
    --workpath build ^
    --specpath . ^
    REM 使用者需要在執行時提供 config.yaml 路徑
    --hidden-import torch ^
    --hidden-import ultralytics ^
    --hidden-import cv2 ^
    --hidden-import numpy ^
    --hidden-import scipy ^
    --hidden-import yaml ^
    --hidden-import pandas ^
    --hidden-import PIL ^
    --collect-data ultralytics ^
    --collect-data torch ^
    generate_pyramid.py

if %errorlevel% equ 0 (
    echo.
    echo 打包成功!
    echo EXE 檔案位置: dist\generate_pyramid.exe
    echo.
    echo 使用說明:
    echo 1. 準備 config.yaml 配置檔案
    echo 2. 執行 generate_pyramid.exe
    echo 3. 按照提示輸入 config.yaml 檔案路徑
) else (
    echo.
    echo 打包失敗，請檢查錯誤訊息
)

echo.
pause