"""
Tests for core.cube_mapping module
"""

from unittest.mock import <PERSON><PERSON>ock, patch

import numpy as np
import pytest


# Mock numba decorator
def mock_jit(*args, **kwargs):
    def decorator(func):
        return func

    return decorator


# Patch numba.jit before importing the module
with patch("numba.jit", side_effect=mock_jit):
    from core.cube_mapping.core import CubeMapper


class TestCubeMapper:
    """Test CubeMapper class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.cube_size = 256
        self.mapper = CubeMapper(self.cube_size)

        # Create sample cube faces for testing
        self.sample_faces = {}
        face_names = ["front", "back", "left", "right", "up", "down"]
        for i, face in enumerate(face_names):
            # Create a simple pattern for each face (different colors)
            self.sample_faces[face] = np.full(
                (self.cube_size, self.cube_size, 3), i * 40, dtype=np.uint8
            )

    def test_init(self):
        """Test CubeMapper initialization"""
        assert isinstance(self.mapper, CubeMapper)

    def test_horizon_to_list_basic(self):
        """Test horizon format to list conversion"""
        # Create horizon format: vertical stacking of faces
        horizon_height = self.cube_size
        horizon_width = self.cube_size * 6
        horizon_image = np.random.randint(
            0, 255, (horizon_height, horizon_width, 3), dtype=np.uint8
        )

        face_list = self.mapper.horizon_to_list(horizon_image)

        assert isinstance(face_list, list)
        assert len(face_list) == 6

        for face in face_list:
            assert face.shape == (self.cube_size, self.cube_size, 3)
            assert face.dtype == np.uint8

    def test_horizon_to_dict_basic(self):
        """Test horizon format to dictionary conversion"""
        # Create horizon format image
        horizon_height = self.cube_size
        horizon_width = self.cube_size * 6
        horizon_image = np.random.randint(
            0, 255, (horizon_height, horizon_width, 3), dtype=np.uint8
        )

        face_dict = self.mapper.horizon_to_dict(horizon_image)

        assert isinstance(face_dict, dict)
        assert len(face_dict) == 6

        expected_faces = ["front", "right", "back", "left", "up", "down"]
        for face_name in expected_faces:
            assert face_name in face_dict
            assert face_dict[face_name].shape == (self.cube_size, self.cube_size, 3)

    def test_list_to_horizon_basic(self):
        """Test list to horizon format conversion"""
        # Create list of faces
        face_list = []
        for i in range(6):
            face = np.full((self.cube_size, self.cube_size, 3), i * 40, dtype=np.uint8)
            face_list.append(face)

        horizon_image = self.mapper.list_to_horizon(face_list)

        expected_height = self.cube_size
        expected_width = self.cube_size * 6
        assert horizon_image.shape == (expected_height, expected_width, 3)
        assert horizon_image.dtype == np.uint8

    def test_dict_to_horizon_basic(self):
        """Test dictionary to horizon format conversion"""
        horizon_image = self.mapper.dict_to_horizon(self.sample_faces)

        expected_height = self.cube_size
        expected_width = self.cube_size * 6
        assert horizon_image.shape == (expected_height, expected_width, 3)
        assert horizon_image.dtype == np.uint8

    def test_dict_to_list_basic(self):
        """Test dictionary to list conversion"""
        face_list = self.mapper.dict_to_list(self.sample_faces)

        assert isinstance(face_list, list)
        assert len(face_list) == 6

        for face in face_list:
            assert face.shape == (self.cube_size, self.cube_size, 3)

    def test_list_to_dict_basic(self):
        """Test list to dictionary conversion"""
        face_list = []
        for i in range(6):
            face = np.full((self.cube_size, self.cube_size, 3), i * 40, dtype=np.uint8)
            face_list.append(face)

        face_dict = self.mapper.list_to_dict(face_list)

        assert isinstance(face_dict, dict)
        assert len(face_dict) == 6

        expected_faces = ["front", "right", "back", "left", "up", "down"]
        for face_name in expected_faces:
            assert face_name in face_dict

    def test_dice_to_horizon_basic(self):
        """Test dice format to horizon conversion"""
        # Create dice format (cross layout)
        dice_height = self.cube_size * 3
        dice_width = self.cube_size * 4
        dice_image = np.random.randint(
            0, 255, (dice_height, dice_width, 3), dtype=np.uint8
        )

        horizon_image = self.mapper.dice_to_horizon(dice_image)

        expected_height = self.cube_size
        expected_width = self.cube_size * 6
        assert horizon_image.shape == (expected_height, expected_width, 3)

    def test_horizon_to_dice_basic(self):
        """Test horizon to dice format conversion"""
        # Create horizon format
        horizon_image = self.mapper.dict_to_horizon(self.sample_faces)

        dice_image = self.mapper.horizon_to_dice(horizon_image)

        expected_height = self.cube_size * 3
        expected_width = self.cube_size * 4
        assert dice_image.shape == (expected_height, expected_width, 3)

    def test_round_trip_horizon_list(self):
        """Test round-trip conversion: horizon -> list -> horizon"""
        # Create original horizon image
        original_horizon = self.mapper.dict_to_horizon(self.sample_faces)

        # Convert to list and back
        face_list = self.mapper.horizon_to_list(original_horizon)
        converted_horizon = self.mapper.list_to_horizon(face_list)

        # Should be identical
        np.testing.assert_array_equal(original_horizon, converted_horizon)

    def test_round_trip_horizon_dict(self):
        """Test round-trip conversion: horizon -> dict -> horizon"""
        # Create original horizon image
        original_horizon = self.mapper.dict_to_horizon(self.sample_faces)

        # Convert to dict and back
        face_dict = self.mapper.horizon_to_dict(original_horizon)
        converted_horizon = self.mapper.dict_to_horizon(face_dict)

        # Should be identical
        np.testing.assert_array_equal(original_horizon, converted_horizon)

    def test_round_trip_dict_list(self):
        """Test round-trip conversion: dict -> list -> dict"""
        # Convert to list and back
        face_list = self.mapper.dict_to_list(self.sample_faces)
        converted_dict = self.mapper.list_to_dict(face_list)

        # Should have same keys and values
        assert set(converted_dict.keys()) == set(self.sample_faces.keys())
        for face_name in self.sample_faces:
            np.testing.assert_array_equal(
                self.sample_faces[face_name], converted_dict[face_name]
            )

    def test_fast_face_extraction(self):
        """Test fast face extraction function"""
        # Create a horizon image
        horizon_image = self.mapper.dict_to_horizon(self.sample_faces)

        # Extract a specific face (e.g., front face at index 0)
        extracted_face = self.mapper.fast_face_extraction(
            horizon_image, 0
        )

        assert extracted_face.shape == (self.cube_size, self.cube_size, 3)

        # Test all face indices
        for face_idx in range(6):
            face = self.mapper.fast_face_extraction(
                horizon_image, face_idx
            )
            assert face.shape == (self.cube_size, self.cube_size, 3)

    def test_format_validation(self):
        """Test validation of cube map formats"""
        # Test with correct horizon dimensions
        correct_horizon = np.random.randint(
            0, 255, (self.cube_size, self.cube_size * 6, 3), dtype=np.uint8
        )

        # Should not raise exceptions
        face_list = self.mapper.horizon_to_list(correct_horizon)
        assert len(face_list) == 6

        # Test with incorrect horizon dimensions
        incorrect_horizon = np.random.randint(
            0, 255, (self.cube_size, self.cube_size * 5, 3), dtype=np.uint8
        )

        # Should handle gracefully or raise appropriate error
        with pytest.raises((ValueError, IndexError, AssertionError)):
            self.mapper.horizon_to_list(incorrect_horizon)

    def test_different_cube_sizes(self):
        """Test with different cube sizes"""
        sizes = [64, 128, 512, 1024]

        for size in sizes:
            # Create test faces
            test_faces = {}
            face_names = ["front", "back", "left", "right", "up", "down"]
            for face in face_names:
                test_faces[face] = np.random.randint(
                    0, 255, (size, size, 3), dtype=np.uint8
                )

            # Test conversions
            horizon = self.mapper.dict_to_horizon(test_faces)
            assert horizon.shape == (size, size * 6, 3)

            # Convert back
            mapper = CubeMapper(size)
            converted_faces = mapper.horizon_to_dict(horizon)
            assert len(converted_faces) == 6

    def test_grayscale_images(self):
        """Test with grayscale images (2D arrays)"""
        # Create grayscale faces
        gray_faces = {}
        face_names = ["front", "back", "left", "right", "up", "down"]
        for i, face in enumerate(face_names):
            gray_faces[face] = np.full(
                (self.cube_size, self.cube_size), i * 40, dtype=np.uint8
            )

        # Test conversion (should handle gracefully or add channel dimension)
        try:
            horizon = self.mapper.dict_to_horizon(gray_faces)
            # If successful, check dimensions
            assert horizon.shape[0] == self.cube_size
            assert horizon.shape[1] == self.cube_size * 6
        except (ValueError, IndexError):
            # It's acceptable if grayscale is not supported
            pass

    def test_invalid_face_dict(self):
        """Test with invalid face dictionary"""
        # Missing faces
        incomplete_faces = {
            "front": np.random.randint(0, 255, (self.cube_size, self.cube_size, 3)),
            "back": np.random.randint(0, 255, (self.cube_size, self.cube_size, 3)),
        }

        with pytest.raises((KeyError, ValueError, AssertionError)):
            self.mapper.dict_to_horizon(incomplete_faces)

        # Wrong face names
        wrong_faces = {
            "invalid_face": np.random.randint(
                0, 255, (self.cube_size, self.cube_size, 3)
            )
        }

        with pytest.raises((KeyError, ValueError, AssertionError)):
            self.mapper.dict_to_horizon(wrong_faces)

    def test_edge_cases(self):
        """Test edge cases and boundary conditions"""
        # Very small cube size
        small_size = 8
        small_faces = {}
        face_names = ["front", "back", "left", "right", "up", "down"]
        for face in face_names:
            small_faces[face] = np.random.randint(
                0, 255, (small_size, small_size, 3), dtype=np.uint8
            )

        horizon = self.mapper.dict_to_horizon(small_faces)
        assert horizon.shape == (small_size, small_size * 6, 3)

        # Test with single pixel cube
        if self.mapper.__class__.__name__ == "CubeMapper":  # Avoid infinite recursion
            pixel_faces = {}
            for face in face_names:
                pixel_faces[face] = np.random.randint(0, 255, (1, 1, 3), dtype=np.uint8)

            pixel_horizon = self.mapper.dict_to_horizon(pixel_faces)
            assert pixel_horizon.shape == (1, 6, 3)

    def test_memory_efficiency(self):
        """Test memory efficiency with larger images"""
        # Test with reasonably large size
        large_size = 512
        large_faces = {}
        face_names = ["front", "back", "left", "right", "up", "down"]
        for face in face_names:
            large_faces[face] = np.random.randint(
                0, 255, (large_size, large_size, 3), dtype=np.uint8
            )

        # Should not raise memory errors
        horizon = self.mapper.dict_to_horizon(large_faces)
        converted_back = self.mapper.horizon_to_dict(horizon, large_size)

        assert len(converted_back) == 6

    def test_data_type_preservation(self):
        """Test that data types are preserved during conversion"""
        # Test with different data types
        dtypes = [np.uint8, np.uint16, np.float32, np.float64]

        for dtype in dtypes:
            typed_faces = {}
            face_names = ["front", "back", "left", "right", "up", "down"]
            for face in face_names:
                if dtype == np.uint8:
                    values = np.random.randint(0, 255, (64, 64, 3))
                elif dtype == np.uint16:
                    values = np.random.randint(0, 65535, (64, 64, 3))
                else:
                    values = np.random.random((64, 64, 3))

                typed_faces[face] = values.astype(dtype)

            horizon = self.mapper.dict_to_horizon(typed_faces)
            mapper = CubeMapper(64)
            converted_back = mapper.horizon_to_dict(horizon)

            # Check that data type is preserved
            assert horizon.dtype == dtype
            for face_name in face_names:
                assert converted_back[face_name].dtype == dtype


class TestCubeMapperCaching:
    """Test caching functionality in CubeMapper"""

    def setup_method(self):
        """Setup test fixtures"""
        self.mapper = CubeMapper()
        self.cube_size = 256

    @patch.object(CubeMapper, "horizon_to_dict")
    def test_caching_mechanism(self, mock_horizon_to_dict):
        """Test if caching is implemented and working"""
        # Mock the method to track calls
        mock_horizon_to_dict.return_value = {}

        # Create test horizon image
        horizon_image = np.random.randint(
            0, 255, (self.cube_size, self.cube_size * 6, 3), dtype=np.uint8
        )

        # If caching is implemented, first call should cache result
        # Multiple calls with same input should only call method once
        try:
            self.mapper.horizon_to_dict(horizon_image, self.cube_size)
            self.mapper.horizon_to_dict(horizon_image, self.cube_size)

            # If caching is working, method should be called less than twice
            # If no caching, it will be called twice
            # This test documents the current behavior
            assert mock_horizon_to_dict.call_count >= 1
        except Exception:
            # If caching is not implemented, that's also fine
            pass


if __name__ == "__main__":
    pytest.main([__file__])


