#!/usr/bin/env python3
"""
統一立方體處理服務模組
Unified Cube Processing Service Module

提供標準化的立方體相關處理功能，包括全景圖到立方體的轉換、結構檢測和圖像保存。
Provides standardized cube-related processing functions, including panorama-to-cube conversion, structure detection, and image saving.

Author: Claude Code Assistant
Date: 2025-01-19
"""

# 1. 標準庫
import logging
import os
from pathlib import Path
from typing import TYPE_CHECKING

# 為了類型註解，在 TYPE_CHECKING 時導入 numpy
if TYPE_CHECKING:
    import numpy as np

# 2. 延遲載入重型庫
def _import_heavy_libs():
    """延遲載入重型庫"""
    global cv2, np
    try:
        import cv2
        import numpy as np
        return True
    except ImportError as e:
        import warnings
        warnings.warn(f"無法載入必要的重型庫: {e}", ImportWarning)
        return False

# 3. 本地模組
try:
    from core.projection import ProjectionCore
    HAS_CORE = True
except ImportError:
    HAS_CORE = False

class CubeService:
    """
    統一的立方體處理服務。
    
    職責:
    - 將全景圖轉換為立方體面。
    - 從文件系統檢測立方體結構。
    - 標準化保存立方體面、預覽圖和縮略圖。
    """
    
    def __init__(self, config):
        """
        初始化立方體服務
        
        Args:
            config: 應用程序配置對象
        """
        if not HAS_CORE:
            raise ImportError("Core module is not available. Cannot initialize CubeService.")
        
        self.config = config
        self.logger = logging.getLogger(__name__)

    def convert_panorama_to_cube(self, panorama_image) -> dict[str, any]:
        """
        將單個全景圖轉換為一組立方體面。
        
        Args:
            panorama_image: 全景圖圖像 (numpy.ndarray)
        
        Returns:
            一個字典，鍵為面名稱 (F, R, B, L, U, D)，值為立方體面圖像。
        """
        # 確保重型庫已載入
        if not _import_heavy_libs():
            raise ImportError("無法載入必要的重型庫")

        try:
            h, w = panorama_image.shape[:2]
            projection_core = ProjectionCore(
                height=h,
                width=w,
                face_size=self.config.processing.cube_face_size,
                mode=self.config.processing.interpolation_method,
                use_gpu=self.config.system.enable_gpu_acceleration
            )

            # 使用字典格式獲取立方體面
            cube_dict = projection_core.equirect_to_cubemap(panorama_image, cube_format="dict")
            self.logger.info(f"成功將 {w}x{h} 的全景圖轉換為 {len(cube_dict)} 個立方體面。")
            return cube_dict
        except Exception as e:
            self.logger.error(f"全景圖到立方體轉換失敗: {e}")
            raise

    def detect_cube_structure(self, scene_dir: Path) -> "dict[int, np.ndarray] | None":
        """
        檢測目錄中的立方體結構並載入圖像。
        
        Args:
            scene_dir: 場景目錄的路徑 (Path object)
        
        Returns:
            如果找到，則返回一個包含圖像的字典 {face_id: image_array}，否則返回 None。
        """
        html5_dir = scene_dir / "html5"
        if html5_dir.is_dir():
            structure = self._find_faces_in_dir(html5_dir)
            if structure:
                self.logger.info(f"在 html5 子目錄中找到立方體結構。")
                return self._load_face_images(structure)

        structure = self._find_faces_in_dir(scene_dir)
        if structure:
            self.logger.info(f"在場景根目錄找到立方體結構。")
            return self._load_face_images(structure)
            
        return None

    def _find_faces_in_dir(self, directory: Path) -> dict | None:
        """在指定目錄中查找一組完整的立方體面"""
        naming_patterns = {
            "numeric": [f"{i}" for i in range(6)],
            "face_letters": ["F", "R", "B", "L", "U", "D"],
            "descriptive": ["front", "right", "back", "left", "up", "down"],
        }
        face_map = {"F":0, "R":1, "B":2, "L":3, "U":4, "D":5, "front":0, "right":1, "back":2, "left":3, "up":4, "down":5}

        for naming_type, pattern in naming_patterns.items():
            found_faces = {}
            for face_name in pattern:
                for ext in [".jpg", ".jpeg", ".png", ".bmp"]:
                    face_file = directory / f"{face_name}{ext}"
                    if face_file.exists():
                        face_id = int(face_name) if naming_type == "numeric" else face_map[face_name.upper()]
                        found_faces[face_id] = str(face_file)
                        break
            
            if len(found_faces) >= 4: # 找到4個以上即可
                return {
                    "source_dir": str(directory),
                    "naming_type": naming_type,
                    "face_files": found_faces,
                }
        return None

    def _load_face_images(self, structure: dict) -> "dict[int, np.ndarray] | None":
        """根據結構字典加載面部圖像"""
        face_images = {}
        for face_id, file_path in structure["face_files"].items():
            try:
                img = cv2.imread(file_path)
                if img is not None:
                    face_images[face_id] = img
                else:
                    self.logger.warning(f"無法讀取立方體面圖像: {file_path}")
            except Exception as e:
                self.logger.error(f"加載立方體面 {file_path} 時出錯: {e}")
        
        if face_images:
            return face_images
        return None

    def save_cube_assets(self, cube_dict: "dict[str, np.ndarray]", output_dir: str):
        """
        保存所有與立方體相關的資產，包括各個面、預覽圖和縮略圖。
        
        Args:
            cube_dict: 包含立方體面的字典。
            output_dir: 保存資產的目標目錄。
        """
        try:
            html5_folder = Path(output_dir) / "html5"
            html5_folder.mkdir(parents=True, exist_ok=True)

            face_mapping = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}

            # 保存立方體面
            for face_name, face_image in cube_dict.items():
                face_idx = face_mapping.get(face_name)
                if face_idx is not None:
                    output_file = html5_folder / f"{face_idx}.jpg"
                    cv2.imwrite(
                        str(output_file), face_image,
                        [cv2.IMWRITE_JPEG_QUALITY, self.config.processing.jpeg_quality]
                    )

            # 保存預覽圖和縮略圖
            self._save_preview_and_thumbnail(cube_dict, output_dir)
            self.logger.info(f"所有立方體資產已保存至: {output_dir}")

        except Exception as e:
            self.logger.error(f"保存立方體資產時失敗: {e}")
            raise

    def _save_preview_and_thumbnail(self, cube_dict: "dict[str, np.ndarray]", output_dir: str):
        """創建並保存預覽圖和縮略圖"""
        # 創建預覽圖 (水平拼接)
        preview_faces = [cube_dict[name] for name in ["F", "R", "B", "L", "U", "D"] if name in cube_dict]
        if not preview_faces:
            return

        preview = np.hstack(preview_faces)
        preview_path = Path(output_dir) / "preview.jpg"
        cv2.imwrite(str(preview_path), preview, [cv2.IMWRITE_JPEG_QUALITY, 85])

        # 創建縮略圖 (使用第一個面)
        thumbnail = cv2.resize(preview_faces[0], (400, 400), interpolation=cv2.INTER_AREA)
        thumbnail_path = Path(output_dir) / "thumbnail.jpg"
        cv2.imwrite(str(thumbnail_path), thumbnail, [cv2.IMWRITE_JPEG_QUALITY, 80])

    def save_cube_images(self, cube_dict: "dict[str, np.ndarray]", output_dir: str):
        """
        統一的立方體保存邏輯，替代多個重複實現。
        
        Args:
            cube_dict: 包含立方體面的字典
            output_dir: 輸出目錄路徑
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 面名稱映射
            face_mapping = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}
            
            saved_count = 0
            for face_name, face_image in cube_dict.items():
                if face_name in face_mapping:
                    face_idx = face_mapping[face_name]
                    face_path = output_path / f"{face_idx}.jpg"
                    
                    # 保存立方體面
                    cv2.imwrite(
                        str(face_path), 
                        face_image,
                        [cv2.IMWRITE_JPEG_QUALITY, self.config.processing.jpeg_quality]
                    )
                    saved_count += 1
                    
            self.logger.info(f"成功保存 {saved_count} 個立方體面到 {output_dir}")
            
        except Exception as e:
            self.logger.error(f"保存立方體圖像失敗: {e}")
            raise
