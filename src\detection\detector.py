"""
目標檢測器模組

提供高性能的目標檢測功能，專注於運行速度和效率：
- 雙模型協同檢測
- 特殊面處理邏輯（第4面跳過，第5面旋轉+中心過濾）
- 過濾過大檢測框
- 合併重疊檢測框
- 批次處理支援
- 統計信息收集
- 可控的結果繪製
"""

import cv2
import numpy as np
import torch
import logging
import os
import time
import sys
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
from ultralytics import YOLO

# Import project modules directly (using pip install -e .)

from config.constants import (
    DEFAULT_CONF_THRESHOLD, DEFAULT_IOU_THRESHOLD, DEFAULT_MAX_AREA_RATIO,
    DEFAULT_FACE5_TEST_CONF, DEFAULT_BLUR_KERNEL_SIZE, DEFAULT_BLUR_SIGMA,
    DetectionClass, DEFAULT_CLASS_NAMES
)

# 高級組件導入 (可選)
try:
    from utils.gpu_manager import get_gpu_manager, DeviceType
    HAS_GPU_MANAGER = True
except ImportError:
    HAS_GPU_MANAGER = False

try:
    from utils.advanced_memory_manager import create_advanced_memory_manager, CachePolicy
    HAS_ADVANCED_MEMORY = True
except ImportError:
    HAS_ADVANCED_MEMORY = False

try:
    from utils.performance_monitor import get_performance_monitor
    HAS_PERFORMANCE_MONITOR = True
except ImportError:
    HAS_PERFORMANCE_MONITOR = False

logger = logging.getLogger(__name__)


@dataclass
class DetectionBox:
    """檢測框數據結構"""
    x1: int
    y1: int
    x2: int
    y2: int
    confidence: float
    class_id: int
    
    @property
    def width(self) -> int:
        return self.x2 - self.x1
    
    @property
    def height(self) -> int:
        return self.y2 - self.y1
    
    @property
    def area(self) -> int:
        return self.width * self.height
    
    @property
    def center(self) -> Tuple[int, int]:
        return ((self.x1 + self.x2) // 2, (self.y1 + self.y2) // 2)


@dataclass 
class DetectionConfig:
    """檢測器配置"""
    primary_model_path: str
    secondary_model_path: Optional[str] = None
    conf_threshold: float = DEFAULT_CONF_THRESHOLD
    iou_threshold: float = DEFAULT_IOU_THRESHOLD
    max_area_ratio: float = DEFAULT_MAX_AREA_RATIO
    face5_test_conf: float = DEFAULT_FACE5_TEST_CONF
    device: Optional[str] = None
    enable_draw: bool = False


class Detector:
    """
    目標檢測器
    
    針對全景圖處理優化的高性能檢測器，專注於速度和核心功能：
    - 雙模型快速檢測
    - 第4面跳過，第5面特殊處理
    - 過濾和合併檢測框
    - 高斯模糊處理
    """
    
    def __init__(self, config: DetectionConfig):
        """初始化快速檢測器"""
        self.config = config
        
        # 初始化高級組件
        self.advanced_memory_manager = None
        self.performance_monitor = None
        
        # 初始化高級記憶體管理器
        if HAS_ADVANCED_MEMORY:
            try:
                self.advanced_memory_manager = create_advanced_memory_manager(
                    max_memory_mb=2048,  # 檢測器使用較少記憶體
                    cache_policy=CachePolicy.LRU,
                    enable_streaming=False,
                    chunk_size_mb=128
                )
                logger.info("檢測器: 高級記憶體管理器已啟用")
            except Exception as e:
                logger.warning(f"檢測器: 高級記憶體管理器初始化失敗: {e}")
        
        # 初始化性能監控器
        if HAS_PERFORMANCE_MONITOR:
            try:
                self.performance_monitor = get_performance_monitor()
                self.performance_monitor.start_monitoring()
                logger.info("檢測器: 性能監控器已啟用")
            except Exception as e:
                logger.warning(f"檢測器: 性能監控器初始化失敗: {e}")
        
        # 設備選擇 - 使用GPU管理器優化
        if config.device is None:
            if HAS_GPU_MANAGER:
                try:
                    gpu_manager = get_gpu_manager()
                    best_device = gpu_manager.get_best_device(preferred_type=DeviceType.CUDA)
                    if best_device:
                        self.device = f"cuda:{best_device.device_id}"
                        logger.info(f"GPU管理器選擇設備: {best_device.name} ({best_device.total_memory}MB)")
                    else:
                        self.device = 'cpu'
                        logger.info("GPU管理器未找到可用CUDA設備，使用CPU")
                except Exception as e:
                    logger.warning(f"GPU管理器初始化失敗: {e}，回退到基本檢測")
                    self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
            else:
                self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            # 處理設備字符串，確保格式正確
            self.device = self._normalize_device_string(config.device)
            
        # 載入主要模型
        try:
            self.primary_model = YOLO(config.primary_model_path)
            self.primary_model.to(self.device)
            logger.info(f"主要模型載入成功: {config.primary_model_path}")
        except Exception as e:
            logger.error(f"主要模型載入失敗: {e}")
            raise
            
        # 載入次要模型（如果提供）
        self.secondary_model = None
        if config.secondary_model_path and os.path.exists(config.secondary_model_path):
            try:
                self.secondary_model = YOLO(config.secondary_model_path)
                self.secondary_model.to(self.device)
                logger.info(f"次要模型載入成功: {config.secondary_model_path}")
            except Exception as e:
                logger.warning(f"次要模型載入失敗: {e}")
                
        # 檢查是否可以使用AMP
        self.use_amp = 'cuda' in self.device
        
        logger.info(f"快速檢測器初始化完成 - 設備: {self.device}, AMP: {self.use_amp}")
    
    def _normalize_device_string(self, device_str: str) -> str:
        """
        標準化設備字符串，確保PyTorch/YOLO可以正確識別
        
        Args:
            device_str: 原始設備字符串
            
        Returns:
            標準化的設備字符串
        """
        device_str = device_str.lower().strip()
        
        # 如果是完整的GPU名稱，轉換為cuda格式
        if 'nvidia' in device_str or 'geforce' in device_str or 'rtx' in device_str or 'gtx' in device_str:
            if torch.cuda.is_available():
                logger.info(f"檢測到GPU設備名稱 '{device_str}'，使用 'cuda'")
                return 'cuda'
            else:
                logger.warning(f"CUDA設備 '{device_str}' 不可用，回退到CPU")
                return 'cpu'
        
        # 如果已經是標準格式
        if device_str in ['cpu', 'cuda'] or device_str.startswith('cuda:'):
            return device_str
        
        # 如果包含cuda關鍵字但格式不對
        if 'cuda' in device_str:
            if torch.cuda.is_available():
                return 'cuda'
            else:
                logger.warning(f"CUDA設備不可用，回退到CPU")
                return 'cpu'
        
        # 默認返回CPU
        logger.warning(f"無法識別設備字符串 '{device_str}'，使用CPU")
        return 'cpu'
        
    def detect(self, image: np.ndarray, model: YOLO, conf_threshold: float) -> List[DetectionBox]:
        """基礎檢測方法"""
        try:
            if self.use_amp:
                with torch.amp.autocast(device_type='cuda'):
                    results = model.predict(
                        image,
                        conf=conf_threshold,
                        iou=self.config.iou_threshold,
                        verbose=False,
                        device=self.device
                    )
            else:
                results = model.predict(
                    image,
                    conf=conf_threshold,
                    iou=self.config.iou_threshold,
                    verbose=False,
                    device=self.device
                )
                
            detections = []
            if results and len(results) > 0:
                for box in results[0].boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    conf_val = box.conf[0].cpu().item()
                    cls = int(box.cls[0].cpu().item())
                    
                    detections.append(DetectionBox(
                        x1=int(x1), y1=int(y1),
                        x2=int(x2), y2=int(y2),
                        confidence=conf_val, class_id=cls
                    ))
                    
            return detections
            
        except Exception as e:
            logger.error(f"檢測失敗: {e}")
            return []
            
    def detect_dual_model(self, image: np.ndarray) -> List[DetectionBox]:
        """雙模型檢測"""
        all_detections = []
        
        # 主要模型檢測
        primary_detections = self.detect(image, self.primary_model, self.config.conf_threshold)
        all_detections.extend(primary_detections)
        
        # 次要模型檢測（如果有）
        if self.secondary_model:
            secondary_detections = self.detect(image, self.secondary_model, self.config.conf_threshold)
            all_detections.extend(secondary_detections)
            
        return all_detections
        
    def detect_face5_with_rotation(self, image: np.ndarray) -> List[DetectionBox]:
        """第5面特殊檢測：90度和270度旋轉"""
        all_detections = []
        h, w = image.shape[:2]
        
        # 原始圖像檢測
        original_detections = self.detect(image, self.primary_model, self.config.face5_test_conf)
        all_detections.extend(original_detections)
        
        # 90度旋轉檢測
        try:
            rotated_90 = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
            rotated_90_detections = self.detect(rotated_90, self.primary_model, self.config.face5_test_conf)
            
            # 轉換座標回原始方向
            for det in rotated_90_detections:
                new_x1 = det.y1
                new_y1 = w - det.x2
                new_x2 = det.y2
                new_y2 = w - det.x1
                
                all_detections.append(DetectionBox(
                    x1=int(new_x1), y1=int(new_y1),
                    x2=int(new_x2), y2=int(new_y2),
                    confidence=det.confidence, class_id=det.class_id
                ))
        except Exception as e:
            logger.error(f"90度旋轉檢測失敗: {e}")
            
        # 270度旋轉檢測
        try:
            rotated_270 = cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
            rotated_270_detections = self.detect(rotated_270, self.primary_model, self.config.face5_test_conf)
            
            # 轉換座標回原始方向
            for det in rotated_270_detections:
                new_x1 = h - det.y2
                new_y1 = det.x1
                new_x2 = h - det.y1
                new_y2 = det.x2
                
                all_detections.append(DetectionBox(
                    x1=int(new_x1), y1=int(new_y1),
                    x2=int(new_x2), y2=int(new_y2),
                    confidence=det.confidence, class_id=det.class_id
                ))
        except Exception as e:
            logger.error(f"270度旋轉檢測失敗: {e}")
            
        # 次要模型檢測（如果有）
        if self.secondary_model:
            secondary_detections = self.detect(image, self.secondary_model, self.config.face5_test_conf)
            all_detections.extend(secondary_detections)
            
        return all_detections
        
    def calculate_iou(self, box1: DetectionBox, box2: DetectionBox) -> float:
        """計算兩個檢測框的IoU"""
        inter_x1 = max(box1.x1, box2.x1)
        inter_y1 = max(box1.y1, box2.y1)
        inter_x2 = min(box1.x2, box2.x2)
        inter_y2 = min(box1.y2, box2.y2)
        
        inter_area = max(0, inter_x2 - inter_x1) * max(0, inter_y2 - inter_y1)
        box1_area = box1.area
        box2_area = box2.area
        union_area = box1_area + box2_area - inter_area
        
        return inter_area / (union_area + 1e-6)
        
    def merge_overlapping_boxes(self, detections: List[DetectionBox]) -> List[DetectionBox]:
        """合併重疊的檢測框"""
        if not detections:
            return []
            
        # 按置信度排序
        detections = sorted(detections, key=lambda x: x.confidence, reverse=True)
        merged = []
        used = set()
        
        for i, best in enumerate(detections):
            if i in used:
                continue
                
            group = [best]
            
            for j, detection in enumerate(detections):
                if j == i or j in used:
                    continue
                    
                # 只合併同類別的檢測框
                if (detection.class_id == best.class_id and 
                    self.calculate_iou(best, detection) > self.config.iou_threshold):
                    group.append(detection)
                    used.add(j)
                    
            # 合併同組的檢測框
            if len(group) > 1:
                x1 = min(det.x1 for det in group)
                y1 = min(det.y1 for det in group)
                x2 = max(det.x2 for det in group)
                y2 = max(det.y2 for det in group)
                conf = max(det.confidence for det in group)
                merged_box = DetectionBox(x1, y1, x2, y2, conf, best.class_id)
                merged.append(merged_box)
                used.add(i)
            else:
                merged.append(best)
                
        return merged
        
    def filter_large_boxes(self, detections: List[DetectionBox], image_shape: Tuple[int, int]) -> List[DetectionBox]:
        """過濾掉佔據圖像面積比例過大的檢測框"""
        if not detections:
            return []
            
        filtered = []
        total_area = image_shape[0] * image_shape[1]
        
        for detection in detections:
            area_ratio = detection.area / total_area
            
            if area_ratio <= self.config.max_area_ratio:
                filtered.append(detection)
            else:
                logger.debug(f"過濾過大檢測框: 面積比例 {area_ratio:.3f} > {self.config.max_area_ratio}")
                
        return filtered
        
    def filter_center_region(self, detections: List[DetectionBox], 
                           image_shape: Tuple[int, int]) -> List[DetectionBox]:
        """過濾圖像中心圓形區域的檢測框（第5面專用）"""
        if not detections:
            return []
            
        filtered = []
        h, w = image_shape
        center_x, center_y = w // 2, h // 2
        radius = int(min(w, h) * 0.35)
        
        for detection in detections:
            box_center_x, box_center_y = detection.center
            distance = ((box_center_x - center_x) ** 2 + (box_center_y - center_y) ** 2) ** 0.5
            
            if distance > radius:
                filtered.append(detection)
            else:
                logger.debug(f"過濾中心區域檢測框: 距離 {distance:.1f} <= {radius}")
                
        return filtered
        
    def apply_gaussian_blur(self, image: np.ndarray, kernel_size: Tuple[int, int] = DEFAULT_BLUR_KERNEL_SIZE) -> np.ndarray:
        """應用高斯模糊"""
        try:
            return cv2.GaussianBlur(image, kernel_size, 0)
        except Exception as e:
            logger.error(f"高斯模糊失敗: {e}")
            return image
            
    def process_and_get_blurred_regions(self, image: np.ndarray, face_id: int) -> Dict[str, Dict]:
        """
        處理圖像並獲取模糊區域
        
        Args:
            image: 輸入圖像
            face_id: 面ID (0-5)
            
        Returns:
            模糊區域字典
        """
        # 第4面（天空面）跳過檢測
        if face_id == 4:
            logger.debug("跳過第4面（天空面）檢測")
            return {}
            
        # 根據面ID選擇檢測策略
        if face_id == 5:
            # 第5面使用旋轉檢測
            detections = self.detect_face5_with_rotation(image)
        else:
            # 其他面使用雙模型檢測
            detections = self.detect_dual_model(image)
            
        # 後處理檢測結果
        detections = self.merge_overlapping_boxes(detections)
        detections = self.filter_large_boxes(detections, image.shape[:2])
        
        # 第5面額外過濾中心區域
        if face_id == 5:
            detections = self.filter_center_region(detections, image.shape[:2])
            
        # 創建模糊區域
        blur_regions = {}
        
        for i, detection in enumerate(detections):
            x1, y1, x2, y2 = detection.x1, detection.y1, detection.x2, detection.y2
            
            # 確保座標在圖像範圍內
            h, w = image.shape[:2]
            x1 = max(0, min(x1, w))
            y1 = max(0, min(y1, h))
            x2 = max(x1, min(x2, w))
            y2 = max(y1, min(y2, h))
            
            if x2 <= x1 or y2 <= y1:
                continue
                
            # 提取區域並應用高斯模糊
            try:
                roi = image[y1:y2, x1:x2]
                if roi.size == 0:
                    continue
                    
                blurred_roi = self.apply_gaussian_blur(roi)
                
                # 🔥 將模糊效果直接應用到原圖像
                image[y1:y2, x1:x2] = blurred_roi
                
                # 可控的檢測框繪製
                if self.config.enable_draw:
                    cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), thickness=3)
                    score_text = f"Score: {detection.confidence:.3f}"
                    font = cv2.FONT_HERSHEY_SIMPLEX
                    cv2.putText(image, score_text, (x1, y1-10), font, 0.7, (255, 255, 255), 2)
                
                region_name = f'region{i+1}'
                blur_regions[region_name] = {
                    'box': [x1, y1, x2, y2, detection.confidence, detection.class_id],
                    'image': blurred_roi  # 保留模糊後的ROI用於統計
                }
                
            except Exception as e:
                logger.error(f"創建模糊區域失敗: {e}")
                continue
                
        return blur_regions
        
    def batch_process_faces(self, face_images: Dict[int, np.ndarray]) -> Dict[int, Dict[str, Dict]]:
        """
        批次處理立方體面
        
        Args:
            face_images: 面圖像字典 {face_id: image}
            
        Returns:
            所有面的模糊區域字典
        """
        logger.info(f"開始批次檢測 {len(face_images)} 個面")
        start_time = time.time()
        
        results = {}
        
        for face_id, image in face_images.items():
            try:
                blur_regions = self.process_and_get_blurred_regions(image, face_id)
                results[face_id] = blur_regions
                
                logger.debug(f"面 {face_id} 處理完成: {len(blur_regions)} 個模糊區域")
                
            except Exception as e:
                logger.error(f"處理面 {face_id} 時發生錯誤: {e}")
                results[face_id] = {}
                
        processing_time = time.time() - start_time
        total_regions = sum(len(regions) for regions in results.values())
        
        logger.info(f"批次處理完成: {total_regions} 個模糊區域, 耗時 {processing_time:.2f}s")
        
        return results
        
    def get_detection_statistics(self, batch_results: Dict[int, Dict[str, Dict]]) -> Dict:
        """
        獲取檢測統計信息
        
        Args:
            batch_results: 批次處理結果
            
        Returns:
            統計信息字典
        """
        stats = {
            'total_faces': len(batch_results),
            'total_blur_regions': 0,
            'regions_by_face': {},
            'detection_by_class': {0: 0, 1: 0},  # 0: face, 1: plate
            'average_confidence': 0,
            'processed_faces': 0,
            'skipped_faces': 0
        }
        
        all_confidences = []
        
        for face_id, blur_regions in batch_results.items():
            region_count = len(blur_regions)
            stats['regions_by_face'][face_id] = region_count
            
            if region_count == 0 and face_id == 4:
                stats['skipped_faces'] += 1
            else:
                stats['processed_faces'] += 1
                
            stats['total_blur_regions'] += region_count
            
            for region_name, region_data in blur_regions.items():
                box = region_data['box']
                confidence = box[4]
                class_id = box[5]
                
                stats['detection_by_class'][class_id] += 1
                all_confidences.append(confidence)
                
        # 計算平均置信度
        if all_confidences:
            stats['average_confidence'] = sum(all_confidences) / len(all_confidences)
            
        return stats
    
    def __del__(self):
        """析構函數 - 清理資源"""
        self.cleanup()
    
    def cleanup(self):
        """清理所有資源 - 解決記憶體洩漏問題"""
        try:
            # 清理YOLO模型（主要記憶體洩漏源）
            if hasattr(self, 'primary_model') and self.primary_model is not None:
                # 移動模型到CPU並刪除引用
                try:
                    self.primary_model.to('cpu')
                except:
                    pass
                del self.primary_model
                self.primary_model = None
                logger.debug("已清理主要YOLO模型")
            
            if hasattr(self, 'secondary_model') and self.secondary_model is not None:
                try:
                    self.secondary_model.to('cpu')
                except:
                    pass
                del self.secondary_model
                self.secondary_model = None
                logger.debug("已清理次要YOLO模型")
            
            # 清理GPU記憶體（關鍵優化）
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                    if hasattr(torch.cuda, 'reset_peak_memory_stats'):
                        torch.cuda.reset_peak_memory_stats()
                    logger.debug("已清理GPU快取")
            except Exception as e:
                logger.debug(f"GPU清理失敗: {e}")
            
            # 清理高級記憶體管理器
            if hasattr(self, 'advanced_memory_manager') and self.advanced_memory_manager:
                try:
                    self.advanced_memory_manager.cleanup()
                    self.advanced_memory_manager = None
                    logger.debug("已清理高級記憶體管理器")
                except Exception as e:
                    logger.debug(f"高級記憶體管理器清理失敗: {e}")
            
            # 停止性能監控器
            if hasattr(self, 'performance_monitor') and self.performance_monitor:
                try:
                    self.performance_monitor.stop_monitoring()
                    self.performance_monitor = None
                    logger.debug("已停止性能監控器")
                except Exception as e:
                    logger.debug(f"性能監控器停止失敗: {e}")
            
            # 強制垃圾回收
            import gc
            collected = gc.collect()
            if collected > 0:
                logger.debug(f"檢測器清理: 回收了{collected}個對象")
                
        except Exception as e:
            logger.debug(f"檢測器資源清理時發生錯誤: {e}")
    
    def reset_cache(self):
        """重置快取 - 用於批次處理中的記憶體優化"""
        try:
            # 清理GPU快取
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                logger.debug("檢測器快取已重置")
            
            # 重置高級記憶體管理器快取
            if hasattr(self, 'advanced_memory_manager') and self.advanced_memory_manager:
                try:
                    if hasattr(self.advanced_memory_manager, 'clear_cache'):
                        self.advanced_memory_manager.clear_cache()
                        logger.debug("高級記憶體管理器快取已重置")
                    elif hasattr(self.advanced_memory_manager, 'cleanup'):
                        self.advanced_memory_manager.cleanup()
                        logger.debug("使用cleanup方法重置高級記憶體管理器")
                    else:
                        logger.debug(f"高級記憶體管理器類型不支援清理: {type(self.advanced_memory_manager)}")
                except Exception as e:
                    logger.debug(f"高級記憶體管理器清理失敗: {e}")
            
            # 執行垃圾回收
            import gc
            gc.collect()
            
        except Exception as e:
            logger.debug(f"檢測器快取重置失敗: {e}")


def main():
    """模組使用示例"""
    print("=== 快速檢測器模組 ===")
    print("\n核心功能：")
    print("1. 雙模型協同檢測")
    print("2. 第4面跳過，第5面特殊處理（90°/270°旋轉+中心過濾）")
    print("3. 過濾過大檢測框")
    print("4. 合併重疊檢測框") 
    print("5. 高斯模糊處理")
    print("6. 批次處理支援")
    print("7. 統計信息收集")
    print("8. 可控的結果繪製")
    
    print("\n使用示例：")
    print("```python")
    print("from detection.detector import Detector, DetectionConfig")
    print("import cv2")
    print()
    print("# 創建配置")
    print("config = DetectionConfig(")
    print("    primary_model_path='models/primary.pt',")
    print("    secondary_model_path='models/secondary.pt',")
    print("    conf_threshold=0.05,")
    print("    iou_threshold=0.3,")
    print("    max_area_ratio=0.3,")
    print("    device='cuda',")
    print("    enable_draw=True")
    print(")")
    print()
    print("# 創建檢測器")
    print("detector = Detector(config)")
    print()
    print("# 單面處理")
    print("image = cv2.imread('face_image.jpg')")
    print("blur_regions = detector.process_and_get_blurred_regions(image, face_id=0)")
    print("print(f'生成 {len(blur_regions)} 個模糊區域')")
    print()
    print("# 批次處理")
    print("face_images = {")
    print("    0: cv2.imread('face_0.jpg'),")
    print("    1: cv2.imread('face_1.jpg'),")
    print("    4: cv2.imread('face_4.jpg'),  # 將被跳過")
    print("    5: cv2.imread('face_5.jpg')   # 使用特殊策略")
    print("}")
    print()
    print("batch_results = detector.batch_process_faces(face_images)")
    print("for face_id, regions in batch_results.items():")
    print("    print(f'面 {face_id}: {len(regions)} 個模糊區域')")
    print()
    print("# 獲取統計信息")
    print("stats = detector.get_detection_statistics(batch_results)")
    print("print(f'總模糊區域: {stats[\"total_blur_regions\"]}')")
    print("print(f'平均置信度: {stats[\"average_confidence\"]:.3f}')")
    print("print(f'處理面數: {stats[\"processed_faces\"]}')")
    print("print(f'跳過面數: {stats[\"skipped_faces\"]}')")
    print("```")
    
    print("\n特殊面處理策略：")
    print("- 第4面（天空）: 自動跳過檢測")
    print("- 第5面（地面）: 90°/270°旋轉檢測 + 中心圓形區域過濾")
    print("- 其他面: 標準雙模型檢測")
    
    print("\n性能優化特性：")
    print("- GPU自動混合精度（AMP）")
    print("- 高效的檢測框合併算法")
    print("- 記憶體友好的批次處理")
    print("- 快速的IoU計算")
    
    print("快速檢測器測試完成")


if __name__ == "__main__":
    main()