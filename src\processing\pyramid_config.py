#!/usr/bin/env python3
"""
Pyramid Configuration - 金字塔配置模組

Author: AI 部門 - 全景處理團隊
Date: 2025-01-25
Version: 2.0.0
"""

from dataclasses import dataclass
from enum import Enum
from typing import Optional

# 嘗試導入配置
try:
    from config.settings import FAST_CONFIG
except ImportError:
    class _DefaultConfig:
        class pyramid:
            max_levels = 8
            tile_size = 512
            quality = 85
    FAST_CONFIG = _DefaultConfig()


class PyramidQuality(Enum):
    """金字塔品質等級"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ULTRA = "ultra"


class TileFormat(Enum):
    """瓦片格式"""
    JPEG = "JPEG"
    PNG = "PNG"
    WEBP = "WEBP"


@dataclass
class PyramidConfig:
    """金字塔生成配置"""
    max_levels: int = 8
    tile_size: int = 512
    quality: PyramidQuality = PyramidQuality.HIGH
    tile_format: TileFormat = TileFormat.JPEG
    enable_threading: bool = True
    max_workers: int = 4
    output_dir: Optional[str] = None


@dataclass
class ProcessingStats:
    """處理統計信息"""
    total_tiles: int = 0
    processed_tiles: int = 0
    failed_tiles: int = 0
    start_time: float = 0.0
    end_time: float = 0.0


@dataclass
class SceneReport:
    """場景報告"""
    scene_name: str
    total_faces: int = 6
    processed_faces: int = 0
    total_tiles: int = 0
    processing_time: float = 0.0
    success: bool = False
    error_message: Optional[str] = None


def create_default_config() -> PyramidConfig:
    """創建默認配置"""
    return PyramidConfig(
        max_levels=FAST_CONFIG.pyramid.max_levels,
        tile_size=FAST_CONFIG.pyramid.tile_size,
        quality=PyramidQuality.HIGH
    )


if __name__ == "__main__":
    config = create_default_config()
    print(f"配置: {config}")
