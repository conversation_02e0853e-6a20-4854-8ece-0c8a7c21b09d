"""
統一路徑掃描器模組
提供各種路徑和檔案掃描功能的統一實現
"""
import glob
import logging
import os
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Protocol

logger = logging.getLogger(__name__)


class ScanMode(Enum):
    """掃描模式"""
    FILES_ONLY = "files_only"
    DIRS_ONLY = "dirs_only"
    ALL = "all"


class FilterMode(Enum):
    """過濾模式"""
    INCLUDE = "include"
    EXCLUDE = "exclude"


@dataclass
class ScanConfig:
    """掃描配置"""
    recursive: bool = False
    max_depth: int = -1  # -1 表示無限制
    follow_symlinks: bool = False
    case_sensitive: bool = True
    scan_mode: ScanMode = ScanMode.ALL
    filter_mode: FilterMode = FilterMode.INCLUDE
    extensions: list[str] = field(default_factory=list)
    patterns: list[str] = field(default_factory=list)
    size_limits: dict[str, int] = field(default_factory=dict)  # min_size, max_size


@dataclass
class ScanResult:
    """掃描結果"""
    path: str
    name: str
    is_dir: bool
    size: int = 0
    modified_time: float = 0.0
    depth: int = 0
    metadata: dict[str, Any] = field(default_factory=dict)


class ScanFilter(Protocol):
    """掃描過濾器協議"""
    
    def should_include(self, path: str, is_dir: bool) -> bool:
        """判斷是否應該包含此路徑"""
        ...


class ExtensionFilter:
    """副檔名過濾器"""
    
    def __init__(self, extensions: list[str], mode: FilterMode = FilterMode.INCLUDE):
        self.extensions = [ext.lower() if not ext.startswith('.') else ext[1:].lower() 
                          for ext in extensions]
        self.mode = mode
    
    def should_include(self, path: str, is_dir: bool) -> bool:
        if is_dir:
            return True
        
        ext = Path(path).suffix.lower()
        if ext.startswith('.'):
            ext = ext[1:]
        
        has_ext = ext in self.extensions
        return has_ext if self.mode == FilterMode.INCLUDE else not has_ext


class PatternFilter:
    """模式過濾器"""
    
    def __init__(self, patterns: list[str], mode: FilterMode = FilterMode.INCLUDE):
        self.patterns = patterns
        self.mode = mode
    
    def should_include(self, path: str, is_dir: bool) -> bool:
        name = os.path.basename(path)
        
        for pattern in self.patterns:
            if pattern in name:
                return self.mode == FilterMode.INCLUDE
        
        return self.mode == FilterMode.EXCLUDE


class SizeFilter:
    """大小過濾器"""
    
    def __init__(self, min_size: int = 0, max_size: int = -1):
        self.min_size = min_size
        self.max_size = max_size
    
    def should_include(self, path: str, is_dir: bool) -> bool:
        if is_dir:
            return True
        
        try:
            size = os.path.getsize(path)
            if size < self.min_size:
                return False
            if self.max_size > 0 and size > self.max_size:
                return False
            return True
        except OSError:
            return False


class PathScanner:
    """統一路徑掃描器"""
    
    def __init__(self, config: ScanConfig | None = None):
        """
        初始化路徑掃描器
        
        Args:
            config: 掃描配置
        """
        self.config = config or ScanConfig()
        self.filters: list[ScanFilter] = []
        self._setup_filters()
    
    def _setup_filters(self):
        """設置過濾器"""
        self.filters.clear()
        
        # 副檔名過濾器
        if self.config.extensions:
            self.filters.append(ExtensionFilter(
                self.config.extensions, 
                self.config.filter_mode
            ))
        
        # 模式過濾器
        if self.config.patterns:
            self.filters.append(PatternFilter(
                self.config.patterns,
                self.config.filter_mode
            ))
        
        # 大小過濾器
        if self.config.size_limits:
            min_size = self.config.size_limits.get('min_size', 0)
            max_size = self.config.size_limits.get('max_size', -1)
            if min_size > 0 or max_size > 0:
                self.filters.append(SizeFilter(min_size, max_size))
    
    def add_filter(self, filter_func: ScanFilter):
        """添加自定義過濾器"""
        self.filters.append(filter_func)
    
    def scan(self, base_path: str) -> list[ScanResult]:
        """
        掃描路徑
        
        Args:
            base_path: 基礎路徑
            
        Returns:
            掃描結果列表
        """
        if not os.path.exists(base_path):
            logger.warning(f"路徑不存在: {base_path}")
            return []
        
        results = []
        
        try:
            if self.config.recursive:
                results = self._scan_recursive(base_path, 0)
            else:
                results = self._scan_single_level(base_path, 0)
        except Exception as e:
            logger.error(f"掃描路徑失敗 {base_path}: {e}")
        
        return results
    
    def _scan_recursive(self, path: str, depth: int) -> list[ScanResult]:
        """遞歸掃描"""
        results = []
        
        # 檢查深度限制
        if self.config.max_depth >= 0 and depth > self.config.max_depth:
            return results
        
        try:
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                
                # 檢查符號鏈接
                if os.path.islink(item_path) and not self.config.follow_symlinks:
                    continue
                
                is_dir = os.path.isdir(item_path)
                
                # 應用過濾器
                if not self._should_include(item_path, is_dir):
                    continue
                
                # 檢查掃描模式
                if not self._matches_scan_mode(is_dir):
                    continue
                
                # 創建結果
                result = self._create_scan_result(item_path, depth)
                if result:
                    results.append(result)
                
                # 遞歸掃描子目錄
                if is_dir and (self.config.max_depth < 0 or depth < self.config.max_depth):
                    sub_results = self._scan_recursive(item_path, depth + 1)
                    results.extend(sub_results)
        
        except Exception as e:
            logger.error(f"掃描目錄失敗 {path}: {e}")
        
        return results
    
    def _scan_single_level(self, path: str, depth: int) -> list[ScanResult]:
        """單層掃描"""
        results = []
        
        try:
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                
                # 檢查符號鏈接
                if os.path.islink(item_path) and not self.config.follow_symlinks:
                    continue
                
                is_dir = os.path.isdir(item_path)
                
                # 應用過濾器
                if not self._should_include(item_path, is_dir):
                    continue
                
                # 檢查掃描模式
                if not self._matches_scan_mode(is_dir):
                    continue
                
                # 創建結果
                result = self._create_scan_result(item_path, depth)
                if result:
                    results.append(result)
        
        except Exception as e:
            logger.error(f"掃描目錄失敗 {path}: {e}")
        
        return results
    
    def _should_include(self, path: str, is_dir: bool) -> bool:
        """檢查是否應該包含此路徑"""
        for filter_func in self.filters:
            if not filter_func.should_include(path, is_dir):
                return False
        return True
    
    def _matches_scan_mode(self, is_dir: bool) -> bool:
        """檢查是否符合掃描模式"""
        if self.config.scan_mode == ScanMode.FILES_ONLY:
            return not is_dir
        elif self.config.scan_mode == ScanMode.DIRS_ONLY:
            return is_dir
        return True  # ScanMode.ALL
    
    def _create_scan_result(self, path: str, depth: int) -> ScanResult | None:
        """創建掃描結果"""
        try:
            stat = os.stat(path)
            is_dir = os.path.isdir(path)
            
            return ScanResult(
                path=path,
                name=os.path.basename(path),
                is_dir=is_dir,
                size=stat.st_size if not is_dir else 0,
                modified_time=stat.st_mtime,
                depth=depth
            )
        except Exception as e:
            logger.error(f"獲取檔案信息失敗 {path}: {e}")
            return None
    
    @staticmethod
    def scan_by_pattern(base_path: str, pattern: str, is_dir: bool = True, 
                       case_sensitive: bool = True) -> list[str]:
        """
        通用模式掃描方法
        
        Args:
            base_path: 基礎路徑
            pattern: 匹配模式
            is_dir: 是否只掃描目錄
            case_sensitive: 是否區分大小寫
            
        Returns:
            匹配的路徑列表
        """
        if not os.path.exists(base_path):
            logger.warning(f"路徑不存在: {base_path}")
            return []
        
        config = ScanConfig(
            scan_mode=ScanMode.DIRS_ONLY if is_dir else ScanMode.ALL,
            patterns=[pattern],
            case_sensitive=case_sensitive
        )
        
        scanner = PathScanner(config)
        results = scanner.scan(base_path)
        
        return [result.path for result in results]
    
    @staticmethod
    def scan_district_folders(base_path: str) -> list[str]:
        """
        掃描包含「區」字的目錄
        
        Args:
            base_path: 基礎路徑
            
        Returns:
            區域資料夾路徑列表
        """
        return PathScanner.scan_by_pattern(base_path, "區", is_dir=True)
    
    @staticmethod
    def get_valid_image_files(directory: str, extensions: list[str], 
                             pattern: str = "*") -> list[str]:
        """
        獲取有效的圖像檔案
        
        Args:
            directory: 目錄路徑
            extensions: 支援的副檔名列表
            pattern: 檔案名模式
            
        Returns:
            有效圖像檔案路徑列表
        """
        if not os.path.exists(directory):
            return []
        
        # 使用 glob 方式保持原有行為
        image_files = []
        for ext in extensions:
            search_pattern = os.path.join(directory, f"{pattern}{ext}")
            files = glob.glob(search_pattern)
            image_files.extend(files)
        
        return sorted(image_files)
    
    @staticmethod
    def find_resource_path(base_path: str, resource_type: str, 
                          city_code: str | None = None) -> str | None:
        """
        查找資源檔案路徑
        
        Args:
            base_path: 基礎路徑
            resource_type: 資源類型
            city_code: 城市代號
            
        Returns:
            資源檔案路徑
        """
        possible_dirs = [
            os.path.join(base_path, resource_type),
            os.path.join(base_path, resource_type.rstrip('s')),
            os.path.join(os.path.dirname(base_path), resource_type),
            os.path.join(os.path.dirname(base_path), resource_type.rstrip('s'))
        ]
        
        for resource_dir in possible_dirs:
            if not os.path.exists(resource_dir):
                continue
            
            found_path = PathScanner._search_resource_in_dir(
                resource_dir, resource_type, city_code)
            if found_path:
                return found_path
        
        logger.warning(f"未找到 {resource_type} 資源")
        return None
    
    @staticmethod
    def _search_resource_in_dir(directory: str, resource_type: str,
                               city_code: str | None = None) -> str | None:
        """在指定目錄中搜索資源檔案"""
        try:
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                if os.path.isfile(item_path):
                    # 根據資源類型檢查檔案
                    if resource_type == 'models' and item.endswith('.pt'):
                        logger.info(f"找到模型檔案: {item_path}")
                        return item_path
                    
                    elif resource_type == 'logo' and city_code:
                        if city_code.lower() in item.lower():
                            logger.info(f"找到 {city_code} 的 logo: {item_path}")
                            return item_path
                
                elif os.path.isdir(item_path):
                    # 遞歸搜索子目錄
                    found = PathScanner._search_resource_in_dir(
                        item_path, resource_type, city_code)
                    if found:
                        return found
        
        except Exception as e:
            logger.error(f"搜索資源檔案失敗: {e}")
        
        return None
    
    @staticmethod
    def is_district_structure(folder_path: str) -> bool:
        """
        檢查是否為區域結構
        
        Args:
            folder_path: 資料夾路徑
            
        Returns:
            是否為區域結構
        """
        return len(PathScanner.scan_district_folders(folder_path)) > 0
    
    @staticmethod
    def count_items_in_directory(directory: str, item_type: str = "all") -> int:
        """
        計算目錄中的項目數量
        
        Args:
            directory: 目錄路徑
            item_type: 項目類型 ("files", "dirs", "all")
            
        Returns:
            項目數量
        """
        if not os.path.exists(directory):
            return 0
        
        try:
            items = os.listdir(directory)
            
            if item_type == "files":
                return sum(1 for item in items 
                          if os.path.isfile(os.path.join(directory, item)))
            elif item_type == "dirs":
                return sum(1 for item in items 
                          if os.path.isdir(os.path.join(directory, item)))
            else:  # "all"
                return len(items)
        
        except Exception as e:
            logger.error(f"計算目錄項目失敗 {directory}: {e}")
            return 0
    
    @staticmethod
    def validate_file_path(file_path: str, expected_extensions: list[str] | None = None) -> bool:
        """
        驗證檔案路徑的有效性
        
        Args:
            file_path: 檔案路徑
            expected_extensions: 期望的檔案副檔名列表
            
        Returns:
            是否有效
        """
        if not file_path or not os.path.exists(file_path):
            return False
        
        if not os.path.isfile(file_path):
            return False
        
        if expected_extensions:
            ext = os.path.splitext(file_path)[1].lower()
            return ext in expected_extensions
        
        return True


class AdvancedPathScanner(PathScanner):
    """進階路徑掃描器 - 支援更複雜的掃描需求"""
    
    def __init__(self, config: ScanConfig | None = None):
        super().__init__(config)
        self.custom_validators: list[Callable[[str], bool]] = []
    
    def add_validator(self, validator: Callable[[str], bool]):
        """添加自定義驗證器"""
        self.custom_validators.append(validator)
    
    def scan_with_metadata(self, base_path: str) -> list[ScanResult]:
        """帶有詳細元數據的掃描"""
        results = self.scan(base_path)
        
        for result in results:
            # 添加額外元數據
            result.metadata = self._collect_metadata(result.path)
        
        return results
    
    def _collect_metadata(self, path: str) -> dict[str, Any]:
        """收集檔案元數據"""
        metadata = {}
        
        try:
            if os.path.isfile(path):
                # 檔案特定元數據
                ext = os.path.splitext(path)[1].lower()
                metadata['extension'] = ext
                metadata['basename'] = os.path.splitext(os.path.basename(path))[0]
                
                # 執行自定義驗證器
                for validator in self.custom_validators:
                    try:
                        metadata[f'validator_{validator.__name__}'] = validator(path)
                    except Exception as e:
                        logger.error(f"驗證器執行失敗: {e}")
            
            elif os.path.isdir(path):
                # 目錄特定元數據
                metadata['item_count'] = PathScanner.count_items_in_directory(path)
                metadata['has_subdirs'] = any(
                    os.path.isdir(os.path.join(path, item))
                    for item in os.listdir(path)
                )
        
        except Exception as e:
            logger.error(f"收集元數據失敗 {path}: {e}")
        
        return metadata


def create_image_scanner(extensions: list[str]) -> PathScanner:
    """創建圖像檔案掃描器"""
    config = ScanConfig(
        scan_mode=ScanMode.FILES_ONLY,
        extensions=extensions,
        filter_mode=FilterMode.INCLUDE
    )
    return PathScanner(config)


def create_directory_scanner(pattern: str | None = None) -> PathScanner:
    """創建目錄掃描器"""
    config = ScanConfig(
        scan_mode=ScanMode.DIRS_ONLY,
        patterns=[pattern] if pattern else [],
        filter_mode=FilterMode.INCLUDE if pattern else FilterMode.EXCLUDE
    )
    return PathScanner(config)


def create_recursive_scanner(max_depth: int = -1) -> PathScanner:
    """創建遞歸掃描器"""
    config = ScanConfig(
        recursive=True,
        max_depth=max_depth
    )
    return PathScanner(config)