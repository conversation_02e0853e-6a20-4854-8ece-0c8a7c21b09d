#!/usr/bin/env python3
"""
Simple pyramid generation test without unicode characters
"""

import os
import sys
import numpy as np
import cv2

# Add src path
sys.path.insert(0, 'src')

from processing.pyramid_generator import PyramidGenerator, PyramidConfig, PyramidQuality

def create_test_faces():
    """Create test cube faces"""
    faces = {}
    face_names = ['F', 'R', 'B', 'L', 'U', 'D']
    colors = [
        (255, 0, 0),    # F - Red
        (0, 255, 0),    # R - Green  
        (0, 0, 255),    # B - <PERSON>
        (255, 255, 0),  # L - Yellow
        (255, 0, 255),  # U - Purple
        (0, 255, 255),  # D - Cyan
    ]
    
    for i, name in enumerate(face_names):
        image = np.zeros((2048, 2048, 3), dtype=np.uint8)
        image[:] = colors[i]
        
        # Add text label
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(image, f"Face {name}", (800, 1000), font, 8, (255, 255, 255), 12)
        cv2.putText(image, f"({i})", (900, 1200), font, 6, (255, 255, 255), 8)
        
        faces[name] = image
    
    return faces

def test_pyramid():
    """Test pyramid generation"""
    print("Starting pyramid test...")
    
    try:
        # Create test data
        cube_faces = create_test_faces()
        thumbnail = cv2.resize(cube_faces['F'], (512, 512))
        
        # Configure generator
        config = PyramidConfig(
            max_levels=3,
            tile_size=512,
            quality=PyramidQuality.HIGH,
            pyramid_levels=[611, 1222, 2445]
        )
        
        generator = PyramidGenerator(config)
        
        # Create output directory
        output_dir = "test_output"
        scene_name = "test_scene"
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate pyramid
        print("Generating pyramid tiles...")
        success = generator.generate_pyramid(
            cube_faces=cube_faces,
            thumbnail=thumbnail,
            output_dir=output_dir,
            scene_name=scene_name
        )
        
        if success:
            print("Pyramid generation successful!")
            
            # Verify output
            scene_path = os.path.join(output_dir, scene_name)
            
            # Check html5 directory
            html5_path = os.path.join(scene_path, "html5")
            if os.path.exists(html5_path):
                print("html5 directory created")
                
                # Check cube faces
                for i in range(6):
                    face_file = os.path.join(html5_path, f"{i}.jpg")
                    if os.path.exists(face_file):
                        print(f"Face {i}.jpg exists")
            
            # Check pyramid directories
            for face_id in range(6):
                face_dir = os.path.join(scene_path, str(face_id))
                if os.path.exists(face_dir):
                    print(f"Face {face_id} pyramid directory exists")
                    
                    # Check levels
                    for level in range(3):
                        level_dir = os.path.join(face_dir, str(level))
                        if os.path.exists(level_dir):
                            tiles = [f for f in os.listdir(level_dir) if f.endswith('.jpg')]
                            print(f"  Level {level}: {len(tiles)} tiles")
            
            # Check thumbnail
            thumbnail_path = os.path.join(scene_path, "thumbnail.jpg")
            if os.path.exists(thumbnail_path):
                print("Thumbnail exists")
                
            # Count total files
            total_files = 0
            for root, dirs, files in os.walk(scene_path):
                for file in files:
                    if file.endswith('.jpg'):
                        total_files += 1
            
            print(f"Total files generated: {total_files}")
            print(f"Output directory: {scene_path}")
            print("Test completed successfully!")
            return True
            
        else:
            print("Pyramid generation failed")
            return False
            
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Simple pyramid generation test")
    print("=" * 40)
    success = test_pyramid()
    if success:
        print("ALL TESTS PASSED!")
    else:
        print("TESTS FAILED!")
    print("=" * 40)