"""
統一的異常處理類別模組

定義 log_utils 模組中使用的所有自定義異常類型，
提供一致的錯誤處理和更好的錯誤分類。
"""


class LogUtilsError(Exception):
    """
    日誌工具基礎異常
    
    所有 log_utils 模組的自定義異常都應該繼承此類。
    """
    pass


class ConfigurationError(LogUtilsError):
    """
    配置相關錯誤
    
    當日誌配置不正確或驗證失敗時拋出。
    """
    pass


class HandlerCreationError(LogUtilsError):
    """
    處理器創建錯誤
    
    當無法創建日誌處理器時拋出。
    """
    pass


class FormatterError(LogUtilsError):
    """
    格式器相關錯誤
    
    當日誌格式器創建或使用失敗時拋出。
    """
    pass


class ImportDependencyError(LogUtilsError):
    """
    依賴導入錯誤
    
    當必要的依賴模組無法導入時拋出。
    """
    pass


class LogManagerError(LogUtilsError):
    """
    日誌管理器錯誤
    
    當日誌管理器操作失敗時拋出。
    """
    pass


class ValidationError(ConfigurationError):
    """
    驗證錯誤
    
    當配置或參數驗證失敗時拋出。
    """
    
    def __init__(self, message: str, validation_errors: list = None):
        super().__init__(message)
        self.validation_errors = validation_errors or []