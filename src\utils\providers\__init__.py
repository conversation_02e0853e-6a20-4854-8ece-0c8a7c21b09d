"""
功能提供者模組
提供各種系統功能的統一接口實現
"""

# 記憶體提供者
from .memory_providers import (
    MemoryProvider,
    SystemMemoryProvider,
    CudaMemoryProvider,
    MemoryProviderFactory,
    CompositeMemoryProvider,
)

# 性能提供者
from .performance_providers import (
    PerformanceProvider,
    SystemPerformanceProvider,
    GPUPerformanceProvider,
)

# 處理提供者
from .processing_providers import (
    ProcessingProvider,
    SequentialProcessingProvider,
    ThreadPoolProcessingProvider,
    ProcessPoolProcessingProvider,
    AsyncProcessingProvider,
    ProcessingProviderFactory,
    HybridProcessingProvider,
    ProcessingMode,
    ProcessingTask,
    ProcessingResult,
)

__all__ = [
    # 記憶體提供者
    "MemoryProvider",
    "SystemMemoryProvider", 
    "CudaMemoryProvider",
    "MemoryProviderFactory",
    "CompositeMemoryProvider",
    
    # 性能提供者
    "PerformanceProvider",
    "SystemPerformanceProvider",
    "GPUPerformanceProvider",
    
    # 處理提供者
    "ProcessingProvider",
    "SequentialProcessingProvider",
    "ThreadPoolProcessingProvider", 
    "ProcessPoolProcessingProvider",
    "AsyncProcessingProvider",
    "ProcessingProviderFactory",
    "HybridProcessingProvider",
    "ProcessingMode",
    "ProcessingTask",
    "ProcessingResult",
]