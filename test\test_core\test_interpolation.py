"""
Tests for core.interpolation module
"""

import tempfile
import threading
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import numpy as np
import pytest


# Mock external dependencies
def mock_jit(*args, **kwargs):
    def decorator(func):
        return func

    return decorator


# Mock GPU libraries
mock_cupy = Mock()
mock_torch = Mock()
mock_pyopencl = Mock()

# Patch imports before importing the module
with patch.dict(
    "sys.modules", {"cupy": mock_cupy, "torch": mock_torch, "pyopencl": mock_pyopencl}
):
    with patch("numba.jit", side_effect=mock_jit):
        with patch("cv2.resize") as mock_cv2_resize:
            mock_cv2_resize.return_value = np.random.randint(
                0, 255, (100, 100, 3), dtype=np.uint8
            )
            from core.interpolation import (AdvancedInterpolator,
                                            GPUInterpolator,
                                            InterpolationCache,
                                            InterpolationMethod,
                                            MultipleInterpolator)


class TestInterpolationMethod:
    """Test InterpolationMethod enumeration"""

    def test_interpolation_method_count(self):
        """Test that we have all 38 interpolation methods"""
        methods = list(InterpolationMethod)
        assert len(methods) == 38

    def test_interpolation_method_values(self):
        """Test some key interpolation method values"""
        assert InterpolationMethod.NEAREST.value == "nearest"
        assert InterpolationMethod.LINEAR.value == "linear"
        assert InterpolationMethod.CUBIC.value == "cubic"
        assert InterpolationMethod.LANCZOS.value == "lanczos"

    def test_interpolation_method_uniqueness(self):
        """Test that all interpolation method values are unique"""
        values = [method.value for method in InterpolationMethod]
        assert len(values) == len(set(values))


class TestAdvancedInterpolator:
    """Test AdvancedInterpolator class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.interpolator = AdvancedInterpolator()
        self.test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        self.target_size = (200, 200)

    def test_init(self):
        """Test AdvancedInterpolator initialization"""
        assert isinstance(self.interpolator, AdvancedInterpolator)
        assert hasattr(self.interpolator, "method")
        assert hasattr(self.interpolator, "cache_enabled")

    def test_init_with_parameters(self):
        """Test AdvancedInterpolator initialization with parameters"""
        interpolator = AdvancedInterpolator(
            method=InterpolationMethod.CUBIC, cache_enabled=False, gpu_enabled=False
        )
        assert interpolator.method == InterpolationMethod.CUBIC
        assert interpolator.cache_enabled is False

    @patch("cv2.resize")
    def test_interpolate_basic(self, mock_resize):
        """Test basic interpolation functionality"""
        # Mock cv2.resize to return expected size
        mock_resize.return_value = np.random.randint(
            0, 255, (*self.target_size, 3), dtype=np.uint8
        )

        result = self.interpolator.interpolate(self.test_image, self.target_size)

        assert result.shape == (*self.target_size, 3)
        assert mock_resize.called

    def test_interpolate_different_methods(self):
        """Test interpolation with different methods"""
        methods_to_test = [
            InterpolationMethod.NEAREST,
            InterpolationMethod.LINEAR,
            InterpolationMethod.CUBIC,
        ]

        for method in methods_to_test:
            interpolator = AdvancedInterpolator(method=method)

            # Should not raise exceptions
            with patch("cv2.resize") as mock_resize:
                mock_resize.return_value = np.random.randint(
                    0, 255, (*self.target_size, 3), dtype=np.uint8
                )
                result = interpolator.interpolate(self.test_image, self.target_size)
                assert result.shape == (*self.target_size, 3)

    def test_interpolate_grayscale(self):
        """Test interpolation with grayscale images"""
        gray_image = np.random.randint(0, 255, (100, 100), dtype=np.uint8)

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, self.target_size, dtype=np.uint8
            )
            result = self.interpolator.interpolate(gray_image, self.target_size)
            assert result.shape == self.target_size

    def test_interpolate_invalid_inputs(self):
        """Test interpolation with invalid inputs"""
        # Invalid target size
        with pytest.raises((ValueError, TypeError)):
            self.interpolator.interpolate(self.test_image, (-1, -1))

        # Empty image
        empty_image = np.array([])
        with pytest.raises((ValueError, IndexError)):
            self.interpolator.interpolate(empty_image, self.target_size)

    def test_set_method(self):
        """Test setting interpolation method"""
        self.interpolator.set_method(InterpolationMethod.LANCZOS)
        assert self.interpolator.method == InterpolationMethod.LANCZOS

    def test_get_supported_methods(self):
        """Test getting supported methods"""
        methods = self.interpolator.get_supported_methods()
        assert isinstance(methods, list)
        assert len(methods) > 0
        assert all(isinstance(method, InterpolationMethod) for method in methods)

    @patch("cv2.resize", side_effect=Exception("CV2 Error"))
    def test_error_handling(self, mock_resize):
        """Test error handling in interpolation"""
        with pytest.raises(Exception):
            self.interpolator.interpolate(self.test_image, self.target_size)


class TestMultipleInterpolator:
    """Test MultipleInterpolator class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.multi_interpolator = MultipleInterpolator()
        self.test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        self.target_size = (200, 200)

    def test_init(self):
        """Test MultipleInterpolator initialization"""
        assert isinstance(self.multi_interpolator, MultipleInterpolator)

    def test_compare_methods(self):
        """Test comparing multiple interpolation methods"""
        methods = [
            InterpolationMethod.NEAREST,
            InterpolationMethod.LINEAR,
            InterpolationMethod.CUBIC,
        ]

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (*self.target_size, 3), dtype=np.uint8
            )

            results = self.multi_interpolator.compare_methods(
                self.test_image, self.target_size, methods
            )

            assert isinstance(results, dict)
            assert len(results) == len(methods)

            for method in methods:
                assert method in results
                assert results[method].shape == (*self.target_size, 3)

    def test_find_best_method(self):
        """Test finding best interpolation method"""
        methods = [InterpolationMethod.NEAREST, InterpolationMethod.LINEAR]

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (*self.target_size, 3), dtype=np.uint8
            )

            best_method = self.multi_interpolator.find_best_method(
                self.test_image, self.target_size, methods
            )

            assert best_method in methods

    def test_benchmark_methods(self):
        """Test benchmarking interpolation methods"""
        methods = [InterpolationMethod.NEAREST, InterpolationMethod.LINEAR]

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (*self.target_size, 3), dtype=np.uint8
            )

            benchmark_results = self.multi_interpolator.benchmark_methods(
                self.test_image, self.target_size, methods, iterations=2
            )

            assert isinstance(benchmark_results, dict)
            for method in methods:
                assert method in benchmark_results
                assert "time" in benchmark_results[method]
                assert isinstance(benchmark_results[method]["time"], (int, float))


class TestGPUInterpolator:
    """Test GPUInterpolator class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        self.target_size = (200, 200)

    def test_init_cpu_fallback(self):
        """Test GPUInterpolator initialization with CPU fallback"""
        # Mock GPU availability to False
        with patch.object(
            GPUInterpolator, "_check_gpu_availability", return_value=False
        ):
            gpu_interpolator = GPUInterpolator()
            assert hasattr(gpu_interpolator, "gpu_available")

    def test_gpu_availability_check(self):
        """Test GPU availability checking"""
        gpu_interpolator = GPUInterpolator()
        # Should have some method to check GPU availability
        assert hasattr(gpu_interpolator, "_check_gpu_availability") or hasattr(
            gpu_interpolator, "gpu_available"
        )

    @patch.object(GPUInterpolator, "_check_gpu_availability", return_value=False)
    def test_interpolate_cpu_fallback(self, mock_gpu_check):
        """Test interpolation falls back to CPU when GPU unavailable"""
        gpu_interpolator = GPUInterpolator()

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (*self.target_size, 3), dtype=np.uint8
            )

            result = gpu_interpolator.interpolate(self.test_image, self.target_size)
            assert result.shape == (*self.target_size, 3)

    def test_cuda_interpolation_mock(self):
        """Test CUDA interpolation (mocked)"""
        # Mock CUDA availability
        with patch.object(
            GPUInterpolator, "_check_gpu_availability", return_value=True
        ):
            with patch.object(GPUInterpolator, "_interpolate_cuda") as mock_cuda:
                mock_cuda.return_value = np.random.randint(
                    0, 255, (*self.target_size, 3), dtype=np.uint8
                )

                gpu_interpolator = GPUInterpolator(backend="cuda")
                result = gpu_interpolator.interpolate(self.test_image, self.target_size)

                assert result.shape == (*self.target_size, 3)

    def test_different_gpu_backends(self):
        """Test different GPU backends"""
        backends = ["cuda", "opencl", "metal"]

        for backend in backends:
            with patch.object(
                GPUInterpolator, "_check_gpu_availability", return_value=False
            ):
                gpu_interpolator = GPUInterpolator(backend=backend)
                # Should initialize without errors even if GPU not available
                assert hasattr(gpu_interpolator, "backend")


class TestInterpolationCache:
    """Test InterpolationCache class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.cache = InterpolationCache(max_size=100)  # 100 MB max
        self.test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        self.target_size = (200, 200)

    def test_init(self):
        """Test InterpolationCache initialization"""
        assert isinstance(self.cache, InterpolationCache)
        assert hasattr(self.cache, "max_size")
        assert hasattr(self.cache, "enabled")

    def test_cache_key_generation(self):
        """Test cache key generation"""
        key1 = self.cache._generate_key(
            self.test_image, self.target_size, InterpolationMethod.LINEAR
        )
        key2 = self.cache._generate_key(
            self.test_image, self.target_size, InterpolationMethod.LINEAR
        )

        # Same inputs should generate same key
        assert key1 == key2

        # Different inputs should generate different keys
        key3 = self.cache._generate_key(
            self.test_image, (300, 300), InterpolationMethod.LINEAR
        )
        assert key1 != key3

    def test_cache_operations(self):
        """Test cache store and retrieve operations"""
        key = "test_key"
        value = np.random.randint(0, 255, (*self.target_size, 3), dtype=np.uint8)

        # Store in cache
        self.cache.store(key, value)

        # Retrieve from cache
        cached_value = self.cache.get(key)

        if cached_value is not None:
            np.testing.assert_array_equal(value, cached_value)

    def test_cache_size_limit(self):
        """Test cache size limiting"""
        # Create cache with very small size
        small_cache = InterpolationCache(max_size=1)  # 1 MB

        # Try to store large data
        large_data = np.random.randint(0, 255, (1000, 1000, 3), dtype=np.uint8)
        small_cache.store("large_key", large_data)

        # Should handle gracefully (either store or reject)
        assert small_cache.get_stats()["current_size"] >= 0

    def test_cache_clear(self):
        """Test cache clearing"""
        # Store some data
        self.cache.store("test_key", self.test_image)

        # Clear cache
        self.cache.clear()

        # Should be empty
        stats = self.cache.get_stats()
        assert stats["current_size"] == 0
        assert stats["item_count"] == 0

    def test_cache_stats(self):
        """Test cache statistics"""
        stats = self.cache.get_stats()

        assert isinstance(stats, dict)
        assert "current_size" in stats
        assert "max_size" in stats
        assert "item_count" in stats
        assert "hit_count" in stats
        assert "miss_count" in stats

    def test_cache_thread_safety(self):
        """Test cache thread safety"""

        def worker(cache, worker_id):
            for i in range(10):
                key = f"worker_{worker_id}_item_{i}"
                value = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
                cache.store(key, value)
                retrieved = cache.get(key)

        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(self.cache, i))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Cache should still be in valid state
        stats = self.cache.get_stats()
        assert stats["current_size"] >= 0


class TestKernelFunctions:
    """Test interpolation kernel functions"""

    def test_lanczos_kernel(self):
        """Test Lanczos kernel function"""
        # This test assumes the kernel functions are accessible
        # In actual implementation, they might be private methods
        try:
            from core.interpolation import _lanczos_kernel

            # Test kernel at different points
            assert _lanczos_kernel(0) == 1.0  # Should be 1 at center
            assert _lanczos_kernel(1) == 0.0  # Should be 0 at integer points
            assert abs(_lanczos_kernel(0.5)) > 0  # Should be non-zero between
        except ImportError:
            # If kernel functions are not directly accessible, skip test
            pytest.skip("Kernel functions not directly accessible")

    def test_mitchell_kernel(self):
        """Test Mitchell kernel function"""
        try:
            from core.interpolation import _mitchell_kernel

            # Test basic properties
            result = _mitchell_kernel(0.5)
            assert isinstance(result, (int, float))
        except ImportError:
            pytest.skip("Mitchell kernel function not directly accessible")


class TestEdgeCasesAndPerformance:
    """Test edge cases and performance aspects"""

    def test_very_small_images(self):
        """Test interpolation with very small images"""
        tiny_image = np.random.randint(0, 255, (2, 2, 3), dtype=np.uint8)
        target_size = (10, 10)

        interpolator = AdvancedInterpolator()

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (*target_size, 3), dtype=np.uint8
            )
            result = interpolator.interpolate(tiny_image, target_size)
            assert result.shape == (*target_size, 3)

    def test_very_large_target_sizes(self):
        """Test interpolation with large target sizes"""
        small_image = np.random.randint(0, 255, (10, 10, 3), dtype=np.uint8)
        large_target = (1000, 1000)

        interpolator = AdvancedInterpolator()

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (*large_target, 3), dtype=np.uint8
            )
            result = interpolator.interpolate(small_image, large_target)
            assert result.shape == (*large_target, 3)

    def test_same_size_interpolation(self):
        """Test interpolation when target size equals input size"""
        image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        target_size = (100, 100)

        interpolator = AdvancedInterpolator()

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = image.copy()
            result = interpolator.interpolate(image, target_size)
            assert result.shape == image.shape

    def test_memory_management(self):
        """Test memory management during interpolation"""
        interpolator = AdvancedInterpolator(cache_enabled=True)

        # Process multiple images to test memory cleanup
        for i in range(10):
            test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)

            with patch("cv2.resize") as mock_resize:
                mock_resize.return_value = np.random.randint(
                    0, 255, (200, 200, 3), dtype=np.uint8
                )
                result = interpolator.interpolate(test_image, (200, 200))
                assert result.shape == (200, 200, 3)

    def test_streaming_processing(self):
        """Test streaming/batch processing capabilities"""
        # Test if the interpolator can handle multiple images efficiently
        interpolator = AdvancedInterpolator()
        images = [
            np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8) for _ in range(5)
        ]

        target_size = (200, 200)
        results = []

        with patch("cv2.resize") as mock_resize:
            mock_resize.return_value = np.random.randint(
                0, 255, (*target_size, 3), dtype=np.uint8
            )

            for image in images:
                result = interpolator.interpolate(image, target_size)
                results.append(result)

        assert len(results) == len(images)
        for result in results:
            assert result.shape == (*target_size, 3)


if __name__ == "__main__":
    pytest.main([__file__])


