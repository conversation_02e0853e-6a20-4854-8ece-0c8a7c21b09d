"""
Test logging integration between refactored modules and log_utils.
"""
import pytest
from unittest.mock import Mock, patch
import tempfile
import os
import numpy as np

# Test logging integration
from utils.factory import UtilsFactory
from utils.config import UtilsConfig
from utils.resource.gpu import GPUManager
from utils.resource.memory import MemoryManager
from processing.factory import ProcessingFactory
from processing.config import ProcessingConfig, DetectionConfig, BlurConfig
from log_utils.factory import get_logger


class TestLoggingIntegration:
    """Test that all refactored modules integrate properly with log_utils."""
    
    def test_utils_factory_logging(self):
        """Test that UtilsFactory uses log_utils properly."""
        config = UtilsConfig(enable_gpu=False)
        
        # This should create logger entries without errors
        factory = UtilsFactory(config)
        
        # Test that services can be created (which generates log entries)
        gpu_manager = factory.get_service(GPUManager)
        memory_manager = factory.get_service(MemoryManager)
        
        # Verify services were created without logging errors
        assert gpu_manager is not None
        assert memory_manager is not None
        
        # Test shutdown logging
        factory.shutdown()
    
    def test_processing_factory_logging(self):
        """Test that ProcessingFactory uses log_utils properly."""
        utils_config = UtilsConfig(enable_gpu=False)
        utils_factory = UtilsFactory(utils_config)
        
        processing_config = ProcessingConfig()
        
        # This should create logger entries without errors
        processing_factory = ProcessingFactory(processing_config, utils_factory)
        
        # Test that steps can be created (which generates log entries)
        cube_step = processing_factory.get_step("cube_generation")
        detection_step = processing_factory.get_step("detection")
        
        # Verify steps were created without logging errors
        assert cube_step is not None
        assert detection_step is not None
        
        # Test pipeline creation logging
        pipeline = processing_factory.create_pipeline()
        assert pipeline is not None
        
        # Test shutdown logging
        processing_factory.shutdown()
        utils_factory.shutdown()
    
    def test_pipeline_execution_logging(self):
        """Test that pipeline execution generates proper log entries."""
        utils_config = UtilsConfig(enable_gpu=False)
        utils_factory = UtilsFactory(utils_config)
        
        processing_config = ProcessingConfig(
            detection=DetectionConfig(enable_detection=False),
            blur=BlurConfig(enable_blur=False)
        )
        processing_factory = ProcessingFactory(processing_config, utils_factory)
        
        pipeline = processing_factory.create_pipeline()
        
        # Test input that should generate log entries
        test_input = {
            "image": np.random.randint(0, 255, (256, 512, 3), dtype=np.uint8),
            "image_path": "/test/panorama.jpg"
        }
        
        # This should execute with proper logging
        results = pipeline.run(test_input)
        
        # Verify execution completed
        assert len(results) > 0
        assert all(hasattr(result, 'step_name') for result in results)
        
        # Cleanup
        processing_factory.shutdown()
        utils_factory.shutdown()
    
    def test_logger_creation(self):
        """Test that loggers can be created for refactored modules."""
        # Test logger creation for different modules
        utils_logger = get_logger("utils.test")
        processing_logger = get_logger("processing.test")
        integration_logger = get_logger("test.integration")
        
        assert utils_logger is not None
        assert processing_logger is not None
        assert integration_logger is not None
        
        # Test that logging calls don't raise exceptions
        utils_logger.info("Test utils logging")
        processing_logger.debug("Test processing logging")
        integration_logger.warning("Test integration logging")
    
    @patch('log_utils.factory.get_logger')
    def test_logging_error_handling(self, mock_get_logger):
        """Test error handling when logging fails."""
        # Mock logger that might fail
        mock_logger = Mock()
        mock_logger.info.side_effect = Exception("Logging failed")
        mock_get_logger.return_value = mock_logger
        
        # Factory creation should still work even if logging fails
        config = UtilsConfig(enable_gpu=False)
        
        try:
            factory = UtilsFactory(config)
            # Should work despite logging errors
            assert factory is not None
        except Exception as e:
            # Should not propagate logging errors
            assert "Logging failed" not in str(e)
    
    def test_cross_module_logging_consistency(self):
        """Test that logging is consistent across utils and processing."""
        utils_config = UtilsConfig(enable_gpu=False)
        utils_factory = UtilsFactory(utils_config)
        
        processing_config = ProcessingConfig()
        processing_factory = ProcessingFactory(processing_config, utils_factory)
        
        # Both factories should use logging without conflicts
        utils_service = utils_factory.get_service(GPUManager)
        processing_step = processing_factory.get_step("cube_generation")
        
        assert utils_service is not None
        assert processing_step is not None
        
        # Test that both can log simultaneously
        utils_factory.get_service(MemoryManager)
        processing_factory.create_pipeline()
        
        # Cleanup
        processing_factory.shutdown()
        utils_factory.shutdown()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

