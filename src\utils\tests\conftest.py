import pytest
from dependency_injector import containers, providers

import sys
from pathlib import Path

# Import project modules directly (using pip install -e .)

from utils.containers import ManagerContainer

@pytest.fixture(scope="session")
def test_container():
    """
    創建一個用於測試的 DI 容器。
    這個容器將在整個測試會話中共享。
    """
    # 創建一個新的容器實例以避免污染全局容器
    container = ManagerContainer()
    
    # 這裡可以覆蓋配置以用於測試
    # 例如，使用測試資料庫或模擬的服務
    container.core.config.from_dict({
        'memory': {'strategy': 'BALANCED'},
        'performance': {'level': 'BASIC', 'interval_sec': 0.1},
        'processing': {'backend': 'THREADING', 'max_workers': 2}
    })
    
    yield container
    
    # 清理資源
    container.unwire()

@pytest.fixture(scope="function")
def memory_manager(test_container):
    """提供一個乾淨的記憶體管理器實例"""
    return test_container.memory_manager()

@pytest.fixture(scope="function")
def performance_monitor(test_container):
    """提供一個乾淨的性能監控器實例"""
    return test_container.performance_monitor()

@pytest.fixture(scope="function")
def distributed_processor(test_container):
    """提供一個乾淨的分散式處理器實例"""
    return test_container.distributed_processor()