"""
統一錯誤處理器模組

提供一致的錯誤處理策略和恢復機制，
確保整個 log_utils 模組有統一的錯誤處理行為。
"""

import logging
import warnings
from typing import Any, Callable, Optional, Type

from .exceptions import (
    ImportDependencyError,
    HandlerCreationError,
    FormatterError,
    ConfigurationError,
    LogUtilsError
)


class ErrorHandler:
    """
    統一錯誤處理器
    
    提供標準化的錯誤處理方法，包括導入錯誤、
    處理器創建錯誤和配置錯誤的處理。
    """
    
    @staticmethod
    def handle_import_error(
        primary_import: Callable[[], Any],
        fallback_import: Optional[Callable[[], Any]] = None,
        fallback_factory: Optional[Callable[[], Any]] = None,
        module_name: str = "未知模組",
        error_message: Optional[str] = None
    ) -> Any:
        """
        處理導入錯誤的統一策略
        
        :param primary_import: 主要導入函數
        :param fallback_import: 備用導入函數
        :param fallback_factory: 降級實現工廠函數
        :param module_name: 模組名稱（用於錯誤訊息）
        :param error_message: 自定義錯誤訊息
        :return: 導入的對象或降級實現
        :raises ImportDependencyError: 當所有導入嘗試都失敗時
        """
        base_message = error_message or f"模組 '{module_name}' 導入失敗"
        
        # 嘗試主要導入
        try:
            return primary_import()
        except ImportError as e:
            primary_error = str(e)
            
            # 嘗試備用導入
            if fallback_import:
                try:
                    return fallback_import()
                except ImportError as fallback_e:
                    # 記錄備用導入也失敗
                    pass
            
            # 使用降級工廠
            if fallback_factory:
                try:
                    warnings.warn(f"{base_message}，使用降級版本", ImportWarning)
                    return fallback_factory()
                except Exception as factory_e:
                    raise ImportDependencyError(
                        f"{base_message}，降級版本也失敗: {factory_e}"
                    ) from e
            
            # 所有嘗試都失敗
            raise ImportDependencyError(f"{base_message}: {primary_error}") from e
    
    @staticmethod
    def handle_handler_creation_error(
        handler_factory: Callable[[], Any],
        fallback_factory: Optional[Callable[[], Any]] = None,
        handler_type: str = "未知處理器",
        logger: Optional[logging.Logger] = None,
        suppress_errors: bool = False
    ) -> Optional[Any]:
        """
        處理處理器創建錯誤
        
        :param handler_factory: 主要處理器工廠函數
        :param fallback_factory: 備用處理器工廠函數
        :param handler_type: 處理器類型名稱
        :param logger: 用於記錄錯誤的日誌器
        :param suppress_errors: 是否抑制錯誤（返回None而不是拋出異常）
        :return: 處理器實例或None
        :raises HandlerCreationError: 當處理器創建失敗且不抑制錯誤時
        """
        try:
            return handler_factory()
        except Exception as e:
            error_msg = f"處理器 '{handler_type}' 創建失敗: {e}"
            
            # 記錄錯誤
            if logger:
                logger.error(error_msg)
            
            # 嘗試備用方案
            if fallback_factory:
                try:
                    fallback_handler = fallback_factory()
                    warning_msg = f"使用備用處理器代替 '{handler_type}'"
                    if logger:
                        logger.warning(warning_msg)
                    else:
                        warnings.warn(warning_msg, UserWarning)
                    return fallback_handler
                except Exception as fallback_e:
                    full_error_msg = f"{error_msg}，備用方案也失敗: {fallback_e}"
                    if logger:
                        logger.error(full_error_msg)
                    
                    if suppress_errors:
                        return None
                    raise HandlerCreationError(full_error_msg) from e
            
            # 沒有備用方案
            if suppress_errors:
                return None
            raise HandlerCreationError(error_msg) from e
    
    @staticmethod
    def handle_formatter_error(
        formatter_factory: Callable[[], Any],
        fallback_factory: Optional[Callable[[], Any]] = None,
        formatter_type: str = "未知格式器",
        logger: Optional[logging.Logger] = None,
        suppress_errors: bool = False
    ) -> Optional[Any]:
        """
        處理格式器創建錯誤
        
        :param formatter_factory: 主要格式器工廠函數
        :param fallback_factory: 備用格式器工廠函數
        :param formatter_type: 格式器類型名稱
        :param logger: 用於記錄錯誤的日誌器
        :param suppress_errors: 是否抑制錯誤
        :return: 格式器實例或None
        :raises FormatterError: 當格式器創建失敗且不抑制錯誤時
        """
        try:
            return formatter_factory()
        except Exception as e:
            error_msg = f"格式器 '{formatter_type}' 創建失敗: {e}"
            
            if logger:
                logger.error(error_msg)
            
            if fallback_factory:
                try:
                    fallback_formatter = fallback_factory()
                    warning_msg = f"使用備用格式器代替 '{formatter_type}'"
                    if logger:
                        logger.warning(warning_msg)
                    else:
                        warnings.warn(warning_msg, UserWarning)
                    return fallback_formatter
                except Exception as fallback_e:
                    full_error_msg = f"{error_msg}，備用方案也失敗: {fallback_e}"
                    if logger:
                        logger.error(full_error_msg)
                    
                    if suppress_errors:
                        return None
                    raise FormatterError(full_error_msg) from e
            
            if suppress_errors:
                return None
            raise FormatterError(error_msg) from e
    
    @staticmethod
    def handle_configuration_error(
        config_factory: Callable[[], Any],
        fallback_factory: Optional[Callable[[], Any]] = None,
        config_name: str = "未知配置",
        logger: Optional[logging.Logger] = None
    ) -> Any:
        """
        處理配置錯誤
        
        :param config_factory: 配置工廠函數
        :param fallback_factory: 備用配置工廠函數
        :param config_name: 配置名稱
        :param logger: 用於記錄錯誤的日誌器
        :return: 配置實例
        :raises ConfigurationError: 當配置創建失敗時
        """
        try:
            return config_factory()
        except Exception as e:
            error_msg = f"配置 '{config_name}' 創建失敗: {e}"
            
            if logger:
                logger.error(error_msg)
            
            if fallback_factory:
                try:
                    fallback_config = fallback_factory()
                    warning_msg = f"使用備用配置代替 '{config_name}'"
                    if logger:
                        logger.warning(warning_msg)
                    else:
                        warnings.warn(warning_msg, UserWarning)
                    return fallback_config
                except Exception as fallback_e:
                    full_error_msg = f"{error_msg}，備用方案也失敗: {fallback_e}"
                    if logger:
                        logger.error(full_error_msg)
                    raise ConfigurationError(full_error_msg) from e
            
            raise ConfigurationError(error_msg) from e
    
    @staticmethod
    def safe_execute(
        operation: Callable[[], Any],
        operation_name: str = "未知操作",
        logger: Optional[logging.Logger] = None,
        default_value: Any = None,
        raise_on_error: bool = True,
        error_type: Type[LogUtilsError] = LogUtilsError
    ) -> Any:
        """
        安全執行操作，提供統一的錯誤處理
        
        :param operation: 要執行的操作
        :param operation_name: 操作名稱
        :param logger: 日誌器
        :param default_value: 失敗時的預設值
        :param raise_on_error: 是否在錯誤時拋出異常
        :param error_type: 要拋出的異常類型
        :return: 操作結果或預設值
        :raises LogUtilsError: 當操作失敗且raise_on_error為True時
        """
        try:
            return operation()
        except Exception as e:
            error_msg = f"操作 '{operation_name}' 執行失敗: {e}"
            
            if logger:
                logger.error(error_msg)
            
            if raise_on_error:
                raise error_type(error_msg) from e
            
            return default_value


class RecoveryStrategy:
    """
    錯誤恢復策略管理器
    
    跟蹤重試次數並決定是否應該繼續嘗試恢復。
    """
    
    def __init__(self, max_attempts: int = 3):
        """
        初始化恢復策略
        
        :param max_attempts: 最大重試次數
        """
        self.max_attempts = max_attempts
        self.recovery_attempts = {}
    
    def should_retry(self, operation_id: str) -> bool:
        """
        判斷是否應該重試操作
        
        :param operation_id: 操作標識符
        :return: 是否應該重試
        """
        attempts = self.recovery_attempts.get(operation_id, 0)
        return attempts < self.max_attempts
    
    def record_attempt(self, operation_id: str) -> int:
        """
        記錄重試次數
        
        :param operation_id: 操作標識符
        :return: 當前重試次數
        """
        self.recovery_attempts[operation_id] = \
            self.recovery_attempts.get(operation_id, 0) + 1
        return self.recovery_attempts[operation_id]
    
    def reset_attempts(self, operation_id: str):
        """
        重置特定操作的重試次數
        
        :param operation_id: 操作標識符
        """
        if operation_id in self.recovery_attempts:
            del self.recovery_attempts[operation_id]
    
    def get_attempts(self, operation_id: str) -> int:
        """
        獲取操作的重試次數
        
        :param operation_id: 操作標識符
        :return: 重試次數
        """
        return self.recovery_attempts.get(operation_id, 0)