import shutil
import sys
import torch
from ultralytics import YOLO
import numpy as np
from numpy.typing import NDArray
from enum import IntEnum
from functools import lru_cache
from typing import Any, Literal, Optional, TypeVar, Union, List, Dict
from scipy.ndimage import map_coordinates
import cv2
import time
import os
import logging
import yaml
import pandas as pd
import csv
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Pool, cpu_count
from collections.abc import Sequence

# --- Configuration Loading ---

"""
進度恢復功能配置範例 (config.yaml):

# 進度恢復設定
progress_settings:
  # 是否啟用檢查功能（檢查 output 資料夾是否有 html5 資料夾）
  enable_check: true
  # 指定進度檔案路徑（可選，若不指定會自動使用最新的或建立新的）
  progress_file: "progress_20250529_141948.csv"
  # 進度檔案儲存目錄（預設為 primary_output_path）
  progress_dir: "/path/to/output"

使用方式：
1. 如果有指定 progress_file 且檔案存在，會使用指定的檔案
2. 如果沒有指定或檔案不存在，會尋找最新的 progress_*.csv 檔案
3. 如果都沒有，會建立新的進度檔案，檔名格式為 progress_YYYYMMDD_HHMMSS.csv

檢查功能：
- enable_check: true  - 檢查 output 資料夾是否有 html5 資料夾，有就跳過，顯示「X 個已跳過」
- enable_check: false - 不檢查，直接從最後一筆記錄繼續處理，顯示「X 個已處理」

恢復邏輯：
- 如果最後一行狀態是「待處理」，從該項目重新開始處理
- 如果最後一行狀態是「完成」，從下一個項目開始處理

進度檔案格式：
地區,場景,狀態,處理時間
中山區,全區,待處理,2025/5/29 14:45
中山區,_taipei_stage3__tpv1990001,待處理,2025/5/29 14:45
中山區,_taipei_stage3__tpv1990001,完成,2025/5/29 14:45
"""


def load_config(config_path='config.yaml'):
    """載入 YAML 設定檔"""
    try:
        with open(config_path, 'r', encoding='utf-8-sig') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        logger.error(f"設定檔 '{config_path}' 不存在。")
        return None
    except Exception as e:
        logger.error(f"讀取設定檔時發生錯誤: {e}")
        return None

# --- Logging Setup ---


def setup_logging(log_file='image_processing.log', display_mode='verbose'):
    """設定日誌記錄器"""
    # 移除所有現有的處理器，以避免重複日誌
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    handlers = [
        logging.FileHandler(log_file, mode='a', encoding='utf-8-sig')
    ]

    if display_mode == 'quiet':
        # 在安靜模式下，控制台只顯示 WARNING 及以上級別的日誌
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        handlers.append(console_handler)
        # 將根日誌級別設定為 INFO，以便所有 INFO 訊息仍能寫入檔案
        logging.root.setLevel(logging.INFO)
    else:  # verbose mode
        # 在詳細模式下，控制台顯示 INFO 及以上級別的日誌
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        handlers.append(console_handler)
        logging.root.setLevel(logging.INFO)

    logging.basicConfig(
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=handlers
    )
    return logging.getLogger(__name__)


def print_progress(current, total, item_name, display_mode):
    """顯示進度，根據 display_mode 決定格式"""
    if display_mode == 'quiet':
        print(f"({current}/{total}) {item_name}", flush=True)
    # verbose mode 使用 logger.info，不需要在此函數處理


logger = setup_logging()  # Initial setup, will be reconfigured in main

# --- Constants and Type Definitions ---
exts = ['.jpg', '.png', '.jpeg', '.bmp', '.tif', '.tiff']
DType = TypeVar("DType", bound=np.generic, covariant=True)
_CACHE_SIZE = 8


class Dim(IntEnum):
    X = 0
    Y = 1
    Z = 2


class Face(IntEnum):
    FRONT = 0
    RIGHT = 1
    BACK = 2
    LEFT = 3
    UP = 4
    DOWN = 5


class DetectionBox:
    def __init__(self, x1: int, y1: int, x2: int, y2: int, confidence: float, class_id: int = 0):
        self.x1 = x1
        self.y1 = y1
        self.x2 = x2
        self.y2 = y2
        self.confidence = confidence
        self.class_id = class_id
    
    @property
    def center(self):
        return ((self.x1 + self.x2) // 2, (self.y1 + self.y2) // 2)

# --- Core Projection and Image Transformation Logic (largely unchanged) ---


def uv2coor(u: NDArray[DType], v: NDArray[DType], h: int, w: int) -> tuple[NDArray[DType], NDArray[DType]]:
    coor_x = (u / (2 * np.pi) + 0.5) * w - 0.5
    coor_y = (-v / np.pi + 0.5) * h - 0.5
    return coor_x, coor_y


def xyz2uv(xyz: NDArray[DType]) -> tuple[NDArray[DType], NDArray[DType]]:
    if not np.all((xyz >= -0.5) & (xyz <= 0.5)):
        raise ValueError("xyz 應在 [-0.5, 0.5] 範圍內")
    x, y, z = xyz[..., 0:1], xyz[..., 1:2], xyz[..., 2:3]
    u = np.arctan2(x, z)
    c = np.hypot(x, z)
    v = np.arctan2(y, c)
    return u, v


@lru_cache(_CACHE_SIZE)
def xyzcube(face_w: int) -> NDArray[np.float32]:
    if face_w <= 0:
        raise ValueError("face_w 必須為正整數")
    out = np.empty((face_w, face_w * 6, 3), np.float32)
    rng = np.linspace(-0.5, 0.5, num=face_w, dtype=np.float32)
    x, y = np.meshgrid(rng, -rng)
    x_flip, y_flip = np.flip(x, 1), np.flip(y, 0)

    def face_slice(index):
        return slice(index * face_w, (index + 1) * face_w)

    out[:, face_slice(Face.FRONT), :] = np.dstack((x, y, np.full_like(x, 0.5)))
    out[:, face_slice(Face.RIGHT), :] = np.dstack(
        (np.full_like(x, 0.5), y, x_flip))
    out[:, face_slice(Face.BACK), :] = np.dstack(
        (x_flip, y, np.full_like(x, -0.5)))
    out[:, face_slice(Face.LEFT), :] = np.dstack((np.full_like(x, -0.5), y, x))
    out[:, face_slice(Face.UP), :] = np.dstack(
        (x, np.full_like(y, 0.5), y_flip))
    out[:, face_slice(Face.DOWN), :] = np.dstack((x, np.full_like(y, -0.5), y))
    out.setflags(write=False)
    return out


class EquirecSampler:
    def __init__(self, coor_x: NDArray, coor_y: NDArray, order: int):
        coor_y += 1
        self._use_cv2 = cv2 and order in (0, 1, 3)
        if self._use_cv2:
            inter_map = {0: cv2.INTER_NEAREST,
                         1: cv2.INTER_LINEAR, 3: cv2.INTER_CUBIC}
            self._order = inter_map[order]
            self._coor_x, self._coor_y = cv2.convertMaps(
                coor_x, coor_y, cv2.CV_16SC2, nninterpolation=(order == 0))
        else:
            self._coor_x, self._coor_y, self._order = coor_x, coor_y, order

    def __call__(self, img: NDArray[DType]) -> NDArray[DType]:
        padded = self._pad(img)
        if self._use_cv2:
            return cv2.remap(padded, self._coor_x, self._coor_y, interpolation=self._order)
        else:
            return map_coordinates(padded, (self._coor_y, self._coor_x), order=self._order, prefilter=False)

    def _pad(self, img: NDArray[DType]) -> NDArray[DType]:
        h, w = img.shape[:2]
        padded = cv2.copyMakeBorder(img, 1, 1, 0, 0, cv2.BORDER_CONSTANT)
        padded[0, :] = np.roll(img[0, :], w // 2, axis=0)
        padded[-1, :] = np.roll(img[-1, :], w // 2, axis=0)
        return padded

    @classmethod
    @lru_cache(_CACHE_SIZE)
    def from_cubemap(cls, face_w: int, h: int, w: int, order: int):
        xyz = xyzcube(face_w)
        u, v = xyz2uv(xyz)
        coor_x, coor_y = uv2coor(u, v, h, w)
        return cls(coor_x, coor_y, order=order)


def cube_h2list(cube_h: NDArray[DType]) -> list[NDArray[DType]]:
    if cube_h.shape[0] * 6 != cube_h.shape[1]:
        raise ValueError("立方體映射的寬度必須是其高度的 6 倍。")
    return np.split(cube_h, 6, axis=1)


def cube_h2dict(cube_h: NDArray[DType]) -> dict[str, NDArray[DType]]:
    return dict(zip("FRBLUD", cube_h2list(cube_h)))


class Projection:
    def __init__(self, h: int, w: int, mode: str = "cubic"):
        self.h, self.w, self.face_w = h, w, w // 4
        self.order = {"nearest": 0, "linear": 1, "cubic": 3}.get(mode, 3)
        self._E2Csampler = None

    def equirec_to_cubemap(self, e_img: NDArray[DType], cube_format: str = "list"):
        if e_img.ndim == 2:
            e_img = e_img[..., None]
            squeeze = True
        else:
            squeeze = False

        if self._E2Csampler is None:
            self._E2Csampler = EquirecSampler.from_cubemap(
                self.face_w, self.h, self.w, self.order)

        cubemap_channels = [self._E2Csampler(
            e_img[..., i]) for i in range(e_img.shape[2])]
        cubemap = np.stack(cubemap_channels, axis=-1)

        if squeeze:
            cubemap = cubemap[..., 0]

        if cube_format == "horizon":
            return cubemap
        elif cube_format == "list":
            return cube_h2list(cubemap)
        elif cube_format == "dict":
            return cube_h2dict(cubemap)
        else:
            raise NotImplementedError

# --- Progress Management ---


class ProgressManager:
    def __init__(self, config):
        self.config = config
        self.output_path = config.get('primary_output_path', '')
        self.progress_dir = config.get('progress_settings', {}).get('progress_dir', config.get('primary_output_path', '.'))
        self.progress_file = None
        self.current_progress = {}
        self.enable_check = config.get('progress_settings', {}).get('enable_check', True)
        self.display_mode = config.get('display_mode', 'verbose')
        
    def get_or_create_progress_file(self):
        """獲取或創建進度檔案"""
        if self.progress_file:
            return self.progress_file
            
        # 確保進度目錄存在
        if not os.path.exists(self.progress_dir):
            try:
                os.makedirs(self.progress_dir, exist_ok=True)
                logger.info(f"創建進度目錄: {self.progress_dir}")
            except Exception as e:
                logger.error(f"無法創建進度目錄 {self.progress_dir}: {e}")
                # 如果無法創建，回退到當前目錄
                self.progress_dir = '.'
                logger.warning(f"回退到當前目錄作為進度目錄")
            
        progress_settings = self.config.get('progress_settings', {})
        specified_file = progress_settings.get('progress_file')
        
        if specified_file and os.path.exists(specified_file):
            self.progress_file = specified_file
            logger.info(f"使用指定的進度檔案: {specified_file}")
        else:
            # 尋找最新的進度檔案
            progress_files = []
            try:
                for file in os.listdir(self.progress_dir):
                    if file.startswith('progress_') and file.endswith('.csv'):
                        progress_files.append(file)
            except OSError as e:
                logger.warning(f"無法讀取進度目錄 {self.progress_dir}: {e}")
                progress_files = []
            
            if progress_files:
                progress_files.sort(reverse=True)
                self.progress_file = os.path.join(self.progress_dir, progress_files[0])
                logger.info(f"使用最新的進度檔案: {self.progress_file}")
            else:
                # 創建新的進度檔案
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                self.progress_file = os.path.join(self.progress_dir, f'progress_{timestamp}.csv')
                self._create_new_progress_file()
                logger.info(f"創建新的進度檔案: {self.progress_file}")
                
        return self.progress_file
        
    def _create_new_progress_file(self):
        """創建新的進度檔案"""
        # 確保檔案的目錄存在
        progress_dir = os.path.dirname(self.progress_file)
        if progress_dir and not os.path.exists(progress_dir):
            try:
                os.makedirs(progress_dir, exist_ok=True)
            except Exception as e:
                logger.error(f"無法創建進度檔案目錄 {progress_dir}: {e}")
                raise
        
        try:
            with open(self.progress_file, 'w', encoding='utf-8-sig', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['地區', '場景', '狀態', '處理時間'])
        except Exception as e:
            logger.error(f"無法創建進度檔案 {self.progress_file}: {e}")
            raise
            
    def load_progress(self):
        """載入進度檔案"""
        progress_file = self.get_or_create_progress_file()
        
        if not os.path.exists(progress_file):
            return {}
            
        try:
            df = pd.read_csv(progress_file, encoding='utf-8-sig')
            if df.empty:
                return {}
            
            # 清理重複記錄：如果同一個區/場景有完成記錄，刪除待處理記錄
            original_count = len(df)
            df_cleaned = self._clean_duplicate_records(df)
            cleaned_count = len(df_cleaned)
            
            if cleaned_count < original_count:
                # 儲存清理後的檔案
                df_cleaned.to_csv(progress_file, index=False, encoding='utf-8-sig')
                logger.info(f"清理進度檔案：移除 {original_count - cleaned_count} 筆重複記錄")
                
            # 將進度轉換為字典格式
            progress_dict = {}
            for _, row in df_cleaned.iterrows():
                key = f"{row['地區']}/{row['場景']}"
                progress_dict[key] = {
                    'status': row['狀態'],
                    'time': row['處理時間']
                }
            
            self.current_progress = progress_dict
            logger.info(f"載入進度檔案完成，共 {len(progress_dict)} 筆記錄")
            return progress_dict
            
        except Exception as e:
            logger.error(f"載入進度檔案失敗: {e}")
            return {}
    
    def _clean_duplicate_records(self, df):
        """清理重複記錄：如果同一個區/場景有完成記錄，刪除待處理記錄"""
        # 為每個區/場景組合找出狀態
        df_grouped = df.groupby(['地區', '場景'])['狀態'].apply(list).reset_index()
        
        # 找出需要清理的組合（既有待處理又有完成的）
        rows_to_remove = []
        
        for _, group in df_grouped.iterrows():
            district = group['地區']
            scene = group['場景']
            statuses = group['狀態']
            
            # 如果既有待處理又有完成，移除待處理的記錄
            if '待處理' in statuses and '完成' in statuses:
                # 找到該組合中所有待處理的記錄索引
                mask = (df['地區'] == district) & (df['場景'] == scene) & (df['狀態'] == '待處理')
                rows_to_remove.extend(df[mask].index.tolist())
        
        # 移除標記的行
        if rows_to_remove:
            df_cleaned = df.drop(rows_to_remove).reset_index(drop=True)
            logger.debug(f"移除重複的待處理記錄：{len(rows_to_remove)} 筆")
        else:
            df_cleaned = df
            
        return df_cleaned
            
    def update_progress(self, district, scene, status):
        """更新進度狀態"""
        current_time = time.strftime("%Y/%m/%d %H:%M")
        progress_file = self.get_or_create_progress_file()
        
        try:
            # 讀取現有進度
            if os.path.exists(progress_file):
                df = pd.read_csv(progress_file, encoding='utf-8-sig')
            else:
                df = pd.DataFrame(columns=['地區', '場景', '狀態', '處理時間'])
            
            # 如果狀態是「完成」，先移除該區/場景的所有「待處理」記錄
            if status == '完成':
                pending_mask = (df['地區'] == district) & (df['場景'] == scene) & (df['狀態'] == '待處理')
                df = df[~pending_mask]
            
            # 更新或新增記錄
            key = f"{district}/{scene}"
            mask = (df['地區'] == district) & (df['場景'] == scene) & (df['狀態'] == status)
            
            if mask.any():
                # 如果已經有相同狀態的記錄，更新時間
                df.loc[mask, '處理時間'] = current_time
            else:
                # 新增記錄
                new_row = pd.DataFrame({
                    '地區': [district],
                    '場景': [scene],
                    '狀態': [status],
                    '處理時間': [current_time]
                })
                df = pd.concat([df, new_row], ignore_index=True)
            
            # 確保目錄存在後儲存檔案
            progress_dir = os.path.dirname(progress_file)
            if progress_dir and not os.path.exists(progress_dir):
                os.makedirs(progress_dir, exist_ok=True)
            
            df.to_csv(progress_file, index=False, encoding='utf-8-sig')
            
            # 更新內存中的進度
            self.current_progress[key] = {
                'status': status,
                'time': current_time
            }
            
            logger.debug(f"更新進度: {district}/{scene} -> {status}")
            
        except Exception as e:
            logger.error(f"更新進度失敗: {e}")
            
    def is_completed(self, district, scene):
        """檢查是否已完成處理"""
        if not self.enable_check:
            return False
            
        # 檢查 output 資料夾是否有 html5 資料夾
        scene_output_path = os.path.join(self.output_path, district, scene)
        html5_path = os.path.join(scene_output_path, 'html5')
        
        if os.path.exists(html5_path) and os.path.isdir(html5_path):
            # 檢查 html5 資料夾是否有內容
            html5_files = os.listdir(html5_path)
            if html5_files:
                # 檢查進度檔案中的狀態，如果是待處理則更新為完成
                key = f"{district}/{scene}"
                if key in self.current_progress:
                    current_status = self.current_progress[key]['status']
                    if current_status == "待處理":
                        logger.info(f"發現 {district}/{scene} 已有 html5 資料夾，將狀態從「待處理」更新為「完成」")
                        self.update_progress(district, scene, "完成")
                
                # 在兩種模式下都顯示跳過訊息
                if self.display_mode == 'quiet':
                    print(f"{district}/{scene} 發現html5 跳過處理", flush=True)
                elif self.display_mode == 'verbose':
                    logger.info(f"場景 {district}/{scene} 已完成（發現 html5 資料夾），跳過處理")
                return True
        
        return False
        
    def get_last_processed_item(self):
        """獲取最後處理的項目"""
        if not self.current_progress:
            return None
            
        # 如果不啟用檢查功能，直接讀取 CSV 檔案的最後一行
        if not self.enable_check:
            try:
                progress_file = self.get_or_create_progress_file()
                if os.path.exists(progress_file):
                    df = pd.read_csv(progress_file, encoding='utf-8-sig')
                    if not df.empty:
                        # 取最後一行
                        last_row = df.iloc[-1]
                        last_key = f"{last_row['地區']}/{last_row['場景']}"
                        last_status = last_row['狀態']
                        last_time = last_row['處理時間']
                        
                        logger.info(f"讀取 CSV 最後一行: {last_key}, 狀態: {last_status}, 時間: {last_time}")
                        
                        return (last_key, {
                            'status': last_status,
                            'time': last_time
                        })
            except Exception as e:
                logger.error(f"讀取 CSV 最後一行失敗: {e}")
                # 如果讀取失敗，回退到原來的邏輯
                pass
        
        # 原來的邏輯：根據時間排序，找到最後一個項目
        sorted_items = sorted(
            self.current_progress.items(),
            key=lambda x: x[1]['time'],
            reverse=True
        )
        
        if sorted_items:
            return sorted_items[0]
            
        return None
        
    def get_pending_items(self, all_items):
        """獲取待處理的項目列表"""
        if not self.enable_check:
            # 如果不開啟檢查功能，從最後一個項目開始
            last_item = self.get_last_processed_item()
            if last_item:
                last_key = last_item[0]
                last_status = last_item[1]['status']
                
                try:
                    last_index = all_items.index(last_key)
                    # 如果最後一行是待處理，從待處理的項目開始
                    if last_status == "待處理":
                        return all_items[last_index:]
                    else:
                        return all_items[last_index + 1:]
                except ValueError:
                    # 如果在 all_items 中找不到最後處理的項目，
                    # 可能是因為項目順序改變或項目不存在
                    # 這種情況下，我們需要更智能地決定從哪裡開始
                    logger.warning(f"進度檔案中的項目 '{last_key}' 在當前項目列表中找不到")
                    logger.debug(f"all_items 包含: {all_items[:5]}... (總共 {len(all_items)} 個項目)")
                    logger.debug(f"進度檔案中的項目: {list(self.current_progress.keys())[:5]}...")
                    
                    # 檢查進度檔案中已處理的項目
                    processed_keys = set(self.current_progress.keys())
                    pending_items = []
                    
                    for item in all_items:
                        if item not in processed_keys:
                            pending_items.append(item)
                        elif self.current_progress[item]['status'] == "待處理":
                            # 如果狀態是待處理，也要包含
                            pending_items.append(item)
                    
                    return pending_items
            return all_items
        
        pending_items = []
        for item in all_items:
            if isinstance(item, str):
                # 處理字串格式的項目 (district/scene)
                if '/' in item:
                    district, scene = item.split('/', 1)
                    if not self.is_completed(district, scene):
                        pending_items.append(item)
                else:
                    pending_items.append(item)
            else:
                # 處理其他格式的項目
                pending_items.append(item)
        
        return pending_items

    def clean_last_pending_folder(self):
        """如果 CSV 最後一行是待處理狀態，清除對應的輸出資料夾"""
        if not self.enable_check:
            try:
                progress_file = self.get_or_create_progress_file()
                if os.path.exists(progress_file):
                    df = pd.read_csv(progress_file, encoding='utf-8-sig')
                    if not df.empty:
                        # 取最後一行
                        last_row = df.iloc[-1]
                        last_status = last_row['狀態']
                        
                        if last_status == "待處理":
                            district = last_row['地區']
                            scene = last_row['場景']
                            
                            # 構建輸出資料夾路徑
                            scene_output_path = os.path.join(self.output_path, district, scene)
                            
                            if os.path.exists(scene_output_path):
                                try:
                                    import shutil
                                    shutil.rmtree(scene_output_path)
                                    logger.info(f"清除待處理項目的輸出資料夾: {scene_output_path}")
                                    
                                    if self.display_mode == 'quiet':
                                        print(f"清除 {district}/{scene} 待處理資料夾", flush=True)
                                    elif self.display_mode == 'verbose':
                                        logger.info(f"已清除場景 {district}/{scene} 的輸出資料夾")
                                        
                                except Exception as e:
                                    logger.error(f"清除資料夾失敗 {scene_output_path}: {e}")
                            else:
                                logger.debug(f"輸出資料夾不存在，無需清除: {scene_output_path}")
                                
            except Exception as e:
                logger.error(f"檢查和清除待處理資料夾時發生錯誤: {e}")


# --- File I/O & Helpers ---


def cv_imread(file_path):
    try:
        img = cv2.imdecode(np.fromfile(file_path, dtype=np.uint8), -1)
        if img is None:
            raise IOError("Image is None")
        return img
    except Exception as e:
        logger.warning(f"無法讀取影像 '{file_path}': {e}")
        return None


def cv_imwrite(file_path, image):
    ext = os.path.splitext(file_path)[1]
    success, encoded_image = cv2.imencode(ext, image)
    if success:
        encoded_image.tofile(file_path)
    return success


def copy_scene_to_secondary(primary_scene_path, config):
    secondary_output_path = config.get('secondary_output_path')
    if not secondary_output_path:
        return

    primary_base_path = config['primary_output_path']
    relative_scene_path = os.path.relpath(
        primary_scene_path, primary_base_path)
    secondary_scene_path = os.path.join(
        secondary_output_path, relative_scene_path)

    logger.info(f"複製結果到次要輸出路徑: '{secondary_scene_path}'")
    try:
        shutil.copytree(primary_scene_path,
                        secondary_scene_path, dirs_exist_ok=True)
    except Exception as e:
        logger.error(f"複製到次要輸出失敗: {e}")

# --- Image Processing Classes ---


class ImageCutter:
    def __init__(self, cube_dict, thumbnail, logo_img, output_scene_folder, pyramid_factors, save_mode="ALL", blur_regions=None):
        self.cube_index_map = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}
        self.logo_img = logo_img
        self.output_scene_folder = output_scene_folder
        self.thumbnail = thumbnail
        self.pyramid_factors = pyramid_factors
        self.save_mode = save_mode
        self.blur_regions = blur_regions or {}

        logger.info(
            f"ImageCutter 初始化: save_mode={save_mode}, blur_regions={self.blur_regions}")

        # 儲存原始和處理後的立方體圖像
        self.original_cubemaps = {}
        self.processed_cubemaps = {}  # 新增：儲存處理後（模糊+logo）的圖像

        cube_images_for_preview = []
        for key, value in cube_dict.items():
            cube_index = self.cube_index_map[key]

            # 儲存原始圖像
            self.original_cubemaps[cube_index] = value

            # 處理圖像（應用模糊）
            processed_face = value.copy()

            # 應用模糊區域（如果有）
            if cube_index in self.blur_regions:
                processed_face = self._apply_blur_regions(
                    processed_face, cube_index)

            # 根據 save_mode 決定是否覆蓋 logo
            should_add_logo = self._should_add_logo(cube_index)
            if cube_index == Face.DOWN and self.logo_img is not None and should_add_logo:
                logger.info(
                    f"正在為 DOWN 面 (索引 {cube_index}) 覆蓋 logo (save_mode: {save_mode})")
                processed_face = self._overlay_logo(processed_face)

            # 儲存處理後的圖像
            self.processed_cubemaps[cube_index] = processed_face
            cube_images_for_preview.append(processed_face)

        if cube_images_for_preview:
            self.preview = np.hstack(cube_images_for_preview)
        else:
            self.preview = None

    def _apply_blur_regions(self, face_image, face_id):
        """應用模糊區域到面圖像"""
        if face_id not in self.blur_regions:
            return face_image

        face_copy = face_image.copy()
        blur_regions_for_face = self.blur_regions[face_id]

        if not blur_regions_for_face or len(blur_regions_for_face) == 0:
            return face_copy

        logger.debug(f"對面 {face_id} 應用 {len(blur_regions_for_face)} 個模糊區域")

        for blur_x1, blur_y1, blur_x2, blur_y2 in blur_regions_for_face:
            try:
                # 確保座標在圖像範圍內
                h, w = face_copy.shape[:2]
                blur_x1 = max(0, min(blur_x1, w))
                blur_y1 = max(0, min(blur_y1, h))
                blur_x2 = max(0, min(blur_x2, w))
                blur_y2 = max(0, min(blur_y2, h))

                if blur_x2 > blur_x1 and blur_y2 > blur_y1:
                    roi = face_copy[blur_y1:blur_y2, blur_x1:blur_x2]
                    if roi.size > 0:
                        blurred_roi = cv2.GaussianBlur(roi, (99, 99), 30)
                        face_copy[blur_y1:blur_y2,
                                  blur_x1:blur_x2] = blurred_roi
                        logger.debug(
                            f"應用模糊到區域 ({blur_x1},{blur_y1},{blur_x2},{blur_y2})")
            except Exception as e:
                logger.warning(f"應用模糊區域失敗: {e}")

        return face_copy

    def _get_faces_to_process(self):
        """根據 save_mode 確定需要處理的面"""
        if self.save_mode == "ALL":
            return list(range(6))

        faces_to_process = set()

        # 添加有模糊區域的面
        for face_id in self.blur_regions:
            blur_regions_for_face = self.blur_regions[face_id]
            if blur_regions_for_face and len(blur_regions_for_face) > 0:
                faces_to_process.add(face_id)
                logger.info(
                    f"面 {face_id} 有 {len(blur_regions_for_face)} 個模糊區域，將被處理")

        # blur_and_logo 模式還要添加 logo 面
        if self.save_mode == "blur_and_logo" and self.logo_img is not None:
            faces_to_process.add(Face.DOWN)
            logger.info(f"blur_and_logo 模式：添加 DOWN 面 (logo)")

        faces_list = list(faces_to_process)
        logger.info(f"根據 save_mode={self.save_mode}，需要處理的面: {faces_list}")
        return faces_list

    def create_folder_structure(self):
        """只為需要處理的面創建文件夾結構"""
        os.makedirs(self.output_scene_folder, exist_ok=True)
        
        faces_to_process = self._get_faces_to_process()
        
        for i in faces_to_process:
            for j in range(len(self.pyramid_factors)):
                folder_path = os.path.join(self.output_scene_folder, str(i), str(j))
                os.makedirs(folder_path, exist_ok=True)
                logger.debug(f"創建文件夾: {folder_path}")
        
        # html5 文件夾總是需要創建（用於預覽）
        os.makedirs(os.path.join(self.output_scene_folder, 'html5'), exist_ok=True)
        
        # 在 blur 模式下記錄預期的瓦片數量
        if self.save_mode == "blur":
            total_expected_tiles = 0
            for face_id in faces_to_process:
                if face_id in self.blur_regions:
                    blur_count = len(self.blur_regions[face_id])
                    logger.info(f"面 {face_id} 有 {blur_count} 個模糊區域")
                    # 估算每個模糊區域可能影響的瓦片數量（粗略估計）
                    estimated_tiles = blur_count * 2  # 假設每個模糊區域平均影響2個瓦片
                    total_expected_tiles += estimated_tiles
            
            logger.info(f"blur 模式預計保存約 {total_expected_tiles} 個瓦片（3個層級總計）")

    def pyramid_image(self, img_id):
        base_image = self.processed_cubemaps.get(img_id)
        if base_image is None:
            logger.warning(f"面 {img_id} 的處理後圖片不存在，跳過處理")
            return

        logger.debug(f"正在處理面 {img_id} 的金字塔")
        original_size = base_image.shape[0]  # 假設是正方形圖像

        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = {}
            for level, size in enumerate(self.pyramid_factors):
                resized_image = cv2.resize(
                    base_image, (size, size), interpolation=cv2.INTER_LANCZOS4)
                level_dir = os.path.join(
                    self.output_scene_folder, str(img_id), str(level))

                # 計算縮放比例
                scale_factor = size / original_size

                futures[executor.submit(
                    self.save_image_slices, resized_image, level_dir, img_id, scale_factor)] = level

            for future in futures:
                level = futures[future]
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"保存面 {img_id} 層級 {level} 失敗: {e}")

    def save_image_slices(self, image, folder, face_id=None, scale_factor=1.0, level=0):
        """統一的瓦片保存方法"""
        if self.save_mode == "blur":
            # blur 模式使用專門的方法
            return self.save_image_slices_with_blur_check(image, folder, face_id, scale_factor, level)
        else:
            # ALL 和 blur_and_logo 模式使用原來的邏輯
            h, w, _ = image.shape
            s = 512
            nx, ny = (w + s - 1) // s, (h + s - 1) // s
            saved_tiles = 0
            total_tiles = nx * ny

            with ThreadPoolExecutor() as executor:
                for y in range(ny):
                    for x in range(nx):
                        tile_x1, tile_y1 = x * s, y * s
                        tile_x2, tile_y2 = min(
                            tile_x1 + s, w), min(tile_y1 + s, h)

                        if self._should_save_tile(tile_x1, tile_y1, tile_x2, tile_y2, face_id, scale_factor):
                            sl = image[tile_y1:tile_y2, tile_x1:tile_x2]
                            executor.submit(cv_imwrite, os.path.join(
                                folder, f'{y}_{x}.jpg'), sl)
                            saved_tiles += 1

            logger.debug(f"面 {face_id}: 保存了 {saved_tiles}/{total_tiles} 個瓦片")
            return saved_tiles

    def save_image_slices_with_blur_check(self, image, folder, face_id, scale_factor, level):
        """只保存包含模糊區域的瓦片 - 精確版本"""
        h, w, _ = image.shape
        s = 512  # 瓦片大小
        nx, ny = (w + s - 1) // s, (h + s - 1) // s
        saved_tiles = 0
        total_tiles = nx * ny

        # 獲取該面的模糊區域
        blur_regions = self.blur_regions.get(face_id, [])
        if not blur_regions:
            logger.info(f"面 {face_id} 層級 {level} 沒有模糊區域，跳過所有瓦片")
            return 0

        # 將模糊區域縮放到當前層級
        scaled_blur_regions = []
        for blur_x1, blur_y1, blur_x2, blur_y2 in blur_regions:
            scaled_x1 = max(0, int(blur_x1 * scale_factor))
            scaled_y1 = max(0, int(blur_y1 * scale_factor))
            scaled_x2 = min(w, int(blur_x2 * scale_factor))
            scaled_y2 = min(h, int(blur_y2 * scale_factor))

            # 確保模糊區域有效
            if scaled_x2 > scaled_x1 and scaled_y2 > scaled_y1:
                scaled_blur_regions.append(
                    (scaled_x1, scaled_y1, scaled_x2, scaled_y2))

        if not scaled_blur_regions:
            logger.info(f"面 {face_id} 層級 {level} 縮放後沒有有效模糊區域")
            return 0

        logger.debug(f"面 {face_id} 層級 {level} 有效模糊區域: {scaled_blur_regions}")

        # 預先計算哪些瓦片包含模糊區域
        tiles_with_blur = set()

        for y in range(ny):
            for x in range(nx):
                tile_x1, tile_y1 = x * s, y * s
                tile_x2, tile_y2 = min(tile_x1 + s, w), min(tile_y1 + s, h)

                # 檢查瓦片是否與模糊區域重疊
                if self._tile_has_significant_blur_overlap(tile_x1, tile_y1, tile_x2, tile_y2, scaled_blur_regions):
                    tiles_with_blur.add((y, x))
                    logger.debug(
                        f"瓦片 {y}_{x} 包含模糊區域: ({tile_x1},{tile_y1},{tile_x2},{tile_y2})")

        if not tiles_with_blur:
            logger.info(f"面 {face_id} 層級 {level} 沒有瓦片包含模糊區域")
            return 0

        logger.info(
            f"面 {face_id} 層級 {level} 將保存 {len(tiles_with_blur)}/{total_tiles} 個包含模糊的瓦片")

        # 只保存包含模糊區域的瓦片
        with ThreadPoolExecutor() as executor:
            tile_futures = []

            for y, x in tiles_with_blur:
                tile_x1, tile_y1 = x * s, y * s
                tile_x2, tile_y2 = min(tile_x1 + s, w), min(tile_y1 + s, h)

                sl = image[tile_y1:tile_y2, tile_x1:tile_x2]
                tile_path = os.path.join(folder, f'{y}_{x}.jpg')
                tile_futures.append(executor.submit(cv_imwrite, tile_path, sl))

                logger.debug(f"✅ 保存瓦片 {y}_{x} (包含模糊)")

            # 等待所有瓦片保存完成
            success_count = 0
            for future in tile_futures:
                if future.result():
                    success_count += 1

        logger.info(f"面 {face_id} 層級 {level}: 成功保存 {success_count} 個模糊瓦片")
        return success_count

    def _tile_has_significant_blur_overlap(self, tile_x1, tile_y1, tile_x2, tile_y2, scaled_blur_regions):
        """
        檢查瓦片是否與模糊區域有顯著重疊

        Args:
            tile_x1, tile_y1, tile_x2, tile_y2: 瓦片座標
            scaled_blur_regions: 縮放後的模糊區域列表

        Returns:
            bool: 是否有顯著重疊
        """
        tile_area = (tile_x2 - tile_x1) * (tile_y2 - tile_y1)

        for blur_x1, blur_y1, blur_x2, blur_y2 in scaled_blur_regions:
            # 計算重疊區域
            overlap_x1 = max(tile_x1, blur_x1)
            overlap_y1 = max(tile_y1, blur_y1)
            overlap_x2 = min(tile_x2, blur_x2)
            overlap_y2 = min(tile_y2, blur_y2)

            # 檢查是否有重疊
            if overlap_x2 > overlap_x1 and overlap_y2 > overlap_y1:
                overlap_area = (overlap_x2 - overlap_x1) * \
                    (overlap_y2 - overlap_y1)

                # 計算重疊比例 - 可以調整這個閾值
                overlap_ratio = overlap_area / tile_area

                # 如果重疊面積超過瓦片的1%，就認為包含模糊區域
                # 這個閾值可以根據需要調整
                if overlap_ratio > 0.01:  # 1% 閾值
                    logger.debug(f"瓦片重疊比例: {overlap_ratio:.3f} > 0.01")
                    return True

                # 或者如果重疊面積超過某個絕對值（例如100像素）
                if overlap_area > 500:  # 100像素閾值
                    logger.debug(f"瓦片重疊面積: {overlap_area} > 100")
                    return True

        return False

    def _tile_contains_blur(self, tile_x1, tile_y1, tile_x2, tile_y2, scaled_blur_regions):
        """檢查瓦片是否包含模糊區域"""
        for blur_x1, blur_y1, blur_x2, blur_y2 in scaled_blur_regions:
            # 檢查矩形重疊 - 只要有任何重疊就算包含模糊
            if not (tile_x2 <= blur_x1 or tile_x1 >= blur_x2 or tile_y2 <= blur_y1 or tile_y1 >= blur_y2):
                logger.debug(
                    f"瓦片 ({tile_x1},{tile_y1},{tile_x2},{tile_y2}) 與模糊區域 ({blur_x1},{blur_y1},{blur_x2},{blur_y2}) 重疊")
                return True
        return False

    def _should_save_tile(self, x1, y1, x2, y2, face_id, scale_factor=1.0):
        """檢查是否應該保存特定tile - 簡化版本，主要邏輯移到 save_image_slices_with_blur_check"""
        if self.save_mode == "ALL":
            return True

        if self.save_mode == "blur":
            # blur 模式的邏輯已經移到 save_image_slices_with_blur_check
            # 這裡只是備用邏輯
            if face_id is None or face_id not in self.blur_regions:
                return False
            return self._tile_intersects_blur_regions(x1, y1, x2, y2, face_id, scale_factor)

        if self.save_mode == "blur_and_logo":
            # 檢查模糊區域
            if face_id is not None and face_id in self.blur_regions:
                if self._tile_intersects_blur_regions(x1, y1, x2, y2, face_id, scale_factor):
                    return True

            # 檢查logo面
            if face_id == Face.DOWN and self.logo_img is not None:
                return True

            return False

        return True

    def _tile_intersects_blur_regions(self, x1, y1, x2, y2, face_id, scale_factor=1.0):
        """檢查tile是否與模糊區域重疊（縮放版本）"""
        if face_id not in self.blur_regions:
            return False

        blur_regions_for_face = self.blur_regions[face_id]

        for blur_x1, blur_y1, blur_x2, blur_y2 in blur_regions_for_face:
            # 根據縮放比例調整模糊區域座標
            scaled_blur_x1 = int(blur_x1 * scale_factor)
            scaled_blur_y1 = int(blur_y1 * scale_factor)
            scaled_blur_x2 = int(blur_x2 * scale_factor)
            scaled_blur_y2 = int(blur_y2 * scale_factor)

            # 檢查矩形重疊
            if not (x2 <= scaled_blur_x1 or x1 >= scaled_blur_x2 or y2 <= scaled_blur_y1 or y1 >= scaled_blur_y2):
                return True

        return False

    def save_previews_and_html5(self):
        """根據 save_mode 保存預覽圖和 html5 文件"""
        # 預覽圖總是保存
        if self.preview is not None:
            cv_imwrite(os.path.join(self.output_scene_folder, 'preview.jpg'),
                       cv2.resize(self.preview, (1536, 256)))

        if self.thumbnail is not None:
            cv_imwrite(os.path.join(self.output_scene_folder, 'thumbnail.jpg'),
                       cv2.resize(self.thumbnail, (400, 200)))

        # html5 文件夾根據 save_mode 選擇性保存
        html5_folder = os.path.join(self.output_scene_folder, 'html5')
        faces_to_process = self._get_faces_to_process()

        logger.info(f"保存到 html5 文件夾的面: {faces_to_process}")

        with ThreadPoolExecutor() as executor:
            for i in faces_to_process:
                # 使用處理後的圖像
                face_img = self.processed_cubemaps.get(i)
                if face_img is not None:
                    file_path = os.path.join(html5_folder, f'{i}.jpg')
                    executor.submit(cv_imwrite, file_path,
                                    cv2.resize(face_img, (2048, 2048)))
                    logger.debug(f"保存面 {i} 到 html5: {file_path}")

    def process_all_pyramids(self):
        """只處理需要的面的金字塔"""
        faces_to_process = self._get_faces_to_process()

        if not faces_to_process:
            logger.info("沒有面需要處理金字塔")
            return

        logger.info(f"正在處理 {len(faces_to_process)} 個面的金字塔: {faces_to_process}")

        with ThreadPoolExecutor(max_workers=min(6, len(faces_to_process))) as executor:
            executor.map(self.pyramid_image, faces_to_process)

    def _should_add_logo(self, cube_index):
        """
        根據 save_mode 和 cube_index 決定是否應該添加 logo

        Args:
            cube_index: 立方體面的索引

        Returns:
            bool: 是否應該添加 logo
        """
        if self.save_mode == "ALL":
            return True  # ALL 模式總是添加 logo
        elif self.save_mode == "blur":
            return False  # blur 模式不添加 logo
        elif self.save_mode == "blur_and_logo":
            return True  # blur_and_logo 模式添加 logo
        else:
            logger.warning(f"未知的 save_mode: {self.save_mode}，預設添加 logo")
            return True

    def _overlay_logo(self, face):
        # 原有的 logo 覆蓋邏輯保持不變
        logger.info("開始覆蓋 logo")
        face_copy = face.copy()
        if len(face_copy.shape) == 2:
            face_copy = cv2.cvtColor(face_copy, cv2.COLOR_GRAY2BGR)

        fh, fw = face_copy.shape[:2]
        lw = int(fw * 0.741)
        lh = int(self.logo_img.shape[0] * (lw / self.logo_img.shape[1]))

        logo = cv2.resize(self.logo_img, (lw, lh))
        x, y = (fw - lw) // 2, (fh - lh) // 2

        if y + lh > fh or x + lw > fw or y < 0 or x < 0:
            logger.warning(f"Logo 位置超出影像邊界，跳過覆蓋")
            return face_copy

        roi = face_copy[y:y+lh, x:x+lw]

        if len(logo.shape) == 3 and logo.shape[2] == 4:
            alpha = logo[:, :, 3:] / 255.0
            roi[:] = (1 - alpha) * roi + alpha * logo[:, :, :3]
        else:
            roi[:] = logo[:, :, :3]

        return face_copy

# --- Model and Detection ---


def detect_face5_with_rotation(image: np.ndarray, models: List, conf_threshold: float = 0.25) -> List[DetectionBox]:
    """第6面特殊檢測：90度和270度旋轉"""
    all_detections = []
    h, w = image.shape[:2]
    
    # 原始圖像檢測
    original_detections = _get_yolo_detections(image, models, conf_threshold)
    all_detections.extend(original_detections)
    
    # 90度旋轉檢測
    try:
        rotated_90 = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
        rotated_90_detections = _get_yolo_detections(rotated_90, models, conf_threshold)
        
        # 轉換座標回原始方向
        for det in rotated_90_detections:
            new_x1 = det.y1
            new_y1 = w - det.x2
            new_x2 = det.y2
            new_y2 = w - det.x1
            
            all_detections.append(DetectionBox(
                x1=int(new_x1), y1=int(new_y1),
                x2=int(new_x2), y2=int(new_y2),
                confidence=det.confidence, class_id=det.class_id
            ))
    except Exception as e:
        logger.error(f"90度旋轉檢測失敗: {e}")
        
    # 270度旋轉檢測
    try:
        rotated_270 = cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
        rotated_270_detections = _get_yolo_detections(rotated_270, models, conf_threshold)
        
        # 轉換座標回原始方向
        for det in rotated_270_detections:
            new_x1 = h - det.y2
            new_y1 = det.x1
            new_x2 = h - det.y1
            new_y2 = det.x2
            
            all_detections.append(DetectionBox(
                x1=int(new_x1), y1=int(new_y1),
                x2=int(new_x2), y2=int(new_y2),
                confidence=det.confidence, class_id=det.class_id
            ))
    except Exception as e:
        logger.error(f"270度旋轉檢測失敗: {e}")
        
    return all_detections


def filter_center_region(detections: List[DetectionBox], 
                       image_shape: tuple) -> List[DetectionBox]:
    """過濾圖像中心圓形區域的檢測框（第6面專用）"""
    if not detections:
        return []
        
    filtered = []
    h, w = image_shape
    center_x, center_y = w // 2, h // 2
    radius = int(min(w, h) * 0.35)
    
    for detection in detections:
        box_center_x, box_center_y = detection.center
        distance = ((box_center_x - center_x) ** 2 + (box_center_y - center_y) ** 2) ** 0.5
        
        if distance > radius:
            filtered.append(detection)
        else:
            logger.debug(f"過濾中心區域檢測框: 距離 {distance:.1f} <= {radius}")
            
    return filtered


def _get_yolo_detections(image: np.ndarray, models: List, conf_threshold: float) -> List[DetectionBox]:
    """從YOLO模型獲取DetectionBox格式的檢測結果"""
    detections = []
    
    for model in models:
        results = model.predict(image, conf=conf_threshold, verbose=False)
        for r in results:
            if r.boxes is not None:
                for box in r.boxes:
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0]) if hasattr(box, 'cls') else 0
                    
                    detections.append(DetectionBox(
                        x1=x1, y1=y1, x2=x2, y2=y2,
                        confidence=confidence, class_id=class_id
                    ))
    
    return detections


def load_models(model_paths):
    try:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"使用設備: {device}")
        models = [YOLO(path) for path in model_paths]
        for model in models:
            model.to(device)
        return models
    except Exception as e:
        logger.error(f"載入模型失敗: {e}")
        return None


def detect_and_blur_face(face_image, models, conf_threshold=0.25, save_mode="ALL", face_id=None):
    """
    偵測並模糊人臉 - 返回模糊區域座標而不是直接模糊圖像

    Args:
        face_image: 輸入影像
        models: YOLO模型列表
        conf_threshold: 信心度閾值
        save_mode: 儲存模式
        face_id: 面ID (0-5)，第6面(face_id=5)會使用特殊檢測方法

    Returns:
        原始影像, 是否有進行檢測, 模糊區域座標列表
    """
    h, w = face_image.shape[:2]
    area_threshold = h * w * 0.03
    
    # 第6面特殊處理（face_id=5 對應 'D' 面）
    if face_id == 5:
        # 使用旋轉檢測方法，優先使用face5_test_conf配置
        face5_conf = conf_threshold  # 預設使用一般置信度
        detections = detect_face5_with_rotation(face_image, models, face5_conf)
        
        # 過濾中心區域
        filtered_detections = filter_center_region(detections, (h, w))
        
        # 轉換為模糊區域格式並應用面積閾值
        blur_regions = []
        for detection in filtered_detections:
            area = (detection.x2 - detection.x1) * (detection.y2 - detection.y1)
            if area <= area_threshold:
                blur_regions.append((detection.x1, detection.y1, detection.x2, detection.y2))
        
        has_detection = len(blur_regions) > 0
        logger.info(f"第6面檢測：原始檢測 {len(detections)} 個，過濾後 {len(filtered_detections)} 個，有效區域 {len(blur_regions)} 個")
    else:
        # 原有的檢測邏輯（適用於其他面）
        all_boxes = []

        # 執行檢測
        for model in models:
            results = model.predict(face_image, conf=conf_threshold, verbose=False)
            for r in results:
                all_boxes.extend(r.boxes.xyxy)

        if not all_boxes:
            return face_image, False, []

        # 收集有效的模糊區域
        blur_regions = []
        for box in all_boxes:
            x1, y1, x2, y2 = map(int, box)
            if (x2 - x1) * (y2 - y1) > area_threshold:
                continue
            blur_regions.append((x1, y1, x2, y2))

        has_detection = len(blur_regions) > 0

    # 根據 save_mode 決定處理策略
    if save_mode == "ALL":
        # ALL 模式：返回原圖和模糊區域，由 ImageCutter 處理
        return face_image, has_detection, blur_regions
    elif save_mode in ["blur", "blur_and_logo"]:
        # blur 模式：只在有檢測時返回模糊區域資訊
        return face_image, has_detection, blur_regions if has_detection else []
    else:
        logger.warning(f"未知的 save_mode: {save_mode}，使用預設行為")
        return face_image, has_detection, blur_regions

# --- Main Processing Functions ---


def process_pano_to_cube(config):
    logger.info("模式: 全景圖到金字塔 (pano_to_cube)")
    input_path = config['input_path']
    output_path_primary = config['primary_output_path']
    models = load_models([config['models']['model1_path'],
                         config['models']['model2_path']])
    if not models:
        return

    save_mode = config.get('detection', {}).get('save_mode', 'ALL')
    logger.info(f"使用儲存模式: {save_mode}")

    # 確保主要輸出目錄存在（這樣進度管理器就能正常工作）
    os.makedirs(output_path_primary, exist_ok=True)

    # 初始化進度管理器
    progress_manager = ProgressManager(config)
    progress_manager.load_progress()

    logo_path = config['logo']['path']
    logo_img = None
    if logo_path:
        logo_img = cv_imread(logo_path)
        if logo_img is not None:
            logger.info(f"Logo 載入成功")

    image_ids = [f for f in os.listdir(
        input_path) if f.lower().endswith(tuple(exts))]
    if not image_ids:
        logger.error(f"在 '{input_path}' 中找不到任何影像。")
        return

    # 準備待處理的項目列表 (在全景模式中，場景名稱就是地區名稱)
    scene_items = []
    for img_id in image_ids:
        scene_name = os.path.splitext(img_id)[0]
        scene_items.append(f"全區/{scene_name}")

    # 獲取待處理的項目
    pending_items = progress_manager.get_pending_items(scene_items)
    
    if not pending_items:
        logger.info("所有項目都已處理完成")
        return

    if progress_manager.enable_check:
        skipped_count = len(scene_items) - len(pending_items)
        logger.info(f"共 {len(image_ids)} 個影像，{skipped_count} 個已跳過，{len(pending_items)} 個待處理")
    else:
        processed_count = len(scene_items) - len(pending_items)
        logger.info(f"共 {len(image_ids)} 個影像，{processed_count} 個已處理，{len(pending_items)} 個待處理")

    h, w, _ = cv_imread(os.path.join(input_path, image_ids[0])).shape
    project_cube = Projection(h, w)

    display_mode = config.get('display_mode', 'verbose')
    processed_count = 0

    for item in pending_items:
        district, scene_name = item.split('/', 1)
        img_id = f"{scene_name}.jpg"  # 假設都是jpg格式
        
        # 檢查檔案是否存在
        img_path = os.path.join(input_path, img_id)
        if not os.path.exists(img_path):
            # 嘗試其他格式
            found = False
            for ext in exts:
                alt_img_path = os.path.join(input_path, f"{scene_name}{ext}")
                if os.path.exists(alt_img_path):
                    img_path = alt_img_path
                    img_id = f"{scene_name}{ext}"
                    found = True
                    break
            if not found:
                logger.warning(f"找不到影像檔案: {scene_name}")
                continue

        processed_count += 1
        # 在 pano_to_cube 模式下，建立 全區/場景名稱 的目錄結構
        scene_folder_path_primary = os.path.join(
            output_path_primary, "全區", scene_name)

        print_progress(processed_count, len(pending_items), img_id, display_mode)

        # 更新進度為待處理
        progress_manager.update_progress(district, scene_name, "待處理")

        img = cv_imread(img_path)
        if img is None:
            continue

        if display_mode == 'verbose':
            logger.info(f"正在處理 {img_id}...")

        start_time = time.time()

        # 生成立方體映射
        original_cube_dict = project_cube.equirec_to_cubemap(
            img, cube_format="dict")

        # 收集所有面的模糊區域資訊
        face_blur_regions = {}
        has_any_detection = False
        cube_index_map = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}

        for face_key, face_img in original_cube_dict.items():
            face_id = cube_index_map[face_key]

            if face_key != 'U':  # 不處理天空面
                # 第6面（'D'面）使用特殊檢測方法
                _, has_detection, blur_regions = detect_and_blur_face(
                    face_img, models, config['detection']['conf_threshold'], save_mode, face_id)

                if blur_regions:  # 只記錄有模糊區域的面
                    face_blur_regions[face_id] = blur_regions
                    has_any_detection = True
                    logger.info(
                        f"面 {face_key}({face_id}) 檢測到 {len(blur_regions)} 個區域")

        # 根據 save_mode 決定是否儲存
        should_save = True
        if save_mode == "blur" and not has_any_detection:
            should_save = False
            logger.info(f"blur 模式：{img_id} 沒有檢測到目標，跳過儲存")
        elif save_mode == "blur_and_logo" and not has_any_detection and logo_img is None:
            should_save = False
            logger.info(f"blur_and_logo 模式：{img_id} 沒有檢測到目標且無logo，跳過儲存")

        if should_save:
            # 創建 ImageCutter 並傳遞模糊區域資訊
            pyra = ImageCutter(
                original_cube_dict,
                img,
                logo_img,
                scene_folder_path_primary,
                config['pyramid_factors'],
                save_mode,
                face_blur_regions  # 傳遞模糊區域資訊
            )

            pyra.create_folder_structure()
            pyra.save_previews_and_html5()
            pyra.process_all_pyramids()
            copy_scene_to_secondary(scene_folder_path_primary, config)

        processing_time = time.time() - start_time

        # 更新進度為完成
        progress_manager.update_progress(district, scene_name, "完成")

        if display_mode == 'verbose':
            status = "處理完成" if should_save else "跳過儲存"
            logger.info(f"{img_id} {status}。")
        elif display_mode == 'quiet':
            status = "處理完成" if should_save else "跳過儲存"
            print(f"{status}，處理時間: {processing_time:.2f} 秒", flush=True)

        upload_to_db(config, {"pano_name": img_id,
                     "scene_name": scene_name, "orientation": "N/A"})


def process_cube_to_cube(config):
    logger.info("模式: 立方體到金字塔 (cube_to_cube) - 批次處理")
    input_path = config['input_path']
    output_path_primary = config['primary_output_path']
    if not all([input_path, output_path_primary]):
        logger.error(
            "請在 config.yaml 中提供有效的 'input_path' 和 'primary_output_path' 路徑。")
        return
    if not os.path.isdir(input_path):
        logger.error(f"輸入路徑 '{input_path}' 不存在或不是一個目錄。")
        return
    os.makedirs(output_path_primary, exist_ok=True)
    
    # 初始化進度管理器（主要輸出目錄已建立）
    progress_manager = ProgressManager(config)
    progress_manager.load_progress()
    
    models = load_models([config['models']['model1_path'],
                         config['models']['model2_path']])
    if not models:
        return

    # 讀取 save_mode 設定
    save_mode = config.get('detection', {}).get('save_mode', 'ALL')
    logger.info(f"使用儲存模式: {save_mode}")

    logo_path = config['logo']['path']
    if logo_path:
        logger.info(f"正在載入 logo: '{logo_path}'")
        logo_img = cv_imread(logo_path)
        if logo_img is not None:
            logger.info(f"Logo 載入成功，尺寸: {logo_img.shape}")
        else:
            logger.warning(f"Logo 載入失敗: '{logo_path}'")
    else:
        logger.info("未設定 logo 路徑")
        logo_img = None

    # Collect all scene paths first
    all_scene_paths = []
    scene_items = []
    for district in os.listdir(input_path):
        dist_path_in = os.path.join(input_path, district)
        if os.path.isdir(dist_path_in):
            for scene in os.listdir(dist_path_in):
                scene_path_in = os.path.join(dist_path_in, scene)
                if os.path.isdir(scene_path_in):
                    all_scene_paths.append(scene_path_in)
                    scene_items.append(f"{district}/{scene}")

    # 獲取待處理的項目
    pending_items = progress_manager.get_pending_items(scene_items)
    
    if not pending_items:
        logger.info("所有項目都已處理完成")
        return

    if progress_manager.enable_check:
        skipped_count = len(scene_items) - len(pending_items)
        logger.info(f"共 {len(all_scene_paths)} 個場景，{skipped_count} 個已跳過，{len(pending_items)} 個待處理")
    else:
        processed_count = len(scene_items) - len(pending_items)
        logger.info(f"共 {len(all_scene_paths)} 個場景，{processed_count} 個已處理，{len(pending_items)} 個待處理")

    display_mode = config.get('display_mode', 'verbose')
    processed_count = 0

    for item in pending_items:
        district, scene_name = item.split('/', 1)
        scene_path_in = os.path.join(input_path, district, scene_name)
        
        # 檢查場景路徑是否存在
        if not os.path.exists(scene_path_in):
            logger.warning(f"場景路徑不存在: {scene_path_in}")
            continue
            
        processed_count += 1
        
        # 顯示進度
        display_name = f"{district}/{scene_name}"
        print_progress(processed_count, len(pending_items), display_name, display_mode)

        # 更新進度為待處理
        progress_manager.update_progress(district, scene_name, "待處理")

        # Reconstruct the output path to maintain the district/scene structure
        relative_path = os.path.relpath(scene_path_in, input_path)
        scene_path_out = os.path.join(output_path_primary, relative_path)

        start_time = time.time()
        process_single_scene(scene_path_in, scene_path_out,
                             config, models, logo_img, save_mode)
        processing_time = time.time() - start_time

        # 更新進度為完成
        progress_manager.update_progress(district, scene_name, "完成")

        if display_mode == 'quiet':
            print(f"處理完成，處理時間: {processing_time:.2f} 秒", flush=True)


def process_list_cube(config):
    logger.info("模式: 清單到金字塔 (list_cube)")
    settings = config['list_cube_settings']
    input_path = config['input_path']
    output_path_primary = config['primary_output_path']
    list_file, list_mode, list_cols = settings['list_file'], settings['list_mode'], settings.get(
        'list_columns', ['區', '場景'])
    if not all([input_path, output_path_primary, list_file]):
        logger.error("請在 config.yaml 中提供有效的路徑。")
        return
    if not os.path.exists(list_file):
        logger.error(f"清單檔案 '{list_file}' 不存在。")
        return
    try:
        df = pd.read_csv(list_file) if list_file.endswith(
            '.csv') else pd.read_excel(list_file)
    except Exception as e:
        logger.error(f"讀取清單檔案 '{list_file}' 失敗: {e}")
        return
    scene_col = list_cols[1]
    if scene_col not in df.columns:
        logger.error(f"清單檔案中找不到場景欄位: '{scene_col}'")
        return
    target_scenes = set(df[scene_col].str.strip().astype(str))

    # 確保主要輸出目錄存在（這樣進度管理器就能正常工作）
    os.makedirs(output_path_primary, exist_ok=True)

    # 初始化進度管理器
    progress_manager = ProgressManager(config)
    progress_manager.load_progress()

    all_scenes_map = {}
    scene_items = []
    # Build a map of all scenes in the input_path, including district structure
    for district in os.listdir(input_path):
        district_path = os.path.join(input_path, district)
        if os.path.isdir(district_path):
            for scene in os.listdir(district_path):
                scene_path = os.path.join(district_path, scene)
                if os.path.isdir(scene_path):
                    # Store scene_name: full_path
                    all_scenes_map[scene] = scene_path
                    scene_items.append(f"{district}/{scene}")

    scenes_to_process_paths = []
    filtered_scene_items = []
    if list_mode == 'include':
        for scene_name, scene_path in all_scenes_map.items():
            if scene_name in target_scenes:
                scenes_to_process_paths.append(scene_path)
                district_name = os.path.basename(os.path.dirname(scene_path))
                filtered_scene_items.append(f"{district_name}/{scene_name}")
    elif list_mode == 'exclude':
        for scene_name, scene_path in all_scenes_map.items():
            if scene_name not in target_scenes:
                scenes_to_process_paths.append(scene_path)
                district_name = os.path.basename(os.path.dirname(scene_path))
                filtered_scene_items.append(f"{district_name}/{scene_name}")
    else:
        logger.error(
            f"未知的 list_mode: '{list_mode}'。請使用 'include' 或 'exclude'。")
        return

    # 獲取待處理的項目
    pending_items = progress_manager.get_pending_items(filtered_scene_items)
    
    if not pending_items:
        logger.info("所有項目都已處理完成")
        return

    if progress_manager.enable_check:
        skipped_count = len(filtered_scene_items) - len(pending_items)
        logger.info(f"找到 {len(scenes_to_process_paths)} 個場景需要處理，{skipped_count} 個已跳過，{len(pending_items)} 個待處理")
    else:
        processed_count = len(filtered_scene_items) - len(pending_items)
        logger.info(f"找到 {len(scenes_to_process_paths)} 個場景需要處理，{processed_count} 個已處理，{len(pending_items)} 個待處理")
    
    models = load_models([config['models']['model1_path'],
                         config['models']['model2_path']])
    if not models:
        return

    # 讀取 save_mode 設定
    save_mode = config.get('detection', {}).get('save_mode', 'ALL')
    logger.info(f"使用儲存模式: {save_mode}")

    logo_path = config['logo']['path']
    if logo_path:
        logger.info(f"正在載入 logo: '{logo_path}'")
        logo_img = cv_imread(logo_path)
        if logo_img is not None:
            logger.info(f"Logo 載入成功，尺寸: {logo_img.shape}")
        else:
            logger.warning(f"Logo 載入失敗: '{logo_path}'")
    else:
        logger.info("未設定 logo 路徑")
        logo_img = None

    display_mode = config.get('display_mode', 'verbose')
    processed_count = 0

    for item in pending_items:
        district, scene_name = item.split('/', 1)
        scene_path_in = os.path.join(input_path, district, scene_name)
        
        # 檢查場景路徑是否存在
        if not os.path.exists(scene_path_in):
            logger.warning(f"場景路徑不存在: {scene_path_in}")
            continue
            
        processed_count += 1
        
        # 顯示進度
        display_name = f"{district}/{scene_name}"
        print_progress(processed_count, len(pending_items), display_name, display_mode)

        # 更新進度為待處理
        progress_manager.update_progress(district, scene_name, "待處理")

        # Reconstruct the output path to maintain the district/scene structure
        relative_path = os.path.relpath(scene_path_in, input_path)
        scene_path_out = os.path.join(output_path_primary, relative_path)

        start_time = time.time()
        process_single_scene(scene_path_in, scene_path_out,
                             config, models, logo_img, save_mode)
        processing_time = time.time() - start_time

        # 更新進度為完成
        progress_manager.update_progress(district, scene_name, "完成")

        if display_mode == 'quiet':
            print(f"處理完成，處理時間: {processing_time:.2f} 秒", flush=True)


def process_single_scene(input_scene_path, primary_output_scene_path, config, models, logo_img, save_mode="ALL"):
    scene_name = os.path.basename(input_scene_path)
    logger.info(f"處理場景: {scene_name} (儲存模式: {save_mode})")

    # 讀取 html5 文件夾中的圖片
    html5_folder = os.path.join(input_scene_path, 'html5')
    if not os.path.isdir(html5_folder):
        logger.warning(f"找不到 html5 資料夾 '{html5_folder}'，跳過。")
        return
    
    face_files = {i: os.path.join(html5_folder, f'{i}.jpg') for i in range(6)}
    original_faces = {i: cv_imread(p) for i, p in face_files.items() if os.path.exists(p)}
    
    if len(original_faces) != 6:
        logger.warning(f"場景 '{scene_name}' 的 html5 資料夾不完整，跳過。")
        return
    
    # 進行檢測並收集模糊區域資訊
    face_blur_regions = {}
    has_any_detection = False

    for face_id, face_img in original_faces.items():
        if face_id != Face.UP:  # 不處理天空面
            # 第6面（Face.DOWN）使用特殊檢測方法
            _, has_detection, blur_regions = detect_and_blur_face(
                face_img, models, config['detection']['conf_threshold'], save_mode, face_id)
            
            if blur_regions:
                face_blur_regions[face_id] = blur_regions
                has_any_detection = True
                logger.info(f"場景 {scene_name} 面 {face_id} 檢測到 {len(blur_regions)} 個區域")

    # 確定需要處理的面
    faces_to_process = set()
    if save_mode == "ALL":
        faces_to_process = set(range(6))
    else:
        # 添加有模糊區域的面
        for face_id in face_blur_regions:
            if face_blur_regions[face_id]:
                faces_to_process.add(face_id)
        
        # blur_and_logo 模式還要添加 logo 面
        if save_mode == "blur_and_logo" and logo_img is not None:
            faces_to_process.add(Face.DOWN)

    # 根據 save_mode 決定是否需要更新
    need_logo_update = logo_img is not None and save_mode in ["ALL", "blur_and_logo"]
    
    if save_mode == "blur" and not has_any_detection:
        logger.info(f"blur 模式：場景 '{scene_name}' 沒有檢測到目標，跳過處理")
        return
    elif save_mode == "blur_and_logo" and not has_any_detection and not need_logo_update:
        logger.info(f"blur_and_logo 模式：場景 '{scene_name}' 沒有檢測到目標且無logo，跳過處理")
        return

    # 如果有 logo 更新需求，標記為需要更新
    if need_logo_update and Face.DOWN not in faces_to_process:
        faces_to_process.add(Face.DOWN)

    # ✅ 關鍵修改：根據 save_mode 決定複製策略
    if save_mode == "ALL":
        # ALL 模式：完整複製後重新生成
        try:
            copy_with_skip(input_scene_path, primary_output_scene_path)
            logger.info(f"場景 {scene_name} 完整複製完成")
        except Exception as e:
            logger.error(f"複製場景失敗: {e}")
            return
    else:
        # blur 和 blur_and_logo 模式：不複製，只創建基本結構
        logger.info(f"blur 模式：不複製原有瓦片，只生成包含模糊區域的瓦片")
        
        # 只複製基本文件（非瓦片文件）
        try:
            copy_basic_files_only(input_scene_path, primary_output_scene_path)
        except Exception as e:
            logger.error(f"複製基本文件失敗: {e}")
            return
    
    logger.info(f"為場景 '{scene_name}' 重新生成金字塔...")
    
    # 重建 cube_dict 用於 ImageCutter
    face_keys_map = {0: 'F', 1: 'R', 2: 'B', 3: 'L', 4: 'U', 5: 'D'}
    cube_dict = {face_keys_map[k]: v for k, v in original_faces.items()}
    
    # 創建 ImageCutter 並傳遞模糊區域資訊
    pyra = ImageCutter(
        cube_dict, 
        original_faces.get(0), 
        logo_img, 
        primary_output_scene_path, 
        config['pyramid_factors'], 
        save_mode, 
        face_blur_regions
    )

    # 創建文件夾結構
    pyra.create_folder_structure()
    
    # 更新 html5 和金字塔
    pyra.save_previews_and_html5()
    pyra.process_all_pyramids()
    
    logger.info(f"場景 '{scene_name}' 更新完成。")
    copy_scene_to_secondary(primary_output_scene_path, config)

# 新增：只複製基本文件的函數
def copy_basic_files_only(src, dst):
    """只複製基本文件，不複製瓦片"""
    os.makedirs(dst, exist_ok=True)
    
    # 複製基本文件（如 preview.jpg, thumbnail.jpg 等）
    for item in os.listdir(src):
        src_item = os.path.join(src, item)
        dst_item = os.path.join(dst, item)
        
        if os.path.isfile(src_item):
            # 複製基本文件
            filename = os.path.basename(src_item).lower()
            if filename not in ['thumbs.db', 'desktop.ini', '.ds_store']:
                try:
                    shutil.copy2(src_item, dst_item)
                    logger.debug(f"複製基本文件: {item}")
                except Exception as e:
                    logger.warning(f"複製文件失敗: {src_item} - {e}")
        
        elif os.path.isdir(src_item):
            item_name = os.path.basename(src_item)
            
            # 跳過瓦片文件夾（數字文件夾）
            if item_name.isdigit():
                logger.debug(f"跳過瓦片文件夾: {item_name}")
                continue
            
            # 跳過 html5 文件夾（會重新生成）
            if item_name == 'html5':
                logger.debug(f"跳過 html5 文件夾（會重新生成）")
                continue
            
            # 複製其他文件夾
            try:
                shutil.copytree(src_item, dst_item, dirs_exist_ok=True)
                logger.debug(f"複製文件夾: {item_name}")
            except Exception as e:
                logger.warning(f"複製文件夾失敗: {src_item} - {e}")

# 移除原來的 selective_copy_for_blur_mode 函數，因為不再需要

# 自定義複製函數（保留給 ALL 模式使用）
def copy_with_skip(src, dst):
    """複製檔案，遇到錯誤時跳過該檔案並繼續"""
    try:
        if os.path.isdir(src):
            os.makedirs(dst, exist_ok=True)
            for item in os.listdir(src):
                src_item = os.path.join(src, item)
                dst_item = os.path.join(dst, item)
                copy_with_skip(src_item, dst_item)
        else:
            # 跳過系統檔案如 Thumbs.db
            filename = os.path.basename(src).lower()
            if filename in ['thumbs.db', 'desktop.ini', '.ds_store']:
                logger.debug(f"跳過系統檔案: {src}")
                return
            shutil.copy2(src, dst)
    except PermissionError as e:
        logger.warning(f"權限不足，跳過檔案: {src} - {e}")
    except Exception as e:
        logger.warning(f"複製檔案失敗，跳過: {src} - {e}")


def upload_to_db(config, data):
    if not config.get('db_upload_settings', {}).get('enabled', False):
        return
    logger.info(f"資料庫上傳功能尚待實現。資料: {data}")


def is_running_as_exe():
    """檢測是否在 exe 環境中運行"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')


def validate_config_file(file_path):
    """驗證配置檔案是否存在且為 yaml 格式"""
    if not file_path:
        return False, "請輸入配置檔案路徑"
    if not os.path.exists(file_path):
        return False, f"配置檔案不存在: {file_path}"
    if not file_path.lower().endswith(('.yaml', '.yml')):
        return False, "請提供 .yaml 或 .yml 格式的配置檔案"
    return True, ""


def get_config_file_path():
    """獲取配置檔案路徑"""
    print("=" * 60)
    print("         Generate Pyramid - 配置檔案設定")
    print("=" * 60)
    print("請輸入配置檔案 (config.yaml) 的路徑")
    print("按 Ctrl+C 可隨時退出")
    print("-" * 60)

    while True:
        try:
            # 嘗試使用預設的 config.yaml
            default_config = "config.yaml"
            if os.path.exists(default_config):
                user_input = input(f"\n📁 配置檔案路徑 [{default_config}]: ").strip()
                if not user_input:
                    user_input = default_config
            else:
                user_input = input("\n📁 配置檔案路徑: ").strip()

            # 驗證配置檔案
            is_valid, error_msg = validate_config_file(user_input)
            if not is_valid:
                print(f"❌ {error_msg}")
                continue

            return user_input

        except (EOFError, KeyboardInterrupt):
            print("\n❌ 已取消輸入")
            return None


def display_config_summary(config):
    """顯示配置摘要並等待確認"""
    print("\n" + "=" * 60)
    print("         配置檔案內容")
    print("=" * 60)

    print("📋 基本設定:")
    print(f"   🔧 處理模式: {config.get('mode', 'pano_to_cube')}")
    print(f"   📺 顯示模式: {config.get('display_mode', 'verbose')}")
    print()

    print("📁 路徑設定:")
    print(f"   📂 輸入路徑: {config.get('input_path', '未設定')}")
    print(f"   📤 主要輸出: {config.get('primary_output_path', '未設定')}")
    secondary = config.get('secondary_output_path', '')
    if secondary:
        print(f"   📤 次要輸出: {secondary}")
    print()

    print("🤖 模型設定:")
    models = config.get('models', {})
    print(f"   🎯 模型1: {models.get('model1_path', '未設定')}")
    print(f"   🎯 模型2: {models.get('model2_path', '未設定')}")
    detection = config.get('detection', {})
    print(f"   🎚️ 信心閾值: {detection.get('conf_threshold', 0.05)}")
    print(f"   💾 儲存模式: {detection.get('save_mode', 'ALL')}")
    print()

    print("🎨 Logo 設定:")
    logo = config.get('logo', {})
    logo_path = logo.get('path', '')
    if logo_path:
        print(f"   🖼️ Logo 路徑: {logo_path}")
        print(f"   📏 縮放比例: {logo.get('scale', 0.741)}")
    else:
        print("   🖼️ Logo: 未設定")
    print()

    print("🔄 進度恢復設定:")
    progress_settings = config.get('progress_settings', {})
    enable_check = progress_settings.get('enable_check', True)
    print(f"   ✅ 檢查功能: {'啟用' if enable_check else '停用'}")
    if enable_check:
        print("      (檢查 html5 資料夾，有就跳過)")
    else:
        print("      (不檢查，從進度檔案繼續)")
    
    progress_file = progress_settings.get('progress_file', '')
    if progress_file:
        print(f"   📄 進度檔案: {progress_file}")
    else:
        print("   📄 進度檔案: 自動選擇最新檔案")
    
    progress_dir = progress_settings.get('progress_dir', config.get('primary_output_path', '.'))
    print(f"   📁 進度目錄: {progress_dir}")
    print()

    # 根據模式顯示特定設定
    mode = config.get('mode', 'pano_to_cube')
    if mode == 'list_cube':
        print("📋 清單模式設定:")
        list_settings = config.get('list_cube_settings', {})
        print(f"   📄 清單檔案: {list_settings.get('list_file', '未設定')}")
        print(f"   📋 處理模式: {list_settings.get('list_mode', 'include')}")
        print(f"   📊 欄位設定: {list_settings.get('list_columns', ['區', '場景'])}")
        print()

    print("🗄️ 資料庫設定:")
    db_settings = config.get('db_upload_settings', {})
    db_enabled = db_settings.get('enabled', False)
    print(f"   📊 資料庫上傳: {'啟用' if db_enabled else '停用'}")
    if db_enabled:
        connection_string = db_settings.get('connection_string', '')
        if connection_string and connection_string != 'your_db_connection_string_here':
            print(f"   🔗 連線字串: {connection_string[:50]}...")
        else:
            print("   🔗 連線字串: 未設定")
    print()

    print("⚠️  請確認以上配置是否正確")
    print("=" * 60)

    while True:
        try:
            confirm = input("\n✅ 確認開始執行? (y/n): ").strip().lower()
            if confirm == 'y':
                print()
                print("🚀 開始執行...")
                print()
                return True
            elif confirm == 'n':
                print()
                print("❌ 已取消執行")
                return False
            else:
                print("請輸入 'y' 或 'n'")
        except (EOFError, KeyboardInterrupt):
            print("\n❌ 已取消執行")
            return False


def main():
    # 在 exe 環境中先詢問配置檔案路徑
    if is_running_as_exe():
        config_path = get_config_file_path()
        if config_path is None:
            input("按任意鍵退出...")
            return

        config = load_config(config_path)
        if config is None:
            print(f"❌ 無法載入配置檔案: {config_path}")
            input("按任意鍵退出...")
            return

        # 顯示配置並等待確認
        if not display_config_summary(config):
            input("按任意鍵退出...")
            return
    else:
        # 在非 exe 環境中嘗試載入預設的 config.yaml
        config = load_config()
        if config is None:
            print("❌ 找不到 config.yaml 檔案")
            print("💡 建議使用 exe 版本或建立 config.yaml 檔案")
            return

    global logger
    logger = setup_logging(config.get(
        'log_file', 'image_processing.log'), config.get('display_mode', 'verbose'))
    config['pyramid_factors'] = [611, 1222, 2445]
    total_start_time = time.time()
    mode = config.get('mode', 'pano_to_cube')

    try:
        if mode == 'pano_to_cube':
            process_pano_to_cube(config)
        elif mode == 'cube_to_cube':
            process_cube_to_cube(config)
        elif mode == 'list_cube':
            process_list_cube(config)
        else:
            logger.error(f"未知的模式: '{mode}'。請檢查 config.yaml。")

        logger.info(f"所有任務完成，總耗時: {time.time() - total_start_time:.2f} 秒")

        # 在 exe 環境中等待用戶確認後退出
        if is_running_as_exe():
            print()
            print("🎉 處理完成！")
            input("按任意鍵退出...")

    except KeyboardInterrupt:
        logger.info("用戶中止執行")
        if is_running_as_exe():
            print()
            print("⚠️  執行已中止")
            input("按任意鍵退出...")
    except Exception as e:
        logger.error(f"執行過程中發生錯誤: {e}")
        if is_running_as_exe():
            print()
            print(f"❌ 執行失敗: {e}")
            input("按任意鍵退出...")


if __name__ == "__main__":
    main()
