"""
插值後端模組

本模組提供了插值運算的高效實現。使用純 NumPy 進行向量化運算，
為主插值器提供高效的計算後端。

注意：原本的 GPU 支援已移除，現在使用優化的 NumPy 實現。
"""

from __future__ import annotations
import warnings
import numpy as np


class InterpolatorBackend:
    """
    插值器後端
    
    提供高效的插值計算介面，使用優化的 NumPy 向量化運算。
    """

    def __init__(self):
        """
        初始化插值器後端。
        """
        pass

    def interpolate(
        self,
        image: np.ndarray,
        x_coords: np.ndarray,
        y_coords: np.ndarray,
        method: str = "bilinear",
    ) -> np.ndarray:
        """
        執行插值的主函數。

        Args:
            image: 輸入的圖像陣列。
            x_coords: 目標點的 x 座標陣列。
            y_coords: 目標點的 y 座標陣列。
            method: 要使用的插值方法。

        Returns:
            插值後的結果陣列。
        """
        if method == "bilinear":
            return self._bilinear(image, x_coords, y_coords)
        elif method == "bicubic":
            return self._bicubic(image, x_coords, y_coords)
        else:
            raise ValueError(f"不支援的插值方法: {method}")

    def _bilinear(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """
        執行雙線性插值的核心邏輯。

        Args:
            image: 圖像陣列。
            x_coords: x 座標陣列。
            y_coords: y 座標陣列。

        Returns:
            插值後的結果陣列。
        """
        h, w = image.shape[:2]

        # 邊界處理：確保所有座標點都在圖像範圍內
        x_coords = np.clip(x_coords, 0, w - 1 - 1e-6)
        y_coords = np.clip(y_coords, 0, h - 1 - 1e-6)

        # 計算周圍四個像素點的整數座標
        x0 = np.floor(x_coords).astype(np.int32)
        y0 = np.floor(y_coords).astype(np.int32)
        x1 = np.minimum(x0 + 1, w - 1)
        y1 = np.minimum(y0 + 1, h - 1)

        # 計算在 x 和 y 方向上的插值權重
        wx = x_coords - x0
        wy = y_coords - y0

        # 根據圖像維度（灰階或彩色）執行插值
        if image.ndim == 2:  # 灰階圖像
            v00, v01 = image[y0, x0], image[y0, x1]
            v10, v11 = image[y1, x0], image[y1, x1]
            result = v00*(1-wx)*(1-wy) + v01*wx*(1-wy) + v10*(1-wx)*wy + v11*wx*wy
        else:  # 彩色圖像
            # 獲取四個角的像素值
            v00 = image[y0, x0]
            v01 = image[y0, x1]
            v10 = image[y1, x0]
            v11 = image[y1, x1]

            # 為了與顏色通道進行廣播，需要擴展權重陣列的維度
            wx = wx[..., None]
            wy = wy[..., None]

            # 向量化插值計算
            result = (v00 * (1 - wx) * (1 - wy) +
                      v01 * wx * (1 - wy) +
                      v10 * (1 - wx) * wy +
                      v11 * wx * wy)

        return result

    def _bicubic(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """
        雙三次插值的簡化實現。

        注意：目前這是一個佔位符實現，它會發出警告並回退到雙線性插值。
        完整的雙三次插值實現需要更複雜的核函數。
        """
        warnings.warn(
            "雙三次插值（bicubic）尚未完全實現，將回退到雙線性插值（bilinear）。",
            UserWarning,
        )
        return self._bilinear(image, x_coords, y_coords)


# 為了向後相容性，提供舊的類名別名
GPUInterpolator = InterpolatorBackend