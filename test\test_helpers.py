"""
測試助手函數和工具
Test Helper Functions and Utilities
"""

import json
import os
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, Mock

import numpy as np


def create_test_image(width=640, height=480, channels=3, dtype=np.uint8):
    """
    建立測試用影像

    Args:
        width: 影像寬度
        height: 影像高度
        channels: 色彩通道數
        dtype: 資料類型

    Returns:
        numpy.ndarray: 測試影像
    """
    if channels == 1:
        return np.random.randint(0, 255, (height, width), dtype=dtype)
    else:
        return np.random.randint(0, 255, (height, width, channels), dtype=dtype)


def create_test_panorama(width=2048, height=1024):
    """
    建立測試用全景影像

    Args:
        width: 全景影像寬度 (通常是高度的2倍)
        height: 全景影像高度

    Returns:
        numpy.ndarray: 測試全景影像
    """
    return create_test_image(width, height, 3)


def create_test_cube_faces(size=512):
    """
    建立測試用立方體面影像

    Args:
        size: 立方體面尺寸

    Returns:
        dict: 包含六個面的字典
    """
    faces = {}
    face_names = ["front", "right", "back", "left", "up", "down"]

    for i, face_name in enumerate(face_names):
        # 每個面使用不同的顏色模式
        face_image = np.full((size, size, 3), i * 40, dtype=np.uint8)
        faces[face_name] = face_image

    return faces


def create_test_detection_results():
    """
    建立測試用檢測結果

    Returns:
        list: 檢測結果列表
    """
    return [
        {
            "bbox": [100, 100, 50, 50],  # x, y, width, height
            "confidence": 0.85,
            "class": "face",
            "class_id": 0,
        },
        {
            "bbox": [200, 150, 80, 40],
            "confidence": 0.72,
            "class": "license_plate",
            "class_id": 1,
        },
    ]


def create_mock_yolo_model():
    """
    建立Mock YOLO模型

    Returns:
        Mock: Mock YOLO模型物件
    """
    mock_model = Mock()

    # Mock prediction results
    mock_result = Mock()
    mock_result.boxes = Mock()
    mock_result.boxes.data = np.array(
        [
            [100, 50, 300, 200, 0.85, 0],  # x1, y1, x2, y2, conf, class
            [400, 100, 600, 300, 0.75, 1],
        ]
    )

    mock_model.predict.return_value = [mock_result]
    return mock_model


def create_mock_gpu_manager(available=False):
    """
    建立Mock GPU管理器

    Args:
        available: GPU是否可用

    Returns:
        Mock: Mock GPU管理器
    """
    mock_gpu = Mock()
    mock_gpu.is_available.return_value = available
    mock_gpu.get_device_count.return_value = 1 if available else 0
    mock_gpu.get_memory_info.return_value = {
        "free": 4000 if available else 0,
        "total": 8000 if available else 0,
        "used": 4000 if available else 0,
    }
    return mock_gpu


def create_temp_config_file(config_data=None):
    """
    建立臨時配置檔案

    Args:
        config_data: 配置資料字典

    Returns:
        str: 臨時檔案路徑
    """
    if config_data is None:
        config_data = {
            "processing": {"cube_size": 1024},
            "model": {"conf_threshold": 0.5},
            "system": {"enable_gpu": False},
        }

    temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False)
    json.dump(config_data, temp_file, indent=2)
    temp_file.close()

    return temp_file.name


def cleanup_temp_file(file_path):
    """
    清理臨時檔案

    Args:
        file_path: 檔案路徑
    """
    try:
        os.unlink(file_path)
    except (OSError, FileNotFoundError):
        pass


def assert_image_shape(image, expected_shape):
    """
    斷言影像尺寸

    Args:
        image: 影像陣列
        expected_shape: 期望的尺寸
    """
    assert (
        image.shape == expected_shape
    ), f"Expected shape {expected_shape}, got {image.shape}"


def assert_valid_bbox(bbox, image_shape):
    """
    斷言邊界框有效性

    Args:
        bbox: 邊界框 [x, y, width, height]
        image_shape: 影像尺寸
    """
    x, y, w, h = bbox
    height, width = image_shape[:2]

    assert x >= 0, f"BBox x coordinate {x} should be >= 0"
    assert y >= 0, f"BBox y coordinate {y} should be >= 0"
    assert x + w <= width, f"BBox right edge {x + w} should be <= image width {width}"
    assert (
        y + h <= height
    ), f"BBox bottom edge {y + h} should be <= image height {height}"
    assert w > 0, f"BBox width {w} should be > 0"
    assert h > 0, f"BBox height {h} should be > 0"


def create_test_coordinate_grid(width, height):
    """
    建立測試用座標網格

    Args:
        width: 網格寬度
        height: 網格高度

    Returns:
        tuple: (u_coords, v_coords) 座標陣列
    """
    u = np.linspace(0, 1, width)
    v = np.linspace(0, 1, height)
    u_grid, v_grid = np.meshgrid(u, v)
    return u_grid, v_grid


def compare_images_approximately(img1, img2, tolerance=1):
    """
    近似比較兩張影像

    Args:
        img1: 第一張影像
        img2: 第二張影像
        tolerance: 容許誤差

    Returns:
        bool: 是否近似相等
    """
    if img1.shape != img2.shape:
        return False

    diff = np.abs(img1.astype(np.float32) - img2.astype(np.float32))
    return np.all(diff <= tolerance)


def generate_test_performance_data():
    """
    生成測試用性能資料

    Returns:
        dict: 性能資料
    """
    return {
        "cpu_usage": 45.2,
        "memory_usage": 1024,  # MB
        "gpu_usage": 78.5,
        "processing_time": 2.34,  # seconds
        "throughput": 12.5,  # images/second
    }


class MockLogger:
    """Mock日誌記錄器"""

    def __init__(self):
        self.messages = {
            "debug": [],
            "info": [],
            "warning": [],
            "error": [],
            "critical": [],
        }

    def debug(self, msg):
        self.messages["debug"].append(msg)

    def info(self, msg):
        self.messages["info"].append(msg)

    def warning(self, msg):
        self.messages["warning"].append(msg)

    def error(self, msg):
        self.messages["error"].append(msg)

    def critical(self, msg):
        self.messages["critical"].append(msg)

    def get_messages(self, level=None):
        if level:
            return self.messages.get(level, [])
        return self.messages


class TestDataManager:
    """測試資料管理器"""

    def __init__(self):
        self.temp_files = []
        self.temp_dirs = []

    def create_temp_image_file(self, width=640, height=480):
        """建立臨時影像檔案"""
        import cv2

        image = create_test_image(width, height)
        temp_file = tempfile.NamedTemporaryFile(suffix=".jpg", delete=False)
        cv2.imwrite(temp_file.name, image)
        self.temp_files.append(temp_file.name)
        return temp_file.name

    def create_temp_dir(self):
        """建立臨時目錄"""
        temp_dir = tempfile.mkdtemp()
        self.temp_dirs.append(temp_dir)
        return temp_dir

    def cleanup(self):
        """清理所有臨時檔案和目錄"""
        for file_path in self.temp_files:
            cleanup_temp_file(file_path)

        for dir_path in self.temp_dirs:
            try:
                import shutil

                shutil.rmtree(dir_path)
            except (OSError, FileNotFoundError):
                pass

        self.temp_files.clear()
        self.temp_dirs.clear()


# 常用測試常數
TEST_IMAGE_SIZES = [(64, 64), (256, 256), (512, 512), (1024, 1024)]

TEST_PANORAMA_SIZES = [(128, 64), (512, 256), (1024, 512), (2048, 1024)]

TEST_CONFIDENCE_THRESHOLDS = [0.3, 0.5, 0.7, 0.9]

TEST_INTERPOLATION_METHODS = ["nearest", "linear", "cubic", "lanczos"]
