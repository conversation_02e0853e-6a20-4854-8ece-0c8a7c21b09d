"""
插值器主類別與策略模組

本模組定義了影像插值的核心執行器：
1.  `AdvancedInterpolator`: 一個功能強大的單一方法插值器，它作為一個協調者，
    能夠根據設定調用不同的CPU/GPU後端、核函數和快取機制來執行插值。
2.  `MultipleInterpolator`: 一個策略驅動的插值器，用於組合多個 `AdvancedInterpolator`
    實例，以實現更複雜的插值效果，如加權混合、自適應選擇等。
"""

import multiprocessing as mp
import warnings
from concurrent.futures import ThreadPoolExecutor
from typing import Optional, Dict, Callable, Any

import numpy as np
from scipy.ndimage import map_coordinates

from config.interpolation import (InterpolationConfig, InterpolationMethod,
                                  MultipleInterpolationConfig,
                                  MultipleInterpolationStrategy)

from .cache import _global_cache
from .gpu_backend import GPUInterpolator
from .kernels.advanced import (_bartlett_kernel_2, _blackman_harris_kernel_2,
                               _catmull_rom_kernel, _gaussian_kernel_default,
                               _hermite_kernel, _kaiser_kernel_default,
                               _lanczos_kernel_2, _lanczos_kernel_3,
                               _lanczos_kernel_4, _lanczos_kernel_8,
                               _mitchell_kernel_default, _nuttall_kernel_2,
                               _sinc_kernel, _spline36_kernel,
                               _spline64_kernel, _tukey_kernel_default,
                               _welch_kernel_2)
from .kernels.basic import (_bilinear_core, _box_kernel, _cubic_convolution,
                            _triangular_kernel)


class AdvancedInterpolator:
    """
    高級插值器 - 核心執行類別

    本類別根據傳入的 `InterpolationConfig` 設定，提供一個統一的 `interpolate` 介面。
    它會自動處理後端選擇（GPU優先）、快取存取、方法分派等複雜邏輯。
    """

    def __init__(self, config: Optional[InterpolationConfig] = None):
        """
        初始化高級插值器。

        Args:
            config: 插值設定物件。若未提供，則使用預設的雙線性插值設定。
        """
        self.config = config or InterpolationConfig(method=InterpolationMethod.BILINEAR)
        self.gpu_interpolator = GPUInterpolator() if self.config.use_gpu else None
        self.cache = _global_cache if self.config.cache_enabled else None

        # 定義一組核心、高效能且推薦使用的插值方法。
        self.core_methods = {
            InterpolationMethod.NEAREST,
            InterpolationMethod.BILINEAR,
            InterpolationMethod.BICUBIC,
            InterpolationMethod.LANCZOS3,
        }

        # 將插值方法枚舉映射到對應的內部實現函數。
        self.method_map: Dict[InterpolationMethod, Callable[..., np.ndarray]] = {
            InterpolationMethod.NEAREST: self._nearest,
            InterpolationMethod.LINEAR: self._linear,
            InterpolationMethod.BILINEAR: self._bilinear,
            InterpolationMethod.BICUBIC: self._bicubic,
            InterpolationMethod.QUADRATIC: self._quadratic,
            InterpolationMethod.CUBIC: self._cubic,
            InterpolationMethod.QUARTIC: self._quartic,
            InterpolationMethod.QUINTIC: self._quintic,
            InterpolationMethod.BSPLINE: self._bspline,
            InterpolationMethod.LANCZOS2: self._lanczos2,
            InterpolationMethod.LANCZOS3: self._lanczos3,
            InterpolationMethod.LANCZOS4: self._lanczos4,
            InterpolationMethod.LANCZOS8: self._lanczos8,
            InterpolationMethod.MITCHELL: self._mitchell,
            InterpolationMethod.CATMULL_ROM: self._catmull_rom,
            InterpolationMethod.GAUSSIAN: self._gaussian,
            InterpolationMethod.SINC: self._sinc,
            InterpolationMethod.KAISER: self._kaiser,
            InterpolationMethod.BLACKMAN: self._blackman,
            InterpolationMethod.HAMMING: self._hamming,
            InterpolationMethod.HANN: self._hann,
            InterpolationMethod.HERMITE: self._hermite,
            InterpolationMethod.TRIANGULAR: self._triangular,
            InterpolationMethod.BOX: self._box,
            InterpolationMethod.WELCH: self._welch,
            InterpolationMethod.BARTLETT: self._bartlett,
            InterpolationMethod.TUKEY: self._tukey,
            InterpolationMethod.BLACKMAN_HARRIS: self._blackman_harris,
            InterpolationMethod.NUTTALL: self._nuttall,
            InterpolationMethod.SPLINE36: self._spline36,
            InterpolationMethod.SPLINE64: self._spline64,
            InterpolationMethod.ANISOTROPIC: self._anisotropic,
        }

        # 初始化用於平行處理的執行緒池。
        max_workers = self.config.parallel_threads or min(mp.cpu_count(), 8)
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)

    def interpolate(
        self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray
    ) -> np.ndarray:
        """
        對給定影像在指定座標點上執行插值。

        處理流程：
        1. 驗證輸入資料的有效性。
        2. 檢查快取中是否存在已計算的結果。
        3. 若啟用GPU且條件滿足，嘗試使用GPU進行插值。
        4. 若GPU失敗或未啟用，則回退到對應的CPU插值方法。
        5. 將計算結果存入快取。
        6. 返回插值後的結果。
        """
        self._validate_input(image, x_coords, y_coords)

        method = self.config.method
        if isinstance(method, str):
            method = InterpolationMethod(method.lower())

        if method not in self.core_methods:
            warnings.warn(
                f"插值方法 '{method.value}' 並非核心推薦方法。"
                f"為獲得最佳性能與穩定性，建議使用: {[m.value for m in self.core_methods]}",
                DeprecationWarning,
            )

        if self.cache:
            cache_key_args = (image.tobytes(), x_coords.tobytes(), y_coords.tobytes())
            cached_result = self.cache.get(
                image.shape, cache_key_args, method.value, self.config.__dict__
            )
            if cached_result is not None:
                return cached_result

        # 嘗試使用GPU
        if (self.config.use_gpu and self.gpu_interpolator and self.gpu_interpolator.cupy_available):
            try:
                # 目前GPU後端僅高效支援部分方法
                if method in [InterpolationMethod.BILINEAR, InterpolationMethod.BICUBIC]:
                    result = self.gpu_interpolator.interpolate_gpu(
                        image, x_coords, y_coords, method.value
                    )
                    if self.cache:
                        self.cache.put(image.shape, cache_key_args, method.value, self.config.__dict__, result)
                    return result
            except Exception as e:
                warnings.warn(f"GPU 插值失敗，將自動回退到 CPU 執行: {e}")

        # 使用CPU執行
        interpolation_func = self.method_map.get(method)
        if not interpolation_func:
            raise ValueError(f"不支援的插值方法: {method.value}")

        result = interpolation_func(image, x_coords, y_coords)

        if self.cache:
            self.cache.put(image.shape, cache_key_args, method.value, self.config.__dict__, result)

        return result

    def _validate_input(
        self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray
    ):
        """驗證輸入資料的維度、形狀和預估記憶體使用量。"""
        if image.ndim not in [2, 3]:
            raise ValueError("輸入圖像必須是 2D (灰階) 或 3D (彩色) 陣列。")
        if x_coords.shape != y_coords.shape:
            raise ValueError("x 和 y 座標陣列的形狀必須完全相同。")
        
        estimated_memory_mb = (image.nbytes + x_coords.nbytes * 2) / (1024**2)
        if estimated_memory_mb > self.config.memory_limit_mb:
            warnings.warn(
                f"預估記憶體使用量 {estimated_memory_mb:.1f}MB 超過設定限制 {self.config.memory_limit_mb}MB。"
            )

    def _nearest(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """最近鄰插值。"""
        return self._scipy_interpolate(image, x_coords, y_coords, order=0)

    def _linear(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """線性插值（在1D上），對於2D圖像等同於雙線性插值。"""
        return self._scipy_interpolate(image, x_coords, y_coords, order=1)

    def _bilinear(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """雙線性插值，對彩色圖像的每個通道獨立處理。"""
        if image.ndim == 3:
            results = [self._scipy_interpolate(image[:, :, c], x_coords, y_coords, order=1) for c in range(image.shape[2])]
            return np.stack(results, axis=-1)
        return self._scipy_interpolate(image, x_coords, y_coords, order=1)

    def _bicubic(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """雙三次插值，使用 Catmull-Rom 核函數實現。"""
        return self._cubic_conv_interpolate(image, x_coords, y_coords, _catmull_rom_kernel)

    def _quadratic(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """二次樣條插值。"""
        return self._scipy_interpolate(image, x_coords, y_coords, order=2)

    def _cubic(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """三次樣條插值。"""
        return self._scipy_interpolate(image, x_coords, y_coords, order=3)

    def _quartic(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """四次樣條插值。"""
        return self._scipy_interpolate(image, x_coords, y_coords, order=4)

    def _quintic(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """五次樣條插值。"""
        return self._scipy_interpolate(image, x_coords, y_coords, order=5)

    def _bspline(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """B-樣條插值，啟用預濾波以獲得更平滑的結果。"""
        return self._scipy_interpolate(image, x_coords, y_coords, order=3, prefilter=True)

    def _lanczos_interpolate(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray, a: int) -> np.ndarray:
        """Lanczos 插值的通用實現，根據參數 'a' 選擇不同的核函數。"""
        kernel_map = {2: _lanczos_kernel_2, 3: _lanczos_kernel_3, 4: _lanczos_kernel_4, 8: _lanczos_kernel_8}
        kernel = kernel_map.get(a, _lanczos_kernel_3) # 預設使用 Lanczos-3
        return self._cubic_conv_interpolate(image, x_coords, y_coords, kernel)

    def _lanczos2(self, image, x, y): return self._lanczos_interpolate(image, x, y, a=2)
    def _lanczos3(self, image, x, y): return self._lanczos_interpolate(image, x, y, a=3)
    def _lanczos4(self, image, x, y): return self._lanczos_interpolate(image, x, y, a=4)
    def _lanczos8(self, image, x, y): return self._lanczos_interpolate(image, x, y, a=8)
    def _mitchell(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _mitchell_kernel_default)
    def _catmull_rom(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _catmull_rom_kernel)
    def _gaussian(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _gaussian_kernel_default)
    def _sinc(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _sinc_kernel)
    def _kaiser(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _kaiser_kernel_default)
    def _hermite(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _hermite_kernel)
    def _triangular(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _triangular_kernel)
    def _box(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _box_kernel)
    def _welch(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _welch_kernel_2)
    def _bartlett(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _bartlett_kernel_2)
    def _tukey(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _tukey_kernel_default)
    def _blackman(self, image, x, y): return self._kaiser(image, x, y)  # 使用 Kaiser 作為別名
    def _hamming(self, image, x, y): return self._kaiser(image, x, y)  # 使用 Kaiser 作為別名
    def _hann(self, image, x, y): return self._kaiser(image, x, y)  # 使用 Kaiser 作為別名
    def _blackman_harris(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _blackman_harris_kernel_2)
    def _nuttall(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _nuttall_kernel_2)
    def _spline36(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _spline36_kernel)
    def _spline64(self, image, x, y): return self._cubic_conv_interpolate(image, x, y, _spline64_kernel)

    def _anisotropic(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """非等向性插值：根據圖像梯度自適應選擇銳化或平滑的插值方法。"""
        gray = np.mean(image, axis=2) if image.ndim == 3 else image
        grad_magnitude = np.sqrt(np.gradient(gray, axis=1)**2 + np.gradient(gray, axis=0)**2)
        # 如果梯度變化大（細節多），使用銳利的 Lanczos；否則使用平滑的 Gaussian。
        method = InterpolationMethod.LANCZOS3 if np.mean(grad_magnitude) > np.std(grad_magnitude) else InterpolationMethod.GAUSSIAN
        return self.method_map[method](image, x_coords, y_coords)

    def _cubic_conv_interpolate(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray, kernel: Callable) -> np.ndarray:
        """基於三次卷積的插值框架，可接受不同的核函數。"""
        if image.ndim == 3:
            results = [_cubic_convolution(image[:, :, c], x_coords, y_coords, kernel).reshape(x_coords.shape) for c in range(image.shape[2])]
            return np.stack(results, axis=-1)
        return _cubic_convolution(image, x_coords, y_coords, kernel).reshape(x_coords.shape)

    def _scipy_interpolate(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray, order: int, prefilter: bool = False) -> np.ndarray:
        """使用 `scipy.ndimage.map_coordinates` 執行基於樣條的插值。"""
        padded = self._pad_image(image, order)
        adjusted_y, adjusted_x = y_coords + order // 2, x_coords + order // 2
        coords = np.stack([adjusted_y, adjusted_x])
        
        if image.ndim == 3:
            results = [map_coordinates(padded[:, :, c], coords, order=order, mode="nearest", prefilter=prefilter) for c in range(image.shape[2])]
            return np.stack(results, axis=-1)
        return map_coordinates(padded, coords, order=order, mode="nearest", prefilter=prefilter)

    def _pad_image(self, image: np.ndarray, order: int) -> np.ndarray:
        """根據插值階數對圖像邊緣進行擴展，以處理邊界效應。"""
        pad_size = max(1, order // 2)
        pad_width = ((pad_size, pad_size), (pad_size, pad_size)) + ((0, 0),) * (image.ndim - 2)
        return np.pad(image, pad_width, mode="edge")


class MultipleInterpolator:
    """
    多重插值器

    根據指定的策略，組合多種插值方法以實現更複雜的插值效果。
    """
    def __init__(self, config: MultipleInterpolationConfig):
        """
        初始化多重插值器。

        Args:
            config: 多重插值設定物件。
        """
        self.config = config
        self.interpolators = {m: AdvancedInterpolator(InterpolationConfig(method=m, cache_enabled=False)) for m in config.methods}

    def interpolate(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray) -> np.ndarray:
        """根據設定的策略執行多重插值。"""
        strategy_func_name = f"_{self.config.strategy.value}_interpolate"
        strategy_func = getattr(self, strategy_func_name, self._sequential_interpolate)
        return strategy_func(image, x_coords, y_coords)

    def _run_per_channel(self, image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray, method_func: Callable[[int], InterpolationMethod]) -> np.ndarray:
        """為每個通道獨立執行插值的通用輔助函數。"""
        if image.ndim == 2:
            return self.interpolators[method_func(0)].interpolate(image, x_coords, y_coords)
        
        tasks = [(image[:, :, c], x_coords, y_coords, self.interpolators[method_func(c)]) for c in range(image.shape[2])]
        
        with ThreadPoolExecutor(max_workers=self.config.methods[0].parallel_threads) as executor:
            results = list(executor.map(lambda p: p[3].interpolate(p[0], p[1], p[2]), tasks))
            
        return np.stack(results, axis=-1)

    def _sequential_interpolate(self, image: np.ndarray, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """順序策略：按R,G,B通道順序循環使用方法列表中的插值器。"""
        return self._run_per_channel(image, x, y, lambda c: self.config.methods[c % len(self.config.methods)])

    def _alternating_interpolate(self, image: np.ndarray, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """交替策略：通常使用列表中的前兩種方法在通道間交替。"""
        return self._run_per_channel(image, x, y, lambda c: self.config.methods[c % 2])

    def _mixed_interpolate(self, image: np.ndarray, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """混合策略：為每個通道隨機選擇一個方法。"""
        return self._run_per_channel(image, x, y, lambda c: np.random.choice(self.config.methods))

    def _double_pass_interpolate(self, image: np.ndarray, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """雙重處理策略：先用第一種方法插值，然後對結果進行微小擾動再用第二種方法插值。"""
        if len(self.config.methods) < 2:
            return self._sequential_interpolate(image, x, y)
        first_pass = self.interpolators[self.config.methods[0]].interpolate(image, x, y)
        x2, y2 = x + np.random.normal(0, 0.1, x.shape), y + np.random.normal(0, 0.1, y.shape)
        return self.interpolators[self.config.methods[1]].interpolate(first_pass, x2, y2)

    def _weighted_interpolate(self, image: np.ndarray, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """加權策略：計算所有方法的插值結果，然後根據權重加權平均。"""
        weights = np.array(self.config.weights or [1.0 / len(self.config.methods)] * len(self.config.methods))
        weights /= np.sum(weights)
        
        results = [self.interpolators[m].interpolate(image, x, y) for m in self.config.methods]
        
        return np.sum([res.astype(np.float64) * w for res, w in zip(results, weights)], axis=0).astype(image.dtype)

    def _adaptive_interpolate(self, image: np.ndarray, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """自適應策略：與`AdvancedInterpolator`中的`_anisotropic`類似，但使用配置中提供的方法。"""
        gray = np.mean(image, axis=2) if image.ndim == 3 else image
        grad_mag = np.sqrt(np.gradient(gray, axis=1)**2 + np.gradient(gray, axis=0)**2)
        
        # 假設方法列表的第一個是銳化型，第二個是平滑型
        sharp_method = self.config.methods[0]
        smooth_method = self.config.methods[1] if len(self.config.methods) > 1 else sharp_method
        
        chosen_method = sharp_method if np.mean(grad_mag) > np.std(grad_mag) else smooth_method
        return self.interpolators[chosen_method].interpolate(image, x, y)

    def _parallel_interpolate(self, image: np.ndarray, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """平行策略：使用執行緒池並行計算所有方法的插值，然後取平均值。"""
        with ThreadPoolExecutor(max_workers=len(self.config.methods)) as executor:
            futures = [executor.submit(self.interpolators[m].interpolate, image, x, y) for m in self.config.methods]
            results = [f.result() for f in futures]
        return np.mean(results, axis=0).astype(image.dtype)
