#!/usr/bin/env python3
"""
對 processing.components.cube_service 的單元測試
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import numpy as np
from pathlib import Path

# Import project modules directly (using pip install -e .)

from processing.components.cube_service import CubeService
from core.projection import ProjectionCore
from config.settings import Config

class TestCubeService(unittest.TestCase):

    def setUp(self):
        """為每個測試設置模擬的配置和服務"""
        self.mock_config = Mock(spec=Config)
        self.mock_config.processing.cube_face_size = 512
        self.mock_config.processing.interpolation_method = "bilinear"
        self.mock_config.system.enable_gpu_acceleration = False
        self.mock_config.processing.jpeg_quality = 90

        self.cube_service = CubeService(self.mock_config)
        
        # 創建一個測試用的全景圖
        self.test_panorama = np.zeros((1024, 2048, 3), dtype=np.uint8)
        # 創建一個模擬的立方體面字典
        self.test_cube_dict = {
            "F": np.zeros((512, 512, 3), dtype=np.uint8),
            "R": np.zeros((512, 512, 3), dtype=np.uint8),
            "B": np.zeros((512, 512, 3), dtype=np.uint8),
            "L": np.zeros((512, 512, 3), dtype=np.uint8),
            "U": np.zeros((512, 512, 3), dtype=np.uint8),
            "D": np.zeros((512, 512, 3), dtype=np.uint8),
        }

    @patch('processing.components.cube_service.ProjectionCore')
    def test_convert_panorama_to_cube(self, MockProjectionCore):
        """測試全景圖到立方體的轉換功能"""
        # 設置 mock 的 ProjectionCore 實例和其返回值
        mock_projection_instance = MockProjectionCore.return_value
        mock_projection_instance.equirect_to_cubemap.return_value = self.test_cube_dict
        
        result_dict = self.cube_service.convert_panorama_to_cube(self.test_panorama)
        
        # 驗證 ProjectionCore 被正確地初始化和調用
        MockProjectionCore.assert_called_once_with(
            height=1024,
            width=2048,
            face_size=512,
            mode="bilinear",
            use_gpu=False
        )
        mock_projection_instance.equirect_to_cubemap.assert_called_once_with(
            self.test_panorama, cube_format="dict"
        )
        
        # 驗證返回結果
        self.assertEqual(len(result_dict), 6)
        self.assertIn("F", result_dict)
        self.assertEqual(result_dict["F"].shape, (512, 512, 3))

    @patch('processing.components.cube_service.cv2.imwrite')
    @patch('processing.components.cube_service.Path.mkdir')
    def test_save_cube_assets(self, mock_mkdir, mock_imwrite):
        """測試保存立方體資產的功能"""
        test_output_dir = "/fake/dir"
        
        self.cube_service.save_cube_assets(self.test_cube_dict, test_output_dir)
        
        # 驗證目錄是否被創建
        mock_mkdir.assert_called_once_with(parents=True, exist_ok=True)
        
        # 驗證 imwrite 被調用了8次 (6個面 + 1個預覽圖 + 1個縮略圖)
        self.assertEqual(mock_imwrite.call_count, 8)
        
        # 驗證其中一個面的保存路徑是否正確
        expected_face_path = str(Path(test_output_dir) / "html5" / "0.jpg")
        
        # 檢查所有 imwrite 調用，看是否有一個匹配預期的路徑
        called_paths = [call.args[0] for call in mock_imwrite.call_args_list]
        self.assertIn(expected_face_path, called_paths)
        
        # 驗證預覽圖和縮略圖的路徑
        expected_preview_path = str(Path(test_output_dir) / "preview.jpg")
        expected_thumbnail_path = str(Path(test_output_dir) / "thumbnail.jpg")
        self.assertIn(expected_preview_path, called_paths)
        self.assertIn(expected_thumbnail_path, called_paths)

if __name__ == '__main__':
    unittest.main()