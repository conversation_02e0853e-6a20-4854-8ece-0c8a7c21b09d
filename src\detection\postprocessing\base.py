"""
後處理基礎類別

定義後處理元件的抽象介面。
"""

from abc import ABC, abstractmethod
from typing import Any


class PostProcessor(ABC):
    """後處理元件的抽象基礎類別"""

    @abstractmethod
    def process(self, detections: list, context: dict[str, Any]) -> list:
        """處理偵測結果

        :param detections: 偵測框列表
        :param context: 處理上下文 (影像形狀、面 ID 等)
        :return: 處理後的偵測列表
        """
        pass

    @abstractmethod
    def get_name(self) -> str:
        """取得用於識別的處理器名稱

        :return: 處理器名稱
        """
        pass

    def get_description(self) -> str:
        """取得人類可讀的描述

        :return: 處理器描述
        """
        return f"後處理器: {self.get_name()}"

    def is_applicable(self, context: dict[str, Any]) -> bool:
        """檢查處理器是否適用於給定的上下文

        :param context: 處理上下文
        :return: 如果應應用處理器則為 True
        """
        return True
