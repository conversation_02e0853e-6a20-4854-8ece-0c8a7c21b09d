"""
基礎插值核函數與演算法模組

本模組提供了幾種最基礎且計算速度快的插值演算法的純 NumPy 實現。
這些函數是構成更複雜插值器的基礎，專為性能而優化。
主要包含：
- 核函數：Box（最近鄰）、Triangular（線性）。
- 核心演算法：雙線性插值 (`_bilinear_core`)、三次卷積 (`_cubic_convolution`)。
"""

from typing import Callable

import numpy as np


def _box_kernel(x: float) -> float:
    """
    Box 核函數，也稱為方波濾波器。
    其效果等同於「最近鄰插值」，選擇最接近的像素值。
    速度最快，但會產生明顯的塊狀或鋸齒狀 artifact。
    """
    return 1.0 if abs(x) <= 0.5 else 0.0


def _triangular_kernel(x: float) -> float:
    """
    三角核函數，也稱為帳篷濾波器。
    其效果等同於「線性插值」。結果比 Box 核函數平滑。
    """
    x = abs(x)
    if x >= 1.0:
        return 0.0
    return 1.0 - x


def _bilinear_core(
    image: np.ndarray, x_coords: np.ndarray, y_coords: np.ndarray
) -> np.ndarray:
    """
    高效能的雙線性插值核心實現。

    Args:
        image: 2D 的來源影像 NumPy 陣列。
        x_coords: 目標點的 x 座標陣列。
        y_coords: 目標點的 y 座標陣列。

    Returns:
        插值後的結果陣列，與座標陣列形狀相同。
    """
    h, w = image.shape
    output_shape = x_coords.shape
    x_flat = x_coords.flatten()
    y_flat = y_coords.flatten()
    
    # 邊界檢查
    valid_mask = (x_flat >= 0) & (x_flat < w - 1) & (y_flat >= 0) & (y_flat < h - 1)
    
    # 初始化結果陣列
    result = np.zeros_like(x_flat, dtype=image.dtype)
    
    if np.any(valid_mask):
        # 只處理有效的座標
        x_valid = x_flat[valid_mask]
        y_valid = y_flat[valid_mask]
        
        # 找到周圍的四個像素點
        x0 = x_valid.astype(int)
        y0 = y_valid.astype(int)
        x1 = np.minimum(x0 + 1, w - 1)
        y1 = np.minimum(y0 + 1, h - 1)
        
        # 計算在 x 和 y 方向上的插值權重
        wx = x_valid - x0
        wy = y_valid - y0
        
        # 獲取四個點的像素值
        v00 = image[y0, x0]
        v01 = image[y0, x1]
        v10 = image[y1, x0]
        v11 = image[y1, x1]
        
        # 根據權重進行加權平均
        interpolated = (
            v00 * (1 - wx) * (1 - wy) +
            v01 * wx * (1 - wy) +
            v10 * (1 - wx) * wy +
            v11 * wx * wy
        )
        
        result[valid_mask] = interpolated
    
    return result.reshape(output_shape)


def _cubic_convolution(
    image: np.ndarray,
    x_coords: np.ndarray,
    y_coords: np.ndarray,
    kernel_func: Callable[[float], float],
) -> np.ndarray:
    """
    一個通用的、高效能的三次卷積插值核心。
    它使用一個 4x4 的鄰域，並透過傳入不同的 `kernel_func`（核函數）
    來實現多種基於卷積的插值演算法，如 Bicubic, Mitchell, Lanczos 等。

    Args:
        image: 2D 的來源影像 NumPy 陣列。
        x_coords: 目標點的 x 座標陣列。
        y_coords: 目標點的 y 座標陣列。
        kernel_func: 用於計算權重的核函數，例如 `_catmull_rom_kernel`。

    Returns:
        插值後的結果陣列。
    """
    h, w = image.shape
    x_flat = x_coords.flatten()
    y_flat = y_coords.flatten()
    n_points = x_flat.size
    
    # 邊界檢查，需要 4x4 鄰域，因此邊界更寬
    valid_mask = (x_flat >= 1) & (x_flat < w - 2) & (y_flat >= 1) & (y_flat < h - 2)
    
    # 初始化結果陣列
    result = np.zeros(n_points, dtype=np.float64)
    
    if np.any(valid_mask):
        x_valid = x_flat[valid_mask]
        y_valid = y_flat[valid_mask]
        
        # 找到中心的整數像素座標
        cx = x_valid.astype(int)
        cy = y_valid.astype(int)
        
        # 計算小數部分位移
        dx = x_valid - cx
        dy = y_valid - cy
        
        # 在 4x4 的鄰域內進行卷積
        interpolated_values = np.zeros(len(x_valid), dtype=np.float64)
        
        for m in range(-1, 3):  # y方向鄰域
            for n in range(-1, 3):  # x方向鄰域
                px = cx + n
                py = cy + m
                
                # 向量化計算權重
                weight_x = np.array([kernel_func(dx_i - n) for dx_i in dx])
                weight_y = np.array([kernel_func(dy_i - m) for dy_i in dy])
                weight = weight_x * weight_y
                
                # 獲取像素值並累加
                pixel_values = image[py, px]
                interpolated_values += pixel_values * weight
        
        # 將結果限制在有效的像素值範圍內
        interpolated_values = np.clip(interpolated_values, 0, 255)
        result[valid_mask] = interpolated_values
    
    return result.astype(image.dtype).reshape(x_coords.shape)