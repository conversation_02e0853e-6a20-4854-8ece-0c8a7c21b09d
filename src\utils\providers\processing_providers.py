"""
處理提供者模組
提供各種分散式處理策略的具體實現
"""
import asyncio
import logging
import multiprocessing
import threading
import time
from abc import ABC, abstractmethod
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Coroutine

import psutil

from ..core.metric_collector import Metric, MetricType, MetricCollector
from ..core.thresholds import SystemThresholds, DEFAULT_THRESHOLDS

logger = logging.getLogger(__name__)


class ProcessingMode(Enum):
    """處理模式"""
    SEQUENTIAL = "sequential"
    THREAD_POOL = "thread_pool"
    PROCESS_POOL = "process_pool"
    ASYNC = "async"
    HYBRID = "hybrid"


@dataclass
class ProcessingTask:
    """處理任務定義"""
    id: str
    func: Callable
    args: tuple = ()
    kwargs: dict = field(default_factory=dict)
    priority: int = 0
    timeout: float | None = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class ProcessingResult:
    """處理結果"""
    task_id: str
    success: bool
    result: Any = None
    error: str | None = None
    execution_time: float = 0.0
    retry_count: int = 0


class ProcessingProvider(ABC):
    """抽象處理提供者"""
    
    @abstractmethod
    def process_task(self, task: ProcessingTask) -> ProcessingResult:
        """處理單個任務"""
        pass
    
    @abstractmethod
    def process_batch(self, tasks: list[ProcessingTask]) -> list[ProcessingResult]:
        """批次處理任務"""
        pass
    
    @abstractmethod
    def get_status(self) -> dict[str, Any]:
        """獲取處理器狀態"""
        pass
    
    @abstractmethod
    def cleanup(self) -> bool:
        """清理資源"""
        pass


class SequentialProcessingProvider(ProcessingProvider, MetricCollector):
    """順序處理提供者"""
    
    def __init__(self, thresholds: SystemThresholds | None = None):
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self._tasks_processed = 0
        self._total_execution_time = 0.0
        self._errors = 0
    
    def get_collector_name(self) -> str:
        return "sequential_processing"
    
    def collect(self) -> list[Metric]:
        """收集處理指標"""
        return [
            Metric(
                name="processing.sequential.tasks_processed",
                value=self._tasks_processed,
                unit="count",
                metric_type=MetricType.CUSTOM
            ),
            Metric(
                name="processing.sequential.avg_execution_time",
                value=self._total_execution_time / max(1, self._tasks_processed),
                unit="ms",
                metric_type=MetricType.CUSTOM
            ),
            Metric(
                name="processing.sequential.error_rate",
                value=(self._errors / max(1, self._tasks_processed)) * 100,
                unit="%",
                metric_type=MetricType.CUSTOM
            ),
        ]
    
    def process_task(self, task: ProcessingTask) -> ProcessingResult:
        """處理單個任務"""
        start_time = time.perf_counter()
        
        try:
            result = task.func(*task.args, **task.kwargs)
            execution_time = (time.perf_counter() - start_time) * 1000
            
            self._tasks_processed += 1
            self._total_execution_time += execution_time
            
            return ProcessingResult(
                task_id=task.id,
                success=True,
                result=result,
                execution_time=execution_time
            )
        except Exception as e:
            execution_time = (time.perf_counter() - start_time) * 1000
            self._errors += 1
            self._tasks_processed += 1
            self._total_execution_time += execution_time
            
            logger.error(f"任務 {task.id} 執行失敗: {e}")
            return ProcessingResult(
                task_id=task.id,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def process_batch(self, tasks: list[ProcessingTask]) -> list[ProcessingResult]:
        """批次處理任務"""
        results = []
        for task in tasks:
            result = self.process_task(task)
            results.append(result)
        return results
    
    def get_status(self) -> dict[str, Any]:
        """獲取處理器狀態"""
        return {
            "mode": "sequential",
            "tasks_processed": self._tasks_processed,
            "error_rate": (self._errors / max(1, self._tasks_processed)) * 100,
            "avg_execution_time": self._total_execution_time / max(1, self._tasks_processed),
            "status": "ready"
        }
    
    def cleanup(self) -> bool:
        """清理資源"""
        return True


class ThreadPoolProcessingProvider(ProcessingProvider, MetricCollector):
    """線程池處理提供者"""
    
    def __init__(self, max_workers: int | None = None, thresholds: SystemThresholds | None = None):
        self.max_workers = max_workers or min(32, (psutil.cpu_count() or 1) + 4)
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self._tasks_processed = 0
        self._total_execution_time = 0.0
        self._errors = 0
        self._active_tasks = 0
        self._lock = threading.Lock()
    
    def get_collector_name(self) -> str:
        return "threadpool_processing"
    
    def collect(self) -> list[Metric]:
        """收集處理指標"""
        return [
            Metric(
                name="processing.threadpool.tasks_processed",
                value=self._tasks_processed,
                unit="count",
                metric_type=MetricType.CUSTOM
            ),
            Metric(
                name="processing.threadpool.active_tasks",
                value=self._active_tasks,
                unit="count",
                metric_type=MetricType.CUSTOM
            ),
            Metric(
                name="processing.threadpool.thread_usage",
                value=(self._active_tasks / self.max_workers) * 100,
                unit="%",
                metric_type=MetricType.CUSTOM
            ),
        ]
    
    def process_task(self, task: ProcessingTask) -> ProcessingResult:
        """處理單個任務"""
        start_time = time.perf_counter()
        
        with self._lock:
            self._active_tasks += 1
        
        try:
            future = self.executor.submit(task.func, *task.args, **task.kwargs)
            result = future.result(timeout=task.timeout)
            execution_time = (time.perf_counter() - start_time) * 1000
            
            with self._lock:
                self._tasks_processed += 1
                self._total_execution_time += execution_time
                self._active_tasks -= 1
            
            return ProcessingResult(
                task_id=task.id,
                success=True,
                result=result,
                execution_time=execution_time
            )
        except Exception as e:
            execution_time = (time.perf_counter() - start_time) * 1000
            
            with self._lock:
                self._errors += 1
                self._tasks_processed += 1
                self._total_execution_time += execution_time
                self._active_tasks -= 1
            
            logger.error(f"任務 {task.id} 執行失敗: {e}")
            return ProcessingResult(
                task_id=task.id,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def process_batch(self, tasks: list[ProcessingTask]) -> list[ProcessingResult]:
        """批次處理任務"""
        start_time = time.perf_counter()
        results = []
        
        # 提交所有任務
        future_to_task = {}
        for task in tasks:
            with self._lock:
                self._active_tasks += 1
            future = self.executor.submit(task.func, *task.args, **task.kwargs)
            future_to_task[future] = task
        
        # 收集結果
        for future in as_completed(future_to_task.keys()):
            task = future_to_task[future]
            task_start_time = time.perf_counter()
            
            try:
                result = future.result()
                execution_time = (time.perf_counter() - task_start_time) * 1000
                
                results.append(ProcessingResult(
                    task_id=task.id,
                    success=True,
                    result=result,
                    execution_time=execution_time
                ))
            except Exception as e:
                execution_time = (time.perf_counter() - task_start_time) * 1000
                self._errors += 1
                
                results.append(ProcessingResult(
                    task_id=task.id,
                    success=False,
                    error=str(e),
                    execution_time=execution_time
                ))
            finally:
                with self._lock:
                    self._active_tasks -= 1
                    self._tasks_processed += 1
        
        total_time = (time.perf_counter() - start_time) * 1000
        with self._lock:
            self._total_execution_time += total_time
        
        return results
    
    def get_status(self) -> dict[str, Any]:
        """獲取處理器狀態"""
        return {
            "mode": "threadpool",
            "max_workers": self.max_workers,
            "active_tasks": self._active_tasks,
            "tasks_processed": self._tasks_processed,
            "thread_usage": (self._active_tasks / self.max_workers) * 100,
            "status": "ready"
        }
    
    def cleanup(self) -> bool:
        """清理資源"""
        try:
            self.executor.shutdown(wait=True, timeout=30)
            return True
        except Exception as e:
            logger.error(f"清理線程池失敗: {e}")
            return False


class ProcessPoolProcessingProvider(ProcessingProvider, MetricCollector):
    """進程池處理提供者"""
    
    def __init__(self, max_workers: int | None = None, thresholds: SystemThresholds | None = None):
        self.max_workers = max_workers or psutil.cpu_count()
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
        self._tasks_processed = 0
        self._total_execution_time = 0.0
        self._errors = 0
        self._active_tasks = 0
        self._lock = threading.Lock()
    
    def get_collector_name(self) -> str:
        return "processpool_processing"
    
    def collect(self) -> list[Metric]:
        """收集處理指標"""
        return [
            Metric(
                name="processing.processpool.tasks_processed",
                value=self._tasks_processed,
                unit="count",
                metric_type=MetricType.CUSTOM
            ),
            Metric(
                name="processing.processpool.active_tasks",
                value=self._active_tasks,
                unit="count",
                metric_type=MetricType.CUSTOM
            ),
            Metric(
                name="processing.processpool.process_usage",
                value=(self._active_tasks / self.max_workers) * 100,
                unit="%",
                metric_type=MetricType.CUSTOM
            ),
        ]
    
    def process_task(self, task: ProcessingTask) -> ProcessingResult:
        """處理單個任務"""
        start_time = time.perf_counter()
        
        with self._lock:
            self._active_tasks += 1
        
        try:
            future = self.executor.submit(task.func, *task.args, **task.kwargs)
            result = future.result(timeout=task.timeout)
            execution_time = (time.perf_counter() - start_time) * 1000
            
            with self._lock:
                self._tasks_processed += 1
                self._total_execution_time += execution_time
                self._active_tasks -= 1
            
            return ProcessingResult(
                task_id=task.id,
                success=True,
                result=result,
                execution_time=execution_time
            )
        except Exception as e:
            execution_time = (time.perf_counter() - start_time) * 1000
            
            with self._lock:
                self._errors += 1
                self._tasks_processed += 1
                self._total_execution_time += execution_time
                self._active_tasks -= 1
            
            logger.error(f"任務 {task.id} 執行失敗: {e}")
            return ProcessingResult(
                task_id=task.id,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def process_batch(self, tasks: list[ProcessingTask]) -> list[ProcessingResult]:
        """批次處理任務"""
        start_time = time.perf_counter()
        results = []
        
        # 提交所有任務
        future_to_task = {}
        for task in tasks:
            with self._lock:
                self._active_tasks += 1
            future = self.executor.submit(task.func, *task.args, **task.kwargs)
            future_to_task[future] = task
        
        # 收集結果
        for future in as_completed(future_to_task.keys()):
            task = future_to_task[future]
            task_start_time = time.perf_counter()
            
            try:
                result = future.result()
                execution_time = (time.perf_counter() - task_start_time) * 1000
                
                results.append(ProcessingResult(
                    task_id=task.id,
                    success=True,
                    result=result,
                    execution_time=execution_time
                ))
            except Exception as e:
                execution_time = (time.perf_counter() - task_start_time) * 1000
                self._errors += 1
                
                results.append(ProcessingResult(
                    task_id=task.id,
                    success=False,
                    error=str(e),
                    execution_time=execution_time
                ))
            finally:
                with self._lock:
                    self._active_tasks -= 1
                    self._tasks_processed += 1
        
        total_time = (time.perf_counter() - start_time) * 1000
        with self._lock:
            self._total_execution_time += total_time
        
        return results
    
    def get_status(self) -> dict[str, Any]:
        """獲取處理器狀態"""
        return {
            "mode": "processpool",
            "max_workers": self.max_workers,
            "active_tasks": self._active_tasks,
            "tasks_processed": self._tasks_processed,
            "process_usage": (self._active_tasks / self.max_workers) * 100,
            "status": "ready"
        }
    
    def cleanup(self) -> bool:
        """清理資源"""
        try:
            self.executor.shutdown(wait=True, timeout=30)
            return True
        except Exception as e:
            logger.error(f"清理進程池失敗: {e}")
            return False


class AsyncProcessingProvider(ProcessingProvider, MetricCollector):
    """異步處理提供者"""
    
    def __init__(self, max_concurrent: int = 100, thresholds: SystemThresholds | None = None):
        self.max_concurrent = max_concurrent
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self._tasks_processed = 0
        self._total_execution_time = 0.0
        self._errors = 0
        self._active_tasks = 0
        self._lock = asyncio.Lock()
    
    def get_collector_name(self) -> str:
        return "async_processing"
    
    def collect(self) -> list[Metric]:
        """收集處理指標"""
        return [
            Metric(
                name="processing.async.tasks_processed",
                value=self._tasks_processed,
                unit="count",
                metric_type=MetricType.CUSTOM
            ),
            Metric(
                name="processing.async.active_tasks",
                value=self._active_tasks,
                unit="count",
                metric_type=MetricType.CUSTOM
            ),
            Metric(
                name="processing.async.concurrency_usage",
                value=(self._active_tasks / self.max_concurrent) * 100,
                unit="%",
                metric_type=MetricType.CUSTOM
            ),
        ]
    
    async def _process_task_async(self, task: ProcessingTask) -> ProcessingResult:
        """異步處理任務"""
        async with self.semaphore:
            start_time = time.perf_counter()
            
            async with self._lock:
                self._active_tasks += 1
            
            try:
                # 如果函數是協程，直接等待
                if asyncio.iscoroutinefunction(task.func):
                    result = await task.func(*task.args, **task.kwargs)
                else:
                    # 在線程池中運行同步函數
                    loop = asyncio.get_event_loop()
                    result = await loop.run_in_executor(None, task.func, *task.args, **task.kwargs)
                
                execution_time = (time.perf_counter() - start_time) * 1000
                
                async with self._lock:
                    self._tasks_processed += 1
                    self._total_execution_time += execution_time
                    self._active_tasks -= 1
                
                return ProcessingResult(
                    task_id=task.id,
                    success=True,
                    result=result,
                    execution_time=execution_time
                )
            except Exception as e:
                execution_time = (time.perf_counter() - start_time) * 1000
                
                async with self._lock:
                    self._errors += 1
                    self._tasks_processed += 1
                    self._total_execution_time += execution_time
                    self._active_tasks -= 1
                
                logger.error(f"異步任務 {task.id} 執行失敗: {e}")
                return ProcessingResult(
                    task_id=task.id,
                    success=False,
                    error=str(e),
                    execution_time=execution_time
                )
    
    def process_task(self, task: ProcessingTask) -> ProcessingResult:
        """處理單個任務（同步接口）"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self._process_task_async(task))
        finally:
            loop.close()
    
    def process_batch(self, tasks: list[ProcessingTask]) -> list[ProcessingResult]:
        """批次處理任務（同步接口）"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self._process_batch_async(tasks))
        finally:
            loop.close()
    
    async def _process_batch_async(self, tasks: list[ProcessingTask]) -> list[ProcessingResult]:
        """異步批次處理"""
        coroutines = [self._process_task_async(task) for task in tasks]
        return await asyncio.gather(*coroutines, return_exceptions=False)
    
    def get_status(self) -> dict[str, Any]:
        """獲取處理器狀態"""
        return {
            "mode": "async",
            "max_concurrent": self.max_concurrent,
            "active_tasks": self._active_tasks,
            "tasks_processed": self._tasks_processed,
            "concurrency_usage": (self._active_tasks / self.max_concurrent) * 100,
            "status": "ready"
        }
    
    def cleanup(self) -> bool:
        """清理資源"""
        return True


class ProcessingProviderFactory:
    """處理提供者工廠"""
    
    @staticmethod
    def create_provider(mode: ProcessingMode, **kwargs) -> ProcessingProvider:
        """根據模式創建處理提供者"""
        if mode == ProcessingMode.SEQUENTIAL:
            return SequentialProcessingProvider(**kwargs)
        elif mode == ProcessingMode.THREAD_POOL:
            return ThreadPoolProcessingProvider(**kwargs)
        elif mode == ProcessingMode.PROCESS_POOL:
            return ProcessPoolProcessingProvider(**kwargs)
        elif mode == ProcessingMode.ASYNC:
            return AsyncProcessingProvider(**kwargs)
        else:
            raise ValueError(f"不支援的處理模式: {mode}")
    
    @staticmethod
    def create_optimal_provider(task_count: int = 1, io_intensive: bool = False, 
                              cpu_intensive: bool = False, **kwargs) -> ProcessingProvider:
        """創建最佳處理提供者"""
        if task_count == 1:
            return SequentialProcessingProvider(**kwargs)
        elif io_intensive:
            return ThreadPoolProcessingProvider(**kwargs)
        elif cpu_intensive:
            return ProcessPoolProcessingProvider(**kwargs)
        else:
            return AsyncProcessingProvider(**kwargs)


class HybridProcessingProvider(ProcessingProvider, MetricCollector):
    """混合處理提供者 - 根據任務特性選擇最佳策略"""
    
    def __init__(self, thresholds: SystemThresholds | None = None):
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self.providers = {
            ProcessingMode.SEQUENTIAL: SequentialProcessingProvider(thresholds),
            ProcessingMode.THREAD_POOL: ThreadPoolProcessingProvider(thresholds=thresholds),
            ProcessingMode.PROCESS_POOL: ProcessPoolProcessingProvider(thresholds=thresholds),
            ProcessingMode.ASYNC: AsyncProcessingProvider(thresholds=thresholds),
        }
        self._tasks_processed = 0
        self._provider_usage = {mode: 0 for mode in ProcessingMode}
    
    def get_collector_name(self) -> str:
        return "hybrid_processing"
    
    def collect(self) -> list[Metric]:
        """收集處理指標"""
        metrics = []
        for mode, provider in self.providers.items():
            if hasattr(provider, 'collect'):
                metrics.extend(provider.collect())
        
        # 添加混合模式特有指標
        metrics.append(Metric(
            name="processing.hybrid.tasks_processed",
            value=self._tasks_processed,
            unit="count",
            metric_type=MetricType.CUSTOM
        ))
        
        return metrics
    
    def _select_provider(self, tasks: list[ProcessingTask]) -> ProcessingProvider:
        """根據任務特性選擇最佳提供者"""
        task_count = len(tasks)
        
        if task_count == 1:
            return self.providers[ProcessingMode.SEQUENTIAL]
        elif task_count <= 10:
            return self.providers[ProcessingMode.THREAD_POOL]
        elif task_count <= 50:
            return self.providers[ProcessingMode.PROCESS_POOL]
        else:
            return self.providers[ProcessingMode.ASYNC]
    
    def process_task(self, task: ProcessingTask) -> ProcessingResult:
        """處理單個任務"""
        provider = self._select_provider([task])
        result = provider.process_task(task)
        self._tasks_processed += 1
        return result
    
    def process_batch(self, tasks: list[ProcessingTask]) -> list[ProcessingResult]:
        """批次處理任務"""
        provider = self._select_provider(tasks)
        results = provider.process_batch(tasks)
        self._tasks_processed += len(tasks)
        return results
    
    def get_status(self) -> dict[str, Any]:
        """獲取處理器狀態"""
        status = {
            "mode": "hybrid",
            "tasks_processed": self._tasks_processed,
            "provider_usage": self._provider_usage,
            "providers": {}
        }
        
        for mode, provider in self.providers.items():
            status["providers"][mode.value] = provider.get_status()
        
        return status
    
    def cleanup(self) -> bool:
        """清理資源"""
        results = []
        for provider in self.providers.values():
            results.append(provider.cleanup())
        return all(results)