"""
Generate Pyramid - 現代化全景影像金字塔生成主程式

這個程式是舊版 generate_pyramid.py 的現代化重構版本，提供完整的
全景影像處理和金字塔生成功能。整合了新的模組化架構，同時保持
向後相容性和完整的功能支援。

主要功能：
- 三種處理模式：pano_to_cube, cube_to_cube, list_cube
- AI 檢測和隱私保護
- Logo 覆蓋功能
- 批次處理支援
- 進度追蹤和日誌
- 配置管理

使用方法：
    python generate_pyramid.py [config.yaml]

Author: AI 部門 - 全景處理團隊  
Version: 2.0.0 (現代化版本)
"""

import os
import sys

# 在任何其他導入之前設置環境變量以禁用 Numba JIT 編譯
# 這可以避免在導入 core.coordinate 時卡住
# os.environ["DISABLE_NUMBA"] = "1"
# os.environ["TESTING"] = "1"

# 確保清除可能影響 GPU 使用的環境變數
import os
if "TESTING" in os.environ:
    del os.environ["TESTING"]
if "DISABLE_GPU" in os.environ:
    del os.environ["DISABLE_GPU"]

import time
import shutil
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Union
import traceback

import cv2
import numpy as np
import yaml
import pandas as pd

# 本地模組匯入 - 使用延遲載入避免長時間 import
def _import_config_modules():
    """延遲載入配置模組"""
    try:
        from config.settings import get_config, Config
        from config.constants import ProcessMode, SaveMode
        return get_config, Config, ProcessMode, SaveMode
    except ImportError as e:
        print(f"配置模組載入失敗: {e}")
        return None, None, None, None

def _import_core_projection():
    """延遲載入核心投影模組 - 使用安全的載入策略"""
    try:
        print("正在嘗試載入 core.projection...")

        # 設置超時機制
        import signal

        def timeout_handler(signum, frame):
            raise TimeoutError("core.projection 載入超時")

        # 在 Windows 上 signal.alarm 不可用，所以使用 try-except
        try:
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(10)  # 10 秒超時
        except AttributeError:
            # Windows 系統，跳過 alarm
            pass

        try:
            from core.projection import ProjectionCore
            print("core.projection 載入成功")
            return ProjectionCore
        finally:
            try:
                signal.alarm(0)  # 取消超時
            except AttributeError:
                pass

    except (ImportError, TimeoutError) as e:
        print(f"核心投影模組載入失敗: {e}")
        print("將使用簡化的投影功能")
        return _create_fallback_projection_core()
    except Exception as e:
        print(f"核心投影模組載入時發生未預期錯誤: {e}")
        print("將使用簡化的投影功能")
        return _create_fallback_projection_core()

def _create_fallback_projection_core():
    """創建一個簡化的投影核心替代品"""
    class FallbackProjectionCore:
        def __init__(self, *args, **kwargs):
            print("使用簡化的投影核心")

        def equirect_to_cubemap(self, image, cube_format="dict"):
            """簡化的全景圖到立方體轉換"""
            print("警告: 使用簡化的投影轉換，功能受限")
            # 返回一個基本的立方體結構
            h, w = image.shape[:2]
            face_size = min(h, w) // 4

            # 創建簡單的立方體面（這只是一個佔位符實現）
            faces = {}
            face_names = ['F', 'R', 'B', 'L', 'U', 'D']

            for i, name in enumerate(face_names):
                # 從原圖中提取一個區域作為立方體面
                start_x = (i * face_size) % w
                start_y = 0
                end_x = min(start_x + face_size, w)
                end_y = min(face_size, h)

                if end_x > start_x and end_y > start_y:
                    face = image[start_y:end_y, start_x:end_x]
                    # 調整大小到標準尺寸
                    import cv2
                    face = cv2.resize(face, (face_size, face_size))
                    faces[name] = face
                else:
                    # 如果無法提取，創建一個黑色面
                    faces[name] = np.zeros((face_size, face_size, 3), dtype=image.dtype)

            return faces

    return FallbackProjectionCore

def _import_detection():
    """延遲載入檢測模組"""
    try:
        from detection.detector import Detector
        return Detector
    except ImportError as e:
        print(f"檢測模組載入失敗: {e}")
        return None

print("開始載入配置模組...")
get_config, Config, ProcessMode, SaveMode = _import_config_modules()
print("配置模組載入完成")

print("開始載入核心投影模組...")
# 嘗試載入原版，如果失敗則使用 fallback
try:
    # 設置環境變量以避免問題
    import os
    os.environ["TESTING"] = "1"
    os.environ["DISABLE_NUMBA"] = "1"

    # 直接載入 ProjectionCore 類別，跳過有問題的中間層
    import sys
    sys.path.insert(0, 'src')

    # 強制清除可能影響 GPU 的環境變數
    import os
    if "TESTING" in os.environ:
        del os.environ["TESTING"]
    if "DISABLE_GPU" in os.environ:
        del os.environ["DISABLE_GPU"]

    # 手動載入必要的依賴
    from core.coordinate.core import CoordinateTransformer
    print("CoordinateTransformer 載入成功")

    # 嘗試載入 ProjectionCore
    from core.projection.core import ProjectionCore
    print("原版 ProjectionCore 載入成功")

except Exception as e:
    print(f"原版 ProjectionCore 載入失敗: {e}")
    print("使用簡化版本")
    ProjectionCore = _create_fallback_projection_core()

print("核心投影模組載入完成")

print("開始載入檢測模組...")
Detector = _import_detection()
print("檢測模組載入完成")
def _import_log_utils():
    """延遲載入日誌工具"""
    try:
        from log_utils import get_logger, setup_logger
        return get_logger, setup_logger
    except ImportError as e:
        print(f"日誌工具載入失敗: {e}")
        import logging
        return logging.getLogger, lambda *args, **kwargs: None

def _import_processing_modules():
    """延遲載入處理模組"""
    try:
        from processing.pyramid_generator import PyramidGenerator
        from processing.pyramid_generator import PyramidConfig, PyramidQuality
        from processing.progress_manager import ProgressManager
        return PyramidGenerator, PyramidConfig, PyramidQuality, ProgressManager
    except ImportError as e:
        print(f"處理模組載入失敗: {e}")
        return None, None, None, None

def _import_utils_modules():
    """延遲載入工具模組"""
    try:
        from utils.image_utils import ImageUtils
        from utils.file_utils import FileUtils
        from utils.gpu_manager import get_gpu_manager
        return ImageUtils, FileUtils, get_gpu_manager
    except ImportError as e:
        print(f"工具模組載入失敗: {e}")
        return None, None, None

print("開始載入日誌工具...")
get_logger, setup_logger = _import_log_utils()
print("日誌工具載入完成")

print("開始載入處理模組...")
PyramidGenerator, PyramidConfig, PyramidQuality, ProgressManager = _import_processing_modules()
print("處理模組載入完成")

print("開始載入工具模組...")
ImageUtils, FileUtils, get_gpu_manager = _import_utils_modules()
print("工具模組載入完成")


class ModernPyramidProcessor:
    """現代化金字塔處理器 - 整合所有處理模式"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = get_logger(__name__)
        
        # 載入配置
        if config_path and os.path.exists(config_path):
            self.config = self._load_yaml_config(config_path)
            self.logger.info(f"載入配置檔案: {config_path}")
        else:
            self.config = get_config()
            self.logger.info("使用預設配置")
        
        # 初始化核心元件
        self._init_components()
        
        # 支援的影像副檔名
        self.supported_exts = [".jpg", ".jpeg", ".png", ".bmp", ".tif", ".tiff"]
    
    def _load_yaml_config(self, config_path: str) -> Dict:
        """載入 YAML 配置檔案"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                yaml_config = yaml.safe_load(f)
            
            # 轉換為我們的配置格式
            # 這裡可以添加配置轉換邏輯
            return yaml_config
        except Exception as e:
            self.logger.error(f"載入配置檔案失敗: {e}")
            return {}
    
    def _init_components(self):
        """初始化核心元件"""
        try:
            # 初始化 GPU 管理器
            if get_gpu_manager is not None:
                self.gpu_manager = get_gpu_manager()
                if hasattr(self.gpu_manager, 'cuda_available') and self.gpu_manager.cuda_available:
                    device_count = len(self.gpu_manager.available_devices)
                    self.logger.info(f"GPU 可用: {device_count} 個裝置")
                else:
                    self.logger.info("使用 CPU 模式")
            else:
                self.gpu_manager = None
                self.logger.info("GPU 管理器不可用，使用 CPU 模式")

            # 延遲初始化標記
            self._detectors_initialized = False
            self._projection_initialized = False
            self._pyramid_generator_initialized = False

            self.logger.info("核心元件基本初始化完成（延遲載入模式）")

        except Exception as e:
            self.logger.error(f"初始化元件失敗: {e}")
            raise

    def _ensure_detectors_initialized(self):
        """確保檢測器已初始化"""
        if not self._detectors_initialized:
            try:
                self._init_detectors()
                self._detectors_initialized = True
            except Exception as e:
                self.logger.error(f"檢測器初始化失敗: {e}")
                self.detector = None
                self._detectors_initialized = True  # 設為 True 避免重複嘗試

    def _ensure_projection_initialized(self):
        """確保投影核心已初始化"""
        if not self._projection_initialized:
            self._init_projection()
            self._projection_initialized = True

    def _ensure_pyramid_generator_initialized(self):
        """確保金字塔生成器已初始化"""
        if not self._pyramid_generator_initialized:
            self._init_pyramid_generator()
            self._pyramid_generator_initialized = True
    
    def _init_detectors(self):
        """初始化檢測器"""
        try:
            if Detector is None:
                self.detector = None
                self.logger.warning("檢測模組不可用，將跳過檢測步驟")
                return

            model_paths = []

            # 從配置中取得模型路徑
            if "models" in self.config:
                models_config = self.config["models"]
                if "model1_path" in models_config:
                    model_paths.append(models_config["model1_path"])
                if "model2_path" in models_config:
                    model_paths.append(models_config["model2_path"])

            if model_paths:
                # 創建檢測配置
                from detection.core.config import DetectionConfig
                detection_config = DetectionConfig(primary_model_path=model_paths[0])
                self.detector = Detector(detection_config)
                self.logger.info(f"檢測器初始化成功，模型: {model_paths}")
            else:
                self.detector = None
                self.logger.warning("未指定檢測模型，將跳過檢測步驟")

        except Exception as e:
            self.logger.error(f"檢測器初始化失敗: {e}")
            self.detector = None
    
    def _init_projection(self):
        """初始化投影核心"""
        try:
            if ProjectionCore is None:
                self.projection = None
                self.logger.error("投影核心模組不可用")
                raise ImportError("ProjectionCore 模組載入失敗")

            # 使用標準的全景圖尺寸
            height = 2048  # 標準全景圖高度
            width = 4096   # 標準全景圖寬度

            # 檢查配置中的 GPU 設定
            enable_gpu = self.config.get("performance", {}).get("enable_gpu", True)
            preferred_device = 'cuda' if enable_gpu else None

            self.projection = ProjectionCore(
                height=height,
                width=width,
                use_gpu=enable_gpu,
                preferred_device=preferred_device
            )

            self.logger.info(f"投影核心初始化成功")
            if self.projection.device_info:
                self.logger.info(f"GPU 啟用: {self.projection.use_gpu}, 使用設備: {self.projection.device_info.name}")
            else:
                self.logger.info(f"GPU 啟用: {self.projection.use_gpu}, 使用設備: CPU")
        except Exception as e:
            self.logger.error(f"投影核心初始化失敗: {e}")
            raise
    
    def _init_pyramid_generator(self):
        """初始化金字塔生成器"""
        try:
            if PyramidGenerator is None or PyramidConfig is None:
                self.pyramid_generator = None
                self.logger.error("金字塔生成器模組不可用")
                raise ImportError("PyramidGenerator 或 PyramidConfig 模組載入失敗")

            # 從配置中取得金字塔參數
            pyramid_factors = self.config.get("pyramid_factors", [611, 1222, 2445])
            max_levels = len(pyramid_factors)
            tile_size = self.config.get("tile_size", 512)
            
            # 建立金字塔配置
            pyramid_config = PyramidConfig(
                max_levels=max_levels,
                tile_size=tile_size,
                quality=PyramidQuality.HIGH,
                pyramid_levels=pyramid_factors  # 傳入金字塔層級
            )

            self.pyramid_generator = PyramidGenerator(pyramid_config)
            self.logger.info(f"金字塔生成器初始化成功 - 層級: {pyramid_factors}, 瓦片大小: {tile_size}")

        except Exception as e:
            self.logger.error(f"金字塔生成器初始化失敗: {e}")
            raise
    
    def process_pano_to_cube(self) -> bool:
        """
        處理模式一：全景圖轉立方體金字塔

        從全景影像生成立方體面，進行檢測和模糊，然後生成金字塔。
        """
        self.logger.info("模式: 全景圖到金字塔 (pano_to_cube)")

        # 確保必要的元件已初始化
        self._ensure_projection_initialized()
        self._ensure_detectors_initialized()
        self._ensure_pyramid_generator_initialized()

        try:
            input_path = self.config["input_path"]
            output_path = self.config["primary_output_path"]
            
            # 檢查路徑
            if not os.path.exists(input_path):
                self.logger.error(f"輸入路徑不存在: {input_path}")
                return False
            
            FileUtils.ensure_directory(output_path)
            
            # 掃描輸入影像
            image_files = self._scan_images(input_path)
            if not image_files:
                self.logger.error(f"在 '{input_path}' 中找不到任何影像")
                return False
            
            self.logger.info(f"找到 {len(image_files)} 個影像檔案")
            
            # 進度管理和恢復功能
            progress_manager = ProgressManager(output_path=output_path)
            display_mode = self.config.get("display_mode", "verbose")
            
            # 加載已完成的場景以支援中斷恢復
            completed_scenes = progress_manager.load_completed_scenes(verify_output=True)
            
            # 檢查是否需要創建 default 目錄結構
            organize_by_district = self.config.get("organize_by_district", True)
            
            # 處理每個影像
            total_skipped = 0
            for i, image_file in enumerate(image_files, 1):
                scene_name = Path(image_file).stem
                
                # 檢查是否應該跳過
                if progress_manager.should_skip_scene("default", scene_name, completed_scenes):
                    total_skipped += 1
                    continue
                
                self._print_progress(i, len(image_files), scene_name, display_mode)
                
                # 保存開始處理狀態
                progress_manager.save_progress(
                    district="default",
                    scene=scene_name,
                    status="開始處理"
                )
                
                # 決定輸出路徑結構
                if organize_by_district:
                    # 創建 default 目錄結構
                    scene_output_dir = os.path.join(output_path, "default", scene_name)
                    FileUtils.ensure_directory(scene_output_dir)
                    actual_output_path = os.path.join(output_path, "default")
                else:
                    # 直接在根目錄
                    actual_output_path = output_path
                    scene_output_dir = os.path.join(output_path, scene_name)
                
                start_time = time.time()
                success = self._process_single_panorama(
                    image_file, actual_output_path, scene_name
                )
                
                processing_time = time.time() - start_time
                
                if success:
                    progress_manager.save_progress(
                        district="default",
                        scene=scene_name,
                        status="完成",
                        processing_time=processing_time
                    )
                    
                    if display_mode == "quiet":
                        print(f"處理完成，處理時間: {processing_time:.2f} 秒", flush=True)
                else:
                    progress_manager.save_progress(
                        district="default",
                        scene=scene_name,
                        status="失敗",
                        processing_time=processing_time
                    )
                    self.logger.error(f"處理失敗: {scene_name}")
                
                # 複製到次要輸出路徑
                self._copy_to_secondary_output(scene_output_dir)
            
            if total_skipped > 0:
                self.logger.info(f"全景圖處理完成 - 跳過已完成: {total_skipped} 個")
            else:
                self.logger.info("全景圖處理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"pano_to_cube 處理失敗: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    def process_cube_to_cube(self) -> bool:
        """
        處理模式二：立方體到立方體金字塔
        
        處理已有的立方體面資料夾，進行檢測和模糊，重新生成金字塔。
        """
        self.logger.info("模式: 立方體到金字塔 (cube_to_cube)")
        
        # 確保必要的元件已初始化
        self._ensure_detectors_initialized()
        self._ensure_pyramid_generator_initialized()
        
        try:
            input_path = self.config["input_path"]
            output_path = self.config["primary_output_path"]
            
            # 檢查路徑
            if not os.path.exists(input_path):
                self.logger.error(f"輸入路徑不存在: {input_path}")
                return False
            
            FileUtils.ensure_directory(output_path)
            
            # 掃描場景目錄
            scene_paths = self._scan_cube_scenes(input_path)
            if not scene_paths:
                self.logger.error(f"在 '{input_path}' 中找不到任何場景")
                return False
            
            self.logger.info(f"找到 {len(scene_paths)} 個場景")
            
            # 進度管理和恢復功能
            progress_manager = ProgressManager(output_path=output_path)
            display_mode = self.config.get("display_mode", "verbose")
            
            # 加載已完成的場景以支援中斷恢復
            completed_scenes = progress_manager.load_completed_scenes(verify_output=True)
            
            # 處理每個場景
            total_skipped = 0
            for i, scene_path in enumerate(scene_paths, 1):
                scene_name = os.path.basename(scene_path)
                district_name = os.path.basename(os.path.dirname(scene_path))
                display_name = f"{district_name}/{scene_name}"
                
                # 檢查是否應該跳過
                if progress_manager.should_skip_scene(district_name, scene_name, completed_scenes):
                    total_skipped += 1
                    continue
                
                self._print_progress(i, len(scene_paths), display_name, display_mode)
                
                # 保存開始處理狀態
                progress_manager.save_progress(
                    district=district_name,
                    scene=scene_name,
                    status="開始處理"
                )
                
                # 構建輸出路徑
                relative_path = os.path.relpath(scene_path, input_path)
                output_scene_path = os.path.join(output_path, relative_path)
                
                start_time = time.time()
                success = self._process_single_cube_scene(
                    scene_path, output_scene_path
                )
                
                processing_time = time.time() - start_time
                
                if success:
                    progress_manager.save_progress(
                        district=district_name,
                        scene=scene_name,
                        status="完成",
                        processing_time=processing_time
                    )
                    
                    if display_mode == "quiet":
                        print(f"處理完成，處理時間: {processing_time:.2f} 秒", flush=True)
                else:
                    progress_manager.save_progress(
                        district=district_name,
                        scene=scene_name,
                        status="失敗",
                        processing_time=processing_time
                    )
                    self.logger.error(f"場景處理失敗: {display_name}")
            
            if total_skipped > 0:
                self.logger.info(f"立方體場景處理完成 - 跳過已完成: {total_skipped} 個")
            else:
                self.logger.info("立方體場景處理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"cube_to_cube 處理失敗: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    def process_list_cube(self) -> bool:
        """
        處理模式三：清單處理模式
        
        根據 CSV/Excel 清單選擇性處理場景。
        """
        self.logger.info("模式: 清單到金字塔 (list_cube)")
        
        # 確保必要的元件已初始化
        self._ensure_detectors_initialized()
        self._ensure_pyramid_generator_initialized()
        
        try:
            settings = self.config["list_cube_settings"]
            input_path = self.config["input_path"]
            output_path = self.config["primary_output_path"]
            
            list_file = settings["list_file"]
            list_mode = settings["list_mode"]  # "include" 或 "exclude"
            list_cols = settings.get("list_columns", ["區", "場景"])
            
            # 檢查清單檔案
            if not os.path.exists(list_file):
                self.logger.error(f"清單檔案不存在: {list_file}")
                return False
            
            # 讀取清單
            try:
                if list_file.endswith(".csv"):
                    df = pd.read_csv(list_file)
                else:
                    df = pd.read_excel(list_file)
            except Exception as e:
                self.logger.error(f"讀取清單檔案失敗: {e}")
                return False
            
            # 取得目標場景列表
            scene_col = list_cols[1]
            if scene_col not in df.columns:
                self.logger.error(f"清單檔案中找不到場景欄位: {scene_col}")
                return False
            
            target_scenes = set(df[scene_col].str.strip().astype(str))
            
            # 掃描所有可用場景
            all_scenes = self._build_scene_map(input_path)
            
            # 根據模式篩選場景
            if list_mode == "include":
                scenes_to_process = {
                    name: path for name, path in all_scenes.items()
                    if name in target_scenes
                }
            elif list_mode == "exclude":
                scenes_to_process = {
                    name: path for name, path in all_scenes.items()
                    if name not in target_scenes
                }
            else:
                self.logger.error(f"未知的清單模式: {list_mode}")
                return False
            
            self.logger.info(f"篩選後需處理 {len(scenes_to_process)} 個場景")
            
            # 進度管理和恢復功能
            progress_manager = ProgressManager(output_path=output_path)
            display_mode = self.config.get("display_mode", "verbose")
            
            # 加載已完成的場景以支援中斷恢復
            completed_scenes = progress_manager.load_completed_scenes(verify_output=True)
            
            # 處理篩選的場景
            
            total_skipped = 0
            for i, (scene_name, scene_path) in enumerate(scenes_to_process.items(), 1):
                district_name = os.path.basename(os.path.dirname(scene_path))
                display_name = f"{district_name}/{scene_name}"
                
                # 檢查是否應該跳過
                if progress_manager.should_skip_scene(district_name, scene_name, completed_scenes):
                    total_skipped += 1
                    continue
                
                self._print_progress(i, len(scenes_to_process), display_name, display_mode)
                
                # 保存開始處理狀態
                progress_manager.save_progress(
                    district=district_name,
                    scene=scene_name,
                    status="開始處理"
                )
                
                # 構建輸出路徑
                relative_path = os.path.relpath(scene_path, input_path)
                output_scene_path = os.path.join(output_path, relative_path)
                
                start_time = time.time()
                success = self._process_single_cube_scene(
                    scene_path, output_scene_path
                )
                
                processing_time = time.time() - start_time
                
                if success:
                    progress_manager.save_progress(
                        district=district_name,
                        scene=scene_name,
                        status="完成",
                        processing_time=processing_time
                    )
                    
                    if display_mode == "quiet":
                        print(f"處理完成，處理時間: {processing_time:.2f} 秒", flush=True)
                else:
                    progress_manager.save_progress(
                        district=district_name,
                        scene=scene_name,
                        status="失敗",
                        processing_time=processing_time
                    )
                    self.logger.error(f"場景處理失敗: {display_name}")
            
            if total_skipped > 0:
                self.logger.info(f"清單處理完成 - 跳過已完成: {total_skipped} 個")
            else:
                self.logger.info("清單處理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"list_cube 處理失敗: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    def _process_single_panorama(
        self, 
        image_path: str, 
        output_dir: str, 
        scene_name: str
    ) -> bool:
        """處理單個全景影像"""
        try:
            # 載入影像
            image = ImageUtils.cv_imread(image_path)
            if image is None:
                self.logger.error(f"無法載入影像: {image_path}")
                return False
            
            # 全景轉立方體
            cube_dict = self.projection.equirect_to_cubemap(
                image, 
                cube_format="dict"
            )
            
            # AI 檢測和模糊
            if self.detector:
                processed_cube_dict = {}
                for face_name, face_image in cube_dict.items():
                    if face_name != "U":  # 不處理頂面
                        processed_face = self._detect_and_blur(face_image)
                    else:
                        processed_face = face_image
                    processed_cube_dict[face_name] = processed_face
            else:
                processed_cube_dict = cube_dict
            
            # 生成金字塔
            result = self.pyramid_generator.generate_pyramid(
                cube_faces=processed_cube_dict,
                thumbnail=image,
                output_dir=output_dir,
                scene_name=scene_name
            )

            return result
            
        except Exception as e:
            self.logger.error(f"處理全景影像失敗: {e}")
            return False
    
    def _process_single_cube_scene(
        self, 
        input_scene_path: str, 
        output_scene_path: str
    ) -> bool:
        """處理單個立方體場景"""
        try:
            scene_name = os.path.basename(input_scene_path)
            
            # 複製原始資料
            if os.path.exists(output_scene_path):
                shutil.rmtree(output_scene_path)
            shutil.copytree(input_scene_path, output_scene_path)
            
            # 檢查 html5 資料夾
            html5_dir = os.path.join(output_scene_path, "html5")
            if not os.path.exists(html5_dir):
                self.logger.warning(f"找不到 html5 資料夾: {html5_dir}")
                return False
            
            # 載入立方體面
            cube_faces = {}
            face_files = {}
            for face_id in range(6):
                face_file = os.path.join(html5_dir, f"{face_id}.jpg")
                if os.path.exists(face_file):
                    face_image = ImageUtils.cv_imread(face_file)
                    if face_image is not None:
                        cube_faces[face_id] = face_image
                        face_files[face_id] = face_file
            
            if len(cube_faces) != 6:
                self.logger.warning(f"場景 {scene_name} 的立方體面不完整")
                return False
            
            # AI 檢測和處理
            need_regenerate = False
            faces_to_regen = []
            
            if self.detector:
                for face_id, face_image in cube_faces.items():
                    if face_id != 4:  # 不處理頂面 (UP = 4)
                        processed_face, was_modified = self._detect_and_blur_with_check(face_image)
                        if was_modified:
                            cube_faces[face_id] = processed_face
                            faces_to_regen.append(face_id)
                            need_regenerate = True
                            
                            # 更新 html5 檔案
                            ImageUtils.cv_imwrite(face_files[face_id], processed_face)
            
            # Logo 處理
            logo_path = self.config.get("logo", {}).get("path")
            if logo_path and os.path.exists(logo_path) and 5 not in faces_to_regen:
                faces_to_regen.append(5)  # DOWN 面
                need_regenerate = True
            
            # 如果需要重新生成金字塔
            if need_regenerate:
                self.logger.info(f"重新生成場景 {scene_name} 的金字塔，面: {faces_to_regen}")
                
                # 重新生成指定面的金字塔
                self._regenerate_pyramid_faces(
                    cube_faces, output_scene_path, faces_to_regen
                )
            else:
                self.logger.info(f"場景 {scene_name} 無需更新")
            
            # 複製到次要輸出
            self._copy_to_secondary_output(output_scene_path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"處理立方體場景失敗: {e}")
            return False
    
    def _detect_and_blur(self, image: np.ndarray) -> np.ndarray:
        """檢測並模糊影像"""
        # 確保檢測器已初始化
        self._ensure_detectors_initialized()
        
        if not hasattr(self, 'detector') or not self.detector:
            return image
        
        try:
            # 執行檢測
            detections = self.detector.detect_dual_model(image)
            
            # 模糊檢測區域
            if detections:
                image_copy = image.copy()
                h, w = image_copy.shape[:2]
                area_threshold = h * w * 0.03  # 3% 面積閾值
                
                for detection in detections:
                    x1, y1, x2, y2 = detection.x1, detection.y1, detection.x2, detection.y2
                    
                    # 檢查區域大小
                    if (x2 - x1) * (y2 - y1) <= area_threshold:
                        roi = image_copy[y1:y2, x1:x2]
                        if roi.size > 0:
                            blurred_roi = cv2.GaussianBlur(roi, (99, 99), 30)
                            image_copy[y1:y2, x1:x2] = blurred_roi
                
                return image_copy
            
            return image
            
        except Exception as e:
            self.logger.error(f"檢測和模糊處理失敗: {e}")
            return image
    
    def _detect_and_blur_with_check(self, image: np.ndarray) -> tuple[np.ndarray, bool]:
        """檢測並模糊影像，返回是否有修改"""
        processed = self._detect_and_blur(image)
        was_modified = not np.array_equal(image, processed)
        return processed, was_modified
    
    def _regenerate_pyramid_faces(
        self, 
        cube_faces: Dict[int, np.ndarray],
        output_scene_path: str,
        faces_to_regen: List[int]
    ):
        """重新生成指定面的金字塔"""
        try:
            # 獲取金字塔層級配置
            pyramid_levels = self.pyramid_generator._get_pyramid_levels()
            
            # 只生成指定面的瓦片
            for face_id in faces_to_regen:
                if face_id in cube_faces:
                    success = self.pyramid_generator.tile_generator.generate_pyramid_tiles(
                        cube_faces[face_id],
                        face_id,
                        output_scene_path,
                        pyramid_levels
                    )
                    if success:
                        self.logger.debug(f"成功重新生成面 {face_id} 的金字塔")
                    else:
                        self.logger.warning(f"重新生成面 {face_id} 的金字塔失敗")
                    
        except Exception as e:
            self.logger.error(f"重新生成金字塔失敗: {e}")
    
    def _scan_images(self, input_dir: str) -> List[str]:
        """掃描輸入目錄中的影像檔案"""
        image_files = []
        for ext in self.supported_exts:
            pattern = os.path.join(input_dir, f"*{ext}")
            image_files.extend([f for f in os.listdir(input_dir) 
                             if f.lower().endswith(ext.lower())])
        
        return [os.path.join(input_dir, f) for f in image_files]
    
    def _scan_cube_scenes(self, input_dir: str) -> List[str]:
        """掃描立方體場景目錄"""
        scene_paths = []
        
        for district in os.listdir(input_dir):
            district_path = os.path.join(input_dir, district)
            if os.path.isdir(district_path):
                for scene in os.listdir(district_path):
                    scene_path = os.path.join(district_path, scene)
                    if os.path.isdir(scene_path):
                        scene_paths.append(scene_path)
        
        return scene_paths
    
    def _build_scene_map(self, input_dir: str) -> Dict[str, str]:
        """建立場景名稱到路徑的映射"""
        scene_map = {}
        
        for district in os.listdir(input_dir):
            district_path = os.path.join(input_dir, district)
            if os.path.isdir(district_path):
                for scene in os.listdir(district_path):
                    scene_path = os.path.join(district_path, scene)
                    if os.path.isdir(scene_path):
                        scene_map[scene] = scene_path
        
        return scene_map
    
    def _copy_to_secondary_output(self, primary_scene_path: str):
        """複製到次要輸出路徑"""
        secondary_output_path = self.config.get("secondary_output_path")
        if not secondary_output_path:
            return
        
        try:
            primary_base_path = self.config["primary_output_path"]
            relative_path = os.path.relpath(primary_scene_path, primary_base_path)
            secondary_scene_path = os.path.join(secondary_output_path, relative_path)
            
            self.logger.info(f"複製到次要輸出: {secondary_scene_path}")
            shutil.copytree(primary_scene_path, secondary_scene_path, dirs_exist_ok=True)
            
        except Exception as e:
            self.logger.error(f"複製到次要輸出失敗: {e}")
    
    def _print_progress(self, current: int, total: int, item_name: str, display_mode: str):
        """顯示進度"""
        if display_mode == "quiet":
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            print(f"({current}/{total}) {item_name} {timestamp}", flush=True)
        else:  # verbose mode
            self.logger.info(f"進度: ({current}/{total}) - 正在處理 {item_name}")


def main():
    """主程式入口"""
    parser = argparse.ArgumentParser(
        description="現代化全景影像金字塔生成系統",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
處理模式：
  pano_to_cube  - 全景圖轉立方體金字塔
  cube_to_cube  - 立方體到立方體金字塔  
  list_cube     - 清單處理模式

範例：
  python generate_pyramid.py config.yaml
  python generate_pyramid.py --help
        """
    )
    
    parser.add_argument(
        "config", 
        nargs="?", 
        default="config.yaml",
        help="配置檔案路徑 (預設: config.yaml)"
    )
    
    parser.add_argument(
        "--mode",
        choices=["pano_to_cube", "cube_to_cube", "list_cube"],
        help="強制指定處理模式"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="顯示詳細資訊"
    )
    
    parser.add_argument(
        "--quiet", "-q", 
        action="store_true",
        help="安靜模式"
    )
    
    args = parser.parse_args()
    
    # 設定日誌
    try:
        if args.verbose:
            setup_logger("generate_pyramid", level="DEBUG")
        elif args.quiet:
            setup_logger("generate_pyramid", level="WARNING")
        else:
            setup_logger("generate_pyramid", level="INFO")
    except TypeError:
        # 如果 setup_logger 是 lambda 函數（導入失敗的情況）
        import logging
        if args.verbose:
            logging.basicConfig(level=logging.DEBUG)
        elif args.quiet:
            logging.basicConfig(level=logging.WARNING)
        else:
            logging.basicConfig(level=logging.INFO)
    
    logger = get_logger(__name__)
    
    try:
        # 檢查配置檔案
        if not os.path.exists(args.config):
            logger.error(f"配置檔案不存在: {args.config}")
            if args.config == "config.yaml":
                logger.info("請建立 config.yaml 檔案或指定其他配置檔案")
            return 1
        
        # 初始化處理器
        logger.info("=== 現代化全景影像金字塔生成系統 ===")
        logger.info(f"配置檔案: {args.config}")
        
        processor = ModernPyramidProcessor(args.config)
        
        # 設置顯示模式（將命令行參數映射到配置）
        if args.quiet:
            processor.config["display_mode"] = "quiet"
        elif args.verbose:
            processor.config["display_mode"] = "verbose"
        else:
            # 使用配置檔案中的設定或預設值
            processor.config.setdefault("display_mode", "verbose")
        
        # 確定處理模式
        if args.mode:
            mode = args.mode
        else:
            mode = processor.config.get("mode", "pano_to_cube")
        
        logger.info(f"處理模式: {mode}")
        
        # 開始處理
        start_time = time.time()
        
        if mode == "pano_to_cube":
            success = processor.process_pano_to_cube()
        elif mode == "cube_to_cube":
            success = processor.process_cube_to_cube()
        elif mode == "list_cube":
            success = processor.process_list_cube()
        else:
            logger.error(f"未知的處理模式: {mode}")
            return 1
        
        total_time = time.time() - start_time
        
        if success:
            logger.info(f"處理完成！總耗時: {total_time:.2f} 秒")
            return 0
        else:
            logger.error(f"處理失敗！總耗時: {total_time:.2f} 秒")
            return 1
            
    except KeyboardInterrupt:
        logger.info("用戶中止執行")
        return 130
    except Exception as e:
        logger.error(f"執行過程中發生錯誤: {e}")
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())