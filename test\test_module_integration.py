"""
Cross-module integration tests between refactored modules (utils, processing) 
and existing legacy modules (core, detection, config, log_utils).
"""
import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import time
from typing import Dict, Any, List

# Refactored modules
from utils.factory import UtilsFactory
from utils.config import UtilsConfig
from processing.factory import ProcessingFactory
from processing.config import ProcessingConfig, CubeGenerationConfig, DetectionConfig

# Legacy modules integration
from config.settings import get_config, Config
from detection.detector import Detector as LegacyDetector
from log_utils.factory import get_logger


class TestLegacyConfigIntegration:
    """Test integration with existing config module."""
    
    def test_utils_config_from_legacy_settings(self):
        """Test creating UtilsConfig from legacy settings."""
        # Get legacy config
        legacy_config = get_config()
        
        # Convert to new config format
        utils_config = UtilsConfig.from_legacy_settings(legacy_config)
        
        assert isinstance(utils_config, UtilsConfig)
        assert utils_config.gpu.enable_gpu == legacy_config.system.enable_gpu
        assert utils_config.memory.strategy.upper() == legacy_config.system.memory_strategy.upper()
    
    def test_processing_config_from_legacy_settings(self):
        """Test creating ProcessingConfig from legacy settings."""
        legacy_config = get_config()
        
        # Convert to new config format
        processing_config = ProcessingConfig.from_legacy_settings(legacy_config)
        
        assert isinstance(processing_config, ProcessingConfig)
        assert processing_config.cube_generation.cube_size == legacy_config.processing.cube_size
        assert processing_config.detection.confidence_threshold == legacy_config.model.conf_threshold
    
    def test_backward_compatibility_factories(self):
        """Test that factories can be created from legacy config."""
        legacy_config = get_config()
        
        # Create factories using legacy config
        utils_factory = UtilsFactory.from_legacy_config(legacy_config)
        processing_factory = ProcessingFactory.from_legacy_config(legacy_config, utils_factory)
        
        assert isinstance(utils_factory, UtilsFactory)
        assert isinstance(processing_factory, ProcessingFactory)
        
        # Test that services can be created
        gpu_manager = utils_factory.get_service(utils.resource.gpu.GPUManager)
        cube_step = processing_factory.get_step("cube_generation")
        
        assert gpu_manager is not None
        assert cube_step is not None


class TestDetectionModuleIntegration:
    """Test integration with existing detection module."""
    
    @pytest.fixture
    def detection_setup(self):
        """Setup for detection integration tests."""
        utils_config = UtilsConfig(enable_gpu=False)  # Disable GPU for testing
        processing_config = ProcessingConfig(
            detection=DetectionConfig(
                enable_detection=True,
                confidence_threshold=0.5,
                enable_face_detection=True,
                enable_gpu_acceleration=False
            )
        )
        
        utils_factory = UtilsFactory(utils_config)
        processing_factory = ProcessingFactory(processing_config, utils_factory)
        
        return processing_factory
    
    @patch('detection.core.detector.Detector')
    def test_detection_step_with_legacy_detector(self, mock_detector_class, detection_setup):
        """Test integration between DetectionStep and legacy detection module."""
        processing_factory = detection_setup
        
        # Mock the legacy detector
        mock_detector = Mock(spec=LegacyDetector)
        mock_detector.batch_process_faces.return_value = {
            0: {"detections": [{"bbox": [10, 10, 50, 50], "confidence": 0.8, "class": "face"}]},
            1: {"detections": [{"bbox": [100, 100, 150, 150], "confidence": 0.7, "class": "plate"}]}
        }
        mock_detector_class.return_value = mock_detector
        
        # Get detection step
        detection_step = processing_factory.get_step("detection")
        
        # Create test input
        input_data = {
            "cubemap_faces": {
                0: np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
                1: np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
            }
        }
        
        context = {}
        result = detection_step.execute(input_data, context)
        
        # Verify integration worked
        assert result.success == True
        assert "detection_results" in result.output_data
        assert "detection_results" in context
        
        # Verify legacy detector was called correctly
        mock_detector.batch_process_faces.assert_called_once()
    
    def test_detection_config_mapping(self, detection_setup):
        """Test that detection configuration maps correctly to legacy detector config."""
        processing_factory = detection_setup
        detection_step = processing_factory.get_step("detection")
        
        # Verify config mapping
        assert detection_step._config.confidence_threshold == 0.5
        assert detection_step._config.enable_face_detection == True
        assert detection_step._config.enable_gpu_acceleration == False


class TestCoreModuleIntegration:
    """Test integration with existing core module."""
    
    @pytest.fixture
    def core_integration_setup(self):
        """Setup for core module integration tests."""
        utils_config = UtilsConfig(enable_gpu=False)
        processing_config = ProcessingConfig(
            cube_generation=CubeGenerationConfig(
                cube_size=512,
                interpolation_method="bilinear",
                enable_caching=True
            )
        )
        
        utils_factory = UtilsFactory(utils_config)
        processing_factory = ProcessingFactory(processing_config, utils_factory)
        
        return processing_factory
    
    @patch('core.projection.EquirectangularProjection')
    @patch('core.cube_mapping.CubeMapper')
    def test_cube_generation_with_core_modules(self, mock_cube_mapper, mock_projection, core_integration_setup):
        """Test integration between CubeGenerationStep and core modules."""
        processing_factory = core_integration_setup
        
        # Mock core module classes
        mock_projection_instance = Mock()
        mock_projection.return_value = mock_projection_instance
        
        mock_cube_mapper_instance = Mock()
        mock_cube_mapper_instance.generate_faces.return_value = [
            np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8) for _ in range(6)
        ]
        mock_cube_mapper.return_value = mock_cube_mapper_instance
        
        # Get cube generation step
        cube_step = processing_factory.get_step("cube_generation")
        
        # Test input
        input_data = {
            "image": np.random.randint(0, 255, (1024, 2048, 3), dtype=np.uint8),
            "image_path": "/test/panorama.jpg"
        }
        
        context = {}
        result = cube_step.execute(input_data, context)
        
        # Verify step execution
        assert result.success == True
        assert "cubemap_faces" in result.output_data
        assert "cubemap_data" in context
    
    def test_coordinate_transformation_integration(self, core_integration_setup):
        """Test integration with core coordinate transformation modules."""
        # This would test integration with core.coordinate module
        # For now, we verify the setup allows for such integration
        processing_factory = core_integration_setup
        cube_step = processing_factory.get_step("cube_generation")
        
        # Verify configuration is properly passed
        assert cube_step._config.cube_size == 512
        assert cube_step._config.interpolation_method == "bilinear"


class TestLogUtilsIntegration:
    """Test integration with existing log_utils module."""
    
    def test_factory_logging_integration(self):
        """Test that factories integrate properly with log_utils."""
        # Create factories
        utils_config = UtilsConfig(enable_gpu=False)
        utils_factory = UtilsFactory(utils_config)
        
        processing_config = ProcessingConfig()
        processing_factory = ProcessingFactory(processing_config, utils_factory)
        
        # Verify no logging errors occur during normal operations
        gpu_manager = utils_factory.get_service(utils.resource.gpu.GPUManager)
        cube_step = processing_factory.get_step("cube_generation")
        
        # These operations should complete without logging errors
        assert gpu_manager is not None
        assert cube_step is not None
    
    def test_pipeline_logging_integration(self):
        """Test that pipeline execution integrates with logging."""
        utils_config = UtilsConfig(enable_gpu=False)
        utils_factory = UtilsFactory(utils_config)
        
        processing_config = ProcessingConfig(
            detection=DetectionConfig(enable_detection=False),
            blur=BlurConfig(enable_blur=False)
        )
        processing_factory = ProcessingFactory(processing_config, utils_factory)
        
        pipeline = processing_factory.create_pipeline()
        
        input_data = {
            "image": np.random.randint(0, 255, (256, 512, 3), dtype=np.uint8)
        }
        
        # Pipeline execution should log properly without errors
        results = pipeline.run(input_data)
        assert len(results) > 0
    
    def test_custom_logger_usage(self):
        """Test that custom loggers from log_utils are used correctly."""
        # Verify logger creation works
        logger = get_logger("test_integration")
        assert logger is not None
        
        # Test logging calls don't raise exceptions
        logger.info("Test integration message")
        logger.debug("Debug message for integration test")


class TestEndToEndSystemIntegration:
    """End-to-end system integration tests."""
    
    @pytest.fixture
    def full_system_setup(self):
        """Setup complete system with all integrations."""
        # Start with legacy config
        legacy_config = get_config()
        
        # Create refactored factories
        utils_factory = UtilsFactory.from_legacy_config(legacy_config)
        processing_factory = ProcessingFactory.from_legacy_config(legacy_config, utils_factory)
        
        return {
            "legacy_config": legacy_config,
            "utils_factory": utils_factory,
            "processing_factory": processing_factory
        }
    
    def test_complete_system_workflow(self, full_system_setup):
        """Test complete workflow using both legacy and refactored modules."""
        setup = full_system_setup
        processing_factory = setup["processing_factory"]
        
        # Create a simplified pipeline (disable heavy operations for testing)
        custom_pipeline = processing_factory.create_custom_pipeline(["cube_generation"])
        
        # Test input
        input_data = {
            "image": np.random.randint(0, 255, (512, 1024, 3), dtype=np.uint8),
            "image_path": "/test/panorama.jpg",
            "metadata": {"test": "integration"}
        }
        
        # Execute pipeline
        start_time = time.perf_counter()
        results = custom_pipeline.run(input_data)
        execution_time = time.perf_counter() - start_time
        
        # Verify results
        assert len(results) == 1
        assert results[0].success == True
        assert results[0].step_name == "CubeGenerationStep"
        assert execution_time < 10.0  # Should complete within reasonable time
    
    def test_configuration_consistency(self, full_system_setup):
        """Test that configurations remain consistent across the system."""
        setup = full_system_setup
        legacy_config = setup["legacy_config"]
        utils_factory = setup["utils_factory"]
        processing_factory = setup["processing_factory"]
        
        # Verify configuration values are properly mapped
        assert utils_factory.config.gpu.enable_gpu == legacy_config.system.enable_gpu
        assert processing_factory.config.cube_generation.cube_size == legacy_config.processing.cube_size
        
        # Verify factories can access each other's services
        gpu_manager = utils_factory.get_service(utils.resource.gpu.GPUManager)
        cube_step = processing_factory.get_step("cube_generation")
        
        assert gpu_manager is not None
        assert cube_step is not None
    
    def test_error_handling_across_modules(self, full_system_setup):
        """Test error handling integration across refactored and legacy modules."""
        setup = full_system_setup
        processing_factory = setup["processing_factory"]
        
        # Test with invalid input to trigger error handling
        invalid_input = {"invalid": "data"}
        
        cube_step = processing_factory.get_step("cube_generation")
        
        # Should handle errors gracefully
        with pytest.raises(Exception):  # Should raise validation error
            cube_step.execute(invalid_input, {})
    
    def test_resource_cleanup_integration(self, full_system_setup):
        """Test that resource cleanup works across all modules."""
        setup = full_system_setup
        utils_factory = setup["utils_factory"]
        processing_factory = setup["processing_factory"]
        
        # Create services
        gpu_manager = utils_factory.get_service(utils.resource.gpu.GPUManager)
        memory_manager = utils_factory.get_service(utils.resource.memory.MemoryManager)
        pipeline = processing_factory.create_pipeline()
        
        # Mock shutdown methods
        gpu_manager.shutdown = Mock()
        memory_manager.shutdown = Mock()
        
        # Shutdown everything
        processing_factory.shutdown()
        utils_factory.shutdown()
        
        # Verify cleanup was called
        gpu_manager.shutdown.assert_called_once()
        memory_manager.shutdown.assert_called_once()


class TestPerformanceIntegrationBaseline:
    """Baseline performance tests for integration scenarios."""
    
    def test_factory_creation_performance(self):
        """Test performance of factory creation with full integration."""
        legacy_config = get_config()
        
        start_time = time.perf_counter()
        utils_factory = UtilsFactory.from_legacy_config(legacy_config)
        processing_factory = ProcessingFactory.from_legacy_config(legacy_config, utils_factory)
        creation_time = time.perf_counter() - start_time
        
        # Factory creation should be fast
        assert creation_time < 1.0  # Less than 1 second
        
        # Verify factories are functional
        assert utils_factory.get_service(utils.resource.gpu.GPUManager) is not None
        assert processing_factory.get_step("cube_generation") is not None
    
    def test_service_access_performance(self):
        """Test performance of service access across modules."""
        utils_config = UtilsConfig(enable_gpu=False)
        utils_factory = UtilsFactory(utils_config)
        
        # Time multiple service accesses
        start_time = time.perf_counter()
        for _ in range(100):
            gpu_manager = utils_factory.get_service(utils.resource.gpu.GPUManager)
        access_time = time.perf_counter() - start_time
        
        # Service access should be fast (cached after first access)
        assert access_time < 0.1  # Less than 0.1 seconds for 100 accesses
    
    def test_pipeline_execution_baseline(self):
        """Establish baseline performance for pipeline execution."""
        utils_config = UtilsConfig(enable_gpu=False)
        utils_factory = UtilsFactory(utils_config)
        
        processing_config = ProcessingConfig(
            detection=DetectionConfig(enable_detection=False),
            blur=BlurConfig(enable_blur=False)
        )
        processing_factory = ProcessingFactory(processing_config, utils_factory)
        
        pipeline = processing_factory.create_custom_pipeline(["cube_generation"])
        
        input_data = {
            "image": np.random.randint(0, 255, (256, 512, 3), dtype=np.uint8)
        }
        
        # Measure execution time
        start_time = time.perf_counter()
        results = pipeline.run(input_data)
        execution_time = time.perf_counter() - start_time
        
        # Store baseline for future comparison
        assert execution_time < 5.0  # Should complete within 5 seconds
        assert len(results) == 1
        assert results[0].success == True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

