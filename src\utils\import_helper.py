"""
導入輔助模組
提供統一的路徑設置功能
"""

import sys
import os
from pathlib import Path

# matplotlib 配置設置
def setup_matplotlib_config():
    """設置繪圖樣式為英文，避免中文顯示問題"""
    try:
        import matplotlib.pyplot as plt
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
        plt.rcParams['axes.unicode_minus'] = False
    except ImportError:
        pass  # 如果 matplotlib 不可用則跳過

def setup_project_paths():
    """
    Path setup is no longer needed with pip install -e .
    This function is kept for backward compatibility but does nothing.
    """
    pass


if __name__ != "__main__":
    # 只有在作為模塊導入時才自動設置
    setup_project_paths()
    setup_matplotlib_config()