"""
座標轉換工具的輔助函式模組 (core.coordinate.utils)

本模組提供與座標轉換相關的輔助工具函式，
這些函式的功能較為通用，不適合直接放在 `core.py` 或 `numba_kernels.py` 中。
"""


def point_in_polygon(x: float, y: float, polygon_points: list[tuple[float, float]]) -> bool:
    """
    使用射線法 (Ray-casting algorithm) 判斷一個 2D 點是否在一個多邊形內部。

    這個演算法的原理是從該點向任意固定方向（此處為正 X 軸方向）發射一條射線，
    並計算這條射線與多邊形邊的交點數量。如果交點數量為奇數，則點在多邊形內；
    如果為偶數（或零），則點在多邊形外。此方法對凹多邊形和凸多邊形都有效。

    :param x: 待測點的 X 座標。
    :param y: 待測點的 Y 座標。
    :param polygon_points: 一個包含多邊形所有頂點座標的元組列表，
                             格式為 `[(x1, y1), (x2, y2), ...]`。頂點應按順時針或逆時針順序排列。
    :return: 如果點在多邊形內部，返回 `True`；否則返回 `False`。
    """
    n = len(polygon_points)
    if n < 3:
        # 一個多邊形至少需要三個頂點
        return False

    inside = False
    p1x, p1y = polygon_points[0]
    for i in range(1, n + 1):
        p2x, p2y = polygon_points[i % n]
        # 檢查射線是否與邊 (p1, p2) 的 Y 軸範圍相交
        if y > min(p1y, p2y) and y <= max(p1y, p2y):
            # 檢查點是否在邊的左側（考慮到射線是向右無限延伸）
            if x <= max(p1x, p2x):
                # 避免除以零的邊緣情況（水平邊）
                if p1y != p2y:
                    # 計算射線與邊的交點的 X 座標
                    x_intersection = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                # 如果點在交點的左側，或者邊是垂直的且點在邊的左側
                if p1x == p2x or x <= x_intersection:
                    # 翻轉 `inside` 狀態，表示又多了一個交點
                    inside = not inside
        p1x, p1y = p2x, p2y
    return inside
