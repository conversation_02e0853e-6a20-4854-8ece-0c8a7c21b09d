#!/usr/bin/env python3
"""
Log Utils v2.0 使用示例 (重構版本)

展示重構後統一 API 和新功能的各種使用方式
包含新的統一工廠系統、配置預設、批量操作等企業級功能
"""

import logging
import sys
from pathlib import Path

# Import project modules directly (using pip install -e .)


def example_new_unified_api():
    """新統一 API 使用示例 (推薦)"""
    print("=== 新統一 API 使用示例 (推薦) ===")

    # 重構後的新 API - 推薦使用
    from log_utils import create_logger, console_logger, debug_logger
    from log_utils import list_available_presets

    # 查看所有可用預設
    print(f"可用預設: {list_available_presets()}")
    print()

    # 使用配置預設創建日誌器
    dev_logger = create_logger("dev_app", "development")
    dev_logger.info("開發模式日誌器創建成功")
    dev_logger.debug("詳細調試信息")

    prod_logger = create_logger("prod_app", "production") 
    prod_logger.info("生產模式日誌器創建成功")
    prod_logger.warning("生產環境警告")
    
    # 使用便利函數
    console_log = console_logger("console_app")
    console_log.info("僅控制台輸出")
    
    debug_log = debug_logger("debug_app")
    debug_log.debug("調試專用日誌")
    print()


def example_old_api_compatibility():
    """舊 API 向後相容性示例"""
    print("=== 舊 API 向後相容性示例 ===")

    # 舊 API 仍然可用
    from log_utils import setup_logger
    from log_utils.factory import create_logger_from_preset

    # 舊的基本創建方式
    logger = setup_logger(
        name="old_example_app",
        level=logging.INFO,
        console_output=True,
        file_output=False,  # 避免創建文件
        use_colors=True,
    )

    logger.info("舊 API 仍然工作正常")
    logger.warning("向後相容性保持 100%")
    
    # 舊的預設創建方式
    preset_logger = create_logger_from_preset("old_preset_app", "development")
    preset_logger.info("舊預設 API 正常工作")
    print()


def example_new_factory_system():
    """新工廠系統使用示例"""
    print("=== 新工廠系統使用示例 ===")

    from log_utils import ConfigPresets, LoggerFactory
    from log_utils.factory import get_global_log_manager
    from log_utils import get_factory_statistics

    # 1. 使用工廠進行批量創建
    factory = LoggerFactory(get_global_log_manager())
    batch_configs = {
        "api": {"preset": "production"},
        "database": {"preset": "debug"},
        "cache": {"preset": "console_only"}
    }
    loggers = factory.batch_create(batch_configs)
    
    for name, logger in loggers.items():
        logger.info(f"批量創建的日誌器: {name}")
    
    # 2. 添加自定義預設
    def custom_preset(**kwargs):
        from log_utils.core.config import LogConfig
        return LogConfig(
            console_output=True,
            file_output=False,
            use_colors=True,
            level=20,  # INFO
            **kwargs
        )
    
    ConfigPresets.add_preset("my_custom", custom_preset)
    custom_logger = factory.create("custom_app", "my_custom")
    custom_logger.info("自定義預設日誌器")
    
    # 3. 查看工廠統計
    stats = get_factory_statistics()
    print(f"已創建日誌器數量: {stats.get('total_loggers', 0)}")
    print(f"可用預設: {stats.get('available_presets', [])}")
    print()


def example_config_usage():
    """配置系統使用示例"""
    print("=== 進階配置系統使用示例 ===")

    from log_utils.core.config import LogConfig, LogConfigPresets
    from log_utils.core.manager import LogManager

    # 使用預設配置
    config = LogConfigPresets.development()
    config.name = "config_example"
    config.file_output = False  # 避免創建文件

    # 創建管理器
    manager = LogManager(config)
    logger = manager.setup_logger("config_example")

    logger.debug("這是調試信息")
    logger.info("這是普通信息")
    logger.warning("這是警告信息")
    print()


def example_formatters():
    """格式器使用示例"""
    print("=== 格式器使用示例 ===")

    from log_utils.formatters import (ColoredFormatter, CompactFormatter,
                                      JSONFormatter)
    from log_utils.handlers import create_console_handler

    # 創建不同格式的處理器
    formatters = [
        ("彩色格式器", ColoredFormatter("%(levelname)s - %(message)s")),
        ("JSON格式器", JSONFormatter()),
        ("緊湊格式器", CompactFormatter()),
    ]

    for name, formatter in formatters:
        print(f"{name}:")
        handler = create_console_handler(level=logging.INFO, use_colors=False)
        handler.setFormatter(formatter)

        logger = logging.getLogger(f"formatter_test_{name}")
        logger.handlers.clear()
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        logger.propagate = False

        logger.info("測試消息")
        print()


def example_presets():
    """預設配置示例"""
    print("=== 預設配置示例 ===")

    from log_utils.factory import create_logger_from_preset

    presets = ["development", "production", "debug", "minimal"]

    for preset in presets:
        logger = create_logger_from_preset(
            f"preset_{preset}", preset, file_output=False
        )
        logger.info(f"使用 {preset} 預設配置")

    print()


def example_advanced_features():
    """高級功能示例"""
    print("=== 高級功能示例 ===")

    from log_utils.factory import (create_debug_logger, get_logging_statistics,
                                   list_loggers, set_logger_level)

    # 創建調試日誌器
    debug_logger = create_debug_logger("debug_example", file_output=False)
    debug_logger.debug("這是調試信息")

    # 顯示統計信息
    stats = get_logging_statistics()
    print(f"目前有 {stats['total_loggers']} 個日誌器")

    # 列出所有日誌器
    loggers = list_loggers()
    print(f"日誌器列表: {loggers}")

    # 設置日誌器級別
    success = set_logger_level("debug_example", logging.WARNING)
    print(f"設置級別: {'成功' if success else '失敗'}")

    print()


def example_backward_compatibility():
    """向後兼容性示例"""
    print("=== 向後兼容性示例 ===")

    from log_utils import (create_tool_logger, setup_basic_logger,
                           setup_simple_logger)

    # 舊的API仍然可用
    basic_logger = setup_basic_logger("old_basic", file_output=False)
    basic_logger.info("舊的基本日誌器")

    tool_logger = create_tool_logger("old_tool", file_output=False)
    tool_logger.info("舊的工具日誌器")

    simple_logger = setup_simple_logger("old_simple", file_output=False)
    simple_logger.info("舊的簡單日誌器")

    print()


def example_error_handling():
    """錯誤處理示例"""
    print("=== 錯誤處理示例 ===")

    from log_utils.core.config import LogConfig
    from log_utils.core.manager import LogManager

    # 測試配置驗證
    try:
        invalid_config = LogConfig(
            name="",  # 無效名稱
            level=999,  # 無效級別
            console_output=False,
            file_output=False,  # 沒有輸出方式
        )
        manager = LogManager(invalid_config)
    except ValueError as e:
        print(f"配置驗證成功捕獲錯誤: {e}")

    print()


def main():
    """主函數"""
    print("🚀 Log Utils v2.0 使用示例 (重構版本)")
    print("展示統一 API 和新功能")
    print("=" * 60)

    examples = [
        # 新功能優先展示
        example_new_unified_api,       # 新統一 API (推薦)
        example_new_factory_system,    # 新工廠系統
        example_old_api_compatibility, # 向後相容性
        example_config_usage,          # 進階配置
        example_formatters,            # 格式器
        example_presets,               # 預設配置
        example_advanced_features,     # 高級功能
        example_backward_compatibility,# 完整向後相容
        example_error_handling,        # 錯誤處理
    ]

    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ 示例執行失敗: {e}")
            import traceback
            traceback.print_exc()

    print("=" * 60)
    print("✅ 所有示例執行完成")
    print("🎯 重構成果：統一API、配置預設、批量操作、100%向後相容")


if __name__ == "__main__":
    main()
