"""
Log Utils 模組 v2.1 - 企業級模組化日誌系統

本 `__init__.py` 檔案是 `log_utils` 模組的總入口，負責定義其公開 API，
並以一種清晰、模組化的方式組織其所有功能。

## 設計哲學

- **易用性優先**: 透過 `factory.py` 提供高階便利函式，讓開發者能用最少的程式碼快速設定功能完備的日誌器。
- **深度可自訂**: 對於進階使用者，可以透過 `core.config` 和 `core.manager` 來深度自訂日誌系統的每一個環節。
- **模組化與可擴展**: 將不同功能（設定、管理、格式化、處理）分離到獨立的模組中，使得系統易於維護且便於未來擴展新功能。
- **穩健性**: 透過優雅的降級處理，即使在缺少某些選用相依性的環境中，也能保證核心功能可用。

## 建議使用模式

```python
# 1. 從 factory 匯入高階函式
from .factory import create_logger_from_preset, get_logger
import logging

# 2. 使用預設組態快速建立日誌器
#    這是最推薦的方式，能滿足 90% 的需求。
dev_logger = create_logger_from_preset("my_module", preset="development")
dev_logger.info("開發模式啟動...")

# 3. 在專案的任何地方，透過名稱獲取已設定好的日誌器
#    這確保了在整個應用程式中使用的是同一個 logger 實例。
module_logger = get_logger("my_module")
module_logger.debug("這是一條來自同一個 logger 的偵錯訊息。")

# 4. 直接使用從 logging 重新匯出的級別常數
dev_logger.log(logging.WARNING, "這是一條警告訊息。")
```
"""

# --- 套件元資料 ---
__package__ = "log_utils"
__version__ = "2.1.0"
__author__ = "AI 部門 - 全景處理團隊"
__docformat__ = "restructuredtext"

# --- 標準庫匯入與重新匯出 ---
# 為了方便使用者，我們直接從這裡重新匯出 logging 的核心元件。
# 使用者因此可以 `from log_utils import DEBUG` 而非 `from logging import DEBUG`。
import logging
import sys

getLogger = logging.getLogger
basicConfig = logging.basicConfig
DEBUG = logging.DEBUG
INFO = logging.INFO
WARNING = logging.WARNING
ERROR = logging.ERROR
CRITICAL = logging.CRITICAL

# --- 相依性檢查 ---
# 優雅地處理 `logging.handlers` 這個在某些精簡 Python 環境中可能不存在的模組。
try:
    import logging.handlers
    HAS_HANDLERS = True
except ImportError:
    HAS_HANDLERS = False
    import warnings
    warnings.warn(
        "標準庫 `logging.handlers` 不可用，檔案輪轉 (file rotation) 等進階日誌處理功能將受限。",
        ImportWarning
    )

# --- 模組化 API 匯出 ---
# 這是模組的核心，定義了 `from log_utils import *` 時會匯出的公開介面。
# 使用 try-except 結構確保即使在匯入失敗時，程式也能以最基本的功能降級執行。
try:
    # 從核心模組匯入
    from .core.config import LogConfig, create_config, LogConfigPresets
    from .core.manager import LogManager

    # 從統一錯誤處理系統匯入
    from .exceptions import (
        LogUtilsError,
        ConfigurationError,
        HandlerCreationError,
        FormatterError,
        ImportDependencyError,
        LogManagerError,
        ValidationError
    )
    from .error_handler import ErrorHandler, RecoveryStrategy
    from .messages import (
        ErrorMessages,
        InfoMessages,
        WarningMessages,
        MessageFormatter,
        MessageCodes
    )

    # 從統一工廠模組匯入新的簡化 API
    from .logger_factory import (
        create_logger,
        get_or_create_logger,
        console_logger,
        file_logger,
        debug_logger,
        production_logger,
        simple_logger,
        tool_logger,
        configure_global_logging,
        list_available_presets,
        get_factory_statistics,
        ConfigPresets,
        LoggerFactory,
    )
    
    # 從主工廠模組匯入最常用的高階 API
    from .factory import (
        setup_logger,
        setup_basic_logger,
        setup_simple_logger,
        create_tool_logger,
        create_logger_from_preset,
        get_logger,
        get_global_log_manager,
        set_logger_level,
        list_loggers,
        get_logging_statistics,
        shutdown_logging,
        cleanup_old_logs,
    )

    # 從格式器模組匯入
    from .formatters import (
        ColoredFormatter,
        JSONFormatter,
        CompactFormatter,
        DetailedFormatter,
        create_formatter,
    )

    # 從處理器模組匯入
    from .handlers import (
        create_console_handler,
        create_file_handler,
        create_handler,
        create_handler_from_preset,
    )

    # `__all__` 列表是 Python 的一個慣例，用來定義模組的「公開 API」。
    # 它明確地列出了可以被外部使用的名稱，有助於靜態分析和程式碼的可讀性。
    __all__ = [
        # -- logging 標準庫重新匯出 --
        "getLogger", "basicConfig", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL",
        
        # -- 核心元件 --
        "LogManager", "LogConfig", "create_config", "LogConfigPresets",
        
        # -- 統一錯誤處理系統 --
        "LogUtilsError", "ConfigurationError", "HandlerCreationError", "FormatterError",
        "ImportDependencyError", "LogManagerError", "ValidationError",
        "ErrorHandler", "RecoveryStrategy",
        "ErrorMessages", "InfoMessages", "WarningMessages", "MessageFormatter", "MessageCodes",
        
        # -- 新的統一工廠 API (建議使用) --
        "create_logger", "get_or_create_logger", "console_logger", "file_logger", 
        "debug_logger", "production_logger", "simple_logger", "tool_logger",
        "configure_global_logging", "list_available_presets", "get_factory_statistics",
        "ConfigPresets", "LoggerFactory",
        
        # -- 高階工廠函式 (向後相容) --
        "setup_logger", "get_logger", "create_logger_from_preset",
        
        # -- 向後相容的工廠函式 --
        "setup_basic_logger", "setup_simple_logger", "create_tool_logger",
        
        # -- 管理函式 --
        "get_global_log_manager", "set_logger_level", "list_loggers",
        "get_logging_statistics", "shutdown_logging", "cleanup_old_logs",

        # -- 格式器相關 --
        "ColoredFormatter", "JSONFormatter", "CompactFormatter", "DetailedFormatter", "create_formatter",
        
        # -- 處理器相關 --
        "create_console_handler", "create_file_handler", "create_handler", "create_handler_from_preset",
        
        # -- 狀態旗標 --
        "HAS_HANDLERS",
    ]

except ImportError as e:
    # --- 穩健的降級處理 ---
    # 如果上述任何一個匯入失敗，發出警告並只匯出最基本的功能。
    import warnings
    warnings.warn(f"部分 `log_utils` 功能因匯入錯誤而無法使用: {e}", ImportWarning)

    # 在降級模式下，只提供最核心、無相依性的 logging 元件
    __all__ = [
        "getLogger", "basicConfig", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL", "HAS_HANDLERS",
    ]


# --- 模組輔助函式 ---

def get_module_info() -> dict[str, str]:
    """
    取得關於本模組的詳細架構資訊。

    :return: 包含模組元資料的字典。
    """
    return {
        "version": __version__,
        "author": __author__,
        "architecture": "模組化 (核心、工廠、格式器、處理器)",
        "design_patterns": "工廠模式 (Factory), 單例模式 (Singleton)",
        "has_handlers_support": str(HAS_HANDLERS),
        "key_features": "可設定的預設組、多樣化的格式器、可插拔的處理器、全域管理",
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}",
    }


def check_compatibility() -> bool:
    """
    執行一個簡單的相容性檢查。

    :return: 如果基本相依性滿足，則回傳 True。
    """
    try:
        import logging, os, threading
        return True
    except ImportError as e:
        warnings.warn(f"相容性檢查失敗: {e}", ImportWarning)
        return False

# 在模組載入時執行一次相容性檢查
_compatibility_ok = check_compatibility()


def main():
    """
    測試整個 log_utils 模組的功能
    """
    print("=" * 60)
    print("測試 log_utils 模組 - 企業級模組化日誌系統")
    print("=" * 60)

    # 測試模組基本信息
    print(f"\n1. 模組基本信息:")
    print(f"   套件名稱: {__package__}")
    print(f"   版本: {__version__}")
    print(f"   作者: {__author__}")
    print(f"   相容性檢查: {'通過' if _compatibility_ok else '失敗'}")
    print(f"   handlers 支援: {HAS_HANDLERS}")

    # 測試模組信息函數
    print(f"\n2. 測試模組信息函數:")
    try:
        module_info = get_module_info()
        print(f"   模組信息獲取成功:")
        for key, value in module_info.items():
            print(f"     {key}: {value}")
    except Exception as e:
        print(f"   模組信息測試失敗: {e}")

    # 測試可用的公開 API
    print(f"\n3. 可用的公開 API:")
    try:
        print(f"   總共 {len(__all__)} 個公開項目")
        print(f"   前10個項目: {__all__[:10]}")

        # 檢查關鍵 API 是否可用
        key_apis = ["get_logger", "setup_logger", "create_logger_from_preset"]
        for api in key_apis:
            if api in globals():
                print(f"   {api}: 可用 ✓")
            else:
                print(f"   {api}: 不可用 ✗")
    except Exception as e:
        print(f"   API 檢查失敗: {e}")

    # 測試基本日誌功能
    print(f"\n4. 測試基本日誌功能:")
    try:
        # 使用重新匯出的 logging 功能
        basic_logger = getLogger("test_basic")
        print(f"   基本日誌器創建成功: {basic_logger.name}")

        # 測試日誌級別常數
        levels = [DEBUG, INFO, WARNING, ERROR, CRITICAL]
        level_names = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        print(f"   日誌級別常數: {dict(zip(level_names, levels))}")
    except Exception as e:
        print(f"   基本日誌功能測試失敗: {e}")

    # 測試高階 API
    print(f"\n5. 測試高階 API:")
    try:
        # 測試 get_logger (實際調用，但有保護)
        if 'get_logger' in globals():
            try:
                import signal
                import time

                def timeout_handler(signum, frame):
                    raise TimeoutError("函數調用超時")

                # 設置超時保護 (僅在非 Windows 系統)
                if hasattr(signal, 'SIGALRM'):
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(5)  # 5秒超時

                logger = get_logger("test_advanced")

                if hasattr(signal, 'SIGALRM'):
                    signal.alarm(0)  # 取消超時

                print(f"   get_logger: 成功 ({logger.name})")
            except TimeoutError:
                print(f"   get_logger: 超時 (可能有死鎖)")
            except Exception as e:
                print(f"   get_logger: 測試失敗 - {e}")

        # 測試預設組態日誌器
        if 'create_logger_from_preset' in globals():
            try:
                dev_logger = create_logger_from_preset("test_preset", preset="development")
                print(f"   create_logger_from_preset: 成功 ({dev_logger.name})")
            except Exception as e:
                print(f"   create_logger_from_preset: 測試失敗 - {e}")

        # 測試日誌器列表
        if 'list_loggers' in globals():
            try:
                loggers = list_loggers()
                print(f"   list_loggers: 成功 (找到 {len(loggers)} 個日誌器)")
            except Exception as e:
                print(f"   list_loggers: 測試失敗 - {e}")

    except Exception as e:
        print(f"   高階 API 測試失敗: {e}")

    # 測試格式器
    print(f"\n6. 測試格式器:")
    try:
        formatters_available = []
        if 'ColoredFormatter' in globals():
            formatters_available.append("ColoredFormatter")
        if 'JSONFormatter' in globals():
            formatters_available.append("JSONFormatter")
        if 'create_formatter' in globals():
            formatters_available.append("create_formatter")

        print(f"   可用格式器: {formatters_available}")
        print(f"   格式器檢查完成 (跳過實際創建以避免問題)")
    except Exception as e:
        print(f"   格式器測試失敗: {e}")

    # 測試處理器
    print(f"\n7. 測試處理器:")
    try:
        handlers_available = []
        if 'create_console_handler' in globals():
            handlers_available.append("create_console_handler")
        if 'create_file_handler' in globals():
            handlers_available.append("create_file_handler")

        print(f"   可用處理器: {handlers_available}")
        print(f"   處理器檢查完成 (跳過實際創建以避免問題)")
    except Exception as e:
        print(f"   處理器測試失敗: {e}")

    # 測試實際日誌輸出
    print(f"\n8. 測試實際日誌輸出:")
    try:
        # 使用基本的 logging 功能進行簡單測試
        basic_logger = getLogger("log_utils_basic_test")
        basic_logger.info("這是來自 log_utils 模組的基本測試信息")
        print("   基本日誌輸出測試完成")
    except Exception as e:
        print(f"   實際日誌輸出測試失敗: {e}")

    # 測試管理功能
    print(f"\n9. 測試管理功能:")
    try:
        management_functions = []
        if 'get_logging_statistics' in globals():
            management_functions.append("get_logging_statistics")
        if 'get_global_log_manager' in globals():
            management_functions.append("get_global_log_manager")

        print(f"   可用管理功能: {management_functions}")
        print(f"   管理功能檢查完成 (跳過實際調用以避免問題)")
    except Exception as e:
        print(f"   管理功能測試失敗: {e}")

    print(f"\n✅ log_utils 模組整體測試完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
