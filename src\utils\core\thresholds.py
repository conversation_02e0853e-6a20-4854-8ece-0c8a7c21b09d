"""
統一閾值配置模組
整合所有系統閾值設定，消除重複定義
"""
from dataclasses import dataclass


@dataclass
class SystemThresholds:
    """統一系統閾值配置"""
    # CPU 閾值
    cpu_warning: float = 80.0
    cpu_critical: float = 90.0
    
    # 記憶體閾值
    memory_warning: float = 80.0
    memory_critical: float = 90.0
    
    # GPU 閾值
    gpu_warning: float = 85.0
    gpu_critical: float = 95.0
    
    # 延遲閾值 (毫秒)
    latency_warning_ms: float = 1000.0
    latency_critical_ms: float = 2000.0
    
    # 磁碟使用率閾值
    disk_warning: float = 85.0
    disk_critical: float = 95.0


# 預設實例
DEFAULT_THRESHOLDS = SystemThresholds()
