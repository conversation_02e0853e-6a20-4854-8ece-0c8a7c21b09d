#!/usr/bin/env python3
"""
場景處理器模組 (已重構)
Scene Processor Module (Refactored)

負責處理單個場景的核心邏輯，作為協調者調用其他服務。
Responsible for core logic of processing individual scenes, acting as a coordinator for other services.

Author: <PERSON> Assistant & Roo
Date: 2025-01-19
"""

import os
import sys
import time
from pathlib import Path
from typing import Any  # 保留 Any 因為它是特殊類型

# Import project modules directly (using pip install -e .)

# 導入依賴
try:
    from log_utils import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

# 延遲載入其他依賴
def _import_processing_deps():
    """延遲載入處理依賴"""
    try:
        from .panorama_processor import PanoramaProcessor
        from .pyramid_reporter import SceneReport
        return PanoramaProcessor, SceneReport
    except ImportError as e:
        import warnings
        warnings.warn(f"無法載入處理依賴: {e}", ImportWarning)
        return None, None

logger = get_logger("scene_processor")

class SceneProcessor:
    """
    場景處理器 - 重構為協調者模式。
    處理單個場景的核心邏輯，委派具體任務給注入的處理器。
    """

    def __init__(
        self,
        config,
        panorama_processor=None,
        progress_manager=None,
        reporter=None,
    ):
        """
        初始化場景處理器

        Args:
            config: 應用程序配置對象
            panorama_processor: 重構後的 PanoramaProcessor 實例 (可選，延遲載入)
            progress_manager: 進度管理器實例
            reporter: 報告生成器實例
        """
        self.config = config
        self._panorama_processor = panorama_processor
        self.progress_manager = progress_manager
        self.reporter = reporter
        self.logger = logger
        self._initialized = False
        self.stats = {
            "total_processed": 0,
            "total_failed": 0,
            "processing_times": [],
        }

    def _ensure_initialized(self):
        """確保處理器已完全初始化"""
        if not self._initialized:
            if self._panorama_processor is None:
                PanoramaProcessor, SceneReport = _import_processing_deps()
                if PanoramaProcessor is None:
                    raise ImportError("無法載入 PanoramaProcessor")
                self._panorama_processor = PanoramaProcessor(self.config)
            self._initialized = True

    @property
    def panorama_processor(self):
        """延遲載入的 panorama_processor 屬性"""
        self._ensure_initialized()
        return self._panorama_processor

    def process_single_scene(self, district: str, scene: str, scene_data: dict) -> bool:
        """
        處理單個場景的完整流程。
        根據場景數據，委派給 PanoramaProcessor 進行處理。
        """
        start_time = time.time()
        scene_key = f"{district}/{scene}"
        
        try:
            self.logger.info(f"開始處理場景: {scene_key}")
            if self.progress_manager:
                self.progress_manager.save_progress(district, scene, "開始處理")

            scene_report = SceneReport(district=district, scene=scene, status="processing")
            
            output_dir = Path(self.config.io.output_path) / district / scene
            output_dir.mkdir(parents=True, exist_ok=True)

            success = False
            blur_stats = {}

            if scene_data.get("panorama_files"):
                pano_file = scene_data["panorama_files"][0]
                success, blur_stats = self.panorama_processor.process_single_panorama(pano_file, str(output_dir))
                if success:
                    scene_report.panorama_count = 1
            elif scene_data.get("cube_structures"):
                cube_structure = scene_data["cube_structures"][0]
                success, blur_stats = self.panorama_processor.process_cube_structure(
                    cube_structure["source_dir"], str(output_dir)
                )
                if success:
                    scene_report.cube_count = 1
            else:
                self.logger.warning(f"場景 {scene_key} 沒有可處理的內容。")
                scene_report.error_message = "沒有可處理的內容"
                success = False

            processing_time = time.time() - start_time
            scene_report.processing_time = processing_time
            scene_report.status = "success" if success else "failed"
            if not success and not scene_report.error_message:
                scene_report.error_message = "處理失敗"

            if self.reporter:
                self.reporter.add_scene_report(scene_report)
            
            if self.progress_manager:
                self.progress_manager.save_progress(
                    district, scene, "完成" if success else "失敗", processing_time, scene_report.error_message
                )

            if success:
                self.stats["total_processed"] += 1
                self.logger.info(f"場景 {scene_key} 處理成功，耗時 {processing_time:.2f} 秒。")
            else:
                self.stats["total_failed"] += 1
                self.logger.error(f"場景 {scene_key} 處理失敗。原因: {scene_report.error_message}")
            
            self.stats["processing_times"].append(processing_time)
            return success

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"處理場景 {scene_key} 時發生未預期錯誤: {e}", exc_info=True)
            
            error_report = SceneReport(
                district=district, scene=scene, status="failed",
                processing_time=processing_time, error_message=str(e)
            )
            if self.reporter:
                self.reporter.add_scene_report(error_report)
            if self.progress_manager:
                self.progress_manager.save_progress(district, scene, "錯誤", processing_time, str(e))
                
            self.stats["total_failed"] += 1
            return False

def create_scene_processor(config, panorama_processor, progress_manager, reporter):
    """創建SceneProcessor實例的工廠函數"""
    return SceneProcessor(
        config=config,
        panorama_processor=panorama_processor,
        progress_manager=progress_manager,
        reporter=reporter
    )
