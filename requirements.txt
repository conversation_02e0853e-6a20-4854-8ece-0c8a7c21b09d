# 全景影像處理系統 v2.0 - 依賴清單
# Panoramic Image Processing System v2.0 - Dependencies
# 基於程式碼分析的實際依賴項 (140+ 檔案, 200k+ tokens)

# ===== 核心依賴 (Core Dependencies) =====
numpy>=1.21.0                    # 數值計算核心 (大量使用)
opencv-python>=4.6.0             # 影像處理 (import cv2)
torch>=1.12.0                    # 深度學習框架 (AI檢測)
torchvision>=0.13.0              # 電腦視覺工具
ultralytics>=8.0.0               # YOLO模型框架

# ===== 系統監控與優化 (System Monitoring & Optimization) =====
psutil>=5.9.0                    # 系統資源監控 (廣泛使用)
numba>=0.56.0                    # JIT編譯優化 (核心算法加速)
watchdog>=2.1.0                  # 檔案系統監控 (配置熱重載)

# ===== 可選依賴 (Optional Dependencies) =====
# GPU加速支援 (GPU Acceleration Support)
# 根據CUDA版本選擇適當的torch版本
# torch --index-url https://download.pytorch.org/whl/cu118
# cupy-cuda11x                   # GPU向量化（根據CUDA版本）

# 分散式計算 (Distributed Computing)
# ray>=2.0.0                     # 分散式計算框架
# dask>=2022.0.0                 # 平行計算

# ===== 開發依賴 (Development Dependencies) =====
# pytest>=6.0.0                 # 測試框架
# pytest-cov>=2.0.0             # 測試覆蓋率
# black>=21.0.0                 # 代碼格式化
# flake8>=3.8.0                 # 代碼檢查
# mypy>=0.800                   # 類型檢查

# ===== 其他工具 (Additional Tools) =====
# pillow>=8.0.0                 # 圖像處理庫
# matplotlib>=3.3.0             # 圖表繪製
# tqdm>=4.50.0                  # 進度條顯示

# ===== 平台特定 (Platform Specific) =====
# Windows額外依賴
# pywin32>=227                  # Windows API（僅Windows）

# Linux額外依賴  
# python3-dev                   # 開發頭文件（系統包管理器安裝）

# ===== 版本說明 (Version Notes) =====
# Python >= 3.8 必需
# 建議 Python 3.9+ 以獲得最佳性能
# GPU支援需要對應的CUDA Toolkit

# ===== 安裝說明 (Installation Instructions) =====
# 基本安裝:
# pip install -r requirements.txt

# GPU支援安裝:
# pip install torch --index-url https://download.pytorch.org/whl/cu118
# pip install cupy-cuda11x

# 開發環境安裝:
# pip install -r requirements.txt
# pip install pytest black flake8 mypy

# ===== 記憶體需求 (Memory Requirements) =====
# 最低: 8GB RAM
# 推薦: 16GB+ RAM
# GPU: 4GB+ VRAM（可選）