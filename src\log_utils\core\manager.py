"""
重構的日誌管理器模組

從原始的 logger.py 中提取 LogManager 類，專注於日誌管理邏輯。
處理器創建邏輯已移至 handlers.py 模組。
"""

import logging
import threading
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

try:
    from ..handlers import create_console_handler, create_file_handler
except ImportError:
    # 如果相對 import 失敗，嘗試絕對 import
    try:
        from handlers import create_console_handler, create_file_handler
    except ImportError:
        # 如果都失敗，創建簡化版本
        import warnings
        warnings.warn("無法載入 handlers 模組，使用簡化功能", ImportWarning)

        def create_console_handler(**kwargs):
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            return handler

        def create_file_handler(filename, **kwargs):
            handler = logging.FileHandler(filename)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            return handler

try:
    from .config import LogConfig
except ImportError:
    try:
        from config import LogConfig
    except ImportError:
        # 創建簡化的 LogConfig
        class LogConfig:
            def __init__(self, **kwargs):
                self.level = kwargs.get('level', logging.INFO)
                self.console_output = kwargs.get('console_output', True)
                self.file_output = kwargs.get('file_output', False)
                self.use_colors = kwargs.get('use_colors', True)
                self.simple_console = kwargs.get('simple_console', False)
                self.color_theme = kwargs.get('color_theme', 'default')
                for k, v in kwargs.items():
                    setattr(self, k, v)

            def validate(self):
                return []

            def get_console_format(self):
                return '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

            def get_file_format(self):
                return '%(asctime)s - %(name)s - %(levelname)s - %(message)s'


class LogManager:
    """
    重構的日誌管理器

    專注於日誌器的配置和生命週期管理，處理器創建邏輯已分離到 handlers.py。
    """

    def __init__(self, config: Optional[LogConfig] = None):
        """
        初始化日誌管理器

        :param config: 日誌配置對象，如果未提供則使用預設配置
        """
        self.config = config or LogConfig()
        self.loggers: Dict[str, logging.Logger] = {}
        self.lock = threading.RLock()  # 使用可重入鎖避免死鎖
        self.is_shutdown = False

        # 驗證配置
        config_errors = self.config.validate()
        if config_errors:
            raise ValueError(f"配置驗證失敗: {'; '.join(config_errors)}")

    def setup_logger(
        self, name: str, override_config: Optional[LogConfig] = None
    ) -> logging.Logger:
        """
        設置日誌器

        :param name: 日誌器名稱
        :param override_config: 覆蓋配置
        :return: 配置好的日誌器
        """
        with self.lock:
            if self.is_shutdown:
                raise RuntimeError("日誌管理器已關閉")

            # 如果logger已存在，直接返回
            if name in self.loggers:
                return self.loggers[name]

            # 使用覆蓋配置或預設配置
            config = override_config or self.config

            # 創建logger
            logger = logging.getLogger(name)
            logger.setLevel(config.level)

            # 清除現有處理器
            logger.handlers.clear()

            # 添加控制台處理器
            if config.console_output:
                console_handler = create_console_handler(
                    level=config.level,
                    use_colors=config.use_colors,
                    simple_format=config.simple_console,
                    format_string=config.get_console_format(),
                    theme=config.color_theme,
                )
                logger.addHandler(console_handler)

            # 添加檔案處理器
            if config.file_output:
                try:
                    file_handler = create_file_handler(
                        filepath=str(config.log_file_path),
                        level=config.level,
                        format_string=config.get_file_format(),
                        max_bytes=config.max_bytes,
                        backup_count=config.backup_count,
                    )
                    logger.addHandler(file_handler)
                except Exception as e:
                    # 如果檔案處理器創建失敗，記錄錯誤但不中斷程序
                    if config.console_output:
                        logger.error(f"檔案處理器創建失敗: {e}")
                    else:
                        # 如果沒有控制台輸出，添加基本控制台處理器避免日誌丟失
                        fallback_handler = create_console_handler(
                            level=logging.ERROR, use_colors=False, simple_format=True
                        )
                        logger.addHandler(fallback_handler)
                        logger.error(f"檔案處理器創建失敗，啟用緊急控制台輸出: {e}")

            # 添加額外處理器
            for handler_config in config.additional_handlers:
                try:
                    handler = self._create_additional_handler(handler_config)
                    if handler:
                        logger.addHandler(handler)
                except Exception as e:
                    logger.warning(f"額外處理器創建失敗: {e}")

            # 避免重複處理 (如果父logger也有處理器)
            logger.propagate = False

            # 緩存logger
            self.loggers[name] = logger

            return logger

    def _create_additional_handler(
        self, handler_config: Dict[str, Any]
    ) -> Optional[logging.Handler]:
        """
        創建額外的處理器

        :param handler_config: 處理器配置
        :return: 處理器實例或None
        """
        from ..handlers import create_handler

        handler_type = handler_config.get("type")
        if not handler_type:
            return None

        # 移除type參數，其餘參數傳遞給工廠函數
        config = handler_config.copy()
        config.pop("type")

        return create_handler(handler_type, **config)

    def get_logger(self, name: str) -> logging.Logger:
        """
        獲取已配置的日誌器，如果不存在則自動創建

        :param name: 日誌器名稱
        :return: 日誌器實例（絕不會是None）
        """
        with self.lock:
            logger = self.loggers.get(name)
            if logger is None:
                # 如果日誌器不存在，使用當前配置自動創建
                logger = self.setup_logger(name)
            return logger

    def list_loggers(self) -> List[str]:
        """
        列出所有已配置的日誌器名稱

        :return: 日誌器名稱列表
        """
        with self.lock:
            return list(self.loggers.keys())

    def remove_logger(self, name: str) -> bool:
        """
        移除日誌器

        :param name: 日誌器名稱
        :return: 是否成功移除
        """
        with self.lock:
            if name in self.loggers:
                logger = self.loggers[name]

                # 關閉所有處理器
                for handler in logger.handlers:
                    handler.close()

                # 清除處理器
                logger.handlers.clear()

                # 從緩存中移除
                del self.loggers[name]

                return True

            return False

    def cleanup_old_logs(self, days_to_keep: int = 30):
        """
        清理舊日誌檔案

        :param days_to_keep: 保留天數
        """
        try:
            log_dir = self.config.log_dir
            if not log_dir.exists():
                return

            cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)

            for log_file in log_dir.glob("*.log*"):
                try:
                    if log_file.stat().st_mtime < cutoff_time:
                        log_file.unlink()
                except (OSError, IOError):
                    # 忽略無法刪除的檔案
                    pass

        except Exception as e:
            # 日誌清理失敗不應該影響主程序
            if self.loggers:
                # 嘗試用任何可用的logger記錄錯誤
                for logger in self.loggers.values():
                    logger.warning(f"日誌清理失敗: {e}")
                    break

    def set_level(self, name: str, level: int) -> bool:
        """
        設置日誌器級別

        :param name: 日誌器名稱
        :param level: 新的日誌級別
        :return: 是否成功設置
        """
        with self.lock:
            if name in self.loggers:
                logger = self.loggers[name]
                logger.setLevel(level)

                # 同時更新所有處理器的級別
                for handler in logger.handlers:
                    handler.setLevel(level)

                return True

            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取日誌管理器統計信息

        :return: 統計信息字典
        """
        with self.lock:
            stats = {
                "total_loggers": len(self.loggers),
                "logger_names": list(self.loggers.keys()),
                "config_name": self.config.name,
                "config_level": self.config.level,
                "console_output": self.config.console_output,
                "file_output": self.config.file_output,
                "log_dir": str(self.config.log_dir),
                "is_shutdown": self.is_shutdown,
            }

            # 每個logger的處理器數量
            logger_handlers = {}
            for name, logger in self.loggers.items():
                logger_handlers[name] = len(logger.handlers)

            stats["logger_handlers"] = logger_handlers

            return stats

    def reload_config(self, new_config: LogConfig):
        """
        重新載入配置

        :param new_config: 新的配置
        """
        with self.lock:
            # 驗證新配置
            config_errors = new_config.validate()
            if config_errors:
                raise ValueError(f"新配置驗證失敗: {'; '.join(config_errors)}")

            # 更新配置
            old_config = self.config
            self.config = new_config

            # 重新配置所有現有logger
            for name, logger in self.loggers.items():
                try:
                    # 清除現有處理器
                    for handler in logger.handlers:
                        handler.close()
                    logger.handlers.clear()

                    # 使用新配置重新設置logger
                    logger.setLevel(new_config.level)

                    # 重新添加處理器
                    if new_config.console_output:
                        console_handler = create_console_handler(
                            level=new_config.level,
                            use_colors=new_config.use_colors,
                            simple_format=new_config.simple_console,
                            format_string=new_config.get_console_format(),
                            theme=new_config.color_theme,
                        )
                        logger.addHandler(console_handler)

                    if new_config.file_output:
                        try:
                            file_handler = create_file_handler(
                                filepath=str(new_config.log_file_path),
                                level=new_config.level,
                                format_string=new_config.get_file_format(),
                                max_bytes=new_config.max_bytes,
                                backup_count=new_config.backup_count,
                            )
                            logger.addHandler(file_handler)
                        except Exception as e:
                            logger.error(f"檔案處理器重新創建失敗: {e}")

                except Exception as e:
                    # 如果某個logger重新配置失敗，恢復舊配置
                    self.config = old_config
                    raise RuntimeError(f"重新載入配置失敗: {e}")

    def shutdown(self):
        """
        關閉日誌管理器
        """
        with self.lock:
            if self.is_shutdown:
                return

            self.is_shutdown = True

            # 關閉所有logger的處理器
            for logger in self.loggers.values():
                for handler in logger.handlers:
                    try:
                        handler.close()
                    except:
                        pass
                logger.handlers.clear()

            # 清空logger緩存
            self.loggers.clear()

    def __enter__(self):
        """上下文管理器進入"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.shutdown()

    def __del__(self):
        """析構函數"""
        try:
            self.shutdown()
        except:
            pass
