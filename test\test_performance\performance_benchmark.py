"""
Performance benchmark framework for validating performance improvements.
Validates the specific performance targets from the requirements:
- Processing speed improvement: 25%+ reduction in end-to-end processing time
- GPU utilization: 80%+ average (up from ~60%)
- Memory usage: 20%+ reduction in peak memory
- Parallel processing: 4x+ parallel task support
- Batch processing: 30%+ reduction in processing time for 100 images
"""
import pytest
import time
import psutil
import threading
import numpy as np
from dataclasses import dataclass
from typing import Dict, List, Any, Callable, Optional
from pathlib import Path
import json
import tempfile
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from unittest.mock import Mock, patch

# Refactored modules
from utils.factory import UtilsFactory
from utils.config import UtilsConfig
from processing.factory import ProcessingFactory
from processing.config import ProcessingConfig, CubeGenerationConfig, DetectionConfig, BlurConfig
from processing.pipeline import ProcessingPipeline

# Legacy modules for comparison
from config.settings import get_config


@dataclass
class PerformanceBenchmark:
    """Data class for storing performance benchmark results."""
    test_name: str
    processing_time: float
    memory_usage_mb: float
    gpu_utilization: float
    throughput_images_per_sec: float
    cpu_utilization: float
    baseline_comparison: Optional[float] = None
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert benchmark to dictionary for serialization."""
        return {
            "test_name": self.test_name,
            "processing_time": self.processing_time,
            "memory_usage_mb": self.memory_usage_mb,
            "gpu_utilization": self.gpu_utilization,
            "throughput_images_per_sec": self.throughput_images_per_sec,
            "cpu_utilization": self.cpu_utilization,
            "baseline_comparison": self.baseline_comparison,
            "metadata": self.metadata or {}
        }


class PerformanceValidator:
    """Validates performance improvements against target benchmarks."""
    
    # Performance targets from requirements
    PROCESSING_TIME_IMPROVEMENT_TARGET = 0.25  # 25% improvement
    GPU_UTILIZATION_TARGET = 0.80  # 80% average utilization
    MEMORY_REDUCTION_TARGET = 0.20  # 20% reduction
    PARALLEL_PROCESSING_MULTIPLIER = 4.0  # 4x parallel tasks
    BATCH_PROCESSING_IMPROVEMENT = 0.30  # 30% improvement for 100 images
    
    def __init__(self, baseline_file: Optional[str] = None):
        """Initialize validator with optional baseline data."""
        self.baseline_data: Dict[str, PerformanceBenchmark] = {}
        if baseline_file and Path(baseline_file).exists():
            self.load_baseline(baseline_file)
    
    def load_baseline(self, baseline_file: str):
        """Load baseline performance data from file."""
        with open(baseline_file, 'r') as f:
            data = json.load(f)
            for test_name, benchmark_data in data.items():
                self.baseline_data[test_name] = PerformanceBenchmark(**benchmark_data)
    
    def save_baseline(self, baseline_file: str, benchmarks: Dict[str, PerformanceBenchmark]):
        """Save baseline performance data to file."""
        data = {name: benchmark.to_dict() for name, benchmark in benchmarks.items()}
        with open(baseline_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def validate_processing_time(self, current: float, baseline: float) -> bool:
        """Validate processing time improvement target."""
        improvement = (baseline - current) / baseline
        return improvement >= self.PROCESSING_TIME_IMPROVEMENT_TARGET
    
    def validate_gpu_utilization(self, utilization: float) -> bool:
        """Validate GPU utilization target."""
        return utilization >= self.GPU_UTILIZATION_TARGET
    
    def validate_memory_reduction(self, current: float, baseline: float) -> bool:
        """Validate memory usage reduction target."""
        reduction = (baseline - current) / baseline
        return reduction >= self.MEMORY_REDUCTION_TARGET
    
    def validate_parallel_processing(self, current_capacity: int, baseline_capacity: int) -> bool:
        """Validate parallel processing capacity improvement."""
        multiplier = current_capacity / baseline_capacity
        return multiplier >= self.PARALLEL_PROCESSING_MULTIPLIER
    
    def validate_batch_processing(self, current_time: float, baseline_time: float) -> bool:
        """Validate batch processing improvement."""
        improvement = (baseline_time - current_time) / baseline_time
        return improvement >= self.BATCH_PROCESSING_IMPROVEMENT


class SystemMonitor:
    """System resource monitoring for performance tests."""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.metrics: List[Dict[str, float]] = []
    
    def start_monitoring(self, interval: float = 0.1):
        """Start system monitoring."""
        self.monitoring = True
        self.metrics = []
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.start()
    
    def stop_monitoring(self) -> Dict[str, float]:
        """Stop monitoring and return aggregated metrics."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        
        if not self.metrics:
            return {"cpu": 0.0, "memory_mb": 0.0, "gpu_utilization": 0.0}
        
        # Calculate averages
        avg_cpu = sum(m["cpu"] for m in self.metrics) / len(self.metrics)
        avg_memory = sum(m["memory_mb"] for m in self.metrics) / len(self.metrics)
        peak_memory = max(m["memory_mb"] for m in self.metrics)
        avg_gpu = sum(m["gpu_utilization"] for m in self.metrics) / len(self.metrics)
        
        return {
            "cpu": avg_cpu,
            "memory_mb": avg_memory,
            "peak_memory_mb": peak_memory,
            "gpu_utilization": avg_gpu
        }
    
    def _monitor_loop(self, interval: float):
        """Monitor system resources in a loop."""
        while self.monitoring:
            try:
                # CPU and memory monitoring
                cpu_percent = psutil.cpu_percent(interval=None)
                memory_info = psutil.virtual_memory()
                memory_mb = memory_info.used / (1024 * 1024)
                
                # Mock GPU monitoring (would integrate with nvidia-ml-py in real scenario)
                gpu_utilization = self._get_gpu_utilization()
                
                self.metrics.append({
                    "cpu": cpu_percent,
                    "memory_mb": memory_mb,
                    "gpu_utilization": gpu_utilization
                })
                
                time.sleep(interval)
            except Exception:
                # Handle monitoring errors gracefully
                pass
    
    def _get_gpu_utilization(self) -> float:
        """Get GPU utilization. Mock implementation for testing."""
        try:
            import torch
            if torch.cuda.is_available():
                # This is a simplified mock - real implementation would use nvidia-ml-py
                return 75.0  # Mock value
            return 0.0
        except ImportError:
            return 0.0


class PerformanceTester:
    """Main performance testing class."""
    
    def __init__(self, validator: PerformanceValidator = None):
        self.validator = validator or PerformanceValidator()
        self.monitor = SystemMonitor()
        self.results: Dict[str, PerformanceBenchmark] = {}
    
    def create_test_data(self, num_images: int = 1, image_size: tuple = (1024, 2048)) -> List[Dict[str, Any]]:
        """Create test data for performance testing."""
        test_data = []
        for i in range(num_images):
            image = np.random.randint(0, 255, (*image_size, 3), dtype=np.uint8)
            test_data.append({
                "image": image,
                "image_path": f"/test/panorama_{i}.jpg",
                "metadata": {"test_id": i}
            })
        return test_data
    
    def benchmark_processing_speed(self, factory: ProcessingFactory, test_data: List[Dict]) -> PerformanceBenchmark:
        """Benchmark end-to-end processing speed."""
        pipeline = factory.create_pipeline()
        
        self.monitor.start_monitoring()
        start_time = time.perf_counter()
        
        results = []
        for data in test_data:
            result = pipeline.run(data)
            results.append(result)
        
        end_time = time.perf_counter()
        system_metrics = self.monitor.stop_monitoring()
        
        processing_time = end_time - start_time
        throughput = len(test_data) / processing_time
        
        benchmark = PerformanceBenchmark(
            test_name="processing_speed",
            processing_time=processing_time,
            memory_usage_mb=system_metrics["peak_memory_mb"],
            gpu_utilization=system_metrics["gpu_utilization"],
            throughput_images_per_sec=throughput,
            cpu_utilization=system_metrics["cpu"],
            metadata={
                "num_images": len(test_data),
                "image_size": test_data[0]["image"].shape if test_data else None,
                "success_rate": sum(1 for r in results if all(step.success for step in r)) / len(results)
            }
        )
        
        return benchmark
    
    def benchmark_memory_usage(self, factory: ProcessingFactory, test_data: List[Dict]) -> PerformanceBenchmark:
        """Benchmark memory usage patterns."""
        pipeline = factory.create_pipeline()
        
        # Measure baseline memory
        baseline_memory = psutil.virtual_memory().used / (1024 * 1024)
        
        self.monitor.start_monitoring()
        start_time = time.perf_counter()
        
        # Process data
        for data in test_data:
            pipeline.run(data)
        
        end_time = time.perf_counter()
        system_metrics = self.monitor.stop_monitoring()
        
        peak_memory = system_metrics["peak_memory_mb"]
        memory_delta = peak_memory - baseline_memory
        
        benchmark = PerformanceBenchmark(
            test_name="memory_usage",
            processing_time=end_time - start_time,
            memory_usage_mb=memory_delta,
            gpu_utilization=system_metrics["gpu_utilization"],
            throughput_images_per_sec=len(test_data) / (end_time - start_time),
            cpu_utilization=system_metrics["cpu"],
            metadata={
                "baseline_memory_mb": baseline_memory,
                "peak_memory_mb": peak_memory,
                "num_images": len(test_data)
            }
        )
        
        return benchmark
    
    def benchmark_parallel_processing(self, factory_creator: Callable, test_data: List[Dict], 
                                    max_workers: int = 8) -> PerformanceBenchmark:
        """Benchmark parallel processing capabilities."""
        
        def process_single_item(data):
            """Process a single item in parallel."""
            factory = factory_creator()
            pipeline = factory.create_pipeline()
            return pipeline.run(data)
        
        # Test different worker counts
        worker_counts = [1, 2, 4, max_workers]
        performance_data = {}
        
        for workers in worker_counts:
            self.monitor.start_monitoring()
            start_time = time.perf_counter()
            
            with ThreadPoolExecutor(max_workers=workers) as executor:
                results = list(executor.map(process_single_item, test_data))
            
            end_time = time.perf_counter()
            system_metrics = self.monitor.stop_monitoring()
            
            performance_data[workers] = {
                "time": end_time - start_time,
                "throughput": len(test_data) / (end_time - start_time),
                "cpu": system_metrics["cpu"],
                "memory": system_metrics["peak_memory_mb"]
            }
        
        # Calculate parallel efficiency
        single_thread_time = performance_data[1]["time"]
        max_worker_time = performance_data[max_workers]["time"]
        parallel_efficiency = single_thread_time / (max_worker_time * max_workers)
        
        benchmark = PerformanceBenchmark(
            test_name="parallel_processing",
            processing_time=max_worker_time,
            memory_usage_mb=performance_data[max_workers]["memory"],
            gpu_utilization=0.0,  # GPU utilization complex in parallel scenario
            throughput_images_per_sec=performance_data[max_workers]["throughput"],
            cpu_utilization=performance_data[max_workers]["cpu"],
            metadata={
                "worker_performance": performance_data,
                "parallel_efficiency": parallel_efficiency,
                "speedup_factor": single_thread_time / max_worker_time
            }
        )
        
        return benchmark
    
    def benchmark_batch_processing(self, factory: ProcessingFactory, num_images: int = 100) -> PerformanceBenchmark:
        """Benchmark batch processing of 100 images (requirement target)."""
        test_data = self.create_test_data(num_images, (512, 1024))  # Smaller for batch testing
        
        pipeline = factory.create_pipeline()
        
        self.monitor.start_monitoring()
        start_time = time.perf_counter()
        
        # Process all images
        batch_results = []
        for data in test_data:
            result = pipeline.run(data)
            batch_results.append(result)
        
        end_time = time.perf_counter()
        system_metrics = self.monitor.stop_monitoring()
        
        total_time = end_time - start_time
        throughput = num_images / total_time
        
        benchmark = PerformanceBenchmark(
            test_name="batch_processing_100",
            processing_time=total_time,
            memory_usage_mb=system_metrics["peak_memory_mb"],
            gpu_utilization=system_metrics["gpu_utilization"],
            throughput_images_per_sec=throughput,
            cpu_utilization=system_metrics["cpu"],
            metadata={
                "num_images": num_images,
                "avg_time_per_image": total_time / num_images,
                "success_rate": sum(1 for r in batch_results if all(step.success for step in r)) / len(batch_results)
            }
        )
        
        return benchmark
    
    def run_gpu_utilization_test(self, factory: ProcessingFactory, test_data: List[Dict]) -> PerformanceBenchmark:
        """Test GPU utilization patterns."""
        # Enable GPU in configuration
        gpu_config = UtilsConfig(enable_gpu=True)
        gpu_factory = UtilsFactory(gpu_config)
        
        processing_config = ProcessingConfig(
            detection=DetectionConfig(enable_gpu_acceleration=True),
            cube_generation=CubeGenerationConfig(cube_size=1024)  # Larger for GPU test
        )
        gpu_processing_factory = ProcessingFactory(processing_config, gpu_factory)
        pipeline = gpu_processing_factory.create_pipeline()
        
        self.monitor.start_monitoring()
        start_time = time.perf_counter()
        
        for data in test_data:
            pipeline.run(data)
        
        end_time = time.perf_counter()
        system_metrics = self.monitor.stop_monitoring()
        
        benchmark = PerformanceBenchmark(
            test_name="gpu_utilization",
            processing_time=end_time - start_time,
            memory_usage_mb=system_metrics["peak_memory_mb"],
            gpu_utilization=system_metrics["gpu_utilization"],
            throughput_images_per_sec=len(test_data) / (end_time - start_time),
            cpu_utilization=system_metrics["cpu"],
            metadata={
                "gpu_enabled": True,
                "num_images": len(test_data)
            }
        )
        
        return benchmark


class PerformanceReportGenerator:
    """Generate performance analysis reports."""
    
    def __init__(self, validator: PerformanceValidator):
        self.validator = validator
    
    def generate_report(self, benchmarks: Dict[str, PerformanceBenchmark], 
                       output_file: Optional[str] = None) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        report = {
            "timestamp": time.time(),
            "benchmarks": {},
            "validations": {},
            "summary": {},
            "recommendations": []
        }
        
        # Process each benchmark
        for name, benchmark in benchmarks.items():
            report["benchmarks"][name] = benchmark.to_dict()
            
            # Validate against targets
            validation_results = self._validate_benchmark(benchmark)
            report["validations"][name] = validation_results
        
        # Generate summary
        report["summary"] = self._generate_summary(benchmarks)
        
        # Generate recommendations
        report["recommendations"] = self._generate_recommendations(benchmarks)
        
        # Save to file if specified
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2)
        
        return report
    
    def _validate_benchmark(self, benchmark: PerformanceBenchmark) -> Dict[str, bool]:
        """Validate a benchmark against performance targets."""
        validations = {}
        
        if benchmark.test_name == "processing_speed" and benchmark.baseline_comparison:
            validations["processing_time_improvement"] = self.validator.validate_processing_time(
                benchmark.processing_time, benchmark.baseline_comparison
            )
        
        if benchmark.test_name == "gpu_utilization":
            validations["gpu_utilization_target"] = self.validator.validate_gpu_utilization(
                benchmark.gpu_utilization
            )
        
        if benchmark.test_name == "memory_usage" and benchmark.baseline_comparison:
            validations["memory_reduction_target"] = self.validator.validate_memory_reduction(
                benchmark.memory_usage_mb, benchmark.baseline_comparison
            )
        
        if benchmark.test_name == "batch_processing_100" and benchmark.baseline_comparison:
            validations["batch_processing_improvement"] = self.validator.validate_batch_processing(
                benchmark.processing_time, benchmark.baseline_comparison
            )
        
        return validations
    
    def _generate_summary(self, benchmarks: Dict[str, PerformanceBenchmark]) -> Dict[str, Any]:
        """Generate performance summary statistics."""
        summary = {
            "total_tests": len(benchmarks),
            "avg_processing_time": 0.0,
            "avg_throughput": 0.0,
            "avg_memory_usage": 0.0,
            "avg_gpu_utilization": 0.0
        }
        
        if benchmarks:
            summary["avg_processing_time"] = sum(b.processing_time for b in benchmarks.values()) / len(benchmarks)
            summary["avg_throughput"] = sum(b.throughput_images_per_sec for b in benchmarks.values()) / len(benchmarks)
            summary["avg_memory_usage"] = sum(b.memory_usage_mb for b in benchmarks.values()) / len(benchmarks)
            summary["avg_gpu_utilization"] = sum(b.gpu_utilization for b in benchmarks.values()) / len(benchmarks)
        
        return summary
    
    def _generate_recommendations(self, benchmarks: Dict[str, PerformanceBenchmark]) -> List[str]:
        """Generate performance optimization recommendations."""
        recommendations = []
        
        # Check GPU utilization
        gpu_benchmarks = [b for b in benchmarks.values() if b.test_name == "gpu_utilization"]
        if gpu_benchmarks and gpu_benchmarks[0].gpu_utilization < 80.0:
            recommendations.append(
                f"GPU utilization is {gpu_benchmarks[0].gpu_utilization:.1f}%, "
                f"below the 80% target. Consider optimizing GPU workload distribution."
            )
        
        # Check memory usage
        memory_benchmarks = [b for b in benchmarks.values() if b.test_name == "memory_usage"]
        if memory_benchmarks and memory_benchmarks[0].memory_usage_mb > 4000:  # 4GB threshold
            recommendations.append(
                f"Peak memory usage is {memory_benchmarks[0].memory_usage_mb:.1f}MB. "
                f"Consider implementing memory streaming strategies."
            )
        
        # Check processing speed
        speed_benchmarks = [b for b in benchmarks.values() if b.test_name == "processing_speed"]
        if speed_benchmarks and speed_benchmarks[0].throughput_images_per_sec < 1.0:
            recommendations.append(
                f"Processing throughput is {speed_benchmarks[0].throughput_images_per_sec:.2f} images/sec. "
                f"Consider parallel processing optimization."
            )
        
        return recommendations


if __name__ == "__main__":
    # Example usage for manual testing
    validator = PerformanceValidator()
    tester = PerformanceTester(validator)
    report_generator = PerformanceReportGenerator(validator)
    
    # Example test data
    test_data = tester.create_test_data(5, (512, 1024))
    
    print("Performance testing framework ready.")
    print(f"Created {len(test_data)} test images for benchmarking.")

