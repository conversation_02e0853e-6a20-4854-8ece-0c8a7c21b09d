#!/usr/bin/env python3
"""
輸入分析器模組 (已重構)
Input Analyzer Module (Refactored)

負責分析輸入目錄結構，檢測全景圖、立方體面等。
此版本移除了對已棄用的 html5_processor 的依賴。
"""

import os
import sys
from pathlib import Path
# 現代類型提示語法 - 基本類型無需額外匯入

# Import project modules directly (using pip install -e .)

from config.constants import SUPPORTED_IMAGE_EXTS
from log_utils import get_logger

try:
    import cv2
    HAS_CV2 = True
except ImportError:
    HAS_CV2 = False

logger = get_logger("input_analyzer")

class InputAnalyzer:
    """輸入分析器 - 分析輸入目錄結構和內容"""

    def __init__(self, config=None):
        """
        初始化輸入分析器
        
        Args:
            config: 配置對象 (可選)
        """
        self.config = config

    def scan_input_directory(self, input_path: str) -> list[tuple[str, str, dict]]:
        """掃描輸入目錄，返回待處理的場景列表"""
        scenes_to_process = []
        if not os.path.exists(input_path):
            logger.error(f"輸入路徑不存在: {input_path}")
            return scenes_to_process
        
        # 檢查是否為直接全景圖模式
        direct_panoramas = self._check_direct_panorama_mode(input_path)
        if direct_panoramas:
            for panorama_file in direct_panoramas:
                panorama_filename = Path(panorama_file).stem
                scene_data = {
                    "has_content": True,
                    "panorama_files": [panorama_file],
                    "cube_structures": [],
                    "scene_path": input_path,
                }
                scenes_to_process.append(("default", panorama_filename, scene_data))
            logger.info(f"直接全景圖模式：找到 {len(direct_panoramas)} 個檔案。")
            return scenes_to_process

        # 檢查是否有區域/場景結構
        if any(os.path.isdir(os.path.join(input_path, item)) for item in os.listdir(input_path)):
            return self._scan_district_scene_structure(input_path)
        else:
            return self._scan_flat_structure(input_path)

    def _check_direct_panorama_mode(self, input_path: str) -> list[str]:
        """檢查是否為直接全景圖模式"""
        panorama_files = []
        try:
            for file_name in os.listdir(input_path):
                file_path = Path(input_path) / file_name
                if file_path.is_file() and self._is_panorama_image(file_path):
                    panorama_files.append(str(file_path))
        except Exception as e:
            logger.debug(f"檢查直接全景圖模式時出錯: {e}")
        return panorama_files

    def _scan_district_scene_structure(self, input_path: str) -> list[tuple[str, str, dict]]:
        """掃描傳統的區域/場景結構"""
        scenes_to_process = []
        for district_name in os.listdir(input_path):
            district_path = os.path.join(input_path, district_name)
            if not os.path.isdir(district_path): continue
            for scene_name in os.listdir(district_path):
                scene_path = os.path.join(district_path, scene_name)
                if not os.path.isdir(scene_path): continue
                scene_data = self.analyze_scene_content(scene_path)
                if scene_data["has_content"]:
                    scenes_to_process.append((district_name, scene_name, scene_data))
        return scenes_to_process

    def _scan_flat_structure(self, input_path: str) -> list[tuple[str, str, dict]]:
        """掃描平面結構（所有子目錄都視為場景）"""
        scenes_to_process = []
        for scene_name in os.listdir(input_path):
            scene_path = os.path.join(input_path, scene_name)
            if not os.path.isdir(scene_path): continue
            scene_data = self.analyze_scene_content(scene_path)
            if scene_data["has_content"]:
                scenes_to_process.append(("default", scene_name, scene_data))
        return scenes_to_process

    def analyze_scene_content(self, scene_path: str) -> dict:
        """分析單個場景的內容，優先檢測立方體結構"""
        scene_data = {
            "has_content": False,
            "panorama_files": [],
            "cube_structures": [],
            "scene_path": scene_path,
        }
        scene_path_obj = Path(scene_path)

        # 優先檢測立方體結構
        cube_structure = self._detect_cube_structure(scene_path_obj)
        if cube_structure:
            scene_data["cube_structures"].append(cube_structure)
            scene_data["has_content"] = True
            logger.info(f"場景 {scene_path} 檢測到立方體結構，將以立方體模式處理。")
        else:
            # 如果沒有立方體結構，再掃描全景圖
            for file_path in scene_path_obj.iterdir():
                if file_path.is_file() and self._is_panorama_image(file_path):
                    scene_data["panorama_files"].append(str(file_path))
            if scene_data["panorama_files"]:
                scene_data["has_content"] = True
                logger.info(f"場景 {scene_path} 檢測到 {len(scene_data['panorama_files'])} 個全景圖檔案。")
        
        return scene_data

    def _is_panorama_image(self, file_path: Path) -> bool:
        """判斷是否為全景圖像"""
        if file_path.suffix.lower() not in SUPPORTED_IMAGE_EXTS:
            return False
        
        name_lower = file_path.name.lower()
        if any(keyword in name_lower for keyword in ["pano", "panorama", "equirect", "全景"]):
            return True
            
        if HAS_CV2:
            return self._check_image_aspect_ratio(file_path)
        return False

    def _check_image_aspect_ratio(self, file_path: Path) -> bool:
        """檢查圖像寬高比是否約為 2:1"""
        try:
            img = cv2.imread(str(file_path))
            if img is None: return False
            h, w = img.shape[:2]
            return 1.8 <= w / h <= 2.2
        except Exception as e:
            logger.debug(f"檢查圖像寬高比時出錯 {file_path}: {e}")
            return False

    def _detect_cube_structure(self, scene_dir: Path) -> dict | None:
        """檢測目錄中的立方體結構"""
        html5_dir = scene_dir / "html5"
        if html5_dir.is_dir():
            structure = self._find_faces_in_dir(html5_dir)
            if structure:
                logger.info(f"在 html5 子目錄中找到立方體結構。")
                return structure

        structure = self._find_faces_in_dir(scene_dir)
        if structure:
            logger.info(f"在場景根目錄找到立方體結構。")
            return structure
            
        return None

    def _find_faces_in_dir(self, directory: Path) -> dict | None:
        """在指定目錄中查找一組完整的立方體面"""
        naming_patterns = {
            "numeric": [f"{i}.jpg" for i in range(6)],
            "face_letters": [f"{c}.jpg" for c in "FRBLUD"],
            "descriptive": ["front.jpg", "right.jpg", "back.jpg", "left.jpg", "up.jpg", "down.jpg"],
        }
        
        for naming_type, pattern in naming_patterns.items():
            found_faces = {}
            for face_id, face_name in enumerate(pattern):
                # 支援多種圖像格式
                for ext in [".jpg", ".jpeg", ".png", ".bmp"]:
                    face_file = directory / Path(face_name).with_suffix(ext)
                    if face_file.exists():
                        found_faces[face_id] = str(face_file)
                        break
            
            # 放寬條件：找到4個以上即可
            if len(found_faces) >= 4:
                return {
                    "source_dir": str(directory),
                    "naming_type": naming_type,
                    "face_files": found_faces,
                }
        return None
