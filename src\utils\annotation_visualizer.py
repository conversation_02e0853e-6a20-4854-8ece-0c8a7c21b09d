"""
標註可視化模組 - 集成座標轉換功能
為全景圖和立方體面生成帶有標註框的可視化圖像
支援座標轉換的可視化驗證
"""

import cv2
import numpy as np
from typing import Any
import random
from pathlib import Path
import colorsys
import math
import sys
from PIL import Image, ImageDraw, ImageFont


class AnnotationVisualizer:
    """標註可視化器 - 集成座標轉換功能"""

    def __init__(self, coord_transformer=None):
        """
        初始化可視化器

        Args:
            coord_transformer: CoordinateTransformer 實例
        """
        self.coord_transformer = coord_transformer

        # 預定義顏色列表（BGR格式）
        self.default_colors = [
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色
            (0, 0, 255),    # 紅色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 洋紅色
            (0, 255, 255),  # 黃色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
            (0, 128, 255),  # 橙紅色
            (128, 255, 0),  # 春綠色
        ]

        # 標籤到顏色的映射
        self.label_colors = {}

        # 字體設置
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.7
        self.font_thickness = 2
        self.box_thickness = 2

        # 座標轉換可視化設置
        self.show_coordinate_mapping = False
        self.mapping_point_size = 3
        self.mapping_line_thickness = 1
        # 新增：中文字體設置
        try:
            # Windows 系統字體路徑
            self.chinese_font = ImageFont.truetype(
                "C:/Windows/Fonts/msyh.ttc", 24)
            self.has_chinese_font = True
        except:
            try:
                # 備用字體
                self.chinese_font = ImageFont.truetype("arial.ttf", 24)
                self.has_chinese_font = False
            except:
                self.chinese_font = ImageFont.load_default()
                self.has_chinese_font = False

    def _draw_chinese_text(self, image: np.ndarray, text: str,
                        position: tuple[int, int],
                        color: tuple[int, int, int] = (255, 255, 255),
                        background_color: tuple[int, int, int] | None = None) -> np.ndarray:
        """使用 PIL 繪製中文文字"""
        # 轉換 BGR 到 RGB
        # rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image)
        draw = ImageDraw.Draw(pil_image)
        
        # 調整字體大小
        font_size = 20  # 適合的字體大小
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", font_size)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # 獲取文字尺寸
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x, y = position
        
        # 如果指定了背景色且不是透明背景，繪製背景
        if background_color is not None and background_color != (0, 0, 0):
            draw.rectangle([x-2, y-text_height-2, x+text_width+2, y+2], 
                        fill=background_color)
        
        # 繪製文字
        draw.text((x, y-text_height), text, font=font, fill=color)
        
        # 轉換回 BGR
        result = np.array(pil_image)
        return result

    def get_label_color(self, label: str) -> tuple[int, int, int]:
        """為標籤分配顏色"""
        if label not in self.label_colors:
            if len(self.label_colors) < len(self.default_colors):
                # 使用預定義顏色
                color_idx = len(self.label_colors)
                self.label_colors[label] = self.default_colors[color_idx]
            else:
                # 生成隨機顏色
                self.label_colors[label] = self._generate_random_color()

        return self.label_colors[label]

    def _generate_random_color(self) -> tuple[int, int, int]:
        """生成隨機顏色（HSV -> BGR）"""
        # 使用HSV色彩空間生成更均勻分布的顏色
        hue = random.random()
        saturation = 0.7 + random.random() * 0.3  # 70-100%
        value = 0.8 + random.random() * 0.2       # 80-100%

        # 轉換為RGB
        rgb = colorsys.hsv_to_rgb(hue, saturation, value)
        # 轉換為BGR並縮放到0-255
        bgr = (int(rgb[2] * 255), int(rgb[1] * 255), int(rgb[0] * 255))

        return bgr

    def draw_rectangle(self, image: np.ndarray, annotation: dict,
                       color: tuple[int, int, int] | None = None,
                       show_coords: bool = False) -> np.ndarray:
        """在圖像上繪製矩形標註"""
        result = image.copy()

        # 獲取標註信息
        if 'points' in annotation and len(annotation['points']) == 2:
            # labelme 格式：points = [[x1,y1], [x2,y2]]
            x1 = int(annotation['points'][0][0])
            y1 = int(annotation['points'][0][1])
            x2 = int(annotation['points'][1][0])
            y2 = int(annotation['points'][1][1])
        else:
            # 內部格式：直接的 x1, y1, x2, y2
            x1 = int(annotation.get('x1', 0))
            y1 = int(annotation.get('y1', 0))
            x2 = int(annotation.get('x2', 0))
            y2 = int(annotation.get('y2', 0))
        label = annotation.get('label', 'unknown')

        # 確保座標在有效範圍內
        h, w = image.shape[:2]
        x1 = max(0, min(x1, w-1))
        y1 = max(0, min(y1, h-1))
        x2 = max(0, min(x2, w-1))
        y2 = max(0, min(y2, h-1))

        # 獲取顏色
        if color is None:
            color = self.get_label_color(label)

        # 繪製矩形框
        cv2.rectangle(result, (x1, y1), (x2, y2), color, self.box_thickness)

        # 準備標籤文字
        label_text = label
        if show_coords:
            label_text += f" ({x1},{y1},{x2},{y2})"

        # 計算標籤位置
        label_x = x1
        label_y = y1 - 10 if y1 - 10 > 30 else y1 + 30

        # 計算文字尺寸
        (text_width, text_height), baseline = cv2.getTextSize(
            label_text, self.font, self.font_scale, self.font_thickness)

        # 計算標籤背景位置
        label_x = x1
        label_y = y1 - 10 if y1 - 10 > text_height else y1 + text_height + 10

        # 確保標籤在圖像範圍內
        if label_y < text_height:
            label_y = y1 + text_height + 10
        if label_x + text_width > w:
            label_x = w - text_width - 5

        # 繪製標籤背景
        cv2.rectangle(result,
                      (label_x - 2, label_y - text_height - 2),
                      (label_x + text_width + 2, label_y + baseline + 2),
                      color, -1)

        # 繪製標籤文字（白色）
        cv2.putText(result, label_text, (label_x, label_y),
                    self.font, self.font_scale, (255, 255, 255),
                    self.font_thickness)

        return result

    def draw_polygon(self, image: np.ndarray, annotation: dict,
                     color: tuple[int, int, int] | None = None,
                     show_coords: bool = False) -> np.ndarray:
        """在圖像上繪製多邊形標註"""
        result = image.copy()

        # 獲取標註信息
        points = annotation.get('points', [])
        label = annotation.get('label', 'unknown')

        if len(points) < 3:
            return result  # 至少需要3個點才能組成多邊形

        # 獲取顏色
        if color is None:
            color = self.get_label_color(label)

        # 轉換點格式
        polygon_points = np.array([[int(p[0]), int(p[1])]
                                  for p in points], np.int32)
        polygon_points = polygon_points.reshape((-1, 1, 2))

        # 繪製多邊形邊界
        cv2.polylines(result, [polygon_points], True,
                      color, self.box_thickness)

        # 繪製半透明填充
        overlay = result.copy()
        cv2.fillPoly(overlay, [polygon_points], color)
        cv2.addWeighted(overlay, 0.3, result, 0.7, 0, result)

        # 計算多邊形中心點用於放置標籤
        center_x = int(np.mean([p[0] for p in points]))
        center_y = int(np.mean([p[1] for p in points]))

        # 繪製標籤
        label_text = label
        if show_coords:
            # 顯示點座標（簡化顯示）
            label_text += f" (多邊形:{len(points)}點)"

        (text_width, text_height), baseline = cv2.getTextSize(
            label_text, self.font, self.font_scale, self.font_thickness)

        # 確保標籤在圖像範圍內
        h, w = image.shape[:2]
        label_x = max(0, min(center_x - text_width // 2, w - text_width))
        label_y = max(text_height, min(center_y, h - 10))

        # 繪製標籤背景
        cv2.rectangle(result,
                      (label_x - 2, label_y - text_height - 2),
                      (label_x + text_width + 2, label_y + baseline + 2),
                      color, -1)

        # 繪製標籤文字
        cv2.putText(result, label_text, (label_x, label_y),
                    self.font, self.font_scale, (255, 255, 255),
                    self.font_thickness)

        return result

    def visualize_panorama_annotations(self, panorama: np.ndarray,
                                       annotations: list[dict],
                                       pano_width: int, pano_height: int,
                                       show_coords: bool = False) -> np.ndarray:
        """
        在全景圖上可視化標註

        Args:
            panorama: 全景圖像
            annotations: 標註列表（全景圖座標系統）
            pano_width: 全景圖寬度
            pano_height: 全景圖高度
            show_coords: 是否顯示座標信息
        """
        result = panorama.copy()

        for annotation in annotations:
            shape_type = annotation.get('shape_type', 'rectangle')

            if shape_type == 'rectangle':
                result = self.draw_rectangle(
                    result, annotation, show_coords=show_coords)
            elif shape_type == 'polygon':
                result = self.draw_polygon(
                    result, annotation, show_coords=show_coords)

        # 添加座標系統信息
        result = self._add_coordinate_system_info(
            result, "全景圖座標系統", pano_width, pano_height)

        return result

    def visualize_cube_face_annotations(self, face_image: np.ndarray,
                                        face_annotations: list[dict],
                                        face_name: str,
                                        cube_size: int,
                                        show_coords: bool = False) -> np.ndarray:
        """
        在立方體面上可視化標註

        Args:
            face_image: 立方體面圖像
            face_annotations: 標註列表（立方體面座標系統）
            face_name: 面名稱
            cube_size: 立方體面尺寸
            show_coords: 是否顯示座標信息
        """
        result = face_image.copy()

        for annotation in face_annotations:
            shape_type = annotation.get('shape_type', 'rectangle')

            if shape_type == 'rectangle':
                result = self.draw_rectangle(
                    result, annotation, show_coords=show_coords)
            elif shape_type == 'polygon':
                result = self.draw_polygon(
                    result, annotation, show_coords=show_coords)

        # 添加座標系統信息
        result = self._add_coordinate_system_info(
            result, f"{face_name} 面座標系統", cube_size, cube_size)

        return result

    def visualize_coordinate_mapping(self, source_image: np.ndarray,
                                     target_image: np.ndarray,
                                     annotation: dict,
                                     source_type: str,
                                     target_type: str,
                                     pano_width: int | None = None,
                                     pano_height: int | None = None,
                                     cube_size: int | None = None,
                                     face_name: str | None = None) -> tuple[np.ndarray, np.ndarray]:
        """
        可視化座標轉換映射關係

        Args:
            source_image: 源圖像
            target_image: 目標圖像
            annotation: 標註信息
            source_type: 源類型 ("panorama" 或 "cube_face")
            target_type: 目標類型 ("panorama" 或 "cube_face")
            pano_width: 全景圖寬度
            pano_height: 全景圖高度
            cube_size: 立方體面尺寸
            face_name: 立方體面名稱

        Returns:
            (源圖像可視化, 目標圖像可視化)
        """
        if not self.coord_transformer:
            raise ValueError("需要 CoordinateTransformer 實例才能進行座標轉換可視化")

        # 創建源圖像可視化
        source_vis = source_image.copy()
        target_vis = target_image.copy()

        # 獲取顏色
        color = self.get_label_color(annotation.get('label', 'unknown'))

        if source_type == "panorama" and target_type == "cube_face":
            # 全景圖到立方體面的轉換
            source_vis = self._visualize_pano_to_cube_mapping(
                source_vis, annotation, color, pano_width, pano_height, cube_size, face_name)

            # 轉換標註到立方體面
            if annotation.get('shape_type') == 'rectangle':
                face_bboxes = self.coord_transformer.convert_panorama_bbox_to_cube_faces(
                    annotation, pano_width, pano_height, cube_size)

                if face_name in face_bboxes and face_bboxes[face_name]:
                    for bbox in face_bboxes[face_name]:
                        target_vis = self.draw_rectangle(
                            target_vis, bbox, color)

        elif source_type == "cube_face" and target_type == "panorama":
            # 立方體面到全景圖的轉換
            source_vis = self.draw_rectangle(source_vis, annotation, color)

            # 轉換標註到全景圖
            if annotation.get('shape_type') == 'rectangle':
                pano_bbox = self.coord_transformer.convert_cube_face_bbox_to_panorama(
                    face_name, annotation, cube_size, pano_width, pano_height)

                if pano_bbox:
                    target_vis = self.draw_rectangle(
                        target_vis, pano_bbox, color)

        # 添加映射說明
        source_vis = self._add_mapping_info(
            source_vis, f"{source_type} (源)", annotation)
        target_vis = self._add_mapping_info(
            target_vis, f"{target_type} (目標)", annotation)

        return source_vis, target_vis

    def _visualize_pano_to_cube_mapping(self, pano_image: np.ndarray,
                                        annotation: dict,
                                        color: tuple[int, int, int],
                                        pano_width: int,
                                        pano_height: int,
                                        cube_size: int,
                                        target_face: str) -> np.ndarray:
        """可視化全景圖到立方體面的映射"""
        result = pano_image.copy()

        # 在全景圖上繪製原始標註
        result = self.draw_rectangle(result, annotation, color)

        # 如果啟用了映射可視化，添加採樣點
        if self.show_coordinate_mapping:
            x1, y1, x2, y2 = annotation['x1'], annotation['y1'], annotation['x2'], annotation['y2']

            # 在邊界框上採樣點
            sample_points = []
            for i in range(0, 11):  # 11個點
                t = i / 10.0
                # 上下邊
                sample_points.extend([
                    (x1 + t * (x2 - x1), y1),
                    (x1 + t * (x2 - x1), y2)
                ])
                # 左右邊
                sample_points.extend([
                    (x1, y1 + t * (y2 - y1)),
                    (x2, y1 + t * (y2 - y1))
                ])

            # 轉換並繪製映射到目標面的點
            for pano_u, pano_v in sample_points:
                face_name, face_x, face_y = self.coord_transformer.panorama_point_to_cube_face(
                    pano_u, pano_v, pano_width, pano_height, cube_size)

                if face_name == target_face:
                    # 在全景圖上標記這些點
                    cv2.circle(result, (int(pano_u), int(pano_v)),
                               self.mapping_point_size, (0, 255, 255), -1)

        return result

    def create_split_view_visualization(self, left_image: np.ndarray,
                                        right_image: np.ndarray,
                                        title: str) -> np.ndarray:
        """創建左右分割視圖"""
        # 調整圖像尺寸使其高度一致
        target_height = min(left_image.shape[0], right_image.shape[0])

        left_resized = cv2.resize(left_image,
                                  (int(left_image.shape[1] * target_height / left_image.shape[0]),
                                   target_height))
        right_resized = cv2.resize(right_image,
                                   (int(right_image.shape[1] * target_height / right_image.shape[0]),
                                    target_height))

        # 水平拼接
        split_view = np.hstack([left_resized, right_resized])

        # 添加分割線
        split_x = left_resized.shape[1]
        cv2.line(split_view, (split_x, 0),
                 (split_x, target_height), (255, 255, 255), 3)

        # 添加標題
        split_view = self._add_title(split_view, title)

        return split_view

    def _add_coordinate_system_info(self, image: np.ndarray,
                                    system_name: str,
                                    width: int,
                                    height: int) -> np.ndarray:
        """添加座標系統信息"""
        result = image.copy()
        h, w = result.shape[:2]

        # 信息文字
        info_lines = [
            system_name,
            f"image size: {width} x {height}",
            f"image range: (0,0) to ({width-1},{height-1})"
        ]

        # 計算信息框尺寸
        line_height = 25
        info_height = len(info_lines) * line_height + 10
        info_width = 320

        # 在右下角放置信息框
        start_x = w - info_width - 10
        start_y = h - info_height - 10

        # 半透明背景
        overlay = result.copy()
        cv2.rectangle(overlay, (start_x, start_y),
                      (start_x + info_width, start_y + info_height),
                      (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, result, 0.3, 0, result)

        # 使用 PIL 繪製中文文字
        for i, line in enumerate(info_lines):
            y_pos = start_y + 20 + i * line_height
            if i == 0:
                # 標題使用較大字體
                result = self._draw_chinese_text(result, line, (start_x + 5, y_pos), 
                                            color=(255, 255, 255), background_color=(0, 0, 0))
            else:
                # 信息使用普通字體
                result = self._draw_chinese_text(result, line, (start_x + 5, y_pos), 
                                            color=(200, 200, 200), background_color=(0, 0, 0))
        
        return result

    def _add_mapping_info(self, image: np.ndarray,
                          image_type: str,
                          annotation: dict) -> np.ndarray:
        """添加映射信息"""
        result = image.copy()

        # 信息文字
        info_text = f"{image_type}: {annotation.get('label', 'unknown')}"

        # 在左上角添加信息
        (text_width, text_height), baseline = cv2.getTextSize(
            info_text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)

        # 背景
        cv2.rectangle(result, (5, 5), (text_width + 15,
                      text_height + 15), (0, 0, 0), -1)

        # 文字
        cv2.putText(result, info_text, (10, text_height + 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return result

    def _add_title(self, image: np.ndarray, title: str) -> np.ndarray:
        """添加標題"""
        result = image.copy()
        h, w = result.shape[:2]

        # 計算標題位置
        (text_width, text_height), baseline = cv2.getTextSize(
            title, cv2.FONT_HERSHEY_SIMPLEX, 1.0, 3)

        x = (w - text_width) // 2
        y = text_height + 15

        # 背景
        cv2.rectangle(result, (x - 10, 5),
                      (x + text_width + 10, y + 10), (0, 0, 0), -1)

        # 標題
        cv2.putText(result, title, (x, y),
                    cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 3)

        return result

    def _add_statistics_overlay(self, image: np.ndarray,
                                annotations: list[dict],
                                title: str) -> np.ndarray:
        """添加統計信息覆蓋層"""
        result = image.copy()
        h, w = result.shape[:2]

        # 統計標註信息
        label_counts = {}
        for ann in annotations:
            label = ann.get('label', 'unknown')
            label_counts[label] = label_counts.get(label, 0) + 1

        # 準備統計文字
        stats_lines = [f"{title} - 標註統計"]
        stats_lines.append(f"總計: {len(annotations)} 個標註")

        for label, count in sorted(label_counts.items()):
            stats_lines.append(f"  {label}: {count}")

        # 計算覆蓋層尺寸
        line_height = 30
        overlay_height = len(stats_lines) * line_height + 20
        overlay_width = 300

        # 創建半透明背景
        overlay = result.copy()
        cv2.rectangle(overlay, (10, 10),
                      (10 + overlay_width, 10 + overlay_height),
                      (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, result, 0.3, 0, result)

        # 使用 PIL 繪製中文文字
        for i, line in enumerate(stats_lines):
            y_pos = 35 + i * line_height
            if i == 0:
                # 標題使用較大字體
                result = self._draw_chinese_text(result, line, (15, y_pos), 
                                            color=(255, 255, 255), background_color=(0, 0, 0))
            else:
                # 統計信息使用普通字體
                result = self._draw_chinese_text(result, line, (15, y_pos), 
                                            color=(200, 200, 200), background_color=(0, 0, 0))
    

        return result

    # 高級接口方法
    def create_panorama_visualization(self, panorama: np.ndarray,
                                      annotations: list[dict],
                                      output_path: str,
                                      pano_width: int | None = None,
                                      pano_height: int | None = None,
                                      show_coords: bool = False) -> bool:
        """創建全景圖可視化"""
        try:
            if pano_width is None:
                pano_width = panorama.shape[1]
            if pano_height is None:
                pano_height = panorama.shape[0]

            # 生成可視化圖像
            vis_image = self.visualize_panorama_annotations(
                panorama, annotations, pano_width, pano_height, show_coords)

            # 添加統計信息
            vis_image = self._add_statistics_overlay(
                vis_image, annotations, "全景圖")

            # 保存圖像
            success = cv2.imwrite(output_path, vis_image)

            if success:
                print(f"✅ 全景圖可視化已保存: {output_path}")
            else:
                print(f"❌ 全景圖可視化保存失敗: {output_path}")

            return success

        except Exception as e:
            print(f"❌ 創建全景圖可視化失敗: {e}")
            return False

    def create_cube_face_visualization(self, face_image: np.ndarray,
                                       face_annotations: list[dict],
                                       face_name: str,
                                       output_path: str,
                                       cube_size: int | None = None,
                                       show_coords: bool = False) -> bool:
        """創建立方體面可視化"""
        try:
            if cube_size is None:
                cube_size = face_image.shape[0]  # 假設是正方形

            if not face_annotations:
                # 如果沒有標註，直接保存原圖
                success = cv2.imwrite(output_path, face_image)
                if success:
                    print(f"📷 {face_name} 面無標註，保存原圖: {output_path}")
                return success

            # 生成可視化圖像
            vis_image = self.visualize_cube_face_annotations(
                face_image, face_annotations, face_name, cube_size, show_coords)

            # 添加統計信息
            vis_image = self._add_statistics_overlay(
                vis_image, face_annotations, f"{face_name} 面")

            # 保存圖像
            success = cv2.imwrite(output_path, vis_image)

            if success:
                print(
                    f"✅ {face_name} 面可視化已保存 ({len(face_annotations)} 個標註): {output_path}")
            else:
                print(f"❌ {face_name} 面可視化保存失敗: {output_path}")

            return success

        except Exception as e:
            print(f"❌ 創建 {face_name} 面可視化失敗: {e}")
            return False

    def create_cube_preview_with_annotations(self, cube_dict: dict[str, np.ndarray],
                                             cube_annotations: dict[str, list[dict]],
                                             output_path: str) -> bool:
        """創建帶標註的立方體預覽圖（水平排列）"""
        try:
            face_names = ['F', 'R', 'B', 'L', 'U', 'D']
            vis_faces = []

            # 為每個面創建可視化
            for face_name in face_names:
                if face_name in cube_dict:
                    face_image = cube_dict[face_name]
                    face_anns = cube_annotations.get(face_name, [])

                    if face_anns:
                        vis_face = self.visualize_cube_face_annotations(
                            face_image, face_anns, face_name, face_image.shape[0])
                    else:
                        vis_face = face_image.copy()

                    # 添加面名稱標籤
                    vis_face = self._add_face_label(
                        vis_face, face_name, len(face_anns))
                    vis_faces.append(vis_face)
                else:
                    # 創建空白面
                    if cube_dict:
                        first_face = next(iter(cube_dict.values()))
                        empty_face = np.zeros_like(first_face)
                        empty_face = self._add_face_label(
                            empty_face, face_name, 0)
                        vis_faces.append(empty_face)

            if vis_faces:
                # 水平拼接
                preview = np.hstack(vis_faces)

                # 添加整體統計信息
                total_annotations = sum(len(anns)
                                        for anns in cube_annotations.values())
                preview = self._add_preview_title(preview, total_annotations)

                # 保存預覽圖
                success = cv2.imwrite(output_path, preview)

                if success:
                    print(
                        f"✅ 立方體預覽圖已保存 ({total_annotations} 個標註): {output_path}")
                else:
                    print(f"❌ 立方體預覽圖保存失敗: {output_path}")

                return success
            else:
                print("❌ 無法創建立方體預覽圖：沒有有效的面")
                return False

        except Exception as e:
            print(f"❌ 創建立方體預覽圖失敗: {e}")
            return False

    def _add_face_label(self, face_image: np.ndarray,
                        face_name: str, annotation_count: int) -> np.ndarray:
        """為立方體面添加名稱標籤"""
        result = face_image.copy()
        h, w = result.shape[:2]

        # 標籤文字
        label_text = f"{face_name} ({annotation_count})"

        # 計算文字尺寸
        (text_width, text_height), baseline = cv2.getTextSize(
            label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)

        # 在右上角放置標籤
        x = w - text_width - 10
        y = text_height + 10

        # 半透明背景
        overlay = result.copy()
        cv2.rectangle(overlay, (x - 5, y - text_height - 5),
                      (x + text_width + 5, y + baseline + 5),
                      (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, result, 0.3, 0, result)

        # 文字
        cv2.putText(result, label_text, (x, y),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        return result

    def _add_preview_title(self, preview: np.ndarray,
                           total_annotations: int) -> np.ndarray:
        """為預覽圖添加標題"""
        result = preview.copy()
        h, w = result.shape[:2]

        # 標題文字
        title_text = f"立方體映射可視化 - 總計 {total_annotations} 個標註"

        # 計算文字尺寸
        (text_width, text_height), baseline = cv2.getTextSize(
            title_text, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 3)

        # 在頂部中央放置標題
        x = (w - text_width) // 2
        y = text_height + 20

        # 半透明背景
        overlay = result.copy()
        cv2.rectangle(overlay, (x - 10, 5),
                      (x + text_width + 10, y + baseline + 10),
                      (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.8, result, 0.2, 0, result)

        # 標題文字
        cv2.putText(result, title_text, (x, y),
                    cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)

        return result


def main():
    """使用示例"""
    print("=== 標註可視化模組 (集成座標轉換) ===")
    print("\n功能說明：")
    print("1. 在全景圖上繪製標註框（支援座標系統顯示）")
    print("2. 在立方體面上繪製標註框（支援座標系統顯示）")
    print("3. 可視化座標轉換映射關係")
    print("4. 生成分割視圖比較轉換前後")
    print("5. 自動分配顏色給不同標籤")
    print("6. 支援矩形和多邊形標註")

    print("\n使用示例：")
    print("""
    from annotation_visualizer import AnnotationVisualizer
    from core.coordinate import CoordinateTransformer
    
    # 創建帶座標轉換功能的可視化器
    coord_transformer = CoordinateTransformer()
    visualizer = AnnotationVisualizer(coord_transformer)
    
    # 可視化全景圖標註（包含座標系統信息）
    vis_pano = visualizer.visualize_panorama_annotations(
        panorama_image, annotations, pano_width, pano_height, show_coords=True)
    
    # 可視化立方體面標註（包含座標系統信息）
    vis_face = visualizer.visualize_cube_face_annotations(
        face_image, face_annotations, "F", cube_size, show_coords=True)
    
    # 可視化座標轉換映射
    source_vis, target_vis = visualizer.visualize_coordinate_mapping(
        pano_image, face_image, annotation, "panorama", "cube_face",
        pano_width, pano_height, cube_size, "F")
    
    # 創建分割視圖
    split_view = visualizer.create_split_view_visualization(
        source_vis, target_vis, "全景圖 → 立方體面 F 轉換")
    """)


if __name__ == "__main__":
    main()
