"""
記憶體提供者模組
提供各種記憶體管理策略的具體實現
"""
import gc
import logging
import threading
import time
from abc import ABC, abstractmethod
from typing import Any

import numpy as np
import psutil
import torch

from ..core.metric_collector import Metric, MetricType
from ..core.thresholds import SystemThresholds, DEFAULT_THRESHOLDS

logger = logging.getLogger(__name__)


class MemoryProvider(ABC):
    """抽象記憶體提供者"""
    
    @abstractmethod
    def get_info(self) -> dict[str, Any]:
        """獲取記憶體信息"""
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """清理記憶體"""
        pass

    @abstractmethod
    def check_availability(self, required_mb: int) -> bool:
        """檢查記憶體可用性"""
        pass

    @abstractmethod
    def get_metrics(self) -> list[Metric]:
        """獲取記憶體指標"""
        pass


class SystemMemoryProvider(MemoryProvider):
    """系統記憶體提供者"""
    
    def __init__(self, thresholds: SystemThresholds | None = None):
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self._last_cleanup = 0
        self._cleanup_interval = 30  # 30秒間隔

    def get_info(self) -> dict[str, Any]:
        """獲取系統記憶體信息"""
        mem = psutil.virtual_memory()
        return {
            "type": "system",
            "total_mb": mem.total / (1024**2),
            "available_mb": mem.available / (1024**2),
            "used_mb": mem.used / (1024**2),
            "free_mb": mem.free / (1024**2),
            "percent": mem.percent,
            "cached_mb": getattr(mem, 'cached', 0) / (1024**2),
            "buffers_mb": getattr(mem, 'buffers', 0) / (1024**2),
        }

    def cleanup(self) -> bool:
        """清理系統記憶體"""
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return False
            
        try:
            # 強制垃圾回收
            collected = gc.collect()
            self._last_cleanup = current_time
            logger.debug(f"系統記憶體清理完成，回收 {collected} 個對象")
            return True
        except Exception as e:
            logger.error(f"系統記憶體清理失敗: {e}")
            return False

    def check_availability(self, required_mb: int) -> bool:
        """檢查系統記憶體可用性"""
        info = self.get_info()
        available = info.get("available_mb", 0)
        return available >= required_mb

    def get_metrics(self) -> list[Metric]:
        """獲取系統記憶體指標"""
        info = self.get_info()
        return [
            Metric(
                name="system.memory.total",
                value=info["total_mb"],
                unit="MB",
                metric_type=MetricType.MEMORY_USAGE
            ),
            Metric(
                name="system.memory.available",
                value=info["available_mb"],
                unit="MB",
                metric_type=MetricType.MEMORY_USAGE
            ),
            Metric(
                name="system.memory.usage",
                value=info["percent"],
                unit="%",
                metric_type=MetricType.MEMORY_USAGE
            ),
        ]

    def estimate_requirement(self, image_shape: tuple, dtype, factor: float = 3.0) -> int:
        """估計處理需求"""
        try:
            single_size = np.prod(image_shape) * np.dtype(dtype).itemsize
            estimated_size = single_size * factor
            return int(estimated_size / (1024 * 1024))
        except Exception:
            return 0


class CudaMemoryProvider(MemoryProvider):
    """CUDA記憶體提供者"""
    
    def __init__(self, device_id: int = 0, thresholds: SystemThresholds | None = None):
        self.device_id = device_id
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self.cuda_available = torch.cuda.is_available()
        self._last_cleanup = 0
        self._cleanup_interval = 10  # 10秒間隔
        
        if self.cuda_available:
            self.device = torch.device(f'cuda:{self.device_id}')
            try:
                props = torch.cuda.get_device_properties(self.device_id)
                self.device_name = props.name
                self.total_memory = props.total_memory
            except Exception as e:
                logger.warning(f"無法獲取CUDA設備屬性: {e}")
                self.device_name = f"CUDA:{device_id}"
                self.total_memory = 0
        else:
            self.device_name = "N/A"
            self.total_memory = 0

    def get_info(self) -> dict[str, Any]:
        """獲取CUDA記憶體信息"""
        if not self.cuda_available:
            return {
                "type": "cuda",
                "device_id": self.device_id,
                "device_name": self.device_name,
                "available": False,
                "total_mb": 0,
                "free_mb": 0,
                "used_mb": 0,
                "percent": 0,
            }
        
        try:
            free, total = torch.cuda.mem_get_info(self.device_id)
            used = total - free
            percent = (used / total * 100) if total > 0 else 0
            
            return {
                "type": "cuda",
                "device_id": self.device_id,
                "device_name": self.device_name,
                "available": True,
                "total_mb": total / (1024**2),
                "free_mb": free / (1024**2),
                "used_mb": used / (1024**2),
                "percent": percent,
                "allocated_mb": torch.cuda.memory_allocated(self.device_id) / (1024**2),
                "cached_mb": torch.cuda.memory_reserved(self.device_id) / (1024**2),
            }
        except Exception as e:
            logger.error(f"獲取CUDA記憶體信息失敗: {e}")
            return {"type": "cuda", "available": False, "error": str(e)}

    def cleanup(self) -> bool:
        """清理CUDA記憶體"""
        if not self.cuda_available:
            return False
            
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return False
            
        try:
            torch.cuda.empty_cache()
            self._last_cleanup = current_time
            logger.debug(f"CUDA記憶體清理完成 (設備 {self.device_id})")
            return True
        except Exception as e:
            logger.error(f"CUDA記憶體清理失敗: {e}")
            return False

    def check_availability(self, required_mb: int) -> bool:
        """檢查CUDA記憶體可用性"""
        if not self.cuda_available:
            return False
        info = self.get_info()
        if not info.get("available", False):
            return False
        free_mb = info.get("free_mb", 0)
        return free_mb >= required_mb

    def get_metrics(self) -> list[Metric]:
        """獲取CUDA記憶體指標"""
        info = self.get_info()
        if not info.get("available", False):
            return []
            
        return [
            Metric(
                name=f"cuda.{self.device_id}.memory.total",
                value=info["total_mb"],
                unit="MB",
                metric_type=MetricType.GPU_MEMORY,
                tags={"device_id": str(self.device_id), "device_name": self.device_name}
            ),
            Metric(
                name=f"cuda.{self.device_id}.memory.free",
                value=info["free_mb"],
                unit="MB",
                metric_type=MetricType.GPU_MEMORY,
                tags={"device_id": str(self.device_id), "device_name": self.device_name}
            ),
            Metric(
                name=f"cuda.{self.device_id}.memory.usage",
                value=info["percent"],
                unit="%",
                metric_type=MetricType.GPU_MEMORY,
                tags={"device_id": str(self.device_id), "device_name": self.device_name}
            ),
        ]


class MemoryProviderFactory:
    """記憶體提供者工廠"""
    
    @staticmethod
    def create_system_provider(thresholds: SystemThresholds | None = None) -> SystemMemoryProvider:
        """創建系統記憶體提供者"""
        return SystemMemoryProvider(thresholds)
    
    @staticmethod
    def create_cuda_provider(device_id: int = 0, thresholds: SystemThresholds | None = None) -> CudaMemoryProvider:
        """創建CUDA記憶體提供者"""
        return CudaMemoryProvider(device_id, thresholds)
    
    @staticmethod
    def create_all_providers(thresholds: SystemThresholds | None = None) -> list[MemoryProvider]:
        """創建所有可用的記憶體提供者"""
        providers = [MemoryProviderFactory.create_system_provider(thresholds)]
        
        # 添加所有可用的CUDA設備
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            for device_id in range(device_count):
                providers.append(MemoryProviderFactory.create_cuda_provider(device_id, thresholds))
        
        return providers


class CompositeMemoryProvider(MemoryProvider):
    """組合記憶體提供者 - 管理多個提供者"""
    
    def __init__(self, providers: list[MemoryProvider] | None = None):
        self.providers = providers or MemoryProviderFactory.create_all_providers()
        self._lock = threading.RLock()
    
    def get_info(self) -> dict[str, Any]:
        """獲取所有提供者的記憶體信息"""
        with self._lock:
            info = {"providers": []}
            for provider in self.providers:
                try:
                    provider_info = provider.get_info()
                    info["providers"].append(provider_info)
                except Exception as e:
                    logger.error(f"獲取提供者信息失敗: {e}")
            return info
    
    def cleanup(self) -> bool:
        """清理所有提供者的記憶體"""
        with self._lock:
            results = []
            for provider in self.providers:
                try:
                    result = provider.cleanup()
                    results.append(result)
                except Exception as e:
                    logger.error(f"提供者清理失敗: {e}")
                    results.append(False)
            return any(results)  # 任何一個成功就算成功
    
    def check_availability(self, required_mb: int) -> bool:
        """檢查是否有任何提供者可以滿足需求"""
        with self._lock:
            for provider in self.providers:
                try:
                    if provider.check_availability(required_mb):
                        return True
                except Exception as e:
                    logger.error(f"檢查提供者可用性失敗: {e}")
            return False
    
    def get_metrics(self) -> list[Metric]:
        """獲取所有提供者的指標"""
        with self._lock:
            all_metrics = []
            for provider in self.providers:
                try:
                    metrics = provider.get_metrics()
                    all_metrics.extend(metrics)
                except Exception as e:
                    logger.error(f"獲取提供者指標失敗: {e}")
            return all_metrics
