"""
性能提供者模組
提供各種性能監控策略的具體實現
"""
import logging
import threading
import time
from abc import ABC, abstractmethod
from typing import Any

import psutil

try:
    import pynvml
    HAS_NVML = True
except ImportError:
    HAS_NVML = False

from ..core.metric_collector import Metric, MetricType, MetricCollector
from ..core.thresholds import SystemThresholds, DEFAULT_THRESHOLDS

logger = logging.getLogger(__name__)


class PerformanceProvider(ABC):
    """抽象性能提供者"""
    
    @abstractmethod
    def collect_metrics(self) -> list[Metric]:
        """收集性能指標"""
        pass
    
    @abstractmethod
    def get_status(self) -> dict[str, Any]:
        """獲取性能狀態"""
        pass


class SystemPerformanceProvider(PerformanceProvider, MetricCollector):
    """系統性能提供者"""
    
    def __init__(self, thresholds: SystemThresholds | None = None):
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self._metrics_cache = []
        self._last_update = 0
        
    def get_collector_name(self) -> str:
        """獲取收集器名稱"""
        return "system_performance"
    
    def collect(self) -> list[Metric]:
        """收集系統性能指標"""
        return self.collect_metrics()
    
    def collect_metrics(self) -> list[Metric]:
        """收集系統性能指標"""
        current_time = time.time()
        # 快取機制：1秒內不重複收集
        if current_time - self._last_update < 1.0 and self._metrics_cache:
            return self._metrics_cache
        
        metrics = []
        
        try:
            # CPU 指標
            cpu_percent = psutil.cpu_percent(interval=None)
            metrics.append(Metric(
                name="system.cpu.usage",
                value=cpu_percent,
                unit="%",
                metric_type=MetricType.CPU_USAGE
            ))
            
            # 記憶體指標
            memory = psutil.virtual_memory()
            metrics.append(Metric(
                name="system.memory.usage",
                value=memory.percent,
                unit="%",
                metric_type=MetricType.MEMORY_USAGE
            ))
            
            # 磁碟使用率
            disk_usage = psutil.disk_usage('/')
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            metrics.append(Metric(
                name="system.disk.usage",
                value=disk_percent,
                unit="%",
                metric_type=MetricType.DISK_USAGE
            ))
            
        except Exception as e:
            logger.error(f"收集系統性能指標失敗: {e}")
            metrics.append(Metric(
                name="system.error",
                value=1.0,
                unit="count",
                tags={"error": str(e)}
            ))
        
        self._metrics_cache = metrics
        self._last_update = current_time
        return metrics
    
    def get_status(self) -> dict[str, Any]:
        """獲取系統性能狀態"""
        try:
            cpu_percent = psutil.cpu_percent(interval=None)
            memory_percent = psutil.virtual_memory().percent
            
            return {
                "cpu_usage": cpu_percent,
                "memory_usage": memory_percent,
                "overall_status": "OK"
            }
        except Exception as e:
            logger.error(f"獲取系統性能狀態失敗: {e}")
            return {"error": str(e), "overall_status": "ERROR"}


class GPUPerformanceProvider(PerformanceProvider, MetricCollector):
    """GPU性能提供者"""
    
    def __init__(self, thresholds: SystemThresholds | None = None):
        self.thresholds = thresholds or DEFAULT_THRESHOLDS
        self.gpu_available = HAS_NVML
        self.gpu_count = 0
        self._metrics_cache = []
        self._last_update = 0
        
        if self.gpu_available:
            try:
                pynvml.nvmlInit()
                self.gpu_count = pynvml.nvmlDeviceGetCount()
            except Exception as e:
                logger.warning(f"初始化GPU監控失敗: {e}")
                self.gpu_available = False
    
    def get_collector_name(self) -> str:
        """獲取收集器名稱"""
        return "gpu_performance"
    
    def collect(self) -> list[Metric]:
        """收集GPU性能指標"""
        return self.collect_metrics()
    
    def collect_metrics(self) -> list[Metric]:
        """收集GPU性能指標"""
        if not self.gpu_available:
            return []
        
        current_time = time.time()
        if current_time - self._last_update < 1.0 and self._metrics_cache:
            return self._metrics_cache
        
        metrics = []
        
        try:
            for device_id in range(self.gpu_count):
                handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
                device_name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
                
                # GPU利用率
                util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                metrics.append(Metric(
                    name=f"gpu.{device_id}.usage",
                    value=util.gpu,
                    unit="%",
                    metric_type=MetricType.GPU_USAGE,
                    tags={"device_id": str(device_id), "device_name": device_name}
                ))
                
                # 記憶體利用率
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                memory_percent = (mem_info.used / mem_info.total * 100) if mem_info.total > 0 else 0
                metrics.append(Metric(
                    name=f"gpu.{device_id}.memory.usage",
                    value=memory_percent,
                    unit="%",
                    metric_type=MetricType.GPU_MEMORY,
                    tags={"device_id": str(device_id), "device_name": device_name}
                ))
        
        except Exception as e:
            logger.error(f"收集GPU性能指標失敗: {e}")
        
        self._metrics_cache = metrics
        self._last_update = current_time
        return metrics
    
    def get_status(self) -> dict[str, Any]:
        """獲取GPU性能狀態"""
        if not self.gpu_available:
            return {"gpu_available": False, "overall_status": "N/A"}
        
        return {
            "gpu_available": True,
            "gpu_count": self.gpu_count,
            "overall_status": "OK"
        }
