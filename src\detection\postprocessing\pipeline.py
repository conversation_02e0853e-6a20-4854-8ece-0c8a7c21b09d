"""
後處理管線

用於鏈接多個後處理操作的可組合管線。
"""

from typing import Any, Callable

from log_utils import get_logger

from .base import PostProcessor

logger = get_logger(__name__)


class PostProcessingPipeline:
    """可組合的後處理管線"""

    def __init__(self):
        self.processors: list[PostProcessor] = []
        self._stats = {
            "total_processed": 0,
            "processors_applied": 0,
            "processors_skipped": 0,
        }

    def add_processor(self, processor: PostProcessor) -> "PostProcessingPipeline":
        """將處理器新增至管線 (可鏈接)

        :param processor: 後處理器實例
        :return: self 以便方法鏈接
        """
        self.processors.append(processor)
        logger.debug(f"已新增處理器: {processor.get_name()}")
        return self

    def add_processors(self, *processors: PostProcessor) -> "PostProcessingPipeline":
        """新增多個處理器 (可鏈接)

        :param processors: 後處理器實例
        :return: self 以便方法鏈接
        """
        for processor in processors:
            self.add_processor(processor)
        return self

    def insert_processor(
        self, index: int, processor: PostProcessor
    ) -> "PostProcessingPipeline":
        """在指定位置插入處理器

        :param index: 要插入的位置
        :param processor: 後處理器實例
        :return: self 以便方法鏈接
        """
        self.processors.insert(index, processor)
        logger.debug(f"已在 {index} 插入處理器: {processor.get_name()}")
        return self

    def remove_processor(self, name: str) -> "PostProcessingPipeline":
        """按名稱移除處理器

        :param name: 要移除的處理器名稱
        :return: self 以便方法鏈接
        """
        self.processors = [p for p in self.processors if p.get_name() != name]
        logger.debug(f"已移除處理器: {name}")
        return self

    def clear(self) -> "PostProcessingPipeline":
        """清除所有處理器

        :return: self 以便方法鏈接
        """
        self.processors.clear()
        logger.debug("已清除所有處理器")
        return self

    def process(self, detections: list, context: dict[str, Any]) -> list:
        """執行處理管線

        :param detections: 輸入的偵測列表
        :param context: 處理上下文
        :return: 處理後的偵測列表
        """
        if not self.processors:
            logger.debug("管線中無處理器，回傳原始偵測結果")
            return detections

        result = detections
        processors_applied = 0
        processors_skipped = 0

        for processor in self.processors:
            try:
                # 檢查處理器是否適用
                if not processor.is_applicable(context):
                    logger.debug(
                        f"跳過處理器 {processor.get_name()} (不適用)"
                    )
                    processors_skipped += 1
                    continue

                # 應用處理器
                before_count = len(result)
                result = processor.process(result, context)
                after_count = len(result)

                logger.debug(
                    f"已應用 {processor.get_name()}: {before_count} -> {after_count} 個偵測結果"
                )
                processors_applied += 1

            except Exception as e:
                logger.error(f"處理器 {processor.get_name()} 失敗: {e}")
                # 繼續處理其他處理器
                continue

        # 更新統計數據
        self._stats["total_processed"] += 1
        self._stats["processors_applied"] += processors_applied
        self._stats["processors_skipped"] += processors_skipped

        logger.debug(
            f"管線完成: {len(detections)} -> {len(result)} 個偵測結果, "
            f"{processors_applied} 個處理器已應用, {processors_skipped} 個已跳過"
        )

        return result

    def get_processor_names(self) -> list[str]:
        """按順序取得處理器名稱列表

        :return: 處理器名稱列表
        """
        return [p.get_name() for p in self.processors]

    def get_processor_descriptions(self) -> list[str]:
        """取得處理器描述列表

        :return: 處理器描述列表
        """
        return [p.get_description() for p in self.processors]

    def has_processor(self, name: str) -> bool:
        """按名稱檢查管線中是否有處理器

        :param name: 處理器名稱
        :return: 如果處理器存在則為 True
        """
        return any(p.get_name() == name for p in self.processors)

    def get_processor(self, name: str) -> PostProcessor | None:
        """按名稱取得處理器

        :param name: 處理器名稱
        :return: 處理器實例或 None
        """
        for processor in self.processors:
            if processor.get_name() == name:
                return processor
        return None

    def get_statistics(self) -> dict[str, int]:
        """取得管線統計數據

        :return: 統計字典
        """
        return self._stats.copy()

    def reset_statistics(self):
        """重設管線統計數據"""
        self._stats = {
            "total_processed": 0,
            "processors_applied": 0,
            "processors_skipped": 0,
        }

    def __len__(self) -> int:
        """取得管線中的處理器數量"""
        return len(self.processors)

    def __bool__(self) -> bool:
        """檢查管線中是否有任何處理器"""
        return len(self.processors) > 0

    def __iter__(self):
        """迭代處理器"""
        return iter(self.processors)

    def __str__(self) -> str:
        """管線的字串表示"""
        if not self.processors:
            return "空管線"

        names = self.get_processor_names()
        return f"管線: {' -> '.join(names)}"
