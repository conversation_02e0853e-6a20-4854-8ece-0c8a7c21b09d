"""
Projection Submodule - 投影轉換模組入口

本 __init__.py 檔案是 core.projection 子模組的入口，負責定義其公開 API
並提供一個統一的介面用於全景圖與立方體圖之間的投影轉換。

## 設計理念

- **高度抽象**: 將底層的座標計算和採樣邏輯封裝在 ProjectionCore 之內
- **後端支援**: 支援 CPU 和 GPU 計算後端，並能根據環境智慧選擇
- **工廠模式**: 提供 create_projection_core 工廠函數，方便使用者建立並管理 ProjectionCore 的實例
- **性能**: 結合了快取和 core 子模組的優化特性以實現高性能轉換

## 建議使用模式

```python
from core.projection import create_projection_core
from config import get_config

# 1. 載入設定
config = get_config()

# 2. 使用工廠函數建立一個投影轉換實例
#    工廠函數會自動處理相依性建立
projection_engine = create_projection_core(config)

# 3. 執行投影轉換
# panorama_image = ...
# cube_faces = projection_engine.equirectangular_to_cubemap(panorama_image)
```
"""

# --- 套件資訊 ---
__package__ = "core.projection"
__version__ = "1.0.0"
__author__ = "AI 部門 - 全景處理團隊"
__docformat__ = "restructuredtext"

# --- 主要元件導入 ---
__all__ = []

try:
    from .core import ProjectionCore, create_projection_core
    __all__.extend(["ProjectionCore", "create_projection_core"])
except ImportError as e:
    import warnings
    warnings.warn(
        f"無法從 core.projection.core 導入元件失敗: {e}. "
        " 投影功能將無法使用.",
        ImportWarning
    )

try:
    from .formats import standardize_cube_input, format_cubemap
    __all__.extend(["standardize_cube_input", "format_cubemap"])
except ImportError as e:
    import warnings
    warnings.warn(
        f"無法從 core.projection.formats 導入格式化函數: {e}. "
        "格式化功能將無法使用.",
        ImportWarning
    )

try:
    from .utils import parallel_process
    __all__.extend(["parallel_process"])
except ImportError as e:
    import warnings
    warnings.warn(
        f"無法從 core.projection.utils 導入工具函數: {e}. "
        "並行處理功能將無法使用.",
        ImportWarning
    )

try:
    from .gpu_backends import GPUBackend
    __all__.extend(["GPUBackend"])
except ImportError as e:
    import warnings
    warnings.warn(
        f"無法從 core.projection.gpu_backends 導入GPU後端: {e}. "
        "GPU加速功能將無法使用.",
        ImportWarning
    )