"""
偵測策略基礎類別

定義偵測策略的抽象介面。
"""

from abc import ABC, abstractmethod
from typing import Any

import numpy as np


class DetectionStrategy(ABC):
    """偵測策略的抽象基礎類別"""

    @abstractmethod
    def detect(self, image: np.n<PERSON><PERSON>, models: dict[str, Any], config: Any) -> list:
        """在給定影像上執行偵測

        :param image: 輸入影像 (numpy 陣列)
        :param models: 可用模型的字典
        :param config: 偵測設定
        :return: 偵測到的偵測框列表
        """
        pass

    @abstractmethod
    def get_name(self) -> str:
        """取得用於識別的策略名稱

        :return: 策略名稱字串
        """
        pass

    def can_handle_face(self, face_id: int) -> bool:
        """檢查此策略是否能處理給定的面 ID

        :param face_id: 立方體面 ID (0-5)
        :return: 如果策略能處理此面則為 True
        """
        return True

    def get_description(self) -> str:
        """取得策略的人類可讀描述

        :return: 策略描述
        """
        return f"偵測策略: {self.get_name()}"
