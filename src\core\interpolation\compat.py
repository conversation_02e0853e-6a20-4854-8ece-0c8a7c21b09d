"""
向後兼容層模組

包含所有為保持與舊版本 API 兼容而保留的類和函數。
這些接口在內部調用新的、重構後的核心功能。
"""

import warnings
from typing import Any, Optional

import cv2
import numpy as np
from numpy.typing import NDArray

from config.interpolation import (InterpolationConfig, InterpolationMethod,
                                  MultipleInterpolationConfig,
                                  MultipleInterpolationStrategy)

from .interpolator import AdvancedInterpolator, MultipleInterpolator


class EquirectSampler:
    """全景圖專用採樣器 - 兼容性層"""

    def __init__(self, coor_x: NDArray, coor_y: NDArray, order: int):
        method_map = {
            0: InterpolationMethod.NEAREST,
            1: InterpolationMethod.BILINEAR,
            2: InterpolationMethod.QUADRATIC,
            3: InterpolationMethod.BICUBIC,
            4: InterpolationMethod.QUARTIC,
            5: InterpolationMethod.QUINTIC,
        }
        self.method = method_map.get(order, InterpolationMethod.BILINEAR)
        self.interpolator = create_interpolator(self.method, cache_enabled=False)
        self.coor_x = coor_x
        self.coor_y = coor_y

    def __call__(self, img: NDArray) -> NDArray:
        padded = self._pad_equirect(img)
        adjusted_y = self.coor_y + 1
        adjusted_x = self.coor_x
        return self.interpolator.interpolate(padded, adjusted_x, adjusted_y)

    def _pad_equirect(self, img: NDArray) -> NDArray:
        if img.ndim == 2:
            w = img.shape[1]
            padded = np.pad(img, ((1, 1), (0, 0)), mode="edge")
            padded[0, :] = np.roll(img[0, :], w // 2)
            padded[-1, :] = np.roll(img[-1, :], w // 2)
        else:
            w = img.shape[1]
            padded = np.pad(img, ((1, 1), (0, 0), (0, 0)), mode="edge")
            padded[0, :, :] = np.roll(img[0, :, :], w // 2, axis=0)
            padded[-1, :, :] = np.roll(img[-1, :, :], w // 2, axis=0)
        return padded


class CubeFaceSampler:
    """立方體面專用採樣器 - 兼容性層"""

    def __init__(
        self, tp: NDArray, coor_x: NDArray, coor_y: NDArray, order: int, h: int, w: int
    ):
        method_map = {
            0: InterpolationMethod.NEAREST,
            1: InterpolationMethod.BILINEAR,
            2: InterpolationMethod.QUADRATIC,
            3: InterpolationMethod.BICUBIC,
            4: InterpolationMethod.QUARTIC,
            5: InterpolationMethod.QUINTIC,
        }
        self.method = method_map.get(order, InterpolationMethod.BILINEAR)
        self.interpolator = create_interpolator(self.method, cache_enabled=False)
        self.tp = tp
        self.coor_x = coor_x + 1
        self.coor_y = coor_y + 1
        self.h = h
        self.w = w

    def __call__(self, cube_faces: NDArray) -> NDArray:
        h, w = cube_faces.shape[-2:]
        if h != self.h or w != self.w:
            raise ValueError(f"輸入尺寸不匹配: {(h, w)} vs {(self.h, self.w)}")
        padded = self._pad_cube_faces(cube_faces)
        return self.interpolator.interpolate(padded[self.tp], self.coor_x, self.coor_y)

    def _pad_cube_faces(self, cube_faces: NDArray) -> NDArray:
        if cube_faces.ndim == 3:
            return np.pad(cube_faces, ((0, 0), (1, 1), (1, 1)), mode="edge")
        else:
            return np.pad(cube_faces, ((0, 0), (1, 1), (1, 1), (0, 0)), mode="edge")


class ImageInterpolator:
    """圖像插值器 - 兼容性層"""

    def __init__(self, mode: str = "bilinear", use_gpu: bool = False):
        mode_map = {
            "nearest": InterpolationMethod.NEAREST,
            "linear": InterpolationMethod.LINEAR,
            "bilinear": InterpolationMethod.BILINEAR,
            "bicubic": InterpolationMethod.BICUBIC,
            "quadratic": InterpolationMethod.QUADRATIC,
            "quad": InterpolationMethod.QUADRATIC,
            "cubic": InterpolationMethod.CUBIC,
            "quartic": InterpolationMethod.QUARTIC,
            "biquartic": InterpolationMethod.QUARTIC,
            "quintic": InterpolationMethod.QUINTIC,
            "biquintic": InterpolationMethod.QUINTIC,
        }
        self.mode = mode.lower()
        method = mode_map.get(self.mode, InterpolationMethod.BILINEAR)
        self.interpolator = create_interpolator(
            method=method, use_gpu=use_gpu, cache_enabled=True
        )

    def interpolate(
        self, image: np.ndarray, x: np.ndarray, y: np.ndarray
    ) -> np.ndarray:
        return self.interpolator.interpolate(image, x, y)

    def get_method_info(self) -> dict:
        return {
            "mode": self.mode,
            "method": self.interpolator.config.method.value,
            "description": f"兼容性層包裝的 {self.mode} 插值",
        }


_MODE_TO_ORDER = {
    "nearest": 0,
    "linear": 1,
    "bilinear": 1,
    "quadratic": 2,
    "biquadratic": 2,
    "quad": 2,
    "cubic": 3,
    "bicubic": 3,
    "quartic": 4,
    "biquartic": 4,
    "quintic": 5,
    "biquintic": 5,
}


def mode_to_order(mode: str) -> int:
    try:
        return _MODE_TO_ORDER[mode.lower()]
    except KeyError:
        raise ValueError(f'未知的插值模式 "{mode}"') from None


def create_interpolator(
    method: str | InterpolationMethod,
    use_gpu: bool = False,
    cache_enabled: bool = True,
    **kwargs,
) -> AdvancedInterpolator:
    config = InterpolationConfig(
        method=method, use_gpu=use_gpu, cache_enabled=cache_enabled, **kwargs
    )
    return AdvancedInterpolator(config)


def create_multiple_interpolator(
    methods: list[str | InterpolationMethod],
    strategy: str | MultipleInterpolationStrategy = "sequential",
    weights: Optional[list[float]] = None,
) -> MultipleInterpolator:
    if isinstance(strategy, str):
        strategy = MultipleInterpolationStrategy(strategy.lower())
    config = MultipleInterpolationConfig(
        strategy=strategy, methods=methods, weights=weights
    )
    return MultipleInterpolator(config)
