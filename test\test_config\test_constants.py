"""
Tests for config.constants module
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import <PERSON>Mock, mock_open, patch

import numpy as np
import pytest

from config.constants import (DETECTION_CLASSES, DETECTION_COLORS, FACE_NAMES,
                              SUPPORTED_IMAGE_EXTS, ErrorCode, Face,
                              InterpolationMode, ProcessingStatus, ProcessMode,
                              SaveMode)
from config.settings import PyramidConfig


class TestFaceEnum:
    """Test Face enumeration"""

    def test_face_values(self):
        """Test that Face enum has correct values"""
        assert Face.FRONT == 0
        assert Face.RIGHT == 1
        assert Face.BACK == 2
        assert Face.LEFT == 3
        assert Face.UP == 4
        assert Face.DOWN == 5

    def test_face_names(self):
        """Test that Face enum has correct names"""
        assert Face(0).name == "FRONT"
        assert Face(1).name == "RIGHT"
        assert Face(2).name == "BACK"
        assert Face(3).name == "LEFT"
        assert Face(4).name == "UP"
        assert Face(5).name == "DOWN"


class TestInterpolationMode:
    """Test InterpolationMode enumeration"""

    def test_interpolation_modes_exist(self):
        """Test that all interpolation modes are defined"""
        modes = ["NEAREST", "LINEAR", "CUBIC", "LANCZOS"] # AREA is not a mode
        for mode in modes:
            assert hasattr(InterpolationMode, mode)

    def test_interpolation_mode_values(self):
        """Test interpolation mode string values"""
        assert InterpolationMode.NEAREST.value == "nearest"
        assert InterpolationMode.LINEAR.value == "linear"
        assert InterpolationMode.CUBIC.value == "cubic"


class TestProcessingStatus:
    """Test ProcessingStatus enumeration"""

    def test_processing_status_values(self):
        """Test processing status values"""
        assert ProcessingStatus.PENDING.value == "pending"
        assert ProcessingStatus.PROCESSING.value == "processing"
        assert ProcessingStatus.COMPLETED.value == "completed"
        assert ProcessingStatus.FAILED.value == "failed" # ERROR is now FAILED


class TestSaveMode:
    """Test SaveMode enumeration"""

    def test_save_mode_values(self):
        """Test save mode values"""
        assert SaveMode.ALL.value == "ALL" # Value is now uppercase
        assert SaveMode.BLUR_ONLY.value == "BLUR_ONLY" # CUBES_ONLY and PYRAMID_ONLY are removed


class TestProcessMode:
    """Test ProcessMode enumeration"""

    def test_process_mode_values(self):
        """Test process mode values"""
        assert ProcessMode.PANO_CUBE_DETECT_PYRAMID.value == "pano-cube-detect-pyramid"
        assert ProcessMode.CUBE_DETECT_PYRAMID.value == "cube-detect-pyramid"
        assert ProcessMode.LIST_CUBE_DETECT_PYRAMID.value == "list-cube-detect-pyramid"


class TestErrorCode:
    """Test ErrorCode enumeration"""

    def test_error_code_values(self):
        """Test error code integer values"""
        assert ErrorCode.SUCCESS == 0
        assert ErrorCode.FILE_NOT_FOUND == 1001
        assert ErrorCode.INVALID_FORMAT == 1002
        assert ErrorCode.PROCESSING_ERROR == 3001
        assert ErrorCode.MEMORY_ERROR == 2001
        assert ErrorCode.GPU_ERROR == 2002


# class TestPyramidConfig:
#     """Test PyramidConfig dataclass"""
#
#     def test_pyramid_config_creation(self):
#         """Test PyramidConfig creation with default values"""
#         config = PyramidConfig()
#         assert config.levels == 6
#         assert config.tile_size == 512
#         assert config.overlap == 0
#         assert config.quality == 95
#         assert config.format == "jpg"
#
#     def test_pyramid_config_custom_values(self):
#         """Test PyramidConfig creation with custom values"""
#         config = PyramidConfig(
#             levels=4, tile_size=256, overlap=2, quality=85, format="png"
#         )
#         assert config.levels == 4
#         assert config.tile_size == 256
#         assert config.overlap == 2
#         assert config.quality == 85
#         assert config.format == "png"
#
#     def test_pyramid_config_validation(self):
#         """Test PyramidConfig validation"""
#         # Test valid config
#         config = PyramidConfig(levels=3, tile_size=128)
#         errors = config.validate()
#         assert len(errors) == 0
#
#         # Test invalid config
#         config = PyramidConfig(levels=0, tile_size=0, quality=150)
#         errors = config.validate()
#         assert len(errors) > 0
#         assert any("levels" in error for error in errors)
#         assert any("tile_size" in error for error in errors)
#         assert any("quality" in error for error in errors)


class TestConstants:
    """Test module constants"""

    def test_supported_image_extensions(self):
        """Test supported image extensions list"""
        assert isinstance(SUPPORTED_IMAGE_EXTS, list)
        assert ".jpg" in SUPPORTED_IMAGE_EXTS
        assert ".png" in SUPPORTED_IMAGE_EXTS
        assert ".jpeg" in SUPPORTED_IMAGE_EXTS
        assert len(SUPPORTED_IMAGE_EXTS) > 5

    def test_cube_faces_list(self):
        """Test cube faces list"""
        assert isinstance(FACE_NAMES, list)
        assert len(FACE_NAMES) == 6
        expected_faces = ["F", "R", "B", "L", "U", "D"]
        assert FACE_NAMES == expected_faces

    # def test_default_pyramid_config(self):
    #     """Test default pyramid configuration"""
    #     assert isinstance(DEFAULT_PYRAMID_CONFIG, PyramidConfig)
    #     assert DEFAULT_PYRAMID_CONFIG.levels == 6
    #     assert DEFAULT_PYRAMID_CONFIG.tile_size == 512


# class TestDetectionClassesLoader:
#     """Test detection classes loading functionality"""
#
#     @patch(
#         "builtins.open",
#         new_callable=mock_open,
#         read_data="face\nlicense_plate\nperson\n",
#     )
#     @patch("os.path.exists", return_value=True)
#     def test_load_detection_classes_success(self, mock_exists, mock_file):
#         """Test successful loading of detection classes"""
#         classes = _load_detection_classes()
#         assert isinstance(classes, list)
#         assert "face" in classes
#         assert "license_plate" in classes
#         assert "person" in classes
#
#     @patch("os.path.exists", return_value=False)
#     def test_load_detection_classes_file_not_found(self, mock_exists):
#         """Test loading detection classes when file doesn't exist"""
#         classes = _load_detection_classes()
#         # Should return default classes
#         assert isinstance(classes, list)
#         assert len(classes) > 0
#
#     @patch("builtins.open", side_effect=IOError("File read error"))
#     @patch("os.path.exists", return_value=True)
#     def test_load_detection_classes_read_error(self, mock_exists, mock_file):
#         """Test loading detection classes with read error"""
#         classes = _load_detection_classes()
#         # Should return default classes on error
#         assert isinstance(classes, list)
#         assert len(classes) > 0
#
#
# class TestDetectionColorsGenerator:
#     """Test detection colors generation"""
#
#     def test_generate_detection_colors_basic(self):
#         """Test basic detection colors generation"""
#         classes = ["face", "license_plate", "person"]
#         colors = _generate_detection_colors(classes)
#
#         assert isinstance(colors, dict)
#         assert len(colors) == len(classes)
#         for cls in classes:
#             assert cls in colors
#             assert isinstance(colors[cls], tuple)
#             assert len(colors[cls]) == 3  # RGB tuple
#             assert all(0 <= c <= 255 for c in colors[cls])
#
#     def test_generate_detection_colors_empty_list(self):
#         """Test detection colors generation with empty list"""
#         colors = _generate_detection_colors([])
#         assert isinstance(colors, dict)
#         assert len(colors) == 0
#
#     def test_generate_detection_colors_reproducible(self):
#         """Test that colors are reproducible with same input"""
#         classes = ["face", "license_plate"]
#         colors1 = _generate_detection_colors(classes)
#         colors2 = _generate_detection_colors(classes)
#
#         # Colors should be the same for same input order
#         assert colors1 == colors2


class TestModuleInitialization:
    """Test module initialization and global variables"""

    def test_detection_classes_initialized(self):
        """Test that DETECTION_CLASSES is properly initialized"""
        assert isinstance(DETECTION_CLASSES, list)
        assert len(DETECTION_CLASSES) > 0

    def test_detection_colors_initialized(self):
        """Test that DETECTION_COLORS is properly initialized"""
        assert isinstance(DETECTION_COLORS, dict)
        assert len(DETECTION_COLORS) == len(DETECTION_CLASSES)

        for cls in DETECTION_CLASSES:
            assert cls in DETECTION_COLORS
            assert isinstance(DETECTION_COLORS[cls], tuple)

    def test_constants_are_immutable_types(self):
        """Test that constants use immutable types where appropriate"""
        # Lists should be used for extensible constants
        assert isinstance(FACE_NAMES, list)
        assert isinstance(SUPPORTED_IMAGE_EXTS, list)

        # Check that we can't accidentally modify certain constants
        with pytest.raises((TypeError, AttributeError)):
            Face.FRONT = 10  # Should not be modifiable


class TestEdgeCases:
    """Test edge cases and error conditions"""

    def test_face_enum_invalid_value(self):
        """Test Face enum with invalid value"""
        with pytest.raises(ValueError):
            Face(6)  # Only 0-5 are valid

    def test_pyramid_config_edge_values(self):
        """Test PyramidConfig with edge case values"""
        # Minimum valid values
        config = PyramidConfig(base_size=512, levels={"small": 128})
        # errors = config.validate() # validate method is removed
        # assert len(errors) == 0

        # Maximum valid values
        config = PyramidConfig(base_size=8192, levels={"large": 4096})
        # errors = config.validate()
        # assert len(errors) == 0

    def test_enum_string_representation(self):
        """Test string representation of enums"""
        assert str(Face.FRONT) == "0"
        assert repr(InterpolationMode.LINEAR) == "<InterpolationMode.LINEAR: 'linear'>"


if __name__ == "__main__":
    pytest.main([__file__])


