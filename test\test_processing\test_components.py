#!/usr/bin/env python3
"""
處理組件測試模組
Processing Components Test Module

提供全面的組件級測試，包括DetectionService, CubeService, BatchOptimizer等。
Provides comprehensive component-level testing including DetectionService, CubeService, BatchOptimizer, etc.

Author: <PERSON> Code Assistant  
Date: 2025-01-19
"""

# 1. 標準庫
import hashlib
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 2. 第三方庫
import numpy as np
import pytest

# 3. 本地模組
from processing.components.detection_service import DetectionService, DetectionResult
from processing.components.cube_service import CubeService
from processing.components.batch_optimizer import BatchOptimizer, BatchMetrics
from processing.components.memory_pool import MemoryPool, get_global_memory_pool, allocate_memory
from processing.components.parallel_coordinator import ParallelCoordinator, ProcessingStrategy, MemoryStrategy


class TestDetectionService:
    """統一檢測服務的專項測試"""
    
    @pytest.fixture
    def mock_detectors(self):
        """創建模擬檢測器，解耦測試依賴"""
        primary = Mock()
        secondary = Mock()
        
        # 模擬檢測結果
        mock_result = Mock()
        mock_result.regions = [Mock()]  # 模擬檢測區域
        mock_result.blurred_image = np.zeros((512, 512, 3), dtype=np.uint8)
        
        primary.process_and_get_blurred_regions.return_value = mock_result
        secondary.process_and_get_blurred_regions.return_value = mock_result
        
        return primary, secondary
    
    @pytest.fixture 
    def detection_service(self, mock_detectors):
        """使用模擬檢測器創建服務"""
        primary, secondary = mock_detectors
        return DetectionService(primary, secondary)
    
    @pytest.fixture
    def test_image(self):
        """創建測試圖像"""
        return np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
    
    def test_detection_service_initialization(self, mock_detectors):
        """測試檢測服務初始化"""
        primary, secondary = mock_detectors
        service = DetectionService(primary, secondary)
        
        assert service.primary_detector == primary
        assert service.secondary_detector == secondary
        assert isinstance(service._detection_cache, dict)
        assert len(service._detection_cache) == 0
    
    def test_cache_key_generation(self, detection_service, test_image):
        """測試緩存鍵生成"""
        # 模擬hashlib.sha256
        with patch('processing.components.detection_service.hashlib.sha256') as mock_hash:
            mock_hash.return_value.hexdigest.return_value = "test_hash"
            
            key = detection_service._generate_cache_key(test_image, 0)
            assert key == "test_hash_0"
            mock_hash.assert_called_once()
    
    def test_process_face_caching(self, detection_service, test_image):
        """測試檢測結果緩存功能"""
        # 第一次調用
        result1 = detection_service.process_face(test_image, 0)
        
        # 第二次調用應該使用緩存
        result2 = detection_service.process_face(test_image, 0)
        
        # 驗證返回相同結果
        assert result1 == result2
        
        # 驗證實際檢測器只被調用一次
        assert detection_service.primary_detector.process_and_get_blurred_regions.call_count == 1
    
    def test_process_face_with_primary_only(self, test_image):
        """測試只有主檢測器的情況"""
        primary = Mock()
        mock_result = DetectionResult(
            regions=[Mock()],
            blurred_image=test_image.copy()
        )
        primary.process_and_get_blurred_regions.return_value = mock_result
        
        service = DetectionService(primary, None)
        result = service.process_face(test_image, 0)
        
        assert result is not None
        assert len(result.regions) == 1
        assert result.blurred_image is not None
        primary.process_and_get_blurred_regions.assert_called_once()
    
    def test_process_face_fallback_to_secondary(self, test_image):
        """測試主檢測器失敗時的備用機制"""
        primary = Mock()
        secondary = Mock()
        
        # 設置主檢測器失敗
        primary.process_and_get_blurred_regions.side_effect = Exception("Detection failed")
        
        mock_result = DetectionResult(regions=[], blurred_image=test_image.copy())
        secondary.process_and_get_blurred_regions.return_value = mock_result
        
        service = DetectionService(primary, secondary)
        result = service.process_face(test_image, 0)
        
        # 驗證備用檢測器被調用
        assert secondary.process_and_get_blurred_regions.called
        assert result.blurred_image is not None
    
    def test_batch_process_faces(self, detection_service):
        """測試批次處理功能"""
        test_faces = {
            0: np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
            1: np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
        }
        
        results = detection_service.batch_process_faces(test_faces)
        
        assert len(results) == 2
        assert 0 in results
        assert 1 in results
        assert all(isinstance(result, DetectionResult) for result in results.values())
    
    def test_clear_cache(self, detection_service, test_image):
        """測試緩存清理功能"""
        # 首先添加一些緩存
        detection_service.process_face(test_image, 0)
        assert len(detection_service._detection_cache) > 0
        
        # 清理緩存
        detection_service.clear_cache()
        assert len(detection_service._detection_cache) == 0


class TestCubeService:
    """立方體服務專項測試"""
    
    @pytest.fixture
    def mock_config(self):
        """創建模擬配置"""
        config = Mock()
        config.processing.cube_face_size = 512
        config.processing.interpolation_method = "bilinear"
        config.processing.jpeg_quality = 95
        config.system.enable_gpu_acceleration = False
        return config
    
    @pytest.fixture
    def cube_service(self, mock_config):
        """創建立方體服務實例"""
        with patch('processing.components.cube_service.HAS_CORE', True):
            return CubeService(mock_config)
    
    @pytest.fixture
    def test_panorama(self):
        """創建測試全景圖"""
        return np.random.randint(0, 255, (1024, 2048, 3), dtype=np.uint8)
    
    def test_cube_service_initialization(self, mock_config):
        """測試立方體服務初始化"""
        with patch('processing.components.cube_service.HAS_CORE', True):
            service = CubeService(mock_config)
            assert service.config == mock_config
            assert service.logger is not None
    
    def test_convert_panorama_to_cube(self, cube_service, test_panorama):
        """測試全景圖到立方體轉換"""
        with patch('processing.components.cube_service.ProjectionCore') as mock_projection:
            mock_core = Mock()
            mock_projection.return_value = mock_core
            
            # 模擬轉換結果
            cube_dict = {
                "F": np.zeros((512, 512, 3), dtype=np.uint8),
                "R": np.zeros((512, 512, 3), dtype=np.uint8),
                "B": np.zeros((512, 512, 3), dtype=np.uint8),
                "L": np.zeros((512, 512, 3), dtype=np.uint8),
                "U": np.zeros((512, 512, 3), dtype=np.uint8),
                "D": np.zeros((512, 512, 3), dtype=np.uint8),
            }
            mock_core.equirect_to_cubemap.return_value = cube_dict
            
            result = cube_service.convert_panorama_to_cube(test_panorama)
            
            assert len(result) == 6
            assert all(face_name in result for face_name in ["F", "R", "B", "L", "U", "D"])
            mock_core.equirect_to_cubemap.assert_called_once()
    
    def test_detect_cube_structure(self, cube_service):
        """測試立方體結構檢測"""
        test_dir = Path("/test/directory")
        
        # 目前這個方法返回None（TODO實現）
        result = cube_service.detect_cube_structure(test_dir)
        assert result is None
    
    def test_save_cube_assets(self, cube_service):
        """測試立方體資產保存"""
        cube_dict = {
            "F": np.zeros((512, 512, 3), dtype=np.uint8),
            "R": np.zeros((512, 512, 3), dtype=np.uint8),
        }
        
        with patch('processing.components.cube_service.cv2.imwrite') as mock_imwrite, \
             patch('processing.components.cube_service.Path.mkdir') as mock_mkdir:
            
            cube_service.save_cube_assets(cube_dict, "/test/output")
            
            # 驗證目錄創建
            mock_mkdir.assert_called()
            
            # 驗證圖像保存（2個面 + 預覽相關）
            assert mock_imwrite.call_count >= 2
    
    def test_save_cube_images(self, cube_service):
        """測試立方體圖像保存"""
        cube_dict = {
            "F": np.zeros((512, 512, 3), dtype=np.uint8),
            "R": np.zeros((512, 512, 3), dtype=np.uint8),
            "B": np.zeros((512, 512, 3), dtype=np.uint8),
        }
        
        with patch('processing.components.cube_service.cv2.imwrite') as mock_imwrite, \
             patch('processing.components.cube_service.Path.mkdir') as mock_mkdir:
            
            cube_service.save_cube_images(cube_dict, "/test/output")
            
            # 驗證保存調用
            assert mock_imwrite.call_count == 3  # 3個面
            mock_mkdir.assert_called()


class TestBatchOptimizer:
    """批處理優化器專項測試"""
    
    @pytest.fixture
    def batch_optimizer(self):
        """創建批處理優化器實例"""
        return BatchOptimizer(max_workers=2, cache_size_mb=100)
    
    @pytest.fixture
    def mock_image_paths(self):
        """創建模擬圖像路徑"""
        return [f"image_{i}.jpg" for i in range(5)]
    
    def test_batch_optimizer_initialization(self):
        """測試批處理優化器初始化"""
        optimizer = BatchOptimizer(max_workers=4, cache_size_mb=2048)
        
        assert optimizer.max_workers == 4
        assert optimizer.cache_size_mb == 2048
        assert isinstance(optimizer._image_cache, dict)
        assert isinstance(optimizer._result_cache, dict)
        assert isinstance(optimizer._metrics, BatchMetrics)
    
    @pytest.mark.asyncio
    async def test_batch_process_images(self, batch_optimizer, mock_image_paths):
        """測試批處理圖像功能"""
        # 模擬處理函數
        def mock_process(image_path):
            time.sleep(0.01)  # 模擬處理時間
            return f"processed_{image_path}"
        
        results = await batch_optimizer.batch_process_images(
            mock_image_paths, mock_process, chunk_size=2
        )
        
        assert len(results) == 5
        assert all(f"image_{i}.jpg" in results for i in range(5))
        assert batch_optimizer._metrics.total_images == 5
    
    def test_group_images_by_size_fallback(self, batch_optimizer, mock_image_paths):
        """測試圖像分組（無utils的情況）"""
        with patch('processing.components.batch_optimizer.HAS_UTILS', False):
            groups = batch_optimizer._group_images_by_size(mock_image_paths, 2)
            
            # 應該按chunk_size簡單分組
            assert len(groups) == 3  # 5個圖像，每組2個，共3組
            assert len(groups[0]) == 2
            assert len(groups[1]) == 2
            assert len(groups[2]) == 1
    
    def test_get_metrics(self, batch_optimizer):
        """測試性能指標獲取"""
        metrics = batch_optimizer.get_metrics()
        
        assert isinstance(metrics, BatchMetrics)
        assert metrics.total_images == 0  # 初始狀態
        assert metrics.successful_processed == 0
        assert metrics.failed_processed == 0
    
    def test_cache_functionality(self, batch_optimizer):
        """測試緩存功能"""
        # 添加一些測試數據到緩存
        batch_optimizer._result_cache["test_key"] = "test_result"
        batch_optimizer._metrics.cache_hits = 1
        
        stats = batch_optimizer.get_cache_stats()
        
        assert stats["cached_results"] == 1
        assert stats["cache_hits"] == 1
        assert "hit_rate" in stats
    
    def test_clear_cache(self, batch_optimizer):
        """測試緩存清理"""
        # 添加測試數據
        batch_optimizer._image_cache["test"] = np.zeros((100, 100, 3))
        batch_optimizer._result_cache["test"] = "result"
        
        batch_optimizer.clear_cache()
        
        assert len(batch_optimizer._image_cache) == 0
        assert len(batch_optimizer._result_cache) == 0


class TestMemoryPool:
    """記憶體池專項測試"""
    
    @pytest.fixture
    def memory_pool(self):
        """創建記憶體池實例"""
        return MemoryPool(max_memory_percent=0.5)  # 50% 記憶體限制
    
    def test_memory_pool_initialization(self):
        """測試記憶體池初始化"""
        pool = MemoryPool(max_memory_percent=0.8)
        
        assert pool.max_memory_percent == 0.8
        assert pool.allocated_memory == 0.0
        assert isinstance(pool._allocations, dict)
        assert pool.total_memory_mb > 0
        assert pool.max_memory_mb > 0
    
    def test_memory_allocation_success(self, memory_pool):
        """測試成功的記憶體分配"""
        with memory_pool.allocate(100.0, "test_operation") as allocated:
            assert allocated is True
            assert memory_pool.allocated_memory == 100.0
            assert len(memory_pool._allocations) == 1
        
        # 分配結束後應該釋放
        assert memory_pool.allocated_memory == 0.0
    
    def test_memory_allocation_failure(self, memory_pool):
        """測試記憶體分配失敗"""
        # 嘗試分配超過限制的記憶體
        huge_size = memory_pool.max_memory_mb * 2
        
        with memory_pool.allocate(huge_size, "huge_operation") as allocated:
            # 第一次應該失敗，但清理後可能成功或仍然失敗
            assert isinstance(allocated, bool)
    
    def test_get_current_usage(self, memory_pool):
        """測試記憶體使用統計"""
        usage = memory_pool.get_current_usage()
        
        assert hasattr(usage, 'allocated_mb')
        assert hasattr(usage, 'available_mb')
        assert hasattr(usage, 'total_mb')
        assert usage.allocated_mb >= 0
    
    def test_allocation_summary(self, memory_pool):
        """測試分配摘要"""
        with memory_pool.allocate(50.0, "test_op1"):
            with memory_pool.allocate(30.0, "test_op2"):
                summary = memory_pool.get_allocation_summary()
                
                assert summary["total_active_allocations"] == 2
                assert summary["total_allocated_mb"] == 80.0
                assert "operation_breakdown" in summary
    
    def test_force_cleanup(self, memory_pool):
        """測試強制清理"""
        # 模擬一些分配
        memory_pool.allocated_memory = 100.0
        memory_pool._allocations["test"] = Mock()
        
        memory_pool.force_cleanup()
        
        assert memory_pool.allocated_memory == 0.0
        assert len(memory_pool._allocations) == 0
    
    def test_global_memory_pool(self):
        """測試全局記憶體池"""
        pool1 = get_global_memory_pool()
        pool2 = get_global_memory_pool()
        
        # 應該返回相同實例（單例模式）
        assert pool1 is pool2
    
    def test_allocate_memory_convenience_function(self):
        """測試便捷分配函數"""
        with allocate_memory(10.0, "test_convenience") as allocated:
            assert isinstance(allocated, bool)


class TestParallelCoordinator:
    """並行協調器專項測試"""
    
    @pytest.fixture
    def coordinator(self):
        """創建並行協調器實例"""
        return ParallelCoordinator()
    
    def test_coordinator_initialization(self):
        """測試並行協調器初始化"""
        coordinator = ParallelCoordinator()
        
        assert coordinator.logger is not None
        assert isinstance(coordinator._services_cache, dict)
        assert coordinator._cache_timeout == 30
    
    def test_get_system_resources(self, coordinator):
        """測試系統資源獲取"""
        resources = coordinator.get_system_resources()
        
        assert hasattr(resources, 'cpu_count')
        assert hasattr(resources, 'memory_total_gb')
        assert hasattr(resources, 'gpu_available')
        assert resources.cpu_count > 0
        assert resources.memory_total_gb > 0
    
    def test_optimize_processing_strategy(self, coordinator):
        """測試處理策略優化"""
        config = coordinator.optimize_processing_strategy(
            task_count=10,
            estimated_memory_per_task_mb=100,
            task_complexity="medium"
        )
        
        assert hasattr(config, 'strategy')
        assert hasattr(config, 'max_workers')
        assert hasattr(config, 'batch_size')
        assert hasattr(config, 'memory_limit_gb')
        assert config.max_workers > 0
        assert config.batch_size > 0
        assert config.memory_limit_gb > 0
    
    def test_strategy_performance_recording(self, coordinator):
        """測試策略性能記錄"""
        coordinator.record_strategy_performance(
            ProcessingStrategy.BALANCED, 
            execution_time=2.5,
            success_rate=0.95
        )
        
        assert "balanced" in coordinator._strategy_performance
        assert len(coordinator._strategy_performance["balanced"]) == 1
    
    def test_strategy_recommendations(self, coordinator):
        """測試策略建議"""
        # 添加一些性能數據
        coordinator.record_strategy_performance(ProcessingStrategy.AGGRESSIVE, 1.0, 0.9)
        coordinator.record_strategy_performance(ProcessingStrategy.BALANCED, 2.0, 0.95)
        
        recommendations = coordinator.get_strategy_recommendations()
        
        assert len(recommendations) >= 2
        assert "recommended" in recommendations or len(recommendations) == 0
    
    def test_system_health_monitoring(self, coordinator):
        """測試系統健康監控"""
        health = coordinator.monitor_system_health()
        
        assert "overall" in health
        assert "cpu_load" in health
        assert "memory_usage" in health
        assert "warnings" in health
        assert "resources" in health
        
        assert health["overall"] in ["good", "warning", "critical"]


# 集成測試類
class TestComponentsIntegration:
    """組件集成測試"""
    
    @pytest.fixture
    def integrated_services(self):
        """創建集成服務環境"""
        # 創建模擬配置
        config = Mock()
        config.processing.cube_face_size = 512
        config.processing.enable_detection = True
        config.processing.enable_blurring = True
        config.processing.jpeg_quality = 95
        config.system.enable_gpu_acceleration = False
        
        # 創建服務實例
        services = {}
        
        # 立方體服務
        with patch('processing.components.cube_service.HAS_CORE', True):
            services['cube_service'] = CubeService(config)
        
        # 檢測服務（使用模擬檢測器）
        mock_detector = Mock()
        mock_result = DetectionResult(
            regions=[Mock()],
            blurred_image=np.zeros((512, 512, 3), dtype=np.uint8)
        )
        mock_detector.process_and_get_blurred_regions.return_value = mock_result
        services['detection_service'] = DetectionService(mock_detector, None)
        
        # 批處理優化器
        services['batch_optimizer'] = BatchOptimizer(max_workers=2)
        
        # 記憶體池
        services['memory_pool'] = MemoryPool()
        
        # 並行協調器
        services['parallel_coordinator'] = ParallelCoordinator()
        
        return services
    
    def test_services_interaction(self, integrated_services):
        """測試服務間交互"""
        cube_service = integrated_services['cube_service']
        detection_service = integrated_services['detection_service']
        
        # 創建測試數據
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        cube_faces = {0: test_image, 1: test_image}
        
        # 測試檢測服務處理立方體面
        results = detection_service.batch_process_faces(cube_faces)
        
        assert len(results) == 2
        assert all(isinstance(result, DetectionResult) for result in results.values())
    
    def test_memory_aware_processing(self, integrated_services):
        """測試記憶體感知處理"""
        memory_pool = integrated_services['memory_pool']
        coordinator = integrated_services['parallel_coordinator']
        
        # 獲取系統資源
        resources = coordinator.get_system_resources()
        
        # 測試記憶體分配
        estimated_size = 50.0  # MB
        with memory_pool.allocate(estimated_size, "test_processing") as allocated:
            if allocated:
                # 處理成功分配的情況
                usage = memory_pool.get_current_usage()
                assert usage.allocated_mb >= estimated_size
            else:
                # 處理分配失敗的情況
                assert memory_pool.allocated_memory < estimated_size