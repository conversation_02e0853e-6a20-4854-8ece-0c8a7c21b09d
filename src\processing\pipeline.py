#!/usr/bin/env python3
"""
處理管線
Processing Pipeline

配置驅動的處理管線協調者，通過依賴注入實現鬆耦合架構。
Configuration-driven processing pipeline coordinator with dependency injection for loose coupling architecture.

Author: <PERSON> Code Assistant
Date: 2025-01-19
"""

# 1. 標準庫
import logging
import time
from dataclasses import dataclass, field
from typing import Any, Callable, Protocol

# 2. 第三方庫
import numpy as np

# 3. 本地模組
try:
    from config.settings import Config
    HAS_CONFIG = True
except ImportError:
    HAS_CONFIG = False
    Config = None

from .components.detection_service import DetectionService
from .components.cube_service import CubeService
from .components.batch_optimizer import BatchOptimizer
from .components.parallel_coordinator import ParallelCoordinator

try:
    from detection.core.data_structures import DetectionBox
except ImportError:
    DetectionBox = Any


@dataclass
class ProcessingContext:
    """統一處理上下文，作為管線中數據流的核心載體"""
    # 初始輸入
    input_path: str
    output_path: str
    config: Config
    
    # 中間數據
    panorama_image: np.ndarray | None = None
    cube_faces: dict[int, np.ndarray] = field(default_factory=dict)
    detections: dict[int, list[DetectionBox]] = field(default_factory=dict)
    
    # 處理結果與統計
    is_successful: bool = True
    error_message: str | None = None
    stats: dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    
    def fail(self, message: str, step: str):
        """標記處理失敗並記錄錯誤信息"""
        self.is_successful = False
        self.error_message = message
        self.stats[f"{step}_failed"] = True
        self.stats["error"] = message
        
    def record_stat(self, key: str, value: Any):
        """記錄統計信息"""
        self.stats[key] = value
        
    def get_summary(self) -> dict[str, Any]:
        """獲取處理摘要"""
        return {
            "success": self.is_successful,
            "input_path": self.input_path,
            "output_path": self.output_path,
            "processing_time": self.processing_time,
            "error_message": self.error_message,
            "stats": self.stats
        }


class ProcessingStep(Protocol):
    """定義單個處理步驟的接口協議"""
    def __call__(self, context: ProcessingContext) -> ProcessingContext:
        """執行處理步驟"""
        ...


@dataclass
class ProcessingResult:
    """統一的處理結果類型"""
    success: bool
    message: str
    context: ProcessingContext
    stats: dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def success_result(cls, context: ProcessingContext, message: str = "處理成功") -> "ProcessingResult":
        """創建成功結果"""
        return cls(
            success=True,
            message=message,
            context=context,
            stats=context.stats
        )
    
    @classmethod  
    def failure_result(cls, context: ProcessingContext, message: str) -> "ProcessingResult":
        """創建失敗結果"""
        return cls(
            success=False,
            message=message,
            context=context,
            stats=context.stats
        )


class ProcessingPipeline:
    """
    配置驅動的處理管線協調者。
    
    特性:
    - 依賴注入架構
    - 可配置的處理步驟
    - 統一的錯誤處理
    - 性能監控
    """
    
    def __init__(self, steps: list[ProcessingStep], config: Config):
        """
        初始化處理管線

        Args:
            steps: 一個處理步驟的列表
            config: 配置對象
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.steps = self._filter_steps_by_config(steps)
        
    def _filter_steps_by_config(self, steps: list[ProcessingStep]) -> list[ProcessingStep]:
        """根據配置過濾處理步驟"""
        filtered_steps = []
        
        # 建立一個查找表來快速檢查是否啟用了某個功能
        enabled_features = {
            "detection": getattr(self.config.processing, 'enable_detection', False),
            "blurring": getattr(self.config.processing, 'enable_blurring', False),
        }
        
        for step in steps:
            step_name = step.__class__.__name__.lower()
            
            if "detect" in step_name and not enabled_features["detection"]:
                self.logger.debug(f"跳過步驟 {step.__class__.__name__} 因為 enable_detection 為 False")
                continue
            
            if "blur" in step_name and (not enabled_features["detection"] or not enabled_features["blurring"]):
                self.logger.debug(f"跳過步驟 {step.__class__.__name__} 因為檢測或模糊被禁用")
                continue
                
            filtered_steps.append(step)
            
        self.logger.info(f"根據配置，將執行 {len(filtered_steps)} 個處理步驟。")
        return filtered_steps
    
    def process_panorama(self, input_path: str, output_path: str) -> ProcessingResult:
        """
        通過配置的處理步驟執行管線
        
        Args:
            input_path: 輸入文件路徑
            output_path: 輸出目錄路徑
            
        Returns:
            ProcessingResult: 處理結果
        """
        start_time = time.time()
        
        # 創建處理上下文
        context = ProcessingContext(
            input_path=input_path,
            output_path=output_path,
            config=self.config
        )
        
        self.logger.info(f"開始處理管線: {input_path}")
        
        # 執行處理步驟
        for i, step in enumerate(self.steps):
            step_name = step.__class__.__name__
            self.logger.debug(f"執行步驟 {i+1}/{len(self.steps)}: {step_name}")
            
            try:
                step_start = time.time()
                context = step(context)
                step_time = time.time() - step_start
                
                context.record_stat(f"{step_name}_time", step_time)
                
                if not context.is_successful:
                    self.logger.error(f"步驟 {step_name} 失敗: {context.error_message}")
                    break
                    
            except Exception as e:
                self.logger.error(f"步驟 {step_name} 發生未預期錯誤: {e}", exc_info=True)
                context.fail(f"未預期錯誤: {e}", step=step_name)
                break
        
        # 記錄總處理時間
        context.processing_time = time.time() - start_time
        context.record_stat("total_processing_time", context.processing_time)
        
        # 創建結果
        if context.is_successful:
            result = ProcessingResult.success_result(context)
            self.logger.info(f"處理管線完成: {input_path} (耗時: {context.processing_time:.2f}秒)")
        else:
            result = ProcessingResult.failure_result(context, context.error_message or "處理失敗")
            self.logger.error(f"處理管線失敗: {input_path}")
        
        return result
    
    def process_cube_faces(self, cube_folder: str, output_folder: str) -> ProcessingResult:
        """
        處理立方體面（跳過投影轉換步驟）
        
        Args:
            cube_folder: 立方體面所在文件夾
            output_folder: 輸出文件夾
            
        Returns:
            ProcessingResult: 處理結果
        """
        start_time = time.time()
        
        context = ProcessingContext(
            input_path=cube_folder,
            output_path=output_folder,
            config=self.config
        )
        
        self.logger.info(f"開始處理立方體面: {cube_folder}")
        
        try:
            # 載入立方體面
            step = LoadCubeFacesStep(self.cube_service)
            context = step(context)
            
            if not context.is_successful:
                return ProcessingResult.failure_result(context, "載入立方體面失敗")
            
            # 檢測和模糊（如果啟用）
            if (self.detection_service and 
                getattr(self.config.processing, 'enable_detection', True)):
                
                detect_step = DetectObjectsStep(self.detection_service)
                context = detect_step(context)
                
                if context.is_successful and getattr(self.config.processing, 'enable_blurring', True):
                    blur_step = ApplyBlurStep(self.detection_service)
                    context = blur_step(context)
            
            # 保存結果
            if context.is_successful:
                save_step = SaveResultsStep(self.cube_service)
                context = save_step(context)
            
        except Exception as e:
            self.logger.error(f"處理立方體面時發生錯誤: {e}", exc_info=True)
            context.fail(f"處理錯誤: {e}", step="cube_processing")
        
        context.processing_time = time.time() - start_time
        
        if context.is_successful:
            return ProcessingResult.success_result(context)
        else:
            return ProcessingResult.failure_result(context, context.error_message or "處理失敗")


# ====== 具體的處理步驟實現 ======

class LoadImageStep:
    """載入圖像步驟"""
    def __init__(self, image_cache: Any | None = None):
        self.image_cache = image_cache
        self.logger = logging.getLogger(self.__class__.__name__)

    def __call__(self, context: ProcessingContext) -> ProcessingContext:
        try:
            import cv2
            from pathlib import Path
            
            img_path_str = str(context.input_path)
            
            # 嘗試從緩存加載
            if self.image_cache:
                cached_image = self.image_cache.load_image(img_path_str)
                if cached_image is not None:
                    context.panorama_image = cached_image
                    self.logger.debug(f"從緩存加載圖像: {img_path_str}")
                    return context

            input_path = Path(context.input_path)
            if not input_path.exists():
                context.fail(f"輸入文件不存在: {context.input_path}", "load_image")
                return context
            
            image = cv2.imread(img_path_str)
            if image is None:
                context.fail(f"無法載入圖像: {context.input_path}", "load_image")
                return context
            
            context.panorama_image = image
            context.record_stat("image_loaded", True)
            context.record_stat("image_shape", image.shape)
            
        except Exception as e:
            context.fail(f"載入圖像失敗: {e}", "load_image")
        
        return context


class ConvertToCubeStep:
    """轉換為立方體步驟"""
    
    def __init__(self, cube_service: CubeService):
        self.cube_service = cube_service
    
    def __call__(self, context: ProcessingContext) -> ProcessingContext:
        try:
            if context.panorama_image is None:
                context.fail("沒有可用的全景圖像進行轉換", "convert_to_cube")
                return context
            
            cube_dict = self.cube_service.convert_panorama_to_cube(context.panorama_image)
            
            face_mapping = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}
            for face_name, face_id in face_mapping.items():
                if face_name in cube_dict:
                    context.cube_faces[face_id] = cube_dict[face_name]
            
            context.record_stat("cube_faces_generated", len(context.cube_faces))
            
        except Exception as e:
            context.fail(f"立方體轉換失敗: {e}", "convert_to_cube")
        
        return context


class LoadCubeFacesStep:
    """載入立方體面步驟"""
    
    def __init__(self, cube_service: CubeService):
        self.cube_service = cube_service
    
    def __call__(self, context: ProcessingContext) -> ProcessingContext:
        try:
            import cv2
            from pathlib import Path
            
            cube_folder = Path(context.input_path)
            if not cube_folder.is_dir():
                context.fail(f"立方體路徑不是一個目錄: {context.input_path}", "load_cube_faces")
                return context
            
            loaded_count = 0
            for i in range(6):
                face_file = cube_folder / f"{i}.jpg"
                if face_file.exists():
                    face_image = cv2.imread(str(face_file))
                    if face_image is not None:
                        context.cube_faces[i] = face_image
                        loaded_count += 1
            
            if loaded_count == 0:
                context.fail("沒有找到有效的立方體面文件", "load_cube_faces")
            else:
                context.record_stat("cube_faces_loaded", loaded_count)
            
        except Exception as e:
            context.fail(f"載入立方體面失敗: {e}", "load_cube_faces")
        
        return context


class DetectObjectsStep:
    """檢測物體步驟"""
    
    def __init__(self, detection_service: DetectionService):
        self.detection_service = detection_service
    
    def __call__(self, context: ProcessingContext) -> ProcessingContext:
        try:
            if not context.cube_faces:
                context.record_stat("detection_skipped", "no_cube_faces")
                return context
            
            detection_results = self.detection_service.batch_process_faces(context.cube_faces)
            context.detections = detection_results
            
            total_detections = sum(len(regions) for regions in detection_results.values())
            context.record_stat("total_detections", total_detections)
            context.record_stat("faces_with_detections",
                              sum(1 for regions in detection_results.values() if regions))
            
        except Exception as e:
            context.fail(f"物體檢測失敗: {e}", "detect_objects")
        
        return context


class ApplyBlurStep:
    """應用模糊步驟"""
    
    def __init__(self, blur_service: Any): # 使用 Any 以避免循環導入
        self.blur_service = blur_service
    
    def __call__(self, context: ProcessingContext) -> ProcessingContext:
        try:
            if not context.detections or not any(context.detections.values()):
                context.record_stat("blur_skipped", "no_detections")
                return context
            
            blurred_faces = self.blur_service.batch_blur_regions(
                images=context.cube_faces,
                detection_results=context.detections
            )
            
            context.cube_faces.update(blurred_faces)
            
            blur_count = sum(len(regions) for regions in context.detections.values())
            context.record_stat("blur_applied", True)
            context.record_stat("blur_regions_count", blur_count)
            
        except Exception as e:
            context.fail(f"模糊處理失敗: {e}", "apply_blur")
        
        return context


class ApplyLogoStep:
    """應用Logo步驟"""
    def __init__(self, config: Config):
        import os
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logo_image = None
        if hasattr(self.config, 'io') and hasattr(self.config.io, 'logo_path') and self.config.io.logo_path and os.path.exists(self.config.io.logo_path):
            self.logo_image = cv2.imread(self.config.io.logo_path, cv2.IMREAD_UNCHANGED)
            if self.logo_image is None:
                self.logger.error(f"無法加載Logo圖像: {self.config.io.logo_path}")

    def __call__(self, context: ProcessingContext) -> ProcessingContext:
        if self.logo_image is None or not context.cube_faces:
            return context
        
        try:
            # 假設Logo應用在底面 (face_id 5)
            face_id_to_apply = 5
            if face_id_to_apply in context.cube_faces:
                face_image = context.cube_faces[face_id_to_apply]
                
                # 簡單實現：將logo放在右下角
                logo_h, logo_w = self.logo_image.shape[:2]
                face_h, face_w = face_image.shape[:2]
                
                # 調整logo大小
                scale = min(face_w / logo_w, face_h / logo_h) * 0.2
                resized_logo = cv2.resize(self.logo_image, (0, 0), fx=scale, fy=scale)
                
                rh, rw = resized_logo.shape[:2]
                x_offset = face_w - rw - 20
                y_offset = face_h - rh - 20
                
                # 處理透明度
                if resized_logo.shape[2] == 4:
                    alpha_s = resized_logo[:, :, 3] / 255.0
                    alpha_l = 1.0 - alpha_s
                    for c in range(0, 3):
                        face_image[y_offset:y_offset+rh, x_offset:x_offset+rw, c] = \
                            (alpha_s * resized_logo[:, :, c] +
                             alpha_l * face_image[y_offset:y_offset+rh, x_offset:x_offset+rw, c])
                else:
                    face_image[y_offset:y_offset+rh, x_offset:x_offset+rw] = resized_logo
                
                context.cube_faces[face_id_to_apply] = face_image
                context.record_stat("logo_applied", True)

        except Exception as e:
            context.fail(f"應用Logo失敗: {e}", "apply_logo")
            
        return context


class SaveResultsStep:
    """保存結果步驟"""
    
    def __init__(self, cube_service: CubeService):
        self.cube_service = cube_service
    
    def __call__(self, context: ProcessingContext) -> ProcessingContext:
        try:
            if not context.cube_faces:
                context.fail("沒有可用的立方體面進行保存", "save_results")
                return context
            
            face_mapping = {0: "F", 1: "R", 2: "B", 3: "L", 4: "U", 5: "D"}
            cube_dict = {
                face_mapping[face_id]: face_image
                for face_id, face_image in context.cube_faces.items()
                if face_id in face_mapping
            }
            
            self.cube_service.save_cube_assets(cube_dict, context.output_path)
            
            context.record_stat("results_saved", True)
            context.record_stat("output_path", context.output_path)
            
        except Exception as e:
            context.fail(f"保存結果失敗: {e}", "save_results")
        
        return context