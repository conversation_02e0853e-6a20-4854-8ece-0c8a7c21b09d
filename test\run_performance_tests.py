#!/usr/bin/env python3
"""
Performance Test Runner for the refactored processing and utils modules.

This script executes comprehensive performance tests to validate that the refactored
modules meet the quantified performance targets from requirements:
- Processing speed improvement: 25%+ reduction in end-to-end processing time
- GPU utilization: 80%+ average (up from ~60%)
- Memory usage: 20%+ reduction in peak memory
- Parallel processing: 4x+ parallel task support
- Batch processing: 30%+ reduction in processing time for 100 images
"""

import sys
import os
import time
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# project_root for test directory paths
project_root = Path(__file__).parent.parent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(project_root / "test" / "performance_test.log")
    ]
)
logger = logging.getLogger(__name__)

try:
    from test.test_performance.performance_benchmark import (
        PerformanceTester, PerformanceValidator, PerformanceReportGenerator,
        PerformanceBenchmark
    )
    from utils.factory import UtilsFactory
    from utils.config import UtilsConfig, GPUConfig, MemoryConfig, PerformanceConfig
    from processing.factory import ProcessingFactory
    from processing.config import (
        ProcessingConfig, CubeGenerationConfig, DetectionConfig, 
        BlurConfig, PipelineConfig
    )
    MODULES_AVAILABLE = True
except ImportError as e:
    logger.error(f"Could not import refactored modules: {e}")
    MODULES_AVAILABLE = False


class PerformanceTestSuite:
    """Comprehensive performance test suite for the refactored modules."""
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.output_dir = output_dir or Path("test/performance_results")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.validator = PerformanceValidator()
        self.tester = PerformanceTester(self.validator)
        self.report_generator = PerformanceReportGenerator(self.validator)
        
        self.results: Dict[str, PerformanceBenchmark] = {}
        self.test_configs = self._create_test_configurations()
        
        # Load baseline data if available
        baseline_file = self.output_dir / "baseline_results.json"
        if baseline_file.exists():
            try:
                self.validator.load_baseline(str(baseline_file))
                logger.info(f"Loaded baseline data from {baseline_file}")
            except Exception as e:
                logger.warning(f"Could not load baseline: {e}")
    
    def _create_test_configurations(self) -> Dict[str, tuple]:
        """Create optimized configurations for different test scenarios."""
        # High performance configuration
        high_perf_utils = UtilsConfig(
            gpu=GPUConfig(
                enable_gpu=True,
                memory_limit_gb=6.0,
                enable_memory_monitoring=True
            ),
            memory=MemoryConfig(
                strategy="AGGRESSIVE",
                max_memory_usage_gb=12.0,
                enable_monitoring=True
            ),
            performance=PerformanceConfig(
                enable_monitoring=True,
                metrics_collection_interval=0.1,
                enable_alerts=False
            )
        )
        
        high_perf_processing = ProcessingConfig(
            cube_generation=CubeGenerationConfig(
                cube_size=2048,
                interpolation_method="bilinear",
                enable_caching=True
            ),
            detection=DetectionConfig(
                enable_detection=True,
                confidence_threshold=0.5,
                enable_gpu_acceleration=True,
                enable_face_detection=True,
                enable_plate_detection=True
            ),
            blur=BlurConfig(
                enable_blur=True,
                blur_type="GAUSSIAN",
                blur_intensity=3.0,
                gaussian_kernel_size=9
            ),
            pipeline=PipelineConfig(
                retry_count=1,
                continue_on_error=True,
                enable_async=False
            )
        )
        
        # Memory optimized configuration
        memory_opt_utils = UtilsConfig(
            gpu=GPUConfig(enable_gpu=False),
            memory=MemoryConfig(
                strategy="CONSERVATIVE",
                max_memory_usage_gb=4.0,
                enable_monitoring=True
            ),
            performance=PerformanceConfig(enable_monitoring=True)
        )
        
        memory_opt_processing = ProcessingConfig(
            cube_generation=CubeGenerationConfig(
                cube_size=1024,
                enable_caching=False
            ),
            detection=DetectionConfig(
                enable_detection=True,
                enable_gpu_acceleration=False
            ),
            blur=BlurConfig(
                enable_blur=True,
                blur_type="GAUSSIAN",
                blur_intensity=2.0
            )
        )
        
        # Parallel processing configuration
        parallel_utils = UtilsConfig(
            gpu=GPUConfig(enable_gpu=False),
            memory=MemoryConfig(strategy="BALANCED"),
            performance=PerformanceConfig(enable_monitoring=True)
        )
        
        parallel_processing = ProcessingConfig(
            cube_generation=CubeGenerationConfig(cube_size=512),
            detection=DetectionConfig(enable_detection=False),
            blur=BlurConfig(enable_blur=False),
            pipeline=PipelineConfig(enable_async=False)
        )
        
        return {
            "high_performance": (high_perf_utils, high_perf_processing),
            "memory_optimized": (memory_opt_utils, memory_opt_processing),
            "parallel_optimized": (parallel_utils, parallel_processing)
        }
    
    def run_processing_speed_tests(self) -> Dict[str, PerformanceBenchmark]:
        """Run processing speed improvement tests."""
        logger.info("Running processing speed tests...")
        speed_results = {}
        
        for config_name, (utils_config, processing_config) in self.test_configs.items():
            if config_name == "parallel_optimized":
                continue  # Skip for speed tests
                
            logger.info(f"Testing speed with {config_name} configuration...")
            
            try:
                utils_factory = UtilsFactory(utils_config)
                processing_factory = ProcessingFactory(processing_config, utils_factory)
                
                # Create test data appropriate for speed testing
                test_data = self.tester.create_test_data(
                    num_images=3,
                    image_size=(1024, 2048) if config_name == "high_performance" else (512, 1024)
                )
                
                benchmark = self.tester.benchmark_processing_speed(processing_factory, test_data)
                benchmark.test_name = f"processing_speed_{config_name}"
                
                # Add baseline comparison if available
                baseline_key = f"processing_speed_{config_name}"
                if baseline_key in self.validator.baseline_data:
                    baseline = self.validator.baseline_data[baseline_key]
                    improvement = (baseline.processing_time - benchmark.processing_time) / baseline.processing_time
                    benchmark.baseline_comparison = improvement
                
                speed_results[benchmark.test_name] = benchmark
                
                # Cleanup
                processing_factory.shutdown()
                utils_factory.shutdown()
                
                logger.info(f"Speed test completed: {benchmark.processing_time:.2f}s, "
                           f"throughput: {benchmark.throughput_images_per_sec:.2f} img/s")
                
            except Exception as e:
                logger.error(f"Speed test failed for {config_name}: {e}")
        
        return speed_results
    
    def run_memory_usage_tests(self) -> Dict[str, PerformanceBenchmark]:
        """Run memory usage and optimization tests."""
        logger.info("Running memory usage tests...")
        memory_results = {}
        
        for config_name, (utils_config, processing_config) in self.test_configs.items():
            if config_name == "parallel_optimized":
                continue
                
            logger.info(f"Testing memory usage with {config_name} configuration...")
            
            try:
                utils_factory = UtilsFactory(utils_config)
                processing_factory = ProcessingFactory(processing_config, utils_factory)
                
                # Create test data for memory testing
                test_data = self.tester.create_test_data(
                    num_images=2,
                    image_size=(1024, 2048)
                )
                
                benchmark = self.tester.benchmark_memory_usage(processing_factory, test_data)
                benchmark.test_name = f"memory_usage_{config_name}"
                
                # Add baseline comparison if available
                baseline_key = f"memory_usage_{config_name}"
                if baseline_key in self.validator.baseline_data:
                    baseline = self.validator.baseline_data[baseline_key]
                    reduction = (baseline.memory_usage_mb - benchmark.memory_usage_mb) / baseline.memory_usage_mb
                    benchmark.baseline_comparison = reduction
                
                memory_results[benchmark.test_name] = benchmark
                
                # Cleanup
                processing_factory.shutdown()
                utils_factory.shutdown()
                
                logger.info(f"Memory test completed: {benchmark.memory_usage_mb:.1f}MB peak")
                
            except Exception as e:
                logger.error(f"Memory test failed for {config_name}: {e}")
        
        return memory_results
    
    def run_gpu_utilization_tests(self) -> Dict[str, PerformanceBenchmark]:
        """Run GPU utilization tests."""
        logger.info("Running GPU utilization tests...")
        gpu_results = {}
        
        # Only test GPU with high performance configuration
        utils_config, processing_config = self.test_configs["high_performance"]
        
        try:
            utils_factory = UtilsFactory(utils_config)
            processing_factory = ProcessingFactory(processing_config, utils_factory)
            
            # Create test data optimized for GPU processing
            test_data = self.tester.create_test_data(
                num_images=5,
                image_size=(1024, 2048)
            )
            
            benchmark = self.tester.run_gpu_utilization_test(processing_factory, test_data)
            benchmark.test_name = "gpu_utilization"
            
            gpu_results[benchmark.test_name] = benchmark
            
            # Cleanup
            processing_factory.shutdown()
            utils_factory.shutdown()
            
            logger.info(f"GPU test completed: {benchmark.gpu_utilization:.1f}% utilization")
            
        except Exception as e:
            logger.error(f"GPU test failed: {e}")
        
        return gpu_results
    
    def run_parallel_processing_tests(self) -> Dict[str, PerformanceBenchmark]:
        """Run parallel processing capacity tests."""
        logger.info("Running parallel processing tests...")
        parallel_results = {}
        
        utils_config, processing_config = self.test_configs["parallel_optimized"]
        
        def create_factory():
            utils_factory = UtilsFactory(utils_config)
            return ProcessingFactory(processing_config, utils_factory)
        
        try:
            # Create test data for parallel testing
            test_data = self.tester.create_test_data(
                num_images=12,
                image_size=(256, 512)
            )
            
            benchmark = self.tester.benchmark_parallel_processing(
                create_factory, test_data, max_workers=8
            )
            benchmark.test_name = "parallel_processing"
            
            parallel_results[benchmark.test_name] = benchmark
            
            speedup = benchmark.metadata.get("speedup_factor", 1.0)
            logger.info(f"Parallel test completed: {speedup:.2f}x speedup with 8 workers")
            
        except Exception as e:
            logger.error(f"Parallel test failed: {e}")
        
        return parallel_results
    
    def run_batch_processing_tests(self) -> Dict[str, PerformanceBenchmark]:
        """Run batch processing efficiency tests."""
        logger.info("Running batch processing tests...")
        batch_results = {}
        
        # Test with memory optimized configuration for batch processing
        utils_config, processing_config = self.test_configs["memory_optimized"]
        
        try:
            utils_factory = UtilsFactory(utils_config)
            processing_factory = ProcessingFactory(processing_config, utils_factory)
            
            # Test with smaller batch size for testing environment
            batch_sizes = [10, 25, 50]
            
            for batch_size in batch_sizes:
                benchmark = self.tester.benchmark_batch_processing(processing_factory, batch_size)
                benchmark.test_name = f"batch_processing_{batch_size}"
                
                # Add baseline comparison if available
                baseline_key = f"batch_processing_{batch_size}"
                if baseline_key in self.validator.baseline_data:
                    baseline = self.validator.baseline_data[baseline_key]
                    improvement = (baseline.processing_time - benchmark.processing_time) / baseline.processing_time
                    benchmark.baseline_comparison = improvement
                
                batch_results[benchmark.test_name] = benchmark
                
                avg_time = benchmark.metadata.get("avg_time_per_image", 0)
                logger.info(f"Batch test completed: {batch_size} images in {benchmark.processing_time:.2f}s "
                           f"({avg_time:.3f}s per image)")
            
            # Cleanup
            processing_factory.shutdown()
            utils_factory.shutdown()
            
        except Exception as e:
            logger.error(f"Batch test failed: {e}")
        
        return batch_results
    
    def run_all_tests(self) -> Dict[str, PerformanceBenchmark]:
        """Run the complete performance test suite."""
        logger.info("Starting comprehensive performance test suite...")
        start_time = time.time()
        
        all_results = {}
        
        # Run all test categories
        test_categories = [
            ("Processing Speed", self.run_processing_speed_tests),
            ("Memory Usage", self.run_memory_usage_tests),
            ("GPU Utilization", self.run_gpu_utilization_tests),
            ("Parallel Processing", self.run_parallel_processing_tests),
            ("Batch Processing", self.run_batch_processing_tests)
        ]
        
        for category_name, test_function in test_categories:
            logger.info(f"\n{'='*60}")
            logger.info(f"Running {category_name} Tests")
            logger.info(f"{'='*60}")
            
            try:
                category_results = test_function()
                all_results.update(category_results)
                logger.info(f"{category_name} tests completed: {len(category_results)} benchmarks")
            except Exception as e:
                logger.error(f"{category_name} tests failed: {e}")
        
        total_time = time.time() - start_time
        logger.info(f"\nAll performance tests completed in {total_time:.1f}s")
        logger.info(f"Total benchmarks: {len(all_results)}")
        
        self.results = all_results
        return all_results
    
    def validate_performance_targets(self) -> Dict[str, Dict[str, Any]]:
        """Validate all results against performance targets."""
        logger.info("\nValidating performance targets...")
        
        validations = {}
        
        # 1. Processing speed improvement (25% target)
        speed_benchmarks = [r for name, r in self.results.items() if "processing_speed" in name]
        if speed_benchmarks:
            improvements = [r.baseline_comparison for r in speed_benchmarks if r.baseline_comparison is not None]
            if improvements:
                avg_improvement = sum(improvements) / len(improvements)
                target_met = avg_improvement >= self.validator.PROCESSING_TIME_IMPROVEMENT_TARGET
                validations["processing_time_improvement"] = {
                    "target": f"{self.validator.PROCESSING_TIME_IMPROVEMENT_TARGET:.1%}",
                    "actual": f"{avg_improvement:.1%}",
                    "met": target_met,
                    "status": "PASS" if target_met else "FAIL"
                }
        
        # 2. GPU utilization (80% target)
        gpu_benchmarks = [r for name, r in self.results.items() if "gpu_utilization" in name]
        if gpu_benchmarks:
            avg_gpu = sum(r.gpu_utilization for r in gpu_benchmarks) / len(gpu_benchmarks)
            target_met = self.validator.validate_gpu_utilization(avg_gpu)
            validations["gpu_utilization"] = {
                "target": f"{self.validator.GPU_UTILIZATION_TARGET:.1%}",
                "actual": f"{avg_gpu:.1f}%",
                "met": target_met,
                "status": "PASS" if target_met else "FAIL"
            }
        
        # 3. Memory reduction (20% target)
        memory_benchmarks = [r for name, r in self.results.items() if "memory_usage" in name]
        if memory_benchmarks:
            reductions = [r.baseline_comparison for r in memory_benchmarks if r.baseline_comparison is not None]
            if reductions:
                avg_reduction = sum(reductions) / len(reductions)
                target_met = avg_reduction >= self.validator.MEMORY_REDUCTION_TARGET
                validations["memory_reduction"] = {
                    "target": f"{self.validator.MEMORY_REDUCTION_TARGET:.1%}",
                    "actual": f"{avg_reduction:.1%}",
                    "met": target_met,
                    "status": "PASS" if target_met else "FAIL"
                }
        
        # 4. Parallel processing (4x target)
        parallel_benchmarks = [r for name, r in self.results.items() if "parallel_processing" in name]
        if parallel_benchmarks:
            speedup = parallel_benchmarks[0].metadata.get("speedup_factor", 1.0)
            target_met = speedup >= self.validator.PARALLEL_PROCESSING_MULTIPLIER
            validations["parallel_capacity"] = {
                "target": f"{self.validator.PARALLEL_PROCESSING_MULTIPLIER:.1f}x",
                "actual": f"{speedup:.1f}x",
                "met": target_met,
                "status": "PASS" if target_met else "FAIL"
            }
        
        # 5. Batch processing improvement (30% target)
        batch_benchmarks = [r for name, r in self.results.items() if "batch_processing" in name]
        if batch_benchmarks:
            improvements = [r.baseline_comparison for r in batch_benchmarks if r.baseline_comparison is not None]
            if improvements:
                avg_improvement = sum(improvements) / len(improvements)
                target_met = avg_improvement >= self.validator.BATCH_PROCESSING_IMPROVEMENT
                validations["batch_efficiency"] = {
                    "target": f"{self.validator.BATCH_PROCESSING_IMPROVEMENT:.1%}",
                    "actual": f"{avg_improvement:.1%}",
                    "met": target_met,
                    "status": "PASS" if target_met else "FAIL"
                }
        
        return validations
    
    def generate_comprehensive_report(self) -> str:
        """Generate a comprehensive performance report."""
        if not self.results:
            return "No performance test results available."
        
        validations = self.validate_performance_targets()
        
        # Generate detailed report
        report = self.report_generator.generate_report(self.results)
        
        # Create summary text
        report_lines = [
            "=" * 80,
            "COMPREHENSIVE PERFORMANCE TEST REPORT",
            "=" * 80,
            f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total tests executed: {len(self.results)}",
            "",
            "PERFORMANCE TARGET VALIDATION:",
            "-" * 40
        ]
        
        for target_name, validation in validations.items():
            status_symbol = "?? if validation["met"] else "??
            report_lines.append(
                f"  {status_symbol} {target_name.replace('_', ' ').title()}: "
                f"{validation['actual']} (target: {validation['target']}) - {validation['status']}"
            )
        
        passed_targets = sum(1 for v in validations.values() if v["met"])
        total_targets = len(validations)
        
        report_lines.extend([
            "",
            f"SUMMARY: {passed_targets}/{total_targets} performance targets met",
            "",
            "DETAILED BENCHMARK RESULTS:",
            "-" * 40
        ])
        
        # Group results by category
        categories = {
            "Processing Speed": [r for name, r in self.results.items() if "processing_speed" in name],
            "Memory Usage": [r for name, r in self.results.items() if "memory_usage" in name],
            "GPU Utilization": [r for name, r in self.results.items() if "gpu_utilization" in name],
            "Parallel Processing": [r for name, r in self.results.items() if "parallel_processing" in name],
            "Batch Processing": [r for name, r in self.results.items() if "batch_processing" in name]
        }
        
        for category, benchmarks in categories.items():
            if not benchmarks:
                continue
                
            report_lines.extend([f"", f"{category}:"])
            for benchmark in benchmarks:
                report_lines.extend([
                    f"  Test: {benchmark.test_name}",
                    f"    Processing time: {benchmark.processing_time:.3f}s",
                    f"    Throughput: {benchmark.throughput_images_per_sec:.2f} images/sec",
                    f"    Memory usage: {benchmark.memory_usage_mb:.1f} MB",
                    f"    CPU utilization: {benchmark.cpu_utilization:.1f}%",
                    f"    GPU utilization: {benchmark.gpu_utilization:.1f}%"
                ])
                
                if benchmark.baseline_comparison is not None:
                    comparison = benchmark.baseline_comparison * 100
                    report_lines.append(f"    Baseline comparison: {comparison:+.1f}%")
        
        report_lines.extend([
            "",
            "RECOMMENDATIONS:",
            "-" * 20
        ])
        
        recommendations = report.get("recommendations", [])
        if recommendations:
            for rec in recommendations:
                report_lines.append(f"  ??{rec}")
        else:
            report_lines.append("  ??All performance metrics are within acceptable ranges")
        
        report_lines.extend(["", "=" * 80])
        
        return "\n".join(report_lines)
    
    def save_results(self, format: str = "json") -> Path:
        """Save test results to file."""
        timestamp = int(time.time())
        
        if format.lower() == "json":
            output_file = self.output_dir / f"performance_results_{timestamp}.json"
            
            export_data = {
                "timestamp": timestamp,
                "performance_targets": {
                    "processing_time_improvement": self.validator.PROCESSING_TIME_IMPROVEMENT_TARGET,
                    "gpu_utilization_target": self.validator.GPU_UTILIZATION_TARGET,
                    "memory_reduction_target": self.validator.MEMORY_REDUCTION_TARGET,
                    "parallel_processing_multiplier": self.validator.PARALLEL_PROCESSING_MULTIPLIER,
                    "batch_processing_improvement": self.validator.BATCH_PROCESSING_IMPROVEMENT
                },
                "validations": self.validate_performance_targets(),
                "results": {name: benchmark.to_dict() for name, benchmark in self.results.items()}
            }
            
            with open(output_file, 'w') as f:
                json.dump(export_data, f, indent=2)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        logger.info(f"Results saved to: {output_file}")
        return output_file


def main():
    """Main performance test execution function."""
    parser = argparse.ArgumentParser(
        description="Run comprehensive performance tests for refactored modules",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Performance Targets:
  ??Processing speed improvement: 25%+
  ??GPU utilization: 80%+ average
  ??Memory reduction: 20%+
  ??Parallel processing: 4x capacity
  ??Batch processing: 30%+ improvement

Examples:
  python run_performance_tests.py                    # Run all tests
  python run_performance_tests.py --speed-only       # Only speed tests
  python run_performance_tests.py --save-baseline    # Save as baseline
  python run_performance_tests.py --report-only      # Generate report only
        """
    )
    
    parser.add_argument("--speed-only", action="store_true",
                       help="Run only processing speed tests")
    parser.add_argument("--memory-only", action="store_true",
                       help="Run only memory usage tests")
    parser.add_argument("--gpu-only", action="store_true",
                       help="Run only GPU utilization tests")
    parser.add_argument("--parallel-only", action="store_true",
                       help="Run only parallel processing tests")
    parser.add_argument("--batch-only", action="store_true",
                       help="Run only batch processing tests")
    parser.add_argument("--save-baseline", action="store_true",
                       help="Save results as baseline for future comparisons")
    parser.add_argument("--report-only", action="store_true",
                       help="Generate report from existing results only")
    parser.add_argument("--output-dir", type=str,
                       help="Output directory for results")
    
    args = parser.parse_args()
    
    if not MODULES_AVAILABLE:
        logger.error("Required modules not available. Please ensure refactored modules are properly installed.")
        return 1
    
    # Initialize test suite
    output_dir = Path(args.output_dir) if args.output_dir else None
    test_suite = PerformanceTestSuite(output_dir)
    
    if args.report_only:
        # Generate report from existing results
        logger.info("Generating report from existing results...")
        report = test_suite.generate_comprehensive_report()
        print("\n" + report)
        return 0
    
    # Determine which tests to run
    if any([args.speed_only, args.memory_only, args.gpu_only, args.parallel_only, args.batch_only]):
        results = {}
        if args.speed_only:
            results.update(test_suite.run_processing_speed_tests())
        if args.memory_only:
            results.update(test_suite.run_memory_usage_tests())
        if args.gpu_only:
            results.update(test_suite.run_gpu_utilization_tests())
        if args.parallel_only:
            results.update(test_suite.run_parallel_processing_tests())
        if args.batch_only:
            results.update(test_suite.run_batch_processing_tests())
    else:
        # Run all tests
        results = test_suite.run_all_tests()
    
    if not results:
        logger.error("No test results generated")
        return 1
    
    # Generate and display report
    report = test_suite.generate_comprehensive_report()
    print("\n" + report)
    
    # Save results
    try:
        output_file = test_suite.save_results("json")
        logger.info(f"Detailed results saved to: {output_file}")
        
        # Save as baseline if requested
        if args.save_baseline:
            baseline_file = test_suite.output_dir / "baseline_results.json"
            test_suite.validator.save_baseline(str(baseline_file), test_suite.results)
            logger.info(f"Results saved as baseline: {baseline_file}")
            
    except Exception as e:
        logger.error(f"Failed to save results: {e}")
    
    # Determine exit code based on performance target validation
    validations = test_suite.validate_performance_targets()
    failed_targets = [name for name, result in validations.items() if not result.get("met", False)]
    
    if failed_targets:
        logger.warning(f"Performance targets not met: {', '.join(failed_targets)}")
        return 1
    else:
        logger.info("All performance targets met!")
        return 0


if __name__ == "__main__":
    sys.exit(main())

