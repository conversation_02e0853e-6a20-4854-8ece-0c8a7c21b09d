# General settings
mode: 'pano_to_cube'  # Options: 'pano_to_cube', 'cube_to_cube', 'list_cube'
display_mode: 'quiet' # Options: 'quiet' (progress bar) or 'verbose' (detailed logs)

# --- Unified Path Settings ---
input_path: 'D:\image\1_test_image\pano_img_test'  
primary_output_path: 'D:\image\1_test_image\pano_out'  # 
secondary_output_path: '' #此輸出是由第一個輸出複製來的

# --- Other Settings ---
log_file: 'image_processing.log'
cpu_cores: null # null to auto-detect, or specify a number

# Model and detection settings
models:
  model1_path: 'D:\1_Panoramic_processing\1_final_code\model\best.pt'
  model2_path: 'D:\1_Panoramic_processing\1_final_code\model\yolo12.pt'
detection:
  conf_threshold: 0.05
  save_mode: "ALL"  # 可選值: "ALL", "blur", "blur_and_logo"

# Logo settings
logo:
  path: 'D:\1_Panoramic_processing\給俊雄金字塔\縣市logo\tpv.png'
  scale: 0.741

# --- Mode-specific settings ---
pano_to_cube_settings:
  batch_size: 10

cube_to_cube_settings: {}

list_cube_settings:
  list_file: 'D:\image\1_test_image\final_200\cube.csv' 
  list_mode: 'exclude' # 'include' or 'exclude'
  list_columns: ['區', '場景']

progress_settings:
  enable_check: true  # 檢查 html5 資料夾
  # progress_file: ''  # 可選
  # progress_dir: ''  

db_upload_settings:
  enabled: false
  connection_string: 'your_db_connection_string_here'
  orientation_source: '/path/to/orientation_data.csv'