# ============================================================================
# 現代化全景影像金字塔生成系統配置文件
# 適用於 generate_pyramid_new.py v2.0.0
# ============================================================================

# 處理模式設定
# 可選值: pano_to_cube, cube_to_cube, list_cube
mode: pano_to_cube

# 路徑設定
input_path: "D:/image/1_test_image/pano_img_test"                    # 輸入路徑
primary_output_path: "D:/image/1_test_image/pano_img_test_out"          # 主要輸出路徑
secondary_output_path: ""                # 次要輸出路徑（可選，用於備份）

# 目錄組織模式
organize_by_district: true  # 創建 default 目錄 (推薦)

# 顯示模式設定
# verbose: 詳細模式，顯示完整進度信息
# quiet: 安靜模式，只顯示關鍵信息
display_mode: quiet

# ============================================================================
# AI 檢測設定
# ============================================================================
detection:
  conf_threshold: 0.25                   # 檢測置信度閾值 (0.0-1.0)
  enable_detection: true                 # 是否啟用 AI 檢測
  skip_face_4: true                      # 是否跳過第4面（頂面）
  special_face_5: true                   # 是否對第5面（底面）特殊處理

# 模型設定
models:
  model1_path: "D:/1_Panoramic_processing/1_final_code/model/best.pt"     # 主要檢測模型路徑
  model2_path: "D:/1_Panoramic_processing/1_final_code/model/yolo12.pt"  # 次要檢測模型路徑（可選）

# ============================================================================
# 金字塔生成設定
# ============================================================================
# 金字塔層級因子（決定每層的解析度）
pyramid_factors: [611, 1222, 2445]

# 金字塔品質設定
pyramid_quality: low                    # 可選值: low, medium, high, ultra

# 瓦片設定
tile_settings:
  tile_size: 512                         # 瓦片大小 (像素)
  tile_format: JPEG                      # 瓦片格式: JPEG, PNG, WEBP
  jpeg_quality: 85                       # JPEG 品質 (1-100)

# ============================================================================
# Logo 設定
# ============================================================================
logo:
  path: "./assets/logo.png"              # Logo 圖片路徑
  enable_logo: false                     # 是否啟用 Logo 覆蓋
  position: "bottom_right"               # Logo 位置: bottom_left, bottom_right, top_left, top_right
  scale: 0.1                             # Logo 縮放比例 (0.0-1.0)
  opacity: 0.8                           # Logo 透明度 (0.0-1.0)

# ============================================================================
# 模糊處理設定
# ============================================================================
blur_settings:
  kernel_size: [51, 51]                  # 高斯模糊核心大小
  sigma: 30                              # 高斯模糊標準差
  min_area_ratio: 0.03                   # 最小模糊區域比例
  enable_blur: true                      # 是否啟用模糊處理

# ============================================================================
# 性能設定
# ============================================================================
performance:
  enable_gpu: true                       # 是否啟用 GPU 加速
  max_workers: 4                         # 最大工作線程數
  memory_limit_mb: 8192                  # 記憶體使用限制 (MB)
  enable_threading: true                 # 是否啟用多線程處理

# ============================================================================
# 清單處理模式設定 (僅在 mode: list_cube 時使用)
# ============================================================================
list_cube_settings:
  list_file: "./scene_list.csv"          # 場景清單文件路徑
  list_mode: "include"                   # 清單模式: include (包含), exclude (排除)
  list_columns: ["區", "場景"]            # CSV 文件的欄位名稱
  
# ============================================================================
# 輸出設定
# ============================================================================
output_settings:
  create_preview: true                   # 是否創建預覽圖
  create_thumbnail: true                 # 是否創建縮略圖
  save_original_faces: true              # 是否保存原始立方體面
  compress_output: false                 # 是否壓縮輸出
  
# 預覽圖設定
preview_settings:
  width: 1536                            # 預覽圖寬度
  height: 256                            # 預覽圖高度
  layout: "horizontal"                   # 佈局: horizontal, grid, custom
  quality: 85                            # 預覽圖品質

# 縮略圖設定
thumbnail_settings:
  size: [400, 400]                       # 縮略圖尺寸 [寬, 高]
  quality: 80                            # 縮略圖品質

# ============================================================================
# 日誌設定
# ============================================================================
logging:
  level: INFO                            # 日誌級別: DEBUG, INFO, WARNING, ERROR
  save_to_file: true                     # 是否保存日誌到文件
  log_file: "./logs/pyramid_generation.log"  # 日誌文件路徑
  max_file_size_mb: 100                  # 日誌文件最大大小 (MB)
  backup_count: 5                        # 日誌文件備份數量

# ============================================================================
# 進度追蹤設定
# ============================================================================
progress:
  enable_csv_output: true                # 是否啟用 CSV 進度輸出
  enable_resume: true                    # 是否啟用中斷恢復
  verify_output: true                    # 是否驗證輸出完整性
  save_statistics: true                  # 是否保存處理統計

# ============================================================================
# 高級設定
# ============================================================================
advanced:
  enable_memory_optimization: true       # 是否啟用記憶體優化
  enable_error_recovery: true            # 是否啟用錯誤恢復
  max_retry_attempts: 3                  # 最大重試次數
  timeout_seconds: 300                   # 處理超時時間 (秒)
  
# 調試設定
debug:
  enable_debug_mode: false               # 是否啟用調試模式
  save_intermediate_files: false         # 是否保存中間文件
  verbose_logging: false                 # 是否啟用詳細日誌
  profile_performance: false             # 是否啟用性能分析

# ============================================================================
# 兼容性設定
# ============================================================================
compatibility:
  legacy_mode: false                     # 是否啟用舊版兼容模式
  force_cpu_mode: false                  # 是否強制使用 CPU 模式
  disable_numba: false                   # 是否禁用 Numba 加速
