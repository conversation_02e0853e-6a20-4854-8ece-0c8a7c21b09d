#!/usr/bin/env python3
"""
對 processing 模組中各處理器之間協同工作的整合測試。
"""

import unittest
from unittest.mock import Mock, patch
import numpy as np
from pathlib import Path
import tempfile
import cv2

# Import project modules directly (using pip install -e .)

from processing.panorama_processor import PanoramaProcessor
from processing.scene_processor import SceneProcessor
from processing.components.detection_service import DetectionService, DetectionResult
from processing.components.cube_service import CubeService
from config.settings import Config

class TestProcessorIntegration(unittest.TestCase):

    def setUp(self):
        """設置模擬的服務和配置"""
        self.mock_config = Mock(spec=Config)
        self.mock_config.io.output_path = "/fake/output"
        self.mock_config.io.logo_path = None
        
        self.mock_detection_service = Mock(spec=DetectionService)
        self.mock_cube_service = Mock(spec=CubeService)

        # 創建一個真實的全景處理器，但注入模擬的服務
        self.panorama_processor = PanoramaProcessor(
            config=self.mock_config,
            cube_service=self.mock_cube_service,
            detection_service=self.mock_detection_service
        )

        # 創建一個真實的場景處理器，注入我們上面創建的模擬全景處理器
        self.scene_processor = SceneProcessor(
            config=self.mock_config,
            panorama_processor=self.panorama_processor
        )

        # 準備模擬數據
        self.test_panorama_image = np.zeros((1024, 2048, 3), dtype=np.uint8)
        self.test_cube_dict = {"F": np.zeros((512, 512, 3), dtype=np.uint8)}
        self.mock_detection_result = DetectionResult(
            regions=[],
            blurred_image=np.ones((512, 512, 3), dtype=np.uint8)
        )

    def test_scene_processor_with_panorama(self):
        """測試 SceneProcessor 處理包含全景圖的場景"""
        
        # 設置模擬服務的返回值
        self.mock_cube_service.convert_panorama_to_cube.return_value = self.test_cube_dict
        self.mock_detection_service.batch_process_faces.return_value = {0: self.mock_detection_result}
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 創建一個假的圖像文件
            pano_path = Path(temp_dir) / "pano.jpg"
            cv2.imwrite(str(pano_path), self.test_panorama_image)

            scene_data = {
                "panorama_files": [str(pano_path)],
                "cube_structures": []
            }
            
            # 執行處理
            success = self.scene_processor.process_single_scene("test_district", "test_scene", scene_data)

            # 驗證
            self.assertTrue(success)
            # 驗證 CubeService 的轉換方法被調用
            self.mock_cube_service.convert_panorama_to_cube.assert_called_once()
            # 驗證 DetectionService 的批處理方法被調用
            self.mock_detection_service.batch_process_faces.assert_called_once()
            # 驗證 CubeService 的保存方法被調用
            self.mock_cube_service.save_cube_assets.assert_called_once()

    @patch('processing.panorama_processor.cv2.imread')
    def test_scene_processor_with_cube_structure(self, mock_imread):
        """測試 SceneProcessor 處理已有的立方體結構"""
        mock_imread.return_value = self.test_cube_dict["F"]
        
        # 設置模擬服務的返回值
        self.mock_detection_service.batch_process_faces.return_value = {0: self.mock_detection_result}

        with tempfile.TemporaryDirectory() as temp_dir:
            # 創建假的立方體面文件
            cube_path = Path(temp_dir)
            (cube_path / "0.jpg").touch()

            scene_data = {
                "panorama_files": [],
                "cube_structures": [{"source_dir": str(cube_path)}]
            }

            # 執行處理
            success = self.scene_processor.process_single_scene("test_district", "test_scene", scene_data)

            # 驗證
            self.assertTrue(success)
            # 驗證 CubeService 的轉換方法 *不* 被調用
            self.mock_cube_service.convert_panorama_to_cube.assert_not_called()
            # 驗證 DetectionService 被調用
            self.mock_detection_service.batch_process_faces.assert_called_once()
            # 驗證 CubeService 的保存方法被調用
            self.mock_cube_service.save_cube_assets.assert_called_once()

if __name__ == '__main__':
    unittest.main()