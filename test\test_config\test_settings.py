"""
Tests for config.settings module
"""

import json
import os
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import MagicMock, mock_open, patch

import pytest

from config.constants import ProcessMode
from config.settings import (DEFAULT_CONFIG,  # FAST_CONFIG,; QUALITY_CONFIG,
                             Config, ModelConfig, PathConfig, ProcessingConfig,
                             SystemConfig, generate_timestamped_filename,
                             get_blur_stats_filename, get_config,
                             get_global_timestamp, get_log_filename,
                             get_model_config, get_path_config,
                             get_processing_config, get_progress_filename,
                             get_system_config, load_config, reset_config,
                             reset_global_timestamp, set_config)


class TestProcessMode:
    """Test ProcessMode enumeration"""

    def test_process_mode_values(self):
        """Test process mode values"""
        assert ProcessMode.PANO_CUBE_DETECT_PYRAMID.value == "pano-cube-detect-pyramid"
        assert ProcessMode.CUBE_DETECT_PYRAMID.value == "cube-detect-pyramid"
        assert ProcessMode.LIST_CUBE_DETECT_PYRAMID.value == "list-cube-detect-pyramid"


class TestPathConfig:
    """Test PathConfig dataclass"""

    def test_path_config_defaults(self):
        """Test PathConfig default values"""
        config = PathConfig()
        assert config.input_dir == "./input"
        assert config.output_dir == "./output"
        assert config.model_dir == "./models"
        assert config.logo_dir == "./logos"
        assert config.temp_dir == "./temp"
        assert config.log_dir == "./logs"

    def test_path_config_custom_values(self):
        """Test PathConfig with custom values"""
        config = PathConfig(
            input_dir="/custom/input",
            output_dir="/custom/output",
            model_dir="/custom/models",
            logo_dir="/custom/logos",
            temp_dir="/custom/temp",
            log_dir="/custom/logs",
        )
        assert config.input_dir == "/custom/input"
        assert config.output_dir == "/custom/output"
        assert config.model_dir == "/custom/models"
        assert config.logo_dir == "/custom/logos"
        assert config.temp_dir == "/custom/temp"
        assert config.log_dir == "/custom/logs"

    @patch("os.makedirs")
    def test_path_config_post_init(self, mock_makedirs):
        """Test PathConfig __post_init__ creates directories"""
        config = PathConfig()
        # Should call makedirs for output_dir, temp_dir, and log_dir
        assert mock_makedirs.call_count == 3


class TestModelConfig:
    """Test ModelConfig dataclass"""

    def test_model_config_defaults(self):
        """Test ModelConfig default values"""
        config = ModelConfig()
        assert config.primary_model_path == "models/yolo_face_plate.pt"
        assert config.secondary_model_path is None
        assert config.conf_threshold == 0.05
        assert config.iou_threshold == 0.3
        assert config.max_area_ratio == 0.03
        assert config.face5_test_conf == 0.25
        assert config.device == "auto"
        assert config.use_amp is True

    def test_model_config_custom_values(self):
        """Test ModelConfig with custom values"""
        config = ModelConfig(
            primary_model_path="custom/model.pt",
            secondary_model_path="custom/secondary.pt",
            conf_threshold=0.7,
            iou_threshold=0.5,
            device="cuda",
            use_amp=False,
        )
        assert config.primary_model_path == "custom/model.pt"
        assert config.secondary_model_path == "custom/secondary.pt"
        assert config.conf_threshold == 0.7
        assert config.iou_threshold == 0.5
        assert config.device == "cuda"
        assert config.use_amp is False


class TestProcessingConfig:
    """Test ProcessingConfig dataclass"""

    def test_processing_config_defaults(self):
        """Test ProcessingConfig default values"""
        config = ProcessingConfig()
        assert config.cube_size == 2048
        assert config.slice_size == 512
        assert config.thumbnail_size == (400, 200)
        assert config.preview_size == (1536, 256)
        assert config.image_quality == 95
        assert config.max_workers == 8
        assert config.batch_size == 10
        assert config.max_memory_mb == 8192
        assert config.blur_kernel_size == (51, 51)
        assert config.mosaic_size == 15
        assert config.blur_intensity == 1.0
        assert config.logo_scale == 0.741
        assert config.logo_cache_size == 10


class TestSystemConfig:
    """Test SystemConfig dataclass"""

    def test_system_config_defaults(self):
        """Test SystemConfig default values"""
        config = SystemConfig()
        assert config.log_level == "INFO"
        assert (
            config.log_format == "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        assert config.log_max_size_mb == 10
        assert config.log_backup_count == 5
        assert config.enable_gpu is True
        assert config.enable_cache is True
        assert config.cache_size_mb == 500
        assert config.enable_profiling is False
        assert config.show_progress is True
        assert config.progress_update_interval == 0.5


class TestConfig:
    """Test main Config class"""

    def test_config_defaults(self):
        """Test Config default values"""
        config = Config()
        assert isinstance(config.paths, PathConfig)
        assert isinstance(config.model, ModelConfig)
        assert isinstance(config.processing, ProcessingConfig)
        assert isinstance(config.system, SystemConfig)
        assert config.mode == ProcessMode.PANO_CUBE_DETECT_PYRAMID
        assert isinstance(config.custom_params, dict)
        assert len(config.custom_params) == 0

    def test_config_apply_mode_fast(self):
        """Test apply_mode for FAST mode"""
        config = Config()
        config.mode = ProcessMode.CUBE_DETECT_PYRAMID
        # config.apply_mode() # apply_mode is removed

        # assert config.processing.cube_size == 1024
        # assert config.processing.image_quality == 85
        # assert config.processing.blur_intensity == 0.8
        # assert config.model.conf_threshold == 0.6

    def test_config_apply_mode_quality(self):
        """Test apply_mode for QUALITY mode"""
        config = Config()
        config.mode = ProcessMode.LIST_CUBE_DETECT_PYRAMID
        # config.apply_mode()

        # assert config.processing.cube_size == 4096
        # assert config.processing.image_quality == 100
        # assert config.processing.blur_intensity == 1.2
        # assert config.model.conf_threshold == 0.4

    def test_config_apply_mode_balanced(self):
        """Test apply_mode for BALANCED mode (no changes)"""
        config = Config()
        original_mode = config.mode
        config.mode = ProcessMode.PANO_CUBE_DETECT_PYRAMID
        # config.apply_mode()

        # BALANCED mode should not change values
        assert config.mode == original_mode

    def test_config_save_and_load(self, temp_dir):
        """Test Config save and load functionality"""
        # Create a config with custom values
        config = Config()
        config.mode = ProcessMode.CUBE_DETECT_PYRAMID
        config.processing.cube_size = 4096
        config.model.conf_threshold = 0.3
        config.custom_params = {"test_param": "test_value"}

        # Save config
        config_file = temp_dir / "test_config.json"
        config.save(str(config_file))

        # Verify file was created
        assert config_file.exists()

        # Load config
        loaded_config = Config.load(str(config_file))

        # Verify loaded values
        assert loaded_config.mode == ProcessMode.CUBE_DETECT_PYRAMID
        assert loaded_config.processing.cube_size == 4096
        assert loaded_config.model.conf_threshold == 0.3
        assert loaded_config.custom_params == {"test_param": "test_value"}

    def test_config_update_simple_keys(self):
        """Test Config update with simple keys"""
        config = Config()
        config.update({"mode": ProcessMode.CUBE_DETECT_PYRAMID})
        assert config.mode == ProcessMode.CUBE_DETECT_PYRAMID

    def test_config_update_nested_keys(self):
        """Test Config update with nested keys"""
        config = Config()
        config.update(
            {
                "model.conf_threshold": 0.7,
                "processing.cube_size": 1024,
                "system.enable_gpu": False,
            }
        )

        assert config.model.conf_threshold == 0.7
        assert config.processing.cube_size == 1024
        assert config.system.enable_gpu is False

    def test_config_update_custom_params(self):
        """Test Config update with custom parameters"""
        config = Config()
        config.update({"custom_param": "custom_value"})
        assert config.custom_params["custom_param"] == "custom_value"

    def test_config_validate_success(self):
        """Test Config validation with valid configuration"""
        config = Config()
        errors = config.validate()
        assert len(errors) == 0

    @patch("os.path.exists", return_value=False)
    def test_config_validate_missing_model_dir(self, mock_exists):
        """Test Config validation with missing model directory"""
        config = Config()
        config.paths.model_dir = "/nonexistent/path"
        errors = config.validate()
        assert len(errors) > 0
        assert any("模型目錄不存在" in error for error in errors)

    def test_config_validate_invalid_values(self):
        """Test Config validation with invalid values"""
        config = Config()
        config.model.conf_threshold = 1.5  # Invalid: > 1
        config.processing.cube_size = 2047  # Invalid: not divisible by 4
        config.processing.image_quality = 150  # Invalid: > 100

        errors = config.validate()
        assert len(errors) >= 3
        assert any("置信度閾值" in error for error in errors)
        assert any("立方體尺寸" in error for error in errors)
        assert any("圖像質量" in error for error in errors)


class TestGlobalConfigManagement:
    """Test global configuration management functions"""

    def test_get_config_singleton(self):
        """Test that get_config returns singleton instance"""
        config1 = get_config()
        config2 = get_config()
        assert config1 is config2

    def test_set_config(self):
        """Test set_config functionality"""
        custom_config = Config()
        custom_config.processing.cube_size = 1024

        set_config(custom_config)
        current_config = get_config()

        assert current_config is custom_config
        assert current_config.processing.cube_size == 1024

    def test_reset_config(self):
        """Test reset_config functionality"""
        # Modify global config
        config = get_config()
        config.processing.cube_size = 1024

        # Reset config
        reset_config()

        # Should have new instance with default values
        new_config = get_config()
        assert new_config.processing.cube_size == 2048  # Default value

    def test_load_config_sets_global(self, temp_dir):
        """Test that load_config sets global configuration"""
        # Create config file
        config_data = {"processing": {"cube_size": 1024}, "mode": "cube-detect-pyramid"}
        config_file = temp_dir / "test_config.json"
        with open(config_file, "w") as f:
            json.dump(config_data, f)

        # Load config
        loaded_config = load_config(str(config_file))
        current_config = get_config()

        assert loaded_config is current_config
        assert current_config.processing.cube_size == 1024
        assert current_config.mode == ProcessMode.CUBE_DETECT_PYRAMID

    def test_convenience_functions(self):
        """Test convenience accessor functions"""
        config = get_config()

        assert get_path_config() is config.paths
        assert get_model_config() is config.model
        assert get_processing_config() is config.processing
        assert get_system_config() is config.system


class TestTimestampManagement:
    """Test timestamp management functions"""

    def test_get_global_timestamp_consistent(self):
        """Test that global timestamp is consistent within session"""
        timestamp1 = get_global_timestamp()
        timestamp2 = get_global_timestamp()
        assert timestamp1 == timestamp2

    def test_reset_global_timestamp(self):
        """Test reset_global_timestamp functionality"""
        original_timestamp = get_global_timestamp()

        # Reset timestamp
        new_timestamp = reset_global_timestamp()
        current_timestamp = get_global_timestamp()

        assert new_timestamp == current_timestamp
        # Note: timestamps might be the same if test runs very quickly

    @patch("config.settings.datetime")
    def test_timestamp_format(self, mock_datetime):
        """Test timestamp format"""
        # Mock datetime to return predictable value
        mock_datetime.now.return_value = datetime(2023, 12, 25, 14, 30, 45)

        # Reset to get new timestamp
        from config.settings import reset_global_timestamp

        timestamp = reset_global_timestamp()

        assert timestamp == "20231225_143045"

    def test_generate_timestamped_filename(self):
        """Test timestamped filename generation"""
        filename = generate_timestamped_filename("test", ".txt")
        assert filename.startswith("test_")
        assert filename.endswith(".txt")
        assert len(filename.split("_")) == 3  # test_YYYYMMDD_HHMMSS.txt

    def test_generate_timestamped_filename_no_extension(self):
        """Test timestamped filename without extension"""
        filename = generate_timestamped_filename("test")
        assert filename.startswith("test_")
        assert "." not in filename

    def test_specific_filename_generators(self):
        """Test specific filename generator functions"""
        progress_file = get_progress_filename()
        log_file = get_log_filename()
        blur_stats_file = get_blur_stats_filename()

        assert progress_file.startswith("progress_")
        assert progress_file.endswith(".csv")

        assert log_file.startswith("program_log_")
        assert log_file.endswith(".log")

        assert blur_stats_file.startswith("blur_stats_")
        assert blur_stats_file.endswith(".csv")


class TestPredefinedConfigs:
    """Test predefined configuration templates"""

    def test_default_config(self):
        """Test DEFAULT_CONFIG template"""
        assert isinstance(DEFAULT_CONFIG, Config)
        assert DEFAULT_CONFIG.mode == ProcessMode.PANO_CUBE_DETECT_PYRAMID
        assert DEFAULT_CONFIG.processing.cube_size == 2048

    # def test_fast_config(self):
    #     """Test FAST_CONFIG template"""
    #     assert isinstance(FAST_CONFIG, Config)
    #     assert FAST_CONFIG.mode == ProcessingMode.FAST
    #     assert FAST_CONFIG.processing.cube_size == 1024
    #     assert FAST_CONFIG.processing.image_quality == 85

    # def test_quality_config(self):
    #     """Test QUALITY_CONFIG template"""
    #     assert isinstance(QUALITY_CONFIG, Config)
    #     assert QUALITY_CONFIG.mode == ProcessingMode.QUALITY
    #     assert QUALITY_CONFIG.processing.cube_size == 4096
    #     assert QUALITY_CONFIG.processing.image_quality == 100


class TestEdgeCasesAndErrorHandling:
    """Test edge cases and error handling"""

    def test_config_load_invalid_json(self, temp_dir):
        """Test loading config with invalid JSON"""
        config_file = temp_dir / "invalid.json"
        with open(config_file, "w") as f:
            f.write("invalid json content")

        with pytest.raises(json.JSONDecodeError):
            Config.load(str(config_file))

    def test_config_load_nonexistent_file(self):
        """Test loading nonexistent config file"""
        with pytest.raises(FileNotFoundError):
            Config.load("/nonexistent/file.json")

    def test_config_save_invalid_path(self):
        """Test saving config to invalid path"""
        config = Config()
        with pytest.raises((OSError, IOError)):
            config.save("/nonexistent/directory/config.json")

    def test_config_update_invalid_nested_key(self):
        """Test config update with invalid nested key"""
        config = Config()
        with pytest.raises(AttributeError):
            config.update({"nonexistent.key": "value"})


if __name__ == "__main__":
    pytest.main([__file__])
