"""
座標轉換核心函式 (core.coordinate.numba_kernels)

本模組包含一系列優化的底層數學函式，使用純 NumPy 實現。
這些函式是座標轉換流程中計算最密集的部分，通過向量化運算提升性能。

主要函式：
- uv_to_coor: 將球面座標轉換為 2D 全景圖像素座標
- coor_to_uv: 將 2D 全景圖像素座標轉換為球面座標
- xyz_to_uv: 將 3D 笛卡爾座標轉換為球面座標
- uv_to_xyz: 將球面座標轉換為 3D 笛卡爾座標
- compute_cube_coords_fast: 計算立方體面上的 2D 像素座標
"""

import numpy as np
from numpy.typing import NDArray


def uv_to_coor(u: NDArray, v: NDArray, h: int, w: int) -> tuple[NDArray, NDArray]:
    """
    將球面座標 (u: 經度, v: 緯度) 陣列轉換為 2D 全景圖的像素座標 (x, y) 陣列。
    
    :param u: 經度陣列，範圍為 [-pi, pi]。
    :param v: 緯度陣列，範圍為 [-pi/2, pi/2]。
    :param h: 全景圖的高度。
    :param w: 全景圖的寬度。
    :return: 包含像素座標 x 和 y 的陣列元組。
    """
    coor_x = (u / (2 * np.pi) + 0.5) * w - 0.5
    coor_y = (-v / np.pi + 0.5) * h - 0.5
    return coor_x, coor_y


def coor_to_uv(
    coor_x: NDArray, coor_y: NDArray, h: int, w: int
) -> tuple[NDArray, NDArray]:
    """
    將 2D 全景圖的像素座標 (x, y) 陣列轉換為球面座標 (u: 經度, v: 緯度) 陣列。
    
    :param coor_x: 像素 x 座標陣列。
    :param coor_y: 像素 y 座標陣列。
    :param h: 全景圖的高度。
    :param w: 全景圖的寬度。
    :return: 包含經度 u 和緯度 v 的陣列元組。
    """
    u = (coor_x + 0.5) / w * 2 * np.pi - np.pi
    v = -(coor_y + 0.5) / h * np.pi + np.pi / 2
    return u, v


def xyz_to_uv(xyz: NDArray) -> tuple[NDArray, NDArray]:
    """
    將 3D 笛卡爾座標 (x, y, z) 陣列轉換為球面座標 (u: 經度, v: 緯度) 陣列。
    
    :param xyz: 形狀為 (..., 3) 的 3D 座標陣列。
    :return: 包含經度 u 和緯度 v 的陣列元組。
    """
    x, y, z = xyz[..., 0], xyz[..., 1], xyz[..., 2]
    u = np.arctan2(x, z)  # u: 經度 (longitude)
    c = np.hypot(x, z)   # 水平投影長度
    v = np.arctan2(y, c)  # v: 緯度 (latitude)
    return u, v


def uv_to_xyz(u: NDArray, v: NDArray) -> NDArray:
    """
    將球面座標 (u, v) 陣列轉換為單位球體上的 3D 笛卡爾座標 (x, y, z) 陣列。
    
    :param u: 經度陣列。
    :param v: 緯度陣列。
    :return: 形狀為 (..., 3) 的 3D 座標陣列。
    """
    cos_v = np.cos(v)
    x = cos_v * np.sin(u)
    y = np.sin(v)
    z = cos_v * np.cos(u)
    
    # 預先配置記憶體以提高效率
    xyz = np.empty((*u.shape, 3), dtype=np.float32)
    xyz[..., 0] = x
    xyz[..., 1] = y
    xyz[..., 2] = z
    return xyz


def _fill_cube_faces(
    out: NDArray, x: NDArray, y: NDArray, x_flip: NDArray, y_flip: NDArray, face_w: int
):
    """
    高效地將六個立方體面的 3D 座標填充到一個水平排列的單一陣列中。
    此函式被設計為在 `CoordinateTransformer.generate_cube_coordinates` 中呼叫。
    
    :param out: 用於存放結果的輸出陣列，形狀為 (face_w, face_w * 6, 3)。
    :param x, y: 標準面的網格座標。
    :param x_flip, y_flip: 翻轉後的網格座標，用於計算其他面。
    :param face_w: 每個立方體面的寬度。
    """
    # 根據立方體貼圖的展開規則，為每個面設定其 3D 座標
    # F, R, B, L, U, D 分別代表前、右、後、左、上、下
    out[:, 0 * face_w : 1 * face_w, 0], out[:, 0 * face_w : 1 * face_w, 1], out[:, 0 * face_w : 1 * face_w, 2] = x, y, 0.5  # 前 (Front)
    out[:, 1 * face_w : 2 * face_w, 0], out[:, 1 * face_w : 2 * face_w, 1], out[:, 1 * face_w : 2 * face_w, 2] = 0.5, y, x_flip # 右 (Right)
    out[:, 2 * face_w : 3 * face_w, 0], out[:, 2 * face_w : 3 * face_w, 1], out[:, 2 * face_w : 3 * face_w, 2] = x_flip, y, -0.5 # 後 (Back)
    out[:, 3 * face_w : 4 * face_w, 0], out[:, 3 * face_w : 4 * face_w, 1], out[:, 3 * face_w : 4 * face_w, 2] = -0.5, y, x # 左 (Left)
    out[:, 4 * face_w : 5 * face_w, 0], out[:, 4 * face_w : 5 * face_w, 1], out[:, 4 * face_w : 5 * face_w, 2] = x, 0.5, y_flip # 上 (Up)
    out[:, 5 * face_w : 6 * face_w, 0], out[:, 5 * face_w : 6 * face_w, 1], out[:, 5 * face_w : 6 * face_w, 2] = x, -0.5, y # 下 (Down)


def _create_initial_face_pattern(h: int, w: int) -> NDArray:
    """
    根據全景圖的寬度，建立一個初始的面類型分佈圖案（不包含上下面）。
    
    :param h: 全景圖高度。
    :param w: 全景圖寬度。
    :return: 一個整數陣列，其中值 0-3 代表前、右、後、左四個面。
    """
    face_type = np.empty((h, w), dtype=np.int32)
    w4, w8 = w // 4, w // 8
    # 根據標準的全景圖到立方體圖的投影關係，設定每個區域對應的面索引。
    # 順序: 後(半)、左、前、右、後(半)
    face_type[:, :w8] = 2  # 後 (Back)
    face_type[:, w8 : w8 + w4] = 3  # 左 (Left)
    face_type[:, w8 + w4 : w8 + 2 * w4] = 0  # 前 (Front)
    face_type[:, w8 + 2 * w4 : w8 + 3 * w4] = 1  # 右 (Right)
    face_type[:, w8 + 3 * w4 :] = 2  # 後 (Back)
    return face_type


def _apply_top_bottom_masks(face_type: NDArray, h: int, w: int):
    """
    在面類型圖案上應用頂部和底部的遮罩，以正確標識出上下面（Up/Down）。
    
    :param face_type: 由 `_create_initial_face_pattern` 建立的初始圖案。
    :param h: 全景圖高度。
    :param w: 全景圖寬度。
    """
    w4, w8, h3 = w // 4, w // 8, h // 3
    
    # 預先計算出上下面的非線性邊界曲線
    lng_values = -np.pi + (2 * np.pi * np.arange(w4) / w4)
    idx = (h / 2 - np.arctan(np.cos(lng_values / 4)) * h / np.pi + 0.5).astype(np.int32)
    
    # 使用向量化操作處理頂部和底部區域
    for i in range(h3):
        # 處理邊緣區塊
        mask = i < idx[:w8]
        if np.any(mask):
            face_type[i, :w8][mask] = 4
            face_type[h - 1 - i, :w8][mask] = 5
        
        # 處理中間的主要區塊
        for k in range(3):
            start = w8 + k * w4
            mask = i < idx
            if np.any(mask):
                face_type[i, start:start + w4][mask] = 4
                face_type[h - 1 - i, start:start + w4][mask] = 5
        
        # 處理剩餘部分
        remainder = w - w8 - 3 * w4
        if remainder > 0:
            mask = i < idx[:remainder]
            if np.any(mask):
                face_type[i, w8 + 3 * w4:][mask] = 4
                face_type[h - 1 - i, w8 + 3 * w4:][mask] = 5


def compute_cube_coords_fast(
    u: NDArray, v: NDArray, face_type: NDArray, face_w: int
) -> tuple[NDArray, NDArray]:
    """
    根據球面座標 (u, v) 和每個點對應的面類型，高效地計算出其在立方體面上的 2D 像素座標。
    
    :param u: 經度陣列。
    :param v: 緯度陣列。
    :param face_type: 每個點對應的面索引陣列。
    :param face_w: 立方體面的寬度。
    :return: 一個包含立方體面 x 和 y 像素座標的陣列元組。
    """
    h, w = u.shape
    coor_x, coor_y = np.zeros_like(u), np.zeros_like(v)
    face_w2 = face_w / 2.0

    # 處理前、右、後、左四個側面 (face_type < 4)
    side_mask = face_type < 4
    if np.any(side_mask):
        u_side = u[side_mask]
        v_side = v[side_mask]
        ft_side = face_type[side_mask]
        
        angle = u_side - (np.pi / 2 * ft_side)
        cos_angle = np.cos(angle)
        
        # 避免除以零
        valid_mask = np.abs(cos_angle) > 1e-6
        if np.any(valid_mask):
            angle_valid = angle[valid_mask]
            cos_angle_valid = cos_angle[valid_mask]
            v_valid = v_side[valid_mask]
            
            x_coords = face_w2 * np.tan(angle_valid)
            y_coords = -face_w2 * np.tan(v_valid) / cos_angle_valid
            
            # 創建臨時陣列來存放結果
            temp_x, temp_y = np.zeros_like(u_side), np.zeros_like(v_side)
            temp_x[valid_mask] = x_coords
            temp_y[valid_mask] = y_coords
            
            coor_x[side_mask] = temp_x
            coor_y[side_mask] = temp_y

    # 處理上面 (face_type == 4)
    up_mask = face_type == 4
    if np.any(up_mask):
        u_up = u[up_mask]
        v_up = v[up_mask]
        c = face_w2 * np.tan(np.pi / 2 - v_up)
        coor_x[up_mask] = c * np.sin(u_up)
        coor_y[up_mask] = c * np.cos(u_up)

    # 處理下面 (face_type == 5)
    down_mask = face_type == 5
    if np.any(down_mask):
        u_down = u[down_mask]
        v_down = v[down_mask]
        c = face_w2 * np.tan(np.pi / 2 - np.abs(v_down))
        coor_x[down_mask] = c * np.sin(u_down)
        coor_y[down_mask] = -c * np.cos(u_down)

    # 將 [-face_w/2, face_w/2] 的座標範圍轉換到 [0, face_w]
    coor_x += face_w2
    coor_y += face_w2

    return coor_x, coor_y