"""
統計收集工具

收集並分析偵測統計數據，用於監控和優化。
"""

import logging
import time
from typing import Any, Dict, List, TypedDict, Union

from log_utils.factory import get_logger

logger = get_logger(__name__)


class FaceStats(TypedDict):
    count: int
    detections: int
    time: float


class BatchStats(TypedDict):
    total_batches: int
    total_faces_in_batches: int
    total_batch_time: float


class ConfidenceStats(TypedDict):
    min: float
    max: float
    sum: float
    count: int


class FaceSummary(TypedDict, total=False):
    processed_count: int
    total_detections: int
    average_detections: float
    total_time: float
    average_time: float


class Stats(TypedDict, total=False):
    total_faces_processed: int
    total_detections: int
    total_processing_time: float
    face_stats: Dict[int, FaceStats]
    batch_stats: BatchStats
    detection_by_class: Dict[int, int]
    confidence_stats: ConfidenceStats
    average_detections_per_face: float
    average_processing_time_per_face: float
    average_confidence: float
    average_batch_time: float
    average_faces_per_batch: float
    face_summary: Dict[int, FaceSummary]


class StatisticsCollector:
    """收集並管理偵測統計數據"""

    def __init__(self):
        self.reset_statistics()

    def reset_statistics(self):
        """重設所有統計數據"""
        self._stats: Stats = {
            "total_faces_processed": 0,
            "total_detections": 0,
            "total_processing_time": 0.0,
            "face_stats": {},  # face_id -> {count, detections, time}
            "batch_stats": {
                "total_batches": 0,
                "total_faces_in_batches": 0,
                "total_batch_time": 0.0,
            },
            "detection_by_class": {0: 0, 1: 0},  # 0: 人臉, 1: 車牌
            "confidence_stats": {
                "min": float("inf"),
                "max": 0.0,
                "sum": 0.0,
                "count": 0,
            },
        }

    def record_detection(
        self, face_id: int, detection_count: int, processing_time: float
    ):
        """記錄單一面的偵測統計數據

        :param face_id: 面 ID (0-5)
        :param detection_count: 偵測到的數量
        :param processing_time: 處理時間 (秒)
        """
        # 更新整體統計
        self._stats["total_faces_processed"] += 1
        self._stats["total_detections"] += detection_count
        self._stats["total_processing_time"] += processing_time

        # 更新特定面的統計
        if face_id not in self._stats["face_stats"]:
            self._stats["face_stats"][face_id] = {
                "count": 0,
                "detections": 0,
                "time": 0.0,
            }

        face_stats = self._stats["face_stats"][face_id]
        face_stats["count"] += 1
        face_stats["detections"] += detection_count
        face_stats["time"] += processing_time

    def record_batch(self, face_count: int, total_regions: int, processing_time: float):
        """記錄批次處理的統計數據

        :param face_count: 批次中的面數
        :param total_regions: 建立的總模糊區域數
        :param processing_time: 總批次處理時間
        """
        batch_stats = self._stats["batch_stats"]
        batch_stats["total_batches"] += 1
        batch_stats["total_faces_in_batches"] += face_count
        batch_stats["total_batch_time"] += processing_time

    def record_detection_details(self, detections: List[Any], face_id: int):
        """記錄詳細的偵測資訊

        :param detections: 偵測框列表
        :param face_id: 面 ID
        """
        for detection in detections:
            # 更新類別統計
            class_id = int(getattr(detection, "class_id", 0))
            if class_id in self._stats["detection_by_class"]:
                self._stats["detection_by_class"][class_id] += 1

            # 更新信賴度統計
            confidence = float(getattr(detection, "confidence", 0.0))
            conf_stats = self._stats["confidence_stats"]
            conf_stats["min"] = min(conf_stats["min"], confidence)
            conf_stats["max"] = max(conf_stats["max"], confidence)
            conf_stats["sum"] += confidence
            conf_stats["count"] += 1

    def analyze_batch_results(
        self, batch_results: Dict[int, Dict[str, Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """分析批次結果並回傳統計數據

        :param batch_results: 批次處理結果
        :return: 分析結果
        """
        stats: Dict[str, Any] = {
            "total_faces": len(batch_results),
            "total_blur_regions": 0,
            "regions_by_face": {},
            "detection_by_class": {0: 0, 1: 0},
            "average_confidence": 0.0,
            "processed_faces": 0,
            "skipped_faces": 0,
            "face_processing_summary": {},
        }

        all_confidences = []

        for face_id, blur_regions in batch_results.items():
            region_count = len(blur_regions)
            stats["regions_by_face"][face_id] = region_count
            stats["total_blur_regions"] += region_count

            # 分類面
            if region_count == 0 and face_id == 4:
                stats["skipped_faces"] += 1
                stats["face_processing_summary"][face_id] = "已跳過 (天空面)"
            else:
                stats["processed_faces"] += 1
                stats["face_processing_summary"][face_id] = f"{region_count} 個區域"

            # 分析偵測細節
            for region_name, region_data in blur_regions.items():
                if "box" in region_data and len(region_data["box"]) >= 6:
                    box = region_data["box"]
                    confidence = box[4]
                    class_id = box[5]

                    if class_id in stats["detection_by_class"]:
                        stats["detection_by_class"][int(class_id)] += 1

                    all_confidences.append(confidence)

        # 計算平均信賴度
        if all_confidences:
            stats["average_confidence"] = sum(all_confidences) / len(all_confidences)
            stats["confidence_range"] = {
                "min": min(all_confidences),
                "max": max(all_confidences),
            }

        return stats

    def get_statistics(self) -> Dict[str, Any]:
        """取得全面的統計數據

        :return: 統計字典
        """
        stats: Stats = self._stats.copy()

        # 計算衍生統計數據
        if stats["total_faces_processed"] > 0:
            stats["average_detections_per_face"] = (
                stats["total_detections"] / stats["total_faces_processed"]
            )
            stats["average_processing_time_per_face"] = (
                stats["total_processing_time"] / stats["total_faces_processed"]
            )
        else:
            stats["average_detections_per_face"] = 0.0
            stats["average_processing_time_per_face"] = 0.0

        # 計算信賴度統計
        conf_stats = stats["confidence_stats"]
        if conf_stats["count"] > 0:
            stats["average_confidence"] = conf_stats["sum"] / conf_stats["count"]
        else:
            stats["average_confidence"] = 0.0
            conf_stats["min"] = 0.0

        # 計算批次統計
        batch_stats = stats["batch_stats"]
        if batch_stats["total_batches"] > 0:
            stats["average_batch_time"] = (
                batch_stats["total_batch_time"] / batch_stats["total_batches"]
            )
            stats["average_faces_per_batch"] = (
                batch_stats["total_faces_in_batches"] / batch_stats["total_batches"]
            )
        else:
            stats["average_batch_time"] = 0.0
            stats["average_faces_per_batch"] = 0.0

        # 特定面的統計
        face_summary: Dict[int, FaceSummary] = {}
        for face_id, face_data in stats["face_stats"].items():
            if face_data["count"] > 0:
                face_summary[face_id] = {
                    "processed_count": face_data["count"],
                    "total_detections": face_data["detections"],
                    "average_detections": face_data["detections"] / face_data["count"],
                    "total_time": face_data["time"],
                    "average_time": face_data["time"] / face_data["count"],
                }

        stats["face_summary"] = face_summary

        return stats

    def get_performance_summary(self) -> dict[str, Any]:
        """取得以效能為重點的摘要

        :return: 效能摘要
        """
        stats = self.get_statistics()

        return {
            "total_faces_processed": stats["total_faces_processed"],
            "total_processing_time": stats["total_processing_time"],
            "average_time_per_face": stats["average_processing_time_per_face"],
            "detections_per_second": (
                stats["total_detections"] / stats["total_processing_time"]
                if stats["total_processing_time"] > 0
                else 0
            ),
            "faces_per_second": (
                stats["total_faces_processed"] / stats["total_processing_time"]
                if stats["total_processing_time"] > 0
                else 0
            ),
            "average_detections_per_face": stats["average_detections_per_face"],
            "batch_performance": {
                "total_batches": stats["batch_stats"]["total_batches"],
                "average_batch_time": stats.get("average_batch_time", 0),
                "average_faces_per_batch": stats.get("average_faces_per_batch", 0),
            },
        }

    def export_statistics(self, format: str = "dict") -> Union[Dict[str, Any], str]:
        """以指定格式匯出統計數據

        :param format: 匯出格式 ('dict' 或 'json')
        :return: 所需格式的統計數據
        """
        stats = self.get_statistics()

        if format == "dict":
            return stats
        elif format == "json":
            import json

            return json.dumps(stats, indent=2, default=str)
        else:
            raise ValueError(f"不支援的格式: {format}")

    def log_summary(self, level: int = logging.INFO):
        """記錄統計摘要

        :param level: 日誌記錄等級
        """
        if not logger:
            return

        perf_summary = self.get_performance_summary()

        logger.log(level, "=== 偵測統計摘要 ===")
        logger.log(
            level, f"總共處理的面數: {perf_summary['total_faces_processed']}"
        )
        logger.log(
            level,
            f"總處理時間: {perf_summary['total_processing_time']:.2f}s",
        )
        logger.log(
            level,
            f"平均每面時間: {perf_summary['average_time_per_face']:.3f}s",
        )
        logger.log(level, f"每秒處理面數: {perf_summary['faces_per_second']:.2f}")
        logger.log(
            level,
            f"平均每面偵測數: {perf_summary['average_detections_per_face']:.2f}",
        )
        logger.log(
            level,
            f"總批次數: {perf_summary['batch_performance']['total_batches']}",
        )
        logger.log(level, "=================================")
