"""
統一快取管理器模組
提供各種快取策略的統一實現
"""
import asyncio
import logging
import pickle
import threading
import time
import weakref
from abc import ABC, abstractmethod
from collections import OrderedDict
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps
from typing import Any, Callable, Generic, TypeVar, Protocol
import hashlib

logger = logging.getLogger(__name__)

T = TypeVar('T')
K = TypeVar('K')
V = TypeVar('V')


class CacheStrategy(Enum):
    """快取策略"""
    LRU = "lru"  # 最少最近使用
    LFU = "lfu"  # 最少頻率使用
    FIFO = "fifo"  # 先進先出
    TTL = "ttl"  # 生存時間
    WEAK = "weak"  # 弱引用


class EvictionPolicy(Enum):
    """驅逐策略"""
    SIZE_BASED = "size_based"
    TIME_BASED = "time_based"
    MEMORY_BASED = "memory_based"
    MANUAL = "manual"


@dataclass
class CacheConfig:
    """快取配置"""
    strategy: CacheStrategy = CacheStrategy.LRU
    max_size: int = 1000
    ttl_seconds: float = 3600.0  # 1小時
    cleanup_interval: float = 300.0  # 5分鐘
    eviction_policy: EvictionPolicy = EvictionPolicy.SIZE_BASED
    enable_stats: bool = True
    thread_safe: bool = True
    enable_persistence: bool = False
    persistence_file: str | None = None


@dataclass
class CacheStats:
    """快取統計"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    size: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return (self.hits / total) if total > 0 else 0.0
    
    @property
    def miss_rate(self) -> float:
        """失誤率"""
        return 1.0 - self.hit_rate
    
    def reset(self):
        """重置統計"""
        self.hits = 0
        self.misses = 0
        self.sets = 0
        self.deletes = 0
        self.evictions = 0


@dataclass
class CacheEntry:
    """快取項目"""
    key: Any
    value: Any
    timestamp: float = field(default_factory=time.time)
    access_count: int = 0
    last_access: float = field(default_factory=time.time)
    ttl: float | None = None
    
    def is_expired(self) -> bool:
        """檢查是否過期"""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl
    
    def touch(self):
        """更新訪問時間和次數"""
        self.last_access = time.time()
        self.access_count += 1


class CacheBackend(ABC, Generic[K, V]):
    """快取後端抽象類"""
    
    @abstractmethod
    def get(self, key: K) -> V | None:
        """獲取值"""
        pass
    
    @abstractmethod
    def set(self, key: K, value: V, ttl: float | None = None) -> bool:
        """設置值"""
        pass
    
    @abstractmethod
    def delete(self, key: K) -> bool:
        """刪除值"""
        pass
    
    @abstractmethod
    def clear(self):
        """清空快取"""
        pass
    
    @abstractmethod
    def size(self) -> int:
        """獲取大小"""
        pass
    
    @abstractmethod
    def keys(self) -> list[K]:
        """獲取所有鍵"""
        pass


class LRUCache(CacheBackend[K, V]):
    """LRU快取實現"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self._cache: OrderedDict[K, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
    
    def get(self, key: K) -> V | None:
        with self._lock:
            if key not in self._cache:
                return None
            
            entry = self._cache[key]
            if entry.is_expired():
                del self._cache[key]
                return None
            
            # 移動到末尾（最近使用）
            self._cache.move_to_end(key)
            entry.touch()
            return entry.value
    
    def set(self, key: K, value: V, ttl: float | None = None) -> bool:
        with self._lock:
            try:
                entry = CacheEntry(key=key, value=value, ttl=ttl)
                
                if key in self._cache:
                    # 更新現有項目
                    self._cache[key] = entry
                    self._cache.move_to_end(key)
                else:
                    # 新增項目
                    if len(self._cache) >= self.max_size:
                        # 驅逐最舊的項目
                        self._cache.popitem(last=False)
                    self._cache[key] = entry
                
                return True
            except Exception as e:
                logger.error(f"設置快取項目失敗: {e}")
                return False
    
    def delete(self, key: K) -> bool:
        with self._lock:
            return self._cache.pop(key, None) is not None
    
    def clear(self):
        with self._lock:
            self._cache.clear()
    
    def size(self) -> int:
        with self._lock:
            return len(self._cache)
    
    def keys(self) -> list[K]:
        with self._lock:
            return list(self._cache.keys())


class TTLCache(CacheBackend[K, V]):
    """TTL快取實現"""
    
    def __init__(self, default_ttl: float = 3600.0):
        self.default_ttl = default_ttl
        self._cache: dict[K, CacheEntry] = {}
        self._lock = threading.RLock()
        self._cleanup_thread = None
        self._stop_cleanup = threading.Event()
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self):
        """啟動清理線程"""
        if self._cleanup_thread is None or not self._cleanup_thread.is_alive():
            self._cleanup_thread = threading.Thread(
                target=self._cleanup_expired,
                daemon=True
            )
            self._cleanup_thread.start()
    
    def _cleanup_expired(self):
        """清理過期項目"""
        while not self._stop_cleanup.wait(60):  # 每分鐘清理一次
            with self._lock:
                expired_keys = []
                for key, entry in self._cache.items():
                    if entry.is_expired():
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self._cache[key]
    
    def get(self, key: K) -> V | None:
        with self._lock:
            if key not in self._cache:
                return None
            
            entry = self._cache[key]
            if entry.is_expired():
                del self._cache[key]
                return None
            
            entry.touch()
            return entry.value
    
    def set(self, key: K, value: V, ttl: float | None = None) -> bool:
        with self._lock:
            try:
                actual_ttl = ttl if ttl is not None else self.default_ttl
                entry = CacheEntry(key=key, value=value, ttl=actual_ttl)
                self._cache[key] = entry
                return True
            except Exception as e:
                logger.error(f"設置快取項目失敗: {e}")
                return False
    
    def delete(self, key: K) -> bool:
        with self._lock:
            return self._cache.pop(key, None) is not None
    
    def clear(self):
        with self._lock:
            self._cache.clear()
    
    def size(self) -> int:
        with self._lock:
            return len(self._cache)
    
    def keys(self) -> list[K]:
        with self._lock:
            return list(self._cache.keys())
    
    def __del__(self):
        """析構時停止清理線程"""
        self._stop_cleanup.set()


class WeakCache(CacheBackend[K, V]):
    """弱引用快取實現"""
    
    def __init__(self):
        self._cache: weakref.WeakValueDictionary = weakref.WeakValueDictionary()
        self._lock = threading.RLock()
    
    def get(self, key: K) -> V | None:
        with self._lock:
            return self._cache.get(key)
    
    def set(self, key: K, value: V, ttl: float | None = None) -> bool:
        with self._lock:
            try:
                self._cache[key] = value
                return True
            except TypeError:
                # 值不支援弱引用
                logger.warning(f"值不支援弱引用: {type(value)}")
                return False
    
    def delete(self, key: K) -> bool:
        with self._lock:
            try:
                del self._cache[key]
                return True
            except KeyError:
                return False
    
    def clear(self):
        with self._lock:
            self._cache.clear()
    
    def size(self) -> int:
        with self._lock:
            return len(self._cache)
    
    def keys(self) -> list[K]:
        with self._lock:
            return list(self._cache.keys())


class CacheManager:
    """統一快取管理器"""
    
    def __init__(self, config: CacheConfig | None = None):
        """
        初始化快取管理器
        
        Args:
            config: 快取配置
        """
        self.config = config or CacheConfig()
        self.stats = CacheStats() if self.config.enable_stats else None
        self._backend = self._create_backend()
        self._lock = threading.RLock() if self.config.thread_safe else None
        
        if self.config.enable_persistence and self.config.persistence_file:
            self._load_from_file()
    
    def _create_backend(self) -> CacheBackend:
        """創建快取後端"""
        if self.config.strategy == CacheStrategy.LRU:
            return LRUCache(self.config.max_size)
        elif self.config.strategy == CacheStrategy.TTL:
            return TTLCache(self.config.ttl_seconds)
        elif self.config.strategy == CacheStrategy.WEAK:
            return WeakCache()
        else:
            raise ValueError(f"不支援的快取策略: {self.config.strategy}")
    
    def _with_lock(func):
        """鎖裝飾器"""
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if self._lock:
                with self._lock:
                    return func(self, *args, **kwargs)
            return func(self, *args, **kwargs)
        return wrapper
    
    @_with_lock
    def get(self, key: Any) -> Any:
        """獲取快取值"""
        value = self._backend.get(key)
        
        if self.stats:
            if value is not None:
                self.stats.hits += 1
            else:
                self.stats.misses += 1
        
        return value
    
    @_with_lock
    def set(self, key: Any, value: Any, ttl: float | None = None) -> bool:
        """設置快取值"""
        success = self._backend.set(key, value, ttl)
        
        if self.stats and success:
            self.stats.sets += 1
            self.stats.size = self._backend.size()
        
        return success
    
    @_with_lock
    def delete(self, key: Any) -> bool:
        """刪除快取值"""
        success = self._backend.delete(key)
        
        if self.stats and success:
            self.stats.deletes += 1
            self.stats.size = self._backend.size()
        
        return success
    
    @_with_lock
    def clear(self):
        """清空快取"""
        self._backend.clear()
        
        if self.stats:
            self.stats.size = 0
    
    @_with_lock
    def size(self) -> int:
        """獲取快取大小"""
        return self._backend.size()
    
    @_with_lock
    def keys(self) -> list[Any]:
        """獲取所有鍵"""
        return self._backend.keys()
    
    def get_stats(self) -> CacheStats | None:
        """獲取統計信息"""
        if self.stats:
            self.stats.size = self.size()
        return self.stats
    
    def reset_stats(self):
        """重置統計信息"""
        if self.stats:
            self.stats.reset()
    
    def _load_from_file(self):
        """從檔案載入快取"""
        if not self.config.persistence_file:
            return
        
        try:
            with open(self.config.persistence_file, 'rb') as f:
                data = pickle.load(f)
                for key, value in data.items():
                    self._backend.set(key, value)
            logger.info(f"從檔案載入快取: {self.config.persistence_file}")
        except FileNotFoundError:
            logger.info("快取檔案不存在，創建新快取")
        except Exception as e:
            logger.error(f"載入快取檔案失敗: {e}")
    
    def save_to_file(self):
        """保存快取到檔案"""
        if not self.config.persistence_file:
            return
        
        try:
            data = {}
            for key in self.keys():
                value = self.get(key)
                if value is not None:
                    data[key] = value
            
            with open(self.config.persistence_file, 'wb') as f:
                pickle.dump(data, f)
            logger.info(f"保存快取到檔案: {self.config.persistence_file}")
        except Exception as e:
            logger.error(f"保存快取檔案失敗: {e}")


class FunctionCache:
    """函數快取裝飾器"""
    
    def __init__(self, cache_manager: CacheManager | None = None,
                 key_func: Callable | None = None,
                 ttl: float | None = None):
        """
        初始化函數快取
        
        Args:
            cache_manager: 快取管理器
            key_func: 鍵生成函數
            ttl: 生存時間
        """
        self.cache_manager = cache_manager or CacheManager()
        self.key_func = key_func or self._default_key_func
        self.ttl = ttl
    
    def _default_key_func(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """預設鍵生成函數"""
        key_data = f"{func_name}:{str(args)}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def __call__(self, func: Callable) -> Callable:
        """裝飾器實現"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成快取鍵
            cache_key = self.key_func(func.__name__, args, kwargs)
            
            # 嘗試從快取獲取
            cached_result = self.cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 執行函數並快取結果
            result = func(*args, **kwargs)
            self.cache_manager.set(cache_key, result, self.ttl)
            
            return result
        
        # 添加快取管理方法
        wrapper.cache_clear = lambda: self.cache_manager.clear()
        wrapper.cache_info = lambda: self.cache_manager.get_stats()
        
        return wrapper


class AsyncCacheManager:
    """異步快取管理器"""
    
    def __init__(self, config: CacheConfig | None = None):
        self.config = config or CacheConfig()
        self.stats = CacheStats() if self.config.enable_stats else None
        self._backend = self._create_backend()
        self._lock = asyncio.Lock()
    
    def _create_backend(self) -> CacheBackend:
        """創建快取後端"""
        # 為了簡化，使用同步後端
        if self.config.strategy == CacheStrategy.LRU:
            return LRUCache(self.config.max_size)
        elif self.config.strategy == CacheStrategy.TTL:
            return TTLCache(self.config.ttl_seconds)
        else:
            raise ValueError(f"不支援的快取策略: {self.config.strategy}")
    
    async def get(self, key: Any) -> Any:
        """異步獲取快取值"""
        async with self._lock:
            value = self._backend.get(key)
            
            if self.stats:
                if value is not None:
                    self.stats.hits += 1
                else:
                    self.stats.misses += 1
            
            return value
    
    async def set(self, key: Any, value: Any, ttl: float | None = None) -> bool:
        """異步設置快取值"""
        async with self._lock:
            success = self._backend.set(key, value, ttl)
            
            if self.stats and success:
                self.stats.sets += 1
                self.stats.size = self._backend.size()
            
            return success
    
    async def delete(self, key: Any) -> bool:
        """異步刪除快取值"""
        async with self._lock:
            success = self._backend.delete(key)
            
            if self.stats and success:
                self.stats.deletes += 1
                self.stats.size = self._backend.size()
            
            return success


# 便捷函數
def lru_cache(max_size: int = 128, ttl: float | None = None):
    """LRU快取裝飾器"""
    config = CacheConfig(
        strategy=CacheStrategy.LRU,
        max_size=max_size,
        ttl_seconds=ttl or 3600.0
    )
    cache_manager = CacheManager(config)
    return FunctionCache(cache_manager, ttl=ttl)


def ttl_cache(ttl: float = 3600.0, max_size: int = 1000):
    """TTL快取裝飾器"""
    config = CacheConfig(
        strategy=CacheStrategy.TTL,
        ttl_seconds=ttl,
        max_size=max_size
    )
    cache_manager = CacheManager(config)
    return FunctionCache(cache_manager, ttl=ttl)


# 全局快取實例
default_cache = CacheManager()
lru_cache_manager = CacheManager(CacheConfig(strategy=CacheStrategy.LRU))
ttl_cache_manager = CacheManager(CacheConfig(strategy=CacheStrategy.TTL))