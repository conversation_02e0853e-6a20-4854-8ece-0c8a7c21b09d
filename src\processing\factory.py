#!/usr/bin/env python3
"""
處理器工廠模組
Processor Factory Module

負責創建和配置處理管線中的所有依賴項，實現依賴注入。
"""

import os
# 現代類型提示語法 - 基本類型無需額外匯入

from config.settings import Config, get_config
from detection.core.config import DetectionConfig
from detection.core.detector import Detector
import sys
from pathlib import Path

# Import project modules directly (using pip install -e .)

from log_utils import get_logger
from .components.cube_service import CubeService
from .components.detection_service import DetectionService
from .components.blur_service import BlurService
from .components.image_cache import ImageCache
from .panorama_processor import PanoramaProcessor
from .scene_processor import SceneProcessor
from .pipeline import (
    ProcessingPipeline,
    LoadImageStep,
    ConvertToCubeStep,
    DetectObjectsStep,
    ApplyBlurStep,
    ApplyLogoStep,
    SaveResultsStep,
)

logger = get_logger("ProcessingFactory")

class ProcessingFactory:
    """
    處理管線工廠，負責創建和配置所有依賴。
    """

    @staticmethod
    def create_pipeline(config: Config | None = None) -> ProcessingPipeline:
        """
        創建一個完全配置好的 ProcessingPipeline 實例。
        """
        if config is None:
            logger.info("未提供配置，將從預設路徑加載。")
            config = get_config()

        logger.info("正在創建處理管線及其依賴...")

        # 1. 創建共享服務
        image_cache = ImageCache(max_size_mb=config.system.cache_size_mb)
        cube_service = CubeService(config)
        detection_service = ProcessingFactory._create_detection_service(config, image_cache)
        blur_service = BlurService(config) if detection_service else None

        # 2. 創建處理步驟並注入依賴
        steps = [
            LoadImageStep(image_cache=image_cache),
            ConvertToCubeStep(cube_service=cube_service),
            DetectObjectsStep(detection_service=detection_service),
            ApplyBlurStep(blur_service=blur_service),
            ApplyLogoStep(config=config),
            SaveResultsStep(cube_service=cube_service),
        ]
        
        # 3. 創建並返回管線
        pipeline = ProcessingPipeline(steps=steps, config=config)
        logger.info("處理管線創建成功。")
        return pipeline

    @staticmethod
    def create_scene_processor(config: Config | None = None) -> SceneProcessor:
        """
        創建一個 SceneProcessor，其內部使用工廠創建的管線。
        """
        if config is None:
            config = get_config()
        
        # 創建 PanoramaProcessor，它將在內部創建自己的管線
        # 注意：這是一個過渡性設計，最終 PanoramaProcessor 應直接接收 pipeline
        panorama_processor = PanoramaProcessor(config=config)
        
        scene_processor = SceneProcessor(
            config=config,
            panorama_processor=panorama_processor
        )
        return scene_processor


    @staticmethod
    def _create_detection_service(config: Config, cache: ImageCache) -> DetectionService | None:
        """創建檢測服務實例"""
        model_path = getattr(config.model, 'model_path', None)
        if not model_path or not os.path.exists(model_path):
            logger.warning("未提供主模型路徑或文件不存在。檢測服務將不會被啟用。")
            return None
        
        try:
            primary_detector = Detector(DetectionConfig.from_config(config.model))
            logger.info(f"主檢測器已創建: {model_path}")

            secondary_detector = None
            sec_model_path = getattr(config.model, 'secondary_model_path', None)
            if sec_model_path and os.path.exists(sec_model_path):
                logger.info(f"正在創建次要檢測器: {sec_model_path}")
                sec_model_config = config.model.copy(deep=True)
                sec_model_config.model_path = sec_model_path
                if hasattr(config.model, 'second_model_conf'):
                    sec_model_config.confidence_threshold = config.model.second_model_conf
                secondary_detector = Detector(DetectionConfig.from_config(sec_model_config))
                logger.info("次要檢測器已創建。")

            service = DetectionService(primary_detector, secondary_detector, cache=cache)
            logger.info("DetectionService 已成功創建。")
            return service
            
        except Exception as e:
            logger.error(f"創建 DetectionService 時發生錯誤: {e}", exc_info=True)
            return None
