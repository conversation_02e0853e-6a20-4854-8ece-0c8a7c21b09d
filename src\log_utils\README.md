# 📝 Log Utils 模組 - 企業級日誌系統 (重構版本)

> **一個功能完備、高度模組化的專業日誌解決方案，經過完整重構，採用現代設計模式和統一 API 的企業級系統。**

[![Log System](https://img.shields.io/badge/Log_System-Enterprise_Grade-brightgreen.svg)](https://github.com/user/repo)
[![API Design](https://img.shields.io/badge/API-Unified_Design-blue.svg)](https://github.com/user/repo)
[![Compatibility](https://img.shields.io/badge/Compatibility-100%25_Backward-orange.svg)](https://github.com/user/repo)
[![Patterns](https://img.shields.io/badge/Patterns-Factory%2BSingleton-purple.svg)](https://github.com/user/repo)

## 🎉 重構成果總覽

**本模組已於 2024 年完成全面重構，帶來顯著改進：**

- ✅ **統一 API 設計**: 從 11+ 個創建函數簡化為 1 個主函數 + 6 個便利函數
- ✅ **配置預設系統**: 9 種內建預設配置，支援自定義預設
- ✅ **代碼重複消除**: 移除 7+ 個重複函數，減少 65+ 行代碼
- ✅ **統一錯誤處理**: 建立完整的異常層次和錯誤處理機制
- ✅ **死鎖問題修復**: 解決複雜鎖定模式導致的死鎖問題
- ✅ **100% 向後相容**: 所有舊 API 繼續可用，平滑升級
- ✅ **批量操作支援**: 企業級批量創建和管理功能
- ✅ **系統整合**: 與其他模組的全面整合和日誌統一管理

---

## 📖 模組概覽

`log_utils` 模組為整個應用程式提供了統一、強大且靈活的日誌記錄功能。它從一個簡單的日誌腳本，歷經完整的重構，演進為一個採用現代設計模式（工廠模式、單例模式）的企業級日誌系統。

此模組不僅提供基礎的日誌記錄功能，更包含了結構化設定、多樣化格式器、可插拔的處理器以及全域日誌管理等進階特性，同時還保持了對舊版 API 的完整向後相容性，確保了系統升級的平滑過渡。

## 🏛️ 架構與設計

- **目前狀態**: ✅ 重構完成
- **核心設計模式**:
    - **工廠模式 (Factory Pattern)**: 透過 `factory.py` 提供一系列便利的工廠函式，極大地簡化了不同類型 Logger 的建立過程。
    - **單例模式 (Singleton Pattern)**: 透過 `get_global_log_manager()` 確保在整個應用程式生命週期中，只有一個全域的日誌管理器實例，便於集中管理。
- **關注點分離 (Separation of Concerns)**:
    - `core`: 負責核心的設定 (`LogConfig`) 和管理 (`LogManager`) 邏輯。
    - `formatters`: 提供多種日誌格式化工具（如彩色輸出、JSON 格式、緊湊格式等）。
    - `handlers`: 提供用於建立不同日誌處理器（如主控台、檔案、SMTP、Syslog）的工廠。

### 📁 檔案結構

```
log_utils/                           # 總計: 11 個檔案, 約 2,800 行
├── __init__.py                      # 模組的公開介面 (Public API)
├── logger.py                        # 舊版日誌器的相容性層 (已棄用)
├── README.md                        # 本文件 (模組的詳細說明書)
│
├── core/                           # 核心管理與設定
│   ├── __init__.py                 # 核心元件的公開介面
│   ├── config.py                   # `LogConfig` 設定類別與預設組態
│   └── manager.py                  # `LogManager` 全域日誌管理器
│
├── factory.py                      # Logger 工廠與高階 API
├── formatters.py                   # 多樣化的日誌格式器
├── handlers.py                     # 日誌處理器 (Handler) 的工廠
├── usage_example.py                # 完整的使用範例腳本
└── test_refactored.py              # 針對重構後架構的綜合測試
```

---

## 🚀 使用範例

### 快速入門 (新的統一 API - 推薦用法)

**重構後的新 API 大幅簡化了日誌器創建流程：**

```python
# 新的統一 API - 一個函數搞定所有需求
from log_utils import create_logger, console_logger, debug_logger

# 範例 1: 使用配置預設快速創建日誌器
dev_logger = create_logger("my_app", "development")
dev_logger.debug("開發模式日誌 - 詳細輸出")
dev_logger.info("應用程式正在啟動...")

# 範例 2: 生產環境日誌器 - 簡潔高效
prod_logger = create_logger("my_app", "production")
prod_logger.info("生產環境日誌")
prod_logger.error("系統錯誤發生")

# 範例 3: 使用便利函數 - 更加簡潔
console_log = console_logger("temp_task")  # 僅控制台輸出
debug_log = debug_logger("troubleshoot")   # 調試模式

console_log.info("僅顯示在控制台")
debug_log.debug("詳細調試信息")
```

### 新 API 與舊 API 對照

```python
# 舊 API (仍然可用)
from log_utils.factory import create_logger_from_preset, setup_basic_logger
from log_utils.factory import create_console_only_logger, create_debug_logger

old_logger1 = setup_basic_logger("app", level=logging.INFO)
old_logger2 = create_console_only_logger("console", level=logging.DEBUG)
old_logger3 = create_debug_logger("debug", "logs/debug.log")

# 新 API (推薦使用)
from log_utils import create_logger, console_logger, debug_logger

new_logger1 = create_logger("app", "default")           # 替代 setup_basic_logger
new_logger2 = console_logger("console")                  # 替代 create_console_only_logger  
new_logger3 = debug_logger("debug")                     # 替代 create_debug_logger
```

### 進階設定

當需要對日誌系統進行深度自訂時，可以直接操作 `LogConfig` 和 `LogManager`。

```python
from log_utils.core.config import LogConfig
from log_utils.core.manager import LogManager
import logging

# 1. 建立一個高度自訂的設定物件
#    - 日誌級別為 INFO，同時輸出到主控台和檔案
#    - 為檔案輸出指定詳細的格式
#    - 額外增加一個 SMTP 處理器，在發生 ERROR 時自動傳送郵件
custom_config = LogConfig(
    name="data_pipeline",
    level=logging.INFO,
    console_output=True,
    file_output=True,
    base_output_path="logs/pipelines",
    file_format="%(asctime)s - %(levelname)s - %(module)s - %(message)s",
    additional_handlers=[
        {
            "type": "smtp",
            "level": logging.ERROR,
            "mailhost": "smtp.example.com",
            "fromaddr": "<EMAIL>",
            "toaddrs": ["<EMAIL>"],
            "subject": "資料管線發生嚴重錯誤"
        }
    ]
)

# 2. 使用此設定初始化日誌管理器
#    推薦使用 `with` 陳述式來確保資源被妥善關閉
with LogManager(config=custom_config) as manager:
    # 3. 建立日誌器
    pipeline_logger = manager.setup_logger("data_pipeline")

    pipeline_logger.info("資料管線任務已啟動。")
    # ... 執行任務 ...
    # 下面這條錯誤日誌將會被寫入檔案、顯示在主控台，並觸發郵件傳送
    pipeline_logger.error("資料轉換步驟失敗！")
```

### 全域日誌器存取模式

這是整個專案中最常見的使用模式，可以在任何地方安全地獲取日誌器實例。

```python
from log_utils.factory import get_logger

# 模式 1: 在模組層級獲取日誌器
logger = get_logger(__name__)
logger.info(f"模組 {__name__} 已成功載入。")

# 模式 2: 在類別中獲取日誌器
class MyService:
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def do_something(self):
        self.logger.debug("正在執行 do_something 方法...")

# 模式 3: 獲取一個特定名稱的日誌器
# 這對於跨模組追蹤特定流程很有幫助
detection_logger = get_logger("detection.core.detector")
detection_logger.debug("開始偵測流程...")
```

---

## 🎯 設定預設組 (Configuration Presets)

### 內建預設組

為了簡化常見場景的設定，模組提供了三種主要的預設組態：

```python
from log_utils.factory import create_logger_from_preset

# 開發預設組 - 詳細的日誌，包含顏色，適合開發階段
dev_logger = create_logger_from_preset("app", "development")

# 生產預設組 - 結構化的日誌，具備檔案輪轉，適合正式環境
prod_logger = create_logger_from_preset("app", "production")

# 偵錯預設組 - 提供最大限度的詳細資訊，用於問題排查
debug_logger = create_logger_from_preset("app", "debug")
```

### 預設組特性對比

| 預設組 | 級別 | 主控台 | 檔案 | 顏色 | 檔案輪轉 | 主要用途 |
|:---|:---|:---:|:---:|:---:|:---:|:---|
| `development` | DEBUG | ✅ | ✅ | ✅ | ❌ | 開發與除錯 |
| `production` | INFO | ❌ | ✅ | ❌ | ✅ | 正式線上環境 |
| `debug` | DEBUG | ✅ | ✅ | ✅ | ❌ | 深度問題排查 |

### 自訂組態

您也可以輕鬆建立自己的組態，以滿足特定需求。

```python
from log_utils.core.config import LogConfig
import logging

# 建立一個自訂組態
custom_config = LogConfig(
    name="custom_app",
    level=logging.WARNING,
    console_output=True,
    file_output=True,
    base_output_path="logs/custom",
    console_format="%(levelname)s: %(message)s",
    file_format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    use_colors=True,
    max_file_size_mb=50, # 50MB 輪轉
    backup_count=10      # 保留 10 個備份
)
```

---

## 🔧 進階特性

### 支援多種格式器

```python
from log_utils.factory import setup_logger
from log_utils.formatters import JSONFormatter, CompactFormatter

# 範例 1: 建立一個輸出 JSON 結構化日誌的 logger
json_logger = setup_logger(
    name="json_app",
    console_format=JSONFormatter(), # 主控台也使用 JSON
    file_output=False
)
json_logger.info("User logged in", extra={"user_id": 123, "ip": "***********"})

# 範例 2: 建立一個用於大量日誌的緊湊格式 logger
compact_logger = setup_logger(
    name="compact_app",
    console_format=CompactFormatter(),
    file_output=False
)
```

### 處理器 (Handler) 工廠

您可以透過工廠函式動態建立並組合不同的日誌處理器。

```python
from log_utils.handlers import create_handler

# 建立一個具備時間輪轉功能的檔案處理器 (每天輪轉一次)
timed_file_handler = create_handler(
    handler_type="timed_rotating",
    filepath="app_daily.log",
    when="midnight",
    backup_count=30 # 保留 30 天的日誌
)

# 建立一個在發生嚴重錯誤時傳送郵件的處理器
smtp_handler = create_handler(
    handler_type="smtp",
    mailhost="smtp.company.com",
    fromaddr="<EMAIL>",
    toaddrs=["<EMAIL>"],
    subject="應用程式發生嚴重錯誤"
)
```

### 上下文感知日誌 (Context-Aware Logging)

使用 `LogManager` 的上下文管理器，可以為特定的程式碼區塊設定獨立的日誌組態。

```python
from log_utils.core.manager import LogManager
from log_utils.handlers import create_handler

# 建立一個全域管理器
with LogManager() as manager:
    # 為影像處理流程設定專屬的日誌器
    processing_logger = manager.setup_logger(
        "processing",
        additional_handlers=[
            create_handler("file", filepath="processing.log")
        ]
    )
    
    # 為偵測流程設定另一個專屬日誌器
    detection_logger = manager.setup_logger(
        "detection",
        additional_handlers=[
            create_handler("file", filepath="detection.log")
        ]
    )
```

---

## 🔄 模組依賴與關係

### 內部依賴

```mermaid
graph TD
    A[factory.py] --> B[core.manager.py];
    B --> C[core.config.py];
    B --> D[handlers.py];
    D --> E[formatters.py];
```

### 外部依賴

- **所有其他模組 (消費者)**: 專案中的每個模組（如 `core`, `detection`, `processing`）都依賴 `log_utils` 來進行日誌記錄。它們通常透過 `from log_utils.factory import get_logger` 來存取此功能。
- **`config` (協作者)**: `log_utils` 會讀取 `config.system` 中的設定（如 `log_level`）作為其預設的日誌組態。

### 整合模式

```python
# 在其他模組中典型的整合模式
from log_utils.factory import get_logger

# 在類別中使用
class SomeService:
    def __init__(self):
        # 使用類別名稱作為 logger 名稱，便於追蹤
        self.logger = get_logger(self.__class__.__name__)
    
    def perform_action(self):
        self.logger.info("動作已成功執行")
        
# 在模組層級使用
logger = get_logger(__name__)

def module_level_function():
    logger.debug("模組級函式被呼叫")
```

---

## 🧪 測試與驗證

### 測試結構

`test_refactored.py` 包含了全面的單元測試和整合測試。

```python
# test_refactored.py 中的測試類別結構
class TestLogUtils:
    def test_factory_functions(self):
        """測試所有工廠函式是否能正確建立 logger"""
        
    def test_preset_configurations(self):
        """測試 'development', 'production', 'debug' 預設組態是否如預期般運作"""
        
    def test_custom_handlers_and_formatters(self):
        """測試自訂處理器和格式器的建立與組合"""
        
    def test_manager_lifecycle_and_context(self):
        """測試 LogManager 的生命週期管理和上下文功能"""
```

### 執行測試

```bash
# 執行所有 log_utils 相關的測試，並顯示詳細輸出
python -m pytest test/test_log_utils/ -v

# 直接執行內建的測試腳本
python log_utils/test_refactored.py

# 執行使用範例腳本，以驗證不同組態下的行為
python log_utils/usage_example.py
```

### 驗證範例

```python
# 測試不同的日誌級別
logger = get_logger("test")
logger.debug("這是一條偵錯訊息，在 INFO 級別下不會顯示。")
logger.info("這是一條普通訊息。")
logger.warning("這是一條警告訊息。")
logger.error("這是一條錯誤訊息。")
logger.critical("這是一條嚴重錯誤訊息。")

# 測試異常記錄
try:
    1 / 0
except Exception:
    # logger.exception 會自動包含堆疊追蹤資訊
    logger.exception("發生了一個未預期的異常！")
```

---

## ⚡ 效能考量

### 高效率的日誌記錄模式

```python
# 對於昂貴的操作，使用延遲格式化 (lazy formatting)
# 只有當 logger 的級別允許時，`expensive_function()` 才會被執行
logger.debug("昂貴操作的結果: %s", expensive_function())

# 應避免使用 f-string，因為它會立即執行函式
# logger.debug(f"昂貴操作的結果: {expensive_function()}") # 不建議

# 對於複雜的偵錯資訊，可以先檢查級別
if logger.isEnabledFor(logging.DEBUG):
    logger.debug("複雜的偵錯資訊: %s", complex_calculation())
```

### 記憶體與磁碟管理

```python
# 自動化的日誌輪轉可以防止磁碟空間被耗盡
config = LogConfig(
    max_file_size_mb=50,    # 在檔案達到 50MB 時進行輪轉
    backup_count=10,        # 保留最近的 10 個備份檔案
    file_output=True
)

# 為高吞吐量的日誌設定緩衝區
handler = create_handler(
    "file",
    filepath="high_volume.log",
    buffer_size=8192  # 8KB 緩衝區
)
```

### 生產環境優化

```python
# 針對效能最佳化的生產環境組態
prod_config = LogConfig(
    name="production_app",
    level=logging.INFO,        # 忽略 DEBUG 訊息以提升效能
    console_output=False,      # 減少 I/O 操作
    file_output=True,
    use_colors=False,          # 避免顏色處理的開銷
    file_format="%(asctime)s [%(levelname)s] %(message)s"  # 使用簡單格式
)
```

---

## 🛠️ 問題排解

### 常見問題

1.  **匯入錯誤**
    ```python
    # 確保使用正確的匯入路徑
    from log_utils.factory import get_logger  # ✅ 正確
    # from log_utils import get_logger        # ❌ 錯誤 (舊版用法)
    ```

2.  **權限問題**
    ```python
    # 處理寫入日誌檔案時的權限錯誤
    try:
        logger = setup_logger("app", base_output_path="/var/log/app")
    except PermissionError:
        logger = setup_logger("app", base_output_path="./logs") # 降級到本地目錄
    ```

3.  **組態衝突**
    ```python
    # 如果遇到重複的 logger 組態，可以手動清理
    import logging
    logging.getLogger("conflicted_logger").handlers.clear()
    ```

### 偵錯模式

```python
# 啟用詳細日誌以進行問題排查
debug_logger = create_logger_from_preset("debug_app", "debug")

# 或手動設定特定模組的日誌級別
import logging
logging.getLogger("log_utils").setLevel(logging.DEBUG)
```

---

## 🔮 未來增強計畫

### 計畫中的功能

1.  **非同步日誌支援**: 為高併發應用程式提供非阻塞的日誌記錄。
2.  **與監控系統整合**: 內建對 Prometheus、Grafana 等監控系統的支援。
3.  **日誌聚合**: 內建對 Logstash、Fluentd 等日誌聚合服務的支援。
4.  **設定熱重載**: 無需重啟應用即可動態更新日誌組態。

### 擴充點

```python
# 擴充自訂的格式器
from log_utils.formatters import ColoredFormatter

class MyCustomFormatter(ColoredFormatter):
    def format(self, record):
        # 加入自訂的格式化邏輯
        record.custom_field = "my_value"
        return super().format(record)

# 擴充自訂的處理器
from log_utils.handlers import create_handler

def create_my_custom_handler(**kwargs):
    # 建立自訂處理器的邏輯
    pass
```

---

**📊 一個為 360° 全景影像處理系統打造的企業級日誌基礎設施**