"""
日誌工廠函式模組 (log_utils.factory)

本模組是 `log_utils` 套件的核心高階 API，提供了一系列便利的工廠函式，
旨在簡化日誌器的建立與管理流程。開發者應優先使用此模組中的函式來與日誌系統互動。

主要功能：
- **全域管理器**: 提供一個執行緒安全的全域日誌管理器 (`LogManager`) 實例。
- **日誌器設定**: 透過 `setup_logger` 和一系列衍生函式，輕鬆設定各種需求的日誌器。
- **預設組態**: `create_logger_from_preset` 函式允許從預先定義的組態（如 "development", "production"）快速建立日誌器。
- **生命週期管理**: 提供 `shutdown_logging` 和 `cleanup_old_logs` 等函式來管理日誌系統的生命週期。
- **向後相容**: 包含了對舊版 API 的支援，確保舊程式碼的平滑過渡。
"""

import logging
import os
import sys
import threading
import warnings
from typing import Any, Dict, Optional

# 導入統一錯誤處理系統
from .error_handler import ErrorHandler
from .exceptions import ImportDependencyError, LogUtilsError
from .messages import ErrorMessages, InfoMessages

# 使用統一錯誤處理來載入核心模組
def _load_core_modules():
    """載入核心模組，使用統一錯誤處理"""
    
    def _primary_import():
        from .core.config import LogConfig, create_config
        from .core.manager import LogManager
        return LogConfig, create_config, LogManager
    
    def _fallback_import():
        from core.config import LogConfig, create_config
        from core.manager import LogManager
        return LogConfig, create_config, LogManager
    
    def _fallback_factory():
        """創建簡化的降級實現"""
        class FallbackLogConfig:
            def __init__(self, **kwargs):
                self.name = kwargs.get('name', 'fallback')
                self.level = kwargs.get('level', logging.INFO)
                self.console_output = kwargs.get('console_output', True)
                self.file_output = kwargs.get('file_output', False)
                self.use_colors = kwargs.get('use_colors', True)
                self.simple_console = kwargs.get('simple_console', False)
                for k, v in kwargs.items():
                    setattr(self, k, v)
        
        def fallback_create_config(preset="development"):
            return FallbackLogConfig(name="fallback", level=logging.INFO)
        
        class FallbackLogManager:
            def __init__(self, config=None):
                self.config = config or FallbackLogConfig()
                self.loggers = {}
                self._initialized = False

            def _ensure_initialized(self):
                """延遲初始化，避免循環依賴"""
                if not self._initialized:
                    self._initialized = True

            def get_logger(self, name):
                self._ensure_initialized()
                if name not in self.loggers:
                    logger = logging.getLogger(name)
                    # 添加基本的控制台處理器
                    if not logger.handlers:
                        handler = logging.StreamHandler()
                        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                        handler.setFormatter(formatter)
                        logger.addHandler(handler)
                        logger.setLevel(logging.INFO)
                    self.loggers[name] = logger
                return self.loggers[name]

            def setup_logger(self, name, config=None):
                return self.get_logger(name)

            def list_loggers(self):
                return list(self.loggers.keys())

            def set_level(self, name, level):
                if name in self.loggers:
                    self.loggers[name].setLevel(level)
                    return True
                return False

            def get_statistics(self):
                return {"total_loggers": len(self.loggers), "active_loggers": len(self.loggers)}
                
            def shutdown(self):
                """關閉管理器"""
                for logger in self.loggers.values():
                    for handler in logger.handlers:
                        handler.close()
                    logger.handlers.clear()
                self.loggers.clear()
                
            def reload_config(self, new_config):
                """重新載入配置（簡化版本）"""
                self.config = new_config
                
            def cleanup_old_logs(self, days_to_keep=30):
                """清理舊日誌（簡化版本）"""
                pass
                
            def remove_logger(self, name):
                """移除日誌器"""
                if name in self.loggers:
                    logger = self.loggers[name]
                    for handler in logger.handlers:
                        handler.close()
                    logger.handlers.clear()
                    del self.loggers[name]
                    return True
                return False
        
        return FallbackLogConfig, fallback_create_config, FallbackLogManager
    
    return ErrorHandler.handle_import_error(
        primary_import=_primary_import,
        fallback_import=_fallback_import,
        fallback_factory=_fallback_factory,
        module_name="core modules"
    )

# 載入核心模組
LogConfig, create_config, LogManager = _load_core_modules()

# --- 全域單例管理器 ---
# _global_log_manager 作為一個單例 (Singleton)，確保在整個應用程式中只有一個日誌管理器實例。
# _global_lock 用於確保在多執行緒環境下，此單例的初始化是執行緒安全的。
_global_log_manager: Optional[LogManager] = None
_global_lock: Optional[threading.Lock] = None


def get_global_log_manager() -> LogManager:
    """
    獲取全域日誌管理器 (LogManager) 的單例實例。

    使用統一錯誤處理系統，確保在各種情況下都能提供可用的管理器。
    
    :return: 全域 LogManager 的實例。
    :raises LogUtilsError: 當無法創建任何形式的管理器時
    """
    global _global_log_manager

    if _global_log_manager is None:
        def _create_standard_manager():
            """創建標準管理器"""
            default_config = LogConfig(
                name="global_default",
                level=logging.INFO,
                console_output=True,
                file_output=False,  # 簡化：禁用檔案輸出以避免路徑問題
                use_colors=True,
                simple_console=False
            )
            return LogManager(default_config)
        
        def _create_fallback_manager():
            """創建降級管理器"""
            class EmergencyLogManager:
                def __init__(self):
                    self.loggers = {}
                
                def get_logger(self, name: str):
                    if name not in self.loggers:
                        logger = logging.getLogger(name)
                        if not logger.handlers:
                            handler = logging.StreamHandler()
                            formatter = logging.Formatter('%(levelname)s - %(message)s')
                            handler.setFormatter(formatter)
                            logger.addHandler(handler)
                            logger.setLevel(logging.INFO)
                        self.loggers[name] = logger
                    return self.loggers[name]
                
                def setup_logger(self, name: str, config=None):
                    return self.get_logger(name)
                
                def list_loggers(self):
                    return list(self.loggers.keys())
                
                def set_level(self, name: str, level: int):
                    if name in self.loggers:
                        self.loggers[name].setLevel(level)
                        return True
                    return False
                
                def get_statistics(self):
                    return {"total_loggers": len(self.loggers)}
                    
                def shutdown(self):
                    for logger in self.loggers.values():
                        for handler in logger.handlers:
                            handler.close()
                        logger.handlers.clear()
                    self.loggers.clear()
                    
                def reload_config(self, new_config):
                    pass  # 簡化版本不支持重新載入
                    
                def cleanup_old_logs(self, days_to_keep=30):
                    pass  # 簡化版本不支持清理
                    
                def remove_logger(self, name):
                    if name in self.loggers:
                        logger = self.loggers[name]
                        for handler in logger.handlers:
                            handler.close()
                        logger.handlers.clear()
                        del self.loggers[name]
                        return True
                    return False
            
            return EmergencyLogManager()
        
        _global_log_manager = ErrorHandler.handle_configuration_error(
            config_factory=_create_standard_manager,
            fallback_factory=_create_fallback_manager,
            config_name="global_log_manager"
        )
    
    return _global_log_manager


def setup_logger(
    name: str,
    level: int = logging.INFO,
    console_output: bool = True,
    file_output: bool = True,
    base_output_path: Optional[str] = None,
    simple_console: bool = False,
    use_colors: bool = True,
    max_bytes: int = 10 * 1024 * 1024, # 10MB
    backup_count: int = 5,
    config: Optional[LogConfig] = None,
) -> logging.Logger:
    """
    設定並返回一個功能完備的日誌器。

    這是一個核心的便利函式，允許透過傳遞參數快速建立一個自訂的日誌器。
    如果提供了 `config` 物件，則會忽略其他所有參數。

    :param name: 日誌器的唯一名稱，例如 `__name__` 或 "my_module"。
    :param level: 日誌器的主級別 (例如 `logging.INFO`, `logging.DEBUG`)。
    :param console_output: 是否將日誌輸出到主控台。
    :param file_output: 是否將日誌輸出到檔案。
    :param base_output_path: 日誌檔案存放的基礎目錄。預設為當前工作目錄下的 `logs/`。
    :param simple_console: 是否在主控台使用簡潔格式（僅級別和訊息）。
    :param use_colors: 是否在主控台輸出中使用 ANSI 顏色。
    :param max_bytes: 每個日誌檔案的最大大小（位元組），用於檔案輪轉。
    :param backup_count: 輪轉時保留的備份檔案數量。
    :param config: (可選) 一個 `LogConfig` 物件。如果提供此參數，將優先使用它，並忽略所有其他參數。
    :return: 一個已設定好的 `logging.Logger` 實例。
    """
    if config is None:
        # 如果沒有提供現成的 config 物件，則根據傳入的參數即時建立一個。
        config = LogConfig(
            name=name,
            level=level,
            console_output=console_output,
            file_output=file_output,
            base_output_path=base_output_path,
            simple_console=simple_console,
            use_colors=use_colors,
            max_bytes=max_bytes,
            backup_count=backup_count,
        )

    manager = get_global_log_manager()
    return manager.setup_logger(name, config)


def setup_basic_logger(
    name: str,
    level: int = logging.INFO,
    base_output_path: Optional[str] = None,
    simple_console: bool = False,
    use_colors: bool = True,
) -> logging.Logger:
    """
    【向後相容】設定一個基本的日誌器，同時輸出到主控台和檔案。

    :param name: 日誌器名稱。
    :param level: 日誌級別。
    :param base_output_path: 日誌檔案的基礎路徑。
    :param simple_console: 是否使用簡潔的主控台格式。
    :param use_colors: 是否在主控台使用顏色。
    :return: 已設定好的 `logging.Logger` 實例。
    """
    return setup_logger(
        name=name,
        level=level,
        console_output=True,
        file_output=True,
        base_output_path=base_output_path,
        simple_console=simple_console,
        use_colors=use_colors,
    )


def setup_simple_logger(
    name: str, level: int = logging.INFO, base_output_path: Optional[str] = None
) -> logging.Logger:
    """
    【向後相容】設定一個簡單的日誌器，使用簡潔格式且不帶顏色。

    :param name: 日誌器名稱。
    :param level: 日誌級別。
    :param base_output_path: 日誌檔案的基礎路徑。
    :return: 已設定好的 `logging.Logger` 實例。
    """
    return setup_logger(
        name=name,
        level=level,
        console_output=True,
        file_output=True,
        base_output_path=base_output_path,
        simple_console=True,
        use_colors=False,
    )


def create_tool_logger(
    tool_name: str,
    base_output_path: Optional[str] = None,
    log_level: int = logging.INFO,
) -> logging.Logger:
    """
    【向後相容】為獨立的工具或腳本建立一個日誌器。

    此函式會自動偵測是否在打包後的執行檔 (exe) 環境中執行，
    並相應地調整主控台的輸出格式（在 exe 模式下使用簡潔格式且不帶顏色）。

    :param tool_name: 工具或腳本的名稱。
    :param base_output_path: 日誌檔案的基礎路徑。
    :param log_level: 日誌級別。
    :return: 已設定好的 `logging.Logger` 實例。
    """
    # 偵測是否為 PyInstaller 或類似工具打包的執行檔模式
    is_exe_mode = (
        hasattr(sys, "frozen")
        or hasattr(sys, "_MEIPASS")
        or "python" not in sys.executable.lower()
    )

    return setup_logger(
        name=tool_name,
        level=log_level,
        console_output=True,
        file_output=True,
        base_output_path=base_output_path,
        simple_console=is_exe_mode,  # 在 exe 模式下，自動使用簡潔格式
        use_colors=not is_exe_mode,    # 在 exe 模式下，自動禁用顏色
    )


def get_logger(name: str) -> logging.Logger:
    """
    從全域管理器中獲取一個已設定的日誌器。

    如果指定名稱的日誌器不存在，它會被自動建立並返回。
    這是在應用程式各個模組中獲取日誌器實例的 **建議方式**。

    :param name: 日誌器的唯一名稱。通常建議使用 `__name__`。
    :return: `logging.Logger` 實例。絕不會是 None。
    """
    manager = get_global_log_manager()
    # LogManager 的 get_logger 內部會處理不存在時的建立邏輯
    return manager.get_logger(name)


def create_console_only_logger(
    name: str,
    level: int = logging.INFO,
    use_colors: bool = True,
    simple_format: bool = False,
) -> logging.Logger:
    """
    建立一個只將日誌輸出到主控台的日誌器。

    :param name: 日誌器名稱。
    :param level: 日誌級別。
    :param use_colors: 是否使用顏色。
    :param simple_format: 是否使用簡潔格式。
    :return: 已設定好的 `logging.Logger` 實例。
    """
    return setup_logger(
        name=name,
        level=level,
        console_output=True,
        file_output=False, # 明確停用檔案輸出
        simple_console=simple_format,
        use_colors=use_colors,
    )


def create_file_only_logger(
    name: str,
    level: int = logging.DEBUG,
    base_output_path: Optional[str] = None,
    max_bytes: int = 20 * 1024 * 1024, # 20MB
    backup_count: int = 5,
) -> logging.Logger:
    """
    建立一個只將日誌輸出到檔案的日誌器。

    :param name: 日誌器名稱。
    :param level: 日誌級別。
    :param base_output_path: 日誌檔案的基礎路徑。
    :param max_bytes: 檔案輪轉的大小上限。
    :param backup_count: 備份檔案的數量。
    :return: 已設定好的 `logging.Logger` 實例。
    """
    return setup_logger(
        name=name,
        level=level,
        console_output=False, # 明確停用主控台輸出
        file_output=True,
        base_output_path=base_output_path,
        max_bytes=max_bytes,
        backup_count=backup_count,
    )


def create_debug_logger(
    name: str, base_output_path: Optional[str] = None, use_colors: bool = True
) -> logging.Logger:
    """
    建立一個專為偵錯設計的日誌器。

    它會使用詳細的檔案格式，記錄包括檔案名稱、行號和函式名稱等資訊。

    :param name: 日誌器名稱。
    :param base_output_path: 日誌檔案的基礎路徑。
    :param use_colors: 是否在主控台使用顏色。
    :return: 已設定好的 `logging.Logger` 實例。
    """
    config = create_config(
        preset="debug",
        name=name,
        base_output_path=base_output_path,
        use_colors=use_colors,
    )
    manager = get_global_log_manager()
    return manager.setup_logger(name, config)


def create_production_logger(
    name: str, base_output_path: Optional[str] = None
) -> logging.Logger:
    """
    建立一個專為生產環境設計的日誌器。

    它使用較高的日誌級別 (INFO)，並有較大的檔案輪轉設定。

    :param name: 日誌器名稱。
    :param base_output_path: 日誌檔案的基礎路徑。
    :return: 已設定好的 `logging.Logger` 實例。
    """
    config = create_config(
        preset="production",
        name=name,
        base_output_path=base_output_path,
    )
    manager = get_global_log_manager()
    return manager.setup_logger(name, config)


def create_logger_from_preset(
    name: str,
    preset: str = "development",
    base_output_path: Optional[str] = None,
    **overrides,
) -> logging.Logger:
    """
    從一個預先定義的組態預設組 (preset) 來建立日誌器。

    這是建立日誌器 **最推薦** 的高階方法。

    :param name: 日誌器的唯一名稱。
    :param preset: 預設組的名稱。可選值為 "development", "production", "debug", "minimal"。
    :param base_output_path: (可選) 覆寫預設組中的日誌檔案基礎路徑。
    :param overrides: (可選) 任何 `LogConfig` 中的屬性，用於進一步覆寫預設組的設定。
    :return: 一個已設定好的 `logging.Logger` 實例。
    :raises ValueError: 如果提供了未知的預設組名稱。
    """
    # 從 `core.config` 模組獲取預設組態
    config = create_config(preset)

    # 套用基本覆寫
    config.name = name
    if base_output_path:
        config.base_output_path = base_output_path

    # 套用任何額外的關鍵字參數覆寫
    if overrides:
        config = config.update(**overrides)

    manager = get_global_log_manager()
    return manager.setup_logger(name, config)


def configure_global_logging(
    level: int = logging.INFO,
    console_output: bool = True,
    file_output: bool = True,
    base_output_path: Optional[str] = None,
    simple_console: bool = False,
    use_colors: bool = True,
):
    """
    設定或重新設定全域日誌管理器的基礎行為。

    :param level: 全域日誌級別。
    :param console_output: 是否啟用主控台輸出。
    :param file_output: 是否啟用檔案輸出。
    :param base_output_path: 全域日誌檔案基礎路徑。
    :param simple_console: 是否使用簡潔主控台格式。
    :param use_colors: 是否在主控台使用顏色。
    """
    global _global_log_manager

    # 建立一個新的全域組態
    config = LogConfig(
        name="global",
        level=level,
        console_output=console_output,
        file_output=file_output,
        base_output_path=base_output_path,
        simple_console=simple_console,
        use_colors=use_colors,
    )

    # 獲取管理器並重新載入組態
    manager = get_global_log_manager()
    manager.reload_config(config)


def cleanup_old_logs(days_to_keep: int = 30):
    """
    清理指定天數之前的舊日誌檔案。

    :param days_to_keep: 要保留的日誌檔案天數。早於此天數的檔案將被刪除。
    """
    manager = get_global_log_manager()
    manager.cleanup_old_logs(days_to_keep)


def shutdown_logging():
    """
    安全地關閉整個日誌系統。

    這會關閉所有檔案處理器，並釋放相關資源。
    建議在應用程式結束時呼叫此函式。
    """
    global _global_log_manager
    if _global_log_manager:
        _global_log_manager.shutdown()
        _global_log_manager = None


def get_logging_statistics() -> Dict[str, Any]:
    """
    獲取目前日誌系統的統計資訊。

    :return: 一個包含統計資訊的字典，例如已建立的日誌器總數。
    """
    manager = get_global_log_manager()
    return manager.get_statistics()


def set_logger_level(name: str, level: int) -> bool:
    """
    在執行期間動態設定指定日誌器的級別。

    :param name: 目標日誌器的名稱。
    :param level: 新的日誌級別 (例如 `logging.DEBUG`)。
    :return: 如果成功找到並設定了日誌器，則返回 `True`，否則返回 `False`。
    """
    manager = get_global_log_manager()
    return manager.set_level(name, level)


def list_loggers() -> list[str]:
    """
    列出所有當前已由管理器設定的日誌器的名稱。

    :return: 一個包含所有日誌器名稱的字串列表。
    """
    manager = get_global_log_manager()
    return manager.list_loggers()


def remove_logger(name: str) -> bool:
    """
    從管理器中移除一個日誌器及其所有處理器。

    :param name: 要移除的日誌器的名稱。
    :return: 如果成功找到並移除了日誌器，則返回 `True`，否則返回 `False`。
    """
    manager = get_global_log_manager()
    return manager.remove_logger(name)


def create_progress_logger(
    name: str, base_output_path: Optional[str] = None, show_progress: bool = True
) -> logging.Logger:
    """
    建立一個專門用於回報進度的日誌器。

    :param name: 日誌器名稱。
    :param base_output_path: 日誌檔案的基礎路徑。
    :param show_progress: 是否在主控台顯示進度條。
    :return: 已設定好的 `logging.Logger` 實例。
    """
    config = create_config(
        "minimal",
        name=name,
        base_output_path=base_output_path,
        additional_handlers=(
            [{"type": "progress"}] if show_progress else []
        ),
    )
    manager = get_global_log_manager()
    return manager.setup_logger(name, config)


def log_progress(
    logger: logging.Logger,
    current: int,
    total: int,
    message: str = "處理中",
):
    """
    使用指定的日誌器記錄一條進度訊息。

    此函式會觸發 `ProgressFormatter` 來顯示進度條。

    :param logger: 用於記錄的日誌器實例 (應由 `create_progress_logger` 建立)。
    :param current: 當前的進度計數。
    :param total: 總的計數。
    :param message: 要顯示的進度訊息。
    """
    logger.info(message, extra={"progress_current": current, "progress_total": total})


# --- 向後相容性函式 ---

def setup_logging(*args, **kwargs):
    """【向後相容】舊的 `setup_logging` 函式。"""
    warnings.warn(
        "`setup_logging` 已棄用，請改用 `setup_basic_logger`。",
        DeprecationWarning,
        stacklevel=2
    )
    return setup_basic_logger(*args, **kwargs)


def get_configured_logger(name: str) -> logging.Logger:
    """【向後相容】舊的 `get_configured_logger` 函式。"""
    warnings.warn(
        "`get_configured_logger` 已棄用，請改用 `get_logger`。",
        DeprecationWarning,
        stacklevel=2
    )
    return get_logger(name)


def main():
    """
    測試 log_utils.factory 模組的主要功能
    """
    print("=" * 60)
    print("測試 log_utils.factory 模組")
    print("=" * 60)

    # 測試全域管理器
    print("\n1. 測試全域日誌管理器:")
    try:
        manager = get_global_log_manager()
        print(f"   全域管理器創建成功: {type(manager)}")
        print(f"   管理器狀態: {'活躍' if manager else '未初始化'}")
    except Exception as e:
        print(f"   全域管理器測試失敗: {e}")

    # 測試基本日誌器創建
    print("\n2. 測試基本日誌器創建:")
    try:
        logger = get_logger("test_factory")
        print(f"   日誌器創建成功: {logger.name}")
        print(f"   日誌器級別: {logging.getLevelName(logger.level)}")
        print(f"   處理器數量: {len(logger.handlers)}")
    except Exception as e:
        print(f"   基本日誌器測試失敗: {e}")

    # 測試預設組態日誌器
    print("\n3. 測試預設組態日誌器:")
    try:
        dev_logger = create_logger_from_preset("test_dev", preset="development")
        print(f"   開發模式日誌器創建成功: {dev_logger.name}")

        prod_logger = create_logger_from_preset("test_prod", preset="production")
        print(f"   生產模式日誌器創建成功: {prod_logger.name}")
    except Exception as e:
        print(f"   預設組態日誌器測試失敗: {e}")

    # 測試日誌輸出
    print("\n4. 測試日誌輸出:")
    try:
        test_logger = get_logger("test_output")
        test_logger.info("這是一條測試信息")
        test_logger.warning("這是一條測試警告")
        print("   日誌輸出測試完成")
    except Exception as e:
        print(f"   日誌輸出測試失敗: {e}")

    # 測試日誌器管理功能
    print("\n5. 測試日誌器管理功能:")
    try:
        loggers = list_loggers()
        print(f"   當前日誌器數量: {len(loggers)}")
        print(f"   日誌器列表: {loggers[:5]}...")  # 只顯示前5個

        stats = get_logging_statistics()
        print(f"   日誌統計: {stats}")
    except Exception as e:
        print(f"   日誌器管理功能測試失敗: {e}")

    # 測試級別設定
    print("\n6. 測試日誌級別設定:")
    try:
        test_logger = get_logger("test_level")
        original_level = test_logger.level

        set_logger_level("test_level", logging.DEBUG)
        new_level = test_logger.level

        print(f"   原始級別: {logging.getLevelName(original_level)}")
        print(f"   新級別: {logging.getLevelName(new_level)}")
        print("   級別設定測試完成")
    except Exception as e:
        print(f"   級別設定測試失敗: {e}")

    # 測試向後相容性
    print("\n7. 測試向後相容性:")
    try:
        basic_logger = setup_basic_logger("test_basic")
        simple_logger = setup_simple_logger("test_simple")
        tool_logger = create_tool_logger("test_tool")

        print(f"   基本日誌器: {basic_logger.name}")
        print(f"   簡單日誌器: {simple_logger.name}")
        print(f"   工具日誌器: {tool_logger.name}")
        print("   向後相容性測試完成")
    except Exception as e:
        print(f"   向後相容性測試失敗: {e}")

    print("\n✅ log_utils.factory 模組測試完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
