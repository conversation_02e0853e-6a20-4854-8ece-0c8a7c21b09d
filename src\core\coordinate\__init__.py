"""
Coordinate Submodule - 座標系統轉換模組

本模組提供高精度的座標系統轉換功能，支援多種座標系統之間的相互轉換。
主要功能包括球面座標、笛卡爾座標、像素座標等的轉換計算。

## 主要功能

- 球面座標與笛卡爾座標轉換
- 像素座標與標準化座標轉換  
- 立方體面座標計算
- 高精度數學運算支援

## 使用範例

```python
from core.coordinate import CoordinateTransformer

transformer = CoordinateTransformer()
# 座標轉換操作...
```
"""

# 套件資訊
__package__ = "core.coordinate"
__version__ = "1.0.0"
__author__ = "AI 部門 - 全景處理團隊"

# 主要元件導入
__all__ = []

try:
    from .core import CoordinateTransformer
    __all__.append("CoordinateTransformer")
except ImportError as e:
    import warnings
    warnings.warn(
        f"無法導入 CoordinateTransformer: {e}. "
        "座標轉換功能將無法使用.",
        ImportWarning
    )

# 嘗試導入工具函數
try:
    from .utils import point_in_polygon
    __all__.append("point_in_polygon")
except ImportError as e:
    import warnings
    warnings.warn(
        f"無法導入工具函數: {e}. "
        "輔助功能將無法使用.",
        ImportWarning
    )

# 嘗試導入 Numba 核心函數
try:
    from . import numba_kernels
    __all__.append("numba_kernels")
except ImportError as e:
    import warnings
    warnings.warn(
        f"無法導入 Numba 加速核心: {e}. "
        "將使用標準 Python 實現.",
        ImportWarning
    )