"""
偵測策略工廠

根據面 ID 和設定來建立和管理偵測策略。
"""

from typing import Optional

# 使用延遲載入來避免長時間的 import
try:
    from log_utils import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

from .base import DetectionStrategy
from .rotation import RotationStrategy
from .skip import SkipStrategy
from .standard import StandardStrategy

logger = get_logger(__name__)


class StrategyFactory:
    """用於建立偵測策略的工廠"""

    def __init__(self):
        self._strategies = {
            "standard": StandardStrategy(),
            "rotation": RotationStrategy(),
            "skip": SkipStrategy(),
        }
        self._face_strategy_mapping = {
            0: "standard",  # 正面
            1: "standard",  # 右面
            2: "standard",  # 背面
            3: "standard",  # 左面
            4: "skip",      # 上面 (天空) - 跳過偵測
            5: "rotation",  # 下面 (地面) - 旋轉偵測
        }

    def get_strategy(self, face_id: int) -> DetectionStrategy:
        """為給定的面 ID 取得適當的策略

        :param face_id: 立方體面 ID (0-5)
        :return: 偵測策略實例
        :raises ValueError: 如果 face_id 無效
        """
        if face_id not in self._face_strategy_mapping:
            raise ValueError(f"無效的面 ID: {face_id}。必須是 0-5。")

        strategy_name = self._face_strategy_mapping[face_id]
        strategy = self._strategies.get(strategy_name)

        if strategy is None:
            logger.error(
                f"找不到策略 '{strategy_name}'，退回使用標準策略"
            )
            strategy = self._strategies["standard"]

        logger.debug(f"為面 {face_id} 選擇了策略 '{strategy.get_name()}'")
        return strategy

    def get_strategy_by_name(self, name: str) -> Optional[DetectionStrategy]:
        """按名稱取得策略

        :param name: 策略名稱
        :return: 策略實例，如果找不到則為 None
        """
        return self._strategies.get(name)

    def register_strategy(self, name: str, strategy: DetectionStrategy):
        """註冊一個新策略

        :param name: 策略名稱
        :param strategy: 策略實例
        """
        self._strategies[name] = strategy
        logger.info(f"已註冊新策略: {name}")

    def set_face_strategy(self, face_id: int, strategy_name: str):
        """覆寫特定面的策略

        :param face_id: 面 ID (0-5)
        :param strategy_name: 策略名稱
        :raises ValueError: 如果 face_id 無效或策略不存在
        """
        if face_id not in range(6):
            raise ValueError(f"無效的面 ID: {face_id}。必須是 0-5。")

        if strategy_name not in self._strategies:
            raise ValueError(f"策略 '{strategy_name}' 尚未註冊。")

        self._face_strategy_mapping[face_id] = strategy_name
        logger.info(f"已設定面 {face_id} 使用策略 '{strategy_name}'")

    def get_available_strategies(self) -> list[str]:
        """取得可用策略名稱的列表

        :return: 策略名稱列表
        """
        return list(self._strategies.keys())

    def get_face_strategy_mapping(self) -> dict[int, str]:
        """取得目前面與策略的對應關係

        :return: 將面 ID 對應到策略名稱的字典
        """
        return self._face_strategy_mapping.copy()
