#!/usr/bin/env python3
"""
進度管理和中斷恢復模組
Progress Management and Resume Module

提供進度追蹤、中斷恢復、CSV輸出等企業級功能。
Provides progress tracking, resume capability, and CSV output for enterprise applications.

Author: <PERSON> Assistant
Date: 2025-01-11
"""

import csv
import datetime
import os
import sys
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Dict

# Import project modules directly (using pip install -e .)

try:
    from log_utils.logger import create_tool_logger

    HAS_LOGGER = True
except ImportError:
    HAS_LOGGER = False


@dataclass
class ProgressRecord:
    """進度記錄數據類"""

    timestamp: str
    district: str
    scene: str
    status: str
    processing_time: float = 0.0
    note: str = ""


@dataclass
class BlurStatsRecord:
    """模糊統計記錄數據類"""

    timestamp: str
    district: str
    scene: str
    face_id: int
    face_name: str
    blur_count: int
    total_area: float
    avg_confidence: float
    details: str


class ProgressManager:
    """
    進度管理器

    負責進度追蹤、中斷恢復、CSV輸出等功能。
    支援智能場景檢查，只檢查場景資料夾是否存在（快速檢查）。
    """

    def __init__(
        self,
        output_path: str,
        timestamp: str | None = None,
        enable_csv: bool = True,
        existing_progress_file: str | None = None,
    ):
        """
        初始化進度管理器

        Args:
            output_path: 輸出目錄路徑
            timestamp: 時間戳記，如果不提供則自動生成
            enable_csv: 是否啟用CSV輸出
            existing_progress_file: 已存在的progress.csv檔案路徑（恢復模式用）
        """
        self.output_path = output_path
        self.timestamp = timestamp or datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.enable_csv = enable_csv
        self.existing_progress_file = existing_progress_file

        # 設置日誌器
        self.logger = self._setup_logger()

        # 設置檔案路徑
        if existing_progress_file:
            # 使用已存在的progress.csv檔案
            self.progress_file = existing_progress_file
            # 對於blur_stats和final_report，仍然在同一目錄下創建新檔案
            progress_dir = os.path.dirname(existing_progress_file)
            progress_basename = os.path.basename(existing_progress_file).replace(".csv", "")
            self.blur_stats_file = os.path.join(progress_dir, f"{progress_basename}_blur_stats.csv")
            self.final_report_file = os.path.join(
                progress_dir, f"{progress_basename}_final_report.csv"
            )
        else:
            # 創建新的時間戳檔案
            self.progress_file = os.path.join(output_path, f"progress_{self.timestamp}.csv")
            self.blur_stats_file = os.path.join(output_path, f"blur_stats_{self.timestamp}.csv")
            self.final_report_file = os.path.join(output_path, f"final_report_{self.timestamp}.csv")

        # 確保輸出目錄存在
        os.makedirs(output_path, exist_ok=True)

        # 初始化CSV檔案
        if self.enable_csv:
            self._initialize_csv_files()

    def _setup_logger(self):
        """設置日誌器"""
        if HAS_LOGGER:
            return create_tool_logger("progress_manager", verbose=False, quiet=False)
        else:

            class SimpleLogger:
                def info(self, msg):
                    print(f"INFO: {msg}")

                def warning(self, msg):
                    print(f"WARNING: {msg}")

                def error(self, msg):
                    print(f"ERROR: {msg}")

                def debug(self, msg):
                    print(f"DEBUG: {msg}")

            return SimpleLogger()

    def _initialize_csv_files(self):
        """初始化CSV檔案"""
        try:
            # 初始化進度檔案（如果使用已存在的檔案則跳過）
            if not self.existing_progress_file:
                with open(self.progress_file, "w", encoding="utf-8", newline="") as f:
                    writer = csv.writer(f)
                    writer.writerow(["時間戳記", "區域", "場景", "狀態", "處理時間(秒)", "備註"])
                self.logger.info(f"新進度檔案初始化完成: {self.progress_file}")
            else:
                self.logger.info(f"使用已存在的進度檔案: {self.progress_file}")

            # 初始化模糊統計檔案（總是創建新的）
            if not os.path.exists(self.blur_stats_file):
                with open(self.blur_stats_file, "w", encoding="utf-8", newline="") as f:
                    writer = csv.writer(f)
                    writer.writerow(
                        [
                            "時間戳記",
                            "區域",
                            "場景",
                            "面ID",
                            "面名稱",
                            "模糊區域數量",
                            "總面積",
                            "平均置信度",
                            "檢測詳情",
                        ]
                    )
                self.logger.info(f"模糊統計檔案初始化完成: {self.blur_stats_file}")

        except Exception as e:
            self.logger.error(f"初始化CSV檔案失敗: {e}")

    def load_completed_scenes(
        self, progress_csv_path: str | None = None, verify_output: bool = True
    ) -> set[str]:
        """
        從進度CSV檔案載入已完成的場景（增強版，包含輸出驗證）

        Args:
            progress_csv_path: 進度CSV檔案路徑，如不提供則使用默認路徑
            verify_output: 是否驗證輸出目錄存在

        Returns:
            已完成且驗證存在的場景集合，格式為 "區域/場景"
        """
        if verify_output:
            return self._load_and_verify_completed_scenes(progress_csv_path)
        else:
            return self._load_basic_completed_scenes(progress_csv_path)

    def _load_basic_completed_scenes(self, progress_csv_path: str | None = None) -> set[str]:
        """
        基本的已完成場景載入（不驗證輸出）
        """
        if progress_csv_path is None:
            progress_csv_path = self.progress_file

        completed_scenes: set[str] = set()

        if not os.path.exists(progress_csv_path):
            self.logger.info("未找到進度檔案，從頭開始處理")
            return completed_scenes

        try:
            with open(progress_csv_path, "r", encoding="utf-8-sig") as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get("狀態") == "完成":
                        scene_key = f"{row.get('區域', '')}/{row.get('場景', '')}"
                        completed_scenes.add(scene_key)

            self.logger.info(f"載入進度檔案: 發現 {len(completed_scenes)} 個已完成場景")

        except Exception as e:
            self.logger.error(f"載入進度檔案失敗: {e}")

        return completed_scenes

    def _load_and_verify_completed_scenes(self, progress_csv_path: str | None = None) -> set[str]:
        """
        載入並驗證已完成的場景（增強版）

        從output目錄下的progress.csv讀取已完成場景，
        並驗證每個場景在output目錄下確實有對應的資料夾

        Args:
            progress_csv_path: 進度CSV檔案路徑，如不提供則搜尋所有可能位置

        Returns:
            已完成且驗證存在的場景集合，格式為 "區域/場景"
        """
        completed_scenes: set[str] = set()

        # 查找output目錄下的progress.csv檔案
        progress_csv_paths = []

        if progress_csv_path:
            # 如果指定了檔案路徑，直接使用
            if os.path.exists(progress_csv_path):
                progress_csv_paths.append(progress_csv_path)
        else:
            # 1. 首先檢查output根目錄
            main_progress_file = os.path.join(self.output_path, "progress.csv")
            if os.path.exists(main_progress_file):
                progress_csv_paths.append(main_progress_file)

            # 2. 查找所有可能的progress檔案（包含時間戳的）
            if os.path.exists(self.output_path):
                for item in os.listdir(self.output_path):
                    if item.startswith("progress_") and item.endswith(".csv"):
                        progress_csv_paths.append(os.path.join(self.output_path, item))

            # 3. 如果沒找到progress檔案，檢查當前工作目錄
            if not progress_csv_paths:
                current_progress = os.path.join(os.getcwd(), "progress.csv")
                if os.path.exists(current_progress):
                    progress_csv_paths.append(current_progress)

        if not progress_csv_paths:
            self.logger.info("未找到任何progress.csv檔案，從頭開始處理")
            return completed_scenes

        # 載入所有progress檔案中的已完成場景
        all_completed_scenes = set()
        for progress_file in progress_csv_paths:
            try:
                self.logger.info(f"讀取進度檔案: {progress_file}")
                file_completed = self._load_completed_from_csv(progress_file)
                all_completed_scenes.update(file_completed)
            except Exception as e:
                self.logger.warning(f"讀取進度檔案失敗 {progress_file}: {e}")

        # 驗證每個已完成場景的輸出是否存在
        verified_count = 0
        self.logger.info("🔍 開始驗證已完成場景的輸出狀態...")

        for scene_key in all_completed_scenes:
            self.logger.info(f"🔎 檢查 {scene_key} 確認完成狀態...")

            if self._verify_scene_output_exists(scene_key):
                completed_scenes.add(scene_key)
                verified_count += 1
                self.logger.info(f"✅ 檢查 {scene_key} 確認完成 - 跳過處理")
            else:
                self.logger.warning(f"⚠️ 檢查 {scene_key} 未完成 - 輸出不存在，將重新處理")

        if verified_count > 0:
            self.logger.info(
                f"📋 恢復模式摘要：載入 {len(all_completed_scenes)} 個已完成場景，驗證存在 {verified_count} 個"
            )
            self.logger.info(
                f"🚀 將延續已完成的工作，繼續處理剩餘的 {len(all_completed_scenes) - verified_count} 個場景"
            )
        else:
            self.logger.info("📋 未找到可恢復的已完成場景，將從頭開始處理")

        return completed_scenes

    def _load_completed_from_csv(self, csv_file_path: str) -> set[str]:
        """
        從CSV檔案載入已完成的場景

        Args:
            csv_file_path: CSV檔案路徑

        Returns:
            已完成場景集合
        """
        completed_scenes = set()

        try:
            with open(csv_file_path, "r", encoding="utf-8-sig") as f:
                # 嘗試讀取第一行來檢測格式
                first_line = f.readline().strip()
                f.seek(0)

                # 如果第一行包含中文標題，使用DictReader
                if "區域" in first_line or "場景" in first_line or "狀態" in first_line:
                    reader = csv.DictReader(f)
                    for row in reader:
                        status = row.get("狀態", "").strip()
                        if status == "完成":
                            district = row.get("區域", "").strip()
                            scene = row.get("場景", "").strip()
                            if district and scene:
                                scene_key = f"{district}/{scene}"
                                completed_scenes.add(scene_key)
                else:
                    # 否則假設是簡單的CSV格式
                    reader = csv.reader(f)
                    for row in reader:
                        if len(row) >= 4:  # 時間戳, 區域, 場景, 狀態
                            if len(row) > 3 and row[3].strip() == "完成":
                                district = row[1].strip()
                                scene = row[2].strip()
                                if district and scene:
                                    scene_key = f"{district}/{scene}"
                                    completed_scenes.add(scene_key)

        except Exception as e:
            self.logger.error(f"讀取CSV檔案失敗 {csv_file_path}: {e}")

        return completed_scenes

    def _verify_scene_output_exists(self, scene_key: str) -> bool:
        """
        驗證場景輸出是否存在

        Args:
            scene_key: 場景鍵值，格式為 "區域/場景"

        Returns:
            True如果輸出存在，False如果不存在
        """
        try:
            parts = scene_key.split("/")
            if len(parts) != 2:
                return False

            district, scene = parts

            # 特別處理 pano_to_cube 模式：default 區域的場景直接在輸出根目錄
            if district == "default":
                # 檢查場景直接在輸出根目錄下
                scene_output_dir = os.path.join(self.output_path, scene)
                if os.path.exists(scene_output_dir) and os.path.isdir(scene_output_dir):
                    # 檢查目錄是否包含金字塔結構
                    if self._is_valid_pyramid_output(scene_output_dir):
                        return True
                
                # 如果上面失敗，檢查是否有 default 子目錄
                fallback_dir = os.path.join(self.output_path, district, scene)
                if os.path.exists(fallback_dir) and os.path.isdir(fallback_dir):
                    if self._is_valid_pyramid_output(fallback_dir):
                        return True
            else:
                # 正常的 cube_to_cube 和 list_cube 模式：區域/場景 結構
                scene_output_dir = os.path.join(self.output_path, district, scene)
                if os.path.exists(scene_output_dir) and os.path.isdir(scene_output_dir):
                    if self._is_valid_pyramid_output(scene_output_dir):
                        return True

            # 檢查可能的全景圖清單模式輸出格式
            district_dir = os.path.join(self.output_path, district)
            if os.path.exists(district_dir):
                for item in os.listdir(district_dir):
                    if scene in item and "cubemap" in item:
                        cubemap_dir = os.path.join(district_dir, item)
                        if self._is_valid_pyramid_output(cubemap_dir):
                            return True

            return False

        except Exception as e:
            self.logger.debug(f"驗證場景輸出時發生錯誤 {scene_key}: {e}")
            return False

    def _is_valid_pyramid_output(self, output_dir: str) -> bool:
        """
        檢查是否為有效的金字塔輸出目錄
        
        Args:
            output_dir: 輸出目錄路徑
            
        Returns:
            True 如果是有效的金字塔輸出
        """
        try:
            if not os.path.exists(output_dir) or not os.path.isdir(output_dir):
                return False
            
            # 檢查是否為空目錄
            if not os.listdir(output_dir):
                return False
            
            # 檢查關鍵文件/目錄
            has_html5 = os.path.exists(os.path.join(output_dir, "html5"))
            has_face_dirs = any(
                os.path.isdir(os.path.join(output_dir, str(i))) 
                for i in range(6)
            )
            has_thumbnail = os.path.exists(os.path.join(output_dir, "thumbnail.jpg"))
            
            # 至少要有 html5 目錄或金字塔面目錄
            return has_html5 or has_face_dirs or has_thumbnail
            
        except Exception:
            return False

    def should_skip_scene(
        self,
        district: str,
        scene: str,
        completed_scenes: set[str],
        output_path: str | None = None,
    ) -> bool:
        """
        檢查場景是否應該跳過（已完成且輸出存在）

        Args:
            district: 區域名稱
            scene: 場景名稱
            completed_scenes: 已完成場景集合
            output_path: 輸出路徑，如不提供則使用默認

        Returns:
            True表示應該跳過，False表示需要處理
        """
        scene_key = f"{district}/{scene}"

        # 檢查是否在已完成列表中
        if scene_key not in completed_scenes:
            return False

        # 場景在已完成列表中，提供詳細的跳過日誌
        self.logger.info(f"⏭️  {scene_key} 已確認完成，跳過處理")
        return True

    def save_progress(
        self,
        district: str,
        scene: str,
        status: str,
        processing_time: float = 0.0,
        note: str = "",
    ):
        """
        保存進度記錄

        Args:
            district: 區域名稱
            scene: 場景名稱
            status: 處理狀態（開始處理、完成、失敗、錯誤）
            processing_time: 處理時間（秒）
            note: 備註信息
        """
        if not self.enable_csv:
            return

        record = ProgressRecord(
            timestamp=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            district=district,
            scene=scene,
            status=status,
            processing_time=processing_time,
            note=note,
        )

        try:
            with open(self.progress_file, "a", encoding="utf-8", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(
                    [
                        record.timestamp,
                        record.district,
                        record.scene,
                        record.status,
                        f"{record.processing_time:.2f}",
                        record.note,
                    ]
                )

        except Exception as e:
            self.logger.error(f"保存進度失敗: {e}")

    def save_blur_stats(
        self,
        district: str,
        scene: str,
        face_id: int,
        face_name: str,
        blur_regions: list[dict],
    ):
        """
        保存模糊統計

        Args:
            district: 區域名稱
            scene: 場景名稱
            face_id: 面ID
            face_name: 面名稱
            blur_regions: 模糊區域列表
        """
        if not self.enable_csv or not blur_regions:
            return

        # 計算統計數據
        total_area = sum(region.get("area", 0) for region in blur_regions)
        avg_confidence = sum(region.get("confidence", 0) for region in blur_regions) / len(
            blur_regions
        )
        details = f"{len(blur_regions)}個區域"

        record = BlurStatsRecord(
            timestamp=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            district=district,
            scene=scene,
            face_id=face_id,
            face_name=face_name,
            blur_count=len(blur_regions),
            total_area=total_area,
            avg_confidence=avg_confidence,
            details=details,
        )

        try:
            with open(self.blur_stats_file, "a", encoding="utf-8", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(
                    [
                        record.timestamp,
                        record.district,
                        record.scene,
                        record.face_id,
                        record.face_name,
                        record.blur_count,
                        f"{record.total_area:.0f}",
                        f"{record.avg_confidence:.3f}",
                        record.details,
                    ]
                )

        except Exception as e:
            self.logger.error(f"保存模糊統計失敗: {e}")

    def save_final_report(self, stats: Dict, total_time: float):
        """
        保存最終統計報告

        Args:
            stats: 統計數據字典
            total_time: 總處理時間
        """
        if not self.enable_csv:
            return

        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            with open(self.final_report_file, "w", encoding="utf-8", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(["項目", "數值", "單位", "備註"])
                writer.writerow(["總處理時間", f"{total_time:.2f}", "秒", ""])
                writer.writerow(["成功處理", stats.get("total_processed", 0), "個", ""])
                writer.writerow(["失敗項目", stats.get("total_failed", 0), "個", ""])
                writer.writerow(
                    [
                        "全景圖轉換",
                        stats.get("panorama_converted", 0),
                        "張",
                        "全景圖→立方體",
                    ]
                )
                writer.writerow(
                    [
                        "立方體處理",
                        stats.get("cube_processed", 0),
                        "個",
                        "立方體結構處理",
                    ]
                )
                writer.writerow(
                    [
                        "模糊檢測",
                        stats.get("blur_detections", 0),
                        "個",
                        "檢測到的模糊區域",
                    ]
                )
                writer.writerow(
                    [
                        "平均處理時間",
                        f"{self._calculate_avg_time(stats):.2f}",
                        "秒/項",
                        "",
                    ]
                )
                writer.writerow(["報告生成時間", timestamp, "", ""])

            self.logger.info(f"最終報告已保存: {self.final_report_file}")

        except Exception as e:
            self.logger.error(f"保存最終報告失敗: {e}")

    def _calculate_avg_time(self, stats: Dict) -> float:
        """計算平均處理時間"""
        processing_times = stats.get("processing_times", [])
        return sum(processing_times) / len(processing_times) if processing_times else 0.0

    def get_progress_summary(self) -> Dict:
        """
        獲取進度摘要

        Returns:
            進度摘要字典
        """
        if not os.path.exists(self.progress_file):
            return {"total": 0, "completed": 0, "failed": 0, "in_progress": 0}

        summary = {
            "total": 0,
            "completed": 0,
            "failed": 0,
            "in_progress": 0,
            "error": 0,
        }

        try:
            with open(self.progress_file, "r", encoding="utf-8-sig") as f:
                reader = csv.DictReader(f)
                for row in reader:
                    summary["total"] += 1
                    status = row.get("狀態", "").lower()

                    if status == "完成":
                        summary["completed"] += 1
                    elif status == "失敗":
                        summary["failed"] += 1
                    elif status == "錯誤":
                        summary["error"] += 1
                    elif status == "開始處理":
                        summary["in_progress"] += 1

        except Exception as e:
            self.logger.error(f"讀取進度摘要失敗: {e}")

        return summary

    def display_final_stats(self, stats: Dict, total_time: float):
        """
        顯示最終統計信息

        Args:
            stats: 統計數據字典
            total_time: 總處理時間
        """
        self.logger.info("=" * 50)
        self.logger.info("處理完成統計報告")
        self.logger.info("=" * 50)

        # 基本統計（無論是否有模型）
        self.logger.info(f"總處理時間: {total_time:.2f}秒")
        self.logger.info(f"成功處理: {stats.get('total_processed', 0)}")
        self.logger.info(f"失敗項目: {stats.get('total_failed', 0)}")

        # 全景轉換統計（重要：無模型時也要記錄）
        if stats.get("panorama_converted", 0) > 0:
            self.logger.info(f"全景圖轉立方體: {stats['panorama_converted']}張")

        # 立方體處理統計
        if stats.get("cube_processed", 0) > 0:
            self.logger.info(f"立方體結構處理: {stats['cube_processed']}個")

        # 模糊檢測統計（僅有模型時顯示）
        if stats.get("blur_detections", 0) > 0:
            self.logger.info(f"模糊區域檢測: {stats['blur_detections']}個")

        # 平均處理時間
        avg_time = self._calculate_avg_time(stats)
        if avg_time > 0:
            self.logger.info(f"平均處理時間: {avg_time:.2f}秒/項")

        # 保存最終報告
        self.save_final_report(stats, total_time)

        self.logger.info("=" * 50)


class TemporalProgressManager:
    """
    臨時進度管理器（用於向後兼容）

    提供簡化的進度管理功能，用於不需要完整CSV功能的場景。
    """

    def __init__(self, output_path: str):
        self.output_path = output_path
        self.progress_data: dict[str, dict[str, str]] = {}

    def mark_completed(self, district: str, scene: str):
        """標記場景為已完成"""
        scene_key = f"{district}/{scene}"
        self.progress_data[scene_key] = {
            "status": "completed",
            "timestamp": datetime.datetime.now().isoformat(),
        }

    def is_completed(self, district: str, scene: str) -> bool:
        """檢查場景是否已完成"""
        scene_key = f"{district}/{scene}"
        return (
            scene_key in self.progress_data
            and self.progress_data[scene_key]["status"] == "completed"
        )

    def get_completion_count(self) -> int:
        """獲取完成數量"""
        return sum(1 for item in self.progress_data.values() if item["status"] == "completed")
