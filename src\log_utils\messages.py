"""
統一的錯誤訊息和日誌訊息管理模組

提供一致的訊息格式和本地化支援，
確保整個 log_utils 模組有統一的訊息風格。
"""

from typing import Dict, Any


class ErrorMessages:
    """
    統一的錯誤訊息管理
    
    定義所有錯誤訊息模板，提供格式化方法。
    """
    
    # 導入相關錯誤
    IMPORT_FAILED = "模組 '{module}' 導入失敗: {error}"
    IMPORT_FALLBACK_ACTIVATED = "模組 '{module}' 主要導入失敗，使用備用導入"
    IMPORT_DEGRADED_MODE = "模組 '{module}' 所有導入失敗，啟用降級模式: {reason}"
    
    # 處理器相關錯誤
    HANDLER_CREATION_FAILED = "處理器 '{handler_type}' 創建失敗: {error}"
    HANDLER_FALLBACK_ACTIVATED = "處理器 '{handler_type}' 創建失敗，使用備用處理器"
    HANDLER_EMERGENCY_CONSOLE = "檔案處理器創建失敗，啟用緊急控制台輸出: {error}"
    
    # 格式器相關錯誤
    FORMATTER_CREATION_FAILED = "格式器 '{formatter_type}' 創建失敗: {error}"
    FORMATTER_FALLBACK_ACTIVATED = "格式器 '{formatter_type}' 創建失敗，使用備用格式器"
    FORMATTER_THEME_INVALID = "未知的顏色主題: '{theme}'. 可用主題: {available_themes}"
    
    # 配置相關錯誤
    CONFIG_VALIDATION_FAILED = "配置驗證失敗: {errors}"
    CONFIG_PRESET_UNKNOWN = "未知預設配置: '{preset}'. 可用預設: {available_presets}"
    CONFIG_FIELD_INVALID = "配置欄位 '{field}' 無效: {reason}"
    CONFIG_PATH_INVALID = "配置路徑 '{path}' 無效: {reason}"
    
    # 管理器相關錯誤
    MANAGER_ALREADY_SHUTDOWN = "日誌管理器已關閉，無法執行操作"
    MANAGER_LOGGER_NOT_FOUND = "日誌器 '{name}' 不存在"
    MANAGER_RELOAD_FAILED = "重新載入配置失敗: {error}"
    
    # 系統相關錯誤
    FILE_OPERATION_FAILED = "檔案操作失敗: {operation} - {error}"
    DIRECTORY_CREATION_FAILED = "目錄創建失敗: {path} - {error}"
    LOG_CLEANUP_FAILED = "日誌清理失敗: {error}"
    
    # 一般性錯誤
    OPERATION_FAILED = "操作 '{operation}' 執行失敗: {error}"
    DEPENDENCY_MISSING = "缺少必要依賴: {dependency}"
    FEATURE_NOT_AVAILABLE = "功能 '{feature}' 在當前環境中不可用: {reason}"
    
    @classmethod
    def format_error(cls, template: str, **kwargs) -> str:
        """
        格式化錯誤訊息
        
        :param template: 訊息模板
        :param kwargs: 格式化參數
        :return: 格式化後的訊息
        """
        try:
            return template.format(**kwargs)
        except KeyError as e:
            return f"訊息格式化失敗 - 缺少參數: {e}. 原始模板: {template}"
        except Exception as e:
            return f"訊息格式化失敗: {e}. 原始模板: {template}"


class InfoMessages:
    """
    資訊性訊息管理
    
    定義系統狀態、操作成功等資訊訊息。
    """
    
    # 初始化相關
    MANAGER_INITIALIZED = "日誌管理器已初始化: {manager_type}"
    LOGGER_CREATED = "日誌器 '{name}' 創建成功"
    HANDLER_ATTACHED = "處理器 '{handler_type}' 已附加到日誌器 '{logger_name}'"
    
    # 配置相關
    CONFIG_LOADED = "配置 '{config_name}' 載入成功"
    CONFIG_VALIDATED = "配置驗證通過"
    PRESET_APPLIED = "預設配置 '{preset}' 已應用"
    
    # 系統操作
    LOG_CLEANUP_SUCCESS = "日誌清理完成，清理了 {count} 個舊檔案"
    DIRECTORY_CREATED = "目錄創建成功: {path}"
    MANAGER_SHUTDOWN = "日誌管理器已安全關閉"
    
    # 降級模式
    DEGRADED_MODE_ACTIVATED = "降級模式已啟用: {reason}"
    FALLBACK_ACTIVATED = "備用方案已啟用: {component}"
    
    @classmethod
    def format_info(cls, template: str, **kwargs) -> str:
        """
        格式化資訊訊息
        
        :param template: 訊息模板
        :param kwargs: 格式化參數
        :return: 格式化後的訊息
        """
        try:
            return template.format(**kwargs)
        except Exception:
            return template


class WarningMessages:
    """
    警告訊息管理
    
    定義系統警告和注意事項。
    """
    
    # 降級警告
    USING_FALLBACK = "正在使用備用 {component}: {reason}"
    FEATURE_LIMITED = "功能 '{feature}' 在當前模式下受限: {limitation}"
    PERFORMANCE_WARNING = "性能警告: {warning}"
    
    # 配置警告
    CONFIG_DEPRECATED = "配置選項 '{option}' 已棄用，請使用 '{replacement}'"
    CONFIG_SUBOPTIMAL = "配置 '{config}' 不是最佳選擇: {suggestion}"
    
    # 系統警告
    RESOURCE_LOW = "系統資源不足: {resource}"
    FILE_PERMISSION_WARNING = "檔案權限可能不足: {file}"
    
    @classmethod
    def format_warning(cls, template: str, **kwargs) -> str:
        """
        格式化警告訊息
        
        :param template: 訊息模板
        :param kwargs: 格式化參數
        :return: 格式化後的訊息
        """
        try:
            return template.format(**kwargs)
        except Exception:
            return template


class MessageFormatter:
    """
    訊息格式化工具
    
    提供統一的訊息格式化方法和工具函數。
    """
    
    @staticmethod
    def format_list(items: list, separator: str = ", ", last_separator: str = " 和 ") -> str:
        """
        格式化列表為友善的字串
        
        :param items: 要格式化的項目列表
        :param separator: 項目間的分隔符
        :param last_separator: 最後一個項目前的分隔符
        :return: 格式化後的字串
        """
        if not items:
            return ""
        
        items_str = [str(item) for item in items]
        
        if len(items_str) == 1:
            return items_str[0]
        elif len(items_str) == 2:
            return f"{items_str[0]}{last_separator}{items_str[1]}"
        else:
            return f"{separator.join(items_str[:-1])}{last_separator}{items_str[-1]}"
    
    @staticmethod
    def format_dict(data: Dict[str, Any], max_items: int = 5) -> str:
        """
        格式化字典為友善的字串
        
        :param data: 要格式化的字典
        :param max_items: 最大顯示項目數
        :return: 格式化後的字串
        """
        if not data:
            return "{}"
        
        items = list(data.items())
        if len(items) <= max_items:
            formatted_items = [f"{k}: {v}" for k, v in items]
            return "{" + ", ".join(formatted_items) + "}"
        else:
            formatted_items = [f"{k}: {v}" for k, v in items[:max_items]]
            remaining = len(items) - max_items
            return "{" + ", ".join(formatted_items) + f", ...還有 {remaining} 項" + "}"
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
        """
        截斷過長的文字
        
        :param text: 原始文字
        :param max_length: 最大長度
        :param suffix: 截斷後的後綴
        :return: 截斷後的文字
        """
        if len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化檔案大小
        
        :param size_bytes: 檔案大小（位元組）
        :return: 格式化後的大小字串
        """
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024.0 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f}{size_names[i]}"


class MessageCodes:
    """
    訊息代碼定義
    
    為不同類型的訊息定義唯一代碼，便於追蹤和分析。
    """
    
    # 錯誤代碼 (E1000-E9999)
    E1001 = "E1001"  # 導入錯誤
    E1002 = "E1002"  # 配置錯誤
    E1003 = "E1003"  # 處理器錯誤
    E1004 = "E1004"  # 格式器錯誤
    E1005 = "E1005"  # 管理器錯誤
    E1006 = "E1006"  # 檔案操作錯誤
    
    # 警告代碼 (W1000-W9999)
    W1001 = "W1001"  # 降級模式警告
    W1002 = "W1002"  # 配置警告
    W1003 = "W1003"  # 性能警告
    W1004 = "W1004"  # 資源警告
    
    # 資訊代碼 (I1000-I9999)
    I1001 = "I1001"  # 初始化成功
    I1002 = "I1002"  # 配置載入成功
    I1003 = "I1003"  # 操作完成
    I1004 = "I1004"  # 系統狀態