"""
偵測資料結構

用於偵測結果和相關資訊的核心資料結構。
"""

from dataclasses import dataclass


@dataclass
class DetectionBox:
    """使用現代化型別提示的偵測框資料結構"""

    x1: int
    y1: int
    x2: int
    y2: int
    confidence: float
    class_id: int

    @property
    def width(self) -> int:
        """取得偵測框寬度"""
        return self.x2 - self.x1

    @property
    def height(self) -> int:
        """取得偵測框高度"""
        return self.y2 - self.y1

    @property
    def area(self) -> int:
        """取得偵測框面積"""
        return self.width * self.height

    @property
    def center(self) -> tuple[int, int]:
        """取得偵測框中心座標"""
        return ((self.x1 + self.x2) // 2, (self.y1 + self.y2) // 2)

    def to_dict(self) -> dict[str, int | float]:
        """轉換為字典表示"""
        return {
            "x1": self.x1,
            "y1": self.y1,
            "x2": self.x2,
            "y2": self.y2,
            "confidence": self.confidence,
            "class_id": self.class_id,
            "width": self.width,
            "height": self.height,
            "area": self.area,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "DetectionBox":
        """從字典建立"""
        return cls(
            x1=data["x1"],
            y1=data["y1"],
            x2=data["x2"],
            y2=data["y2"],
            confidence=data["confidence"],
            class_id=data["class_id"],
        )
