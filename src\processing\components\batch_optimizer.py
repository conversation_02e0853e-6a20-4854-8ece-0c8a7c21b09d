#!/usr/bin/env python3
"""
批處理優化器模組
Batch Optimizer Module

提供智能批處理優化功能，解決重複圖像載入和串行處理瓶頸。
Provides intelligent batch optimization to resolve duplicate image loading and serial processing bottlenecks.

Author: <PERSON> Assistant
Date: 2025-01-19
"""

# 1. 標準庫
import asyncio
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Callable

# 2. 第三方庫
import numpy as np

# 3. 本地模組
try:
    from utils.image_utils import get_image_info
    HAS_UTILS = True
except ImportError:
    HAS_UTILS = False


@dataclass
class BatchMetrics:
    """批處理性能指標"""
    total_images: int = 0
    successful_processed: int = 0
    failed_processed: int = 0
    total_time: float = 0.0
    avg_time_per_image: float = 0.0
    memory_peak_mb: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0


class BatchOptimizer:
    """
    批處理優化器，解決性能瓶頸。
    
    特性:
    - 智能圖像分組策略
    - 並行處理不同組
    - 結果緩存機制
    - 記憶體感知調度
    """
    
    def __init__(self, max_workers: int = 4, cache_size_mb: int = 2048):
        """
        初始化批處理優化器
        
        Args:
            max_workers: 最大並行工作線程數
            cache_size_mb: 緩存大小限制（MB）
        """
        self.max_workers = max_workers
        self.cache_size_mb = cache_size_mb
        self._image_cache: dict[str, np.ndarray] = {}
        self._result_cache: dict[str, Any] = {}
        self._metrics = BatchMetrics()
        self.logger = logging.getLogger(__name__)
        
    async def batch_process_images(
        self, 
        image_paths: list[str], 
        processing_func: Callable,
        chunk_size: int = 8
    ) -> dict[str, Any]:
        """
        批處理圖像，避免重複載入
        
        Args:
            image_paths: 圖像路徑列表
            processing_func: 處理函數
            chunk_size: 分組大小
            
        Returns:
            處理結果字典
        """
        start_time = time.time()
        self._metrics.total_images = len(image_paths)
        
        try:
            # 1. 圖像分組策略
            image_groups = self._group_images_by_size(image_paths, chunk_size)
            self.logger.info(f"將 {len(image_paths)} 個圖像分為 {len(image_groups)} 組進行處理")
            
            # 2. 並行處理各組
            results = {}
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                for group in image_groups:
                    future = executor.submit(self._process_group, group, processing_func)
                    futures.append(future)
                    
                for completed_future in as_completed(futures):
                    try:
                        group_results = completed_future.result()
                        results.update(group_results)
                        self._metrics.successful_processed += len(group_results)
                    except Exception as e:
                        self.logger.error(f"處理組時發生錯誤: {e}")
                        self._metrics.failed_processed += 1
                        
        except Exception as e:
            self.logger.error(f"批處理過程發生錯誤: {e}")
            raise
        finally:
            self._metrics.total_time = time.time() - start_time
            if self._metrics.successful_processed > 0:
                self._metrics.avg_time_per_image = (
                    self._metrics.total_time / self._metrics.successful_processed
                )
                
        return results
    
    def _group_images_by_size(self, image_paths: list[str], chunk_size: int) -> list[list[str]]:
        """
        按圖像大小智能分組，優化記憶體使用
        
        Args:
            image_paths: 圖像路徑列表
            chunk_size: 基礎分組大小
            
        Returns:
            分組後的圖像路徑列表
        """
        if not HAS_UTILS:
            # 簡單分組fallback
            return [
                image_paths[i:i + chunk_size] 
                for i in range(0, len(image_paths), chunk_size)
            ]
        
        # 獲取圖像大小信息
        size_groups: dict[str, list[str]] = {"small": [], "medium": [], "large": []}
        
        for image_path in image_paths:
            try:
                info = get_image_info(image_path)
                if info and 'width' in info and 'height' in info:
                    total_pixels = info['width'] * info['height']
                    if total_pixels < 1024 * 1024:  # < 1MP
                        size_groups["small"].append(image_path)
                    elif total_pixels < 4 * 1024 * 1024:  # < 4MP
                        size_groups["medium"].append(image_path)
                    else:  # >= 4MP
                        size_groups["large"].append(image_path)
                else:
                    size_groups["medium"].append(image_path)  # 默認分組
            except Exception as e:
                self.logger.warning(f"無法獲取圖像 {image_path} 的大小信息: {e}")
                size_groups["medium"].append(image_path)
        
        # 創建分組
        groups = []
        for size_category, paths in size_groups.items():
            if paths:
                category_chunk_size = chunk_size
                if size_category == "large":
                    category_chunk_size = max(1, chunk_size // 2)  # 大圖像較小分組
                elif size_category == "small":
                    category_chunk_size = chunk_size * 2  # 小圖像較大分組
                    
                for i in range(0, len(paths), category_chunk_size):
                    groups.append(paths[i:i + category_chunk_size])
        
        return groups
    
    def _process_group(self, image_paths: list[str], processing_func: Callable) -> dict[str, Any]:
        """
        處理單個圖像組
        
        Args:
            image_paths: 圖像路徑列表
            processing_func: 處理函數
            
        Returns:
            處理結果字典
        """
        group_results = {}
        
        for image_path in image_paths:
            try:
                # 檢查緩存
                cache_key = str(Path(image_path).resolve())
                if cache_key in self._result_cache:
                    group_results[image_path] = self._result_cache[cache_key]
                    self._metrics.cache_hits += 1
                    continue
                
                # 處理圖像
                result = processing_func(image_path)
                group_results[image_path] = result
                
                # 緩存結果
                self._result_cache[cache_key] = result
                self._metrics.cache_misses += 1
                
            except Exception as e:
                self.logger.error(f"處理圖像 {image_path} 時發生錯誤: {e}")
                group_results[image_path] = {"error": str(e)}
        
        return group_results
    
    def get_metrics(self) -> BatchMetrics:
        """獲取批處理性能指標"""
        return self._metrics
    
    def clear_cache(self):
        """清理緩存"""
        self._image_cache.clear()
        self._result_cache.clear()
        self.logger.info("批處理優化器緩存已清理")
    
    def get_cache_stats(self) -> dict[str, Any]:
        """獲取緩存統計信息"""
        total_requests = self._metrics.cache_hits + self._metrics.cache_misses
        hit_rate = self._metrics.cache_hits / total_requests if total_requests > 0 else 0
        
        return {
            "cache_hits": self._metrics.cache_hits,
            "cache_misses": self._metrics.cache_misses,
            "hit_rate": hit_rate,
            "cached_images": len(self._image_cache),
            "cached_results": len(self._result_cache)
        }


def create_batch_optimizer(max_workers: int = 4, cache_size_mb: int = 2048) -> BatchOptimizer:
    """
    創建批處理優化器的工廠函數
    
    Args:
        max_workers: 最大並行工作線程數  
        cache_size_mb: 緩存大小限制（MB）
        
    Returns:
        BatchOptimizer實例
    """
    return BatchOptimizer(max_workers=max_workers, cache_size_mb=cache_size_mb)