"""
常數定義模組

本模組集中定義了整個全景圖處理系統中使用的所有靜態常數。
這些常數涵蓋了檔案處理、AI檢測、影像處理、座標系統、錯誤碼等多個方面，
旨在提供一個統一、穩定且易於維護的常數參考中心。
"""

import math
from enum import Enum, IntEnum
from typing import Dict, List, Tuple

# ==================== 檔案相關常數 ====================

# 定義系統支援的輸入圖像檔案副檔名列表。
SUPPORTED_IMAGE_EXTS: List[str] = [
    ".jpg",
    ".jpeg",
    ".png",
    ".bmp",
    ".tif",
    ".tiff",
    ".webp",
]

# 定義系統支援的標籤檔案格式，用於讀取AI模型的標註資訊。
SUPPORTED_LABEL_EXTS: List[str] = [".json", ".xml", ".txt"]

# 預設輸出的檔案名稱，用於預覽圖、縮圖和進度記錄。
DEFAULT_PREVIEW_NAME = "preview.jpg"  # 預覽圖的預設檔案名稱
DEFAULT_THUMBNAIL_NAME = "thumbnail.jpg"  # 縮圖的預設檔案名稱
DEFAULT_PROGRESS_FILE = "progress.csv"  # 處理進度記錄的預設檔案名稱

# 系統處理的檔案大小限制，單位為MB，以防止記憶體溢出。
MAX_IMAGE_SIZE_MB = 500  # 單一影像檔案的最大大小
MAX_BATCH_SIZE_MB = 5000  # 批次處理的總檔案大小上限

# 用於 LRU (Least Recently Used) 快取的預設大小，主要用於快取座標轉換等計算結果。
_CACHE_SIZE = 8


# ==================== 檢測相關常數 ====================

# AI 物件偵測的相關預設閾值。
DEFAULT_CONF_THRESHOLD = 0.05  # 物件偵測的預設置信度閾值，低於此值的偵測結果將被忽略。
DEFAULT_IOU_THRESHOLD = 0.3  # 非極大值抑制（NMS）的交並比（IoU）閾值，用於合併重疊的偵測框。
DEFAULT_MAX_AREA_RATIO = 0.03  # 偵測框相對於影像總面積的最大比例，用於過濾過大的無效偵測。
DEFAULT_FACE5_TEST_CONF = 0.25  # 專用於Face5模型的測試置信度。

# 預設的模糊化處理核心大小，用於對偵測到的隱私區域進行模糊。
DEFAULT_BLUR_KERNEL_SIZE = (51, 51)
DEFAULT_BLUR_SIGMA = 30  # 高斯模糊的標準差，控制模糊程度

# 偵測相關的幾何設定。
DEFAULT_FACE_PADDING = 0.1  # 人臉偵測框的擴充比例，確保模糊區域能完全覆蓋人臉。
DEFAULT_DETECTION_BATCH_SIZE = 4  # AI模型進行批次推論時的預設批次大小。

# ==================== 處理相關常數 ====================

# 影像處理流程中的性能相關常數。
MAX_WORKERS = 4  # 平行處理時的最大工作者（執行緒/進程）數量。
DEFAULT_BATCH_SIZE = 10  # 批次處理影像的預設數量。
DEFAULT_QUEUE_SIZE = 100  # 處理佇列的最大容量。

# 立方體映射（Cubemap）的尺寸設定。
DEFAULT_CUBE_SIZE = 2048  # 生成的立方體每個面的預設邊長（像素）。
MIN_CUBE_SIZE = 512  # 允許的最小立方體面邊長。
MAX_CUBE_SIZE = 8192  # 允許的最大立方體面邊長。

# 影像金字塔（Pyramid）的生成設定。
DEFAULT_PYRAMID_LEVELS = 6  # 預設生成的金字塔層級數。
DEFAULT_TILE_SIZE = 256  # 金字塔切片的預設大小（像素）。

# 系統記憶體管理相關常數。
DEFAULT_MEMORY_LIMIT_MB = 4096  # 預設的系統記憶體使用上限。
DEFAULT_CACHE_LIMIT_MB = 1024  # 預設的快取記憶體使用上限。


# ==================== 立方體映射常數 ====================


class Face(IntEnum):
    """
    定義立方體六個面的索引，使用整數枚舉便於陣列操作。
    """
    FRONT = 0  # 前面 (Z軸正方向)
    RIGHT = 1  # 右面 (X軸正方向)
    BACK = 2  # 後面 (Z軸負方向)
    LEFT = 3  # 左面 (X軸負方向)
    UP = 4  # 上面 (Y軸正方向)
    DOWN = 5  # 下面 (Y軸負方向)


# 立方體面的標準單字母縮寫名稱。
FACE_NAMES: List[str] = ["F", "R", "B", "L", "U", "D"]

# 立方體面縮寫與繁體中文名稱的對應字典。
FACE_NAMES_CN: Dict[str, str] = {
    "F": "前面",
    "R": "右面",
    "B": "後面",
    "L": "左面",
    "U": "上面",
    "D": "下面",
}

# 定義立方體面在「骰子展開圖」佈局中的二維網格座標。
DICE_LAYOUT: Dict[str, Tuple[int, int]] = {
    "U": (1, 0),  # 上面
    "L": (0, 1),  # 左面
    "F": (1, 1),  # 前面
    "R": (2, 1),  # 右面
    "B": (3, 1),  # 後面
    "D": (1, 2),  # 下面
}

# 指定疊加標誌（Logo）的目標面索引，通常只在底面疊加。
LOGO_FACE_INDEX = 5  # 對應 Face.DOWN


# ==================== 座標系統常數 ====================

# 數學計算中常用的圓周率相關常數。
PI = math.pi
TWO_PI = 2 * math.pi
HALF_PI = math.pi / 2

# 球面座標（UV座標）的範圍定義 (u_min, u_max, v_min, v_max)。
UV_RANGE = (-PI, PI, -HALF_PI, HALF_PI)


# 影像插值模式的枚舉（簡易版，詳細版在 interpolation.py）。
class InterpolationMode(Enum):
    """簡易插值模式枚舉。"""
    NEAREST = "nearest"  # 最近鄰插值
    LINEAR = "linear"  # 線性插值
    CUBIC = "cubic"  # 三次樣條插值
    LANCZOS = "lanczos"  # Lanczos插值


# ==================== 錯誤碼定義 ====================


class ErrorCode(IntEnum):
    """
    定義系統中所有標準化的錯誤碼，便於錯誤追蹤和處理。
    """
    SUCCESS = 0  # 操作成功
    FILE_NOT_FOUND = 1001  # 檔案未找到錯誤
    INVALID_FORMAT = 1002  # 無效的檔案格式錯誤
    MEMORY_ERROR = 2001  # 記憶體分配錯誤
    GPU_ERROR = 2002  # GPU相關錯誤
    PROCESSING_ERROR = 3001  # 影像處理過程中的通用錯誤
    VALIDATION_ERROR = 3002  # 輸入或配置驗證失敗
    CONFIG_ERROR = 4001  # 系統配置錯誤
    UNKNOWN_ERROR = 9999  # 未知或未分類的錯誤


# ==================== 狀態碼定義 ====================


class ProcessingStatus(Enum):
    """
    定義處理任務的生命週期狀態。
    """
    PENDING = "pending"  # 等待處理
    PROCESSING = "processing"  # 正在處理
    COMPLETED = "completed"  # 處理完成
    FAILED = "failed"  # 處理失敗
    SKIPPED = "skipped"  # 因特定原因跳過
    CANCELLED = "cancelled"  # 處理被取消


class SaveMode(Enum):
    """
    定義儲存處理結果時的模式。
    """
    ALL = "ALL"  # 儲存所有處理後的立方體面
    BLUR_ONLY = "BLUR_ONLY"  # 僅儲存包含模糊區域的立方體面，以節省空間


class ProcessMode(Enum):
    """
    定義系統的主要處理流程模式。
    """
    PANO_CUBE_DETECT_PYRAMID = (
        "pano-cube-detect-pyramid"  # 完整流程：全景圖 -> 立方體圖 -> 檢測 -> 金字塔
    )
    CUBE_DETECT_PYRAMID = "cube-detect-pyramid"  # 從已有的立方體圖開始：立方體圖 -> 檢測 -> 金字塔
    LIST_CUBE_DETECT_PYRAMID = (
        "list-cube-detect-pyramid"  # 從已有的立方體圖列表開始：立方體圖列表 -> 檢測 -> 金字塔
    )


# ==================== 訊息模板 ====================

# 用於格式化標準錯誤訊息的字典。
ERROR_MESSAGES = {
    ErrorCode.FILE_NOT_FOUND: "找不到檔案: {filepath}",
    ErrorCode.INVALID_FORMAT: "無效的檔案格式: {format}",
    ErrorCode.MEMORY_ERROR: "記憶體不足: 需要 {required}MB，可用 {available}MB",
    ErrorCode.GPU_ERROR: "GPU 錯誤: {message}",
    ErrorCode.PROCESSING_ERROR: "處理錯誤: {message}",
    ErrorCode.VALIDATION_ERROR: "驗證錯誤: {message}",
    ErrorCode.CONFIG_ERROR: "配置錯誤: {message}",
    ErrorCode.UNKNOWN_ERROR: "未知錯誤: {message}",
}

# 用於向使用者顯示處理進度的訊息模板。
PROGRESS_MESSAGES = {
    "start": "開始處理 {name}...",
    "processing": "正在處理 {current}/{total} ({percent:.1f}%)...",
    "completed": "處理完成: {name}",
    "failed": "處理失敗: {name} - {error}",
    "summary": "總計: {total}, 完成: {completed}, 失敗: {failed}",
}


# ==================== 動態常數初始化 ====================


def _load_detection_classes() -> List[str]:
    """
    從外部文件 `detection_classes.txt` 動態加載AI模型可檢測的類別名稱。
    若文件不存在，則返回預設的類別列表。
    """
    try:
        # 嘗試相對 import
        from . import settings
        classes_file = settings.CONFIG_DIR / "detection_classes.txt"
    except ImportError:
        # 如果相對 import 失敗，使用絕對路徑
        from pathlib import Path
        current_dir = Path(__file__).parent
        classes_file = current_dir / "detection_classes.txt"

    if classes_file.exists():
        with open(classes_file, "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip()]
    return ["person", "face", "license_plate"]  # 預設檢測類別


def _generate_detection_colors(classes: List[str]) -> Dict[str, Tuple[int, int, int]]:
    """
    為每個檢測類別生成一個穩定且唯一的顏色。
    使用MD5雜湊演算法確保每次執行時，相同類別名稱都能得到相同的顏色，
    這對於視覺化結果的一致性非常重要。
    """
    import hashlib

    colors = {}
    for cls in classes:
        # 使用類別名稱的MD5雜湊值來生成顏色，確保顏色穩定不變。
        hash_obj = hashlib.md5(cls.encode())
        hex_color = hash_obj.hexdigest()
        # 從雜湊值中提取R, G, B分量。
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        colors[cls] = (r, g, b)
    return colors


# 檢測類別枚舉
class DetectionClass(Enum):
    """檢測類別枚舉"""
    PERSON = "person"
    FACE = "face"
    LICENSE_PLATE = "license_plate"

# 預設類別名稱
DEFAULT_CLASS_NAMES = ["person", "face", "license_plate"]

# 在模組加載時執行，初始化動態常數。
DETECTION_CLASSES = _load_detection_classes()
DETECTION_COLORS = _generate_detection_colors(DETECTION_CLASSES)


if __name__ == "__main__":
    # 此區塊的程式碼僅在直接執行此腳本時運行，用於測試和展示常數內容。
    print("=== 常數定義模組使用範例 ===")
    print(f"支援的圖像格式: {SUPPORTED_IMAGE_EXTS}")
    print(f"\n立方體面名稱: {FACE_NAMES}")
    print(f"立方體面索引: {dict(Face.__members__.items())}")
    print(f"\n錯誤碼定義: {list(ErrorCode)}")
    print(f"處理狀態: {[s.value for s in ProcessingStatus]}")
    print(f"\n動態載入的檢測類別: {DETECTION_CLASSES}")
    print(f"為檢測類別生成的顏色: {DETECTION_COLORS}")
