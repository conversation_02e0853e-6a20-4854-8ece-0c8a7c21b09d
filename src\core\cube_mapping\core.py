"""
立方體貼圖（Cubemap）處理與格式轉換模組 (core.cube_mapping.core)

本模組提供 `CubeMapper` 類別，專門用於處理立方體貼圖的各種格式之間的轉換。
支援的格式包括：
- **水平排列 (horizon)**: 六個面在水平方向上拼接成一張寬圖。
- **列表 (list)**: 一個包含六個面的 NumPy 陣列的 Python 列表。
- **字典 (dict)**: 一個以面名稱為鍵（'F', 'R', 'B', 'L', 'U', 'D'），面對應的 NumPy 陣列為值的 Python 字典。
- **骰子展開 (dice)**: 模擬骰子展開的十字形佈局。

此模組旨在提供一個高效、靈活的工具，以滿足不同應用場景對立方體貼圖資料結構的需求。
"""

import sys
import time
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import numpy as np

# 在測試環境中禁用性能監控以避免導入問題
import os
if os.environ.get("TESTING") == "1":
    HAS_PERFORMANCE_MONITOR = False
else:
    # 嘗試導入可選的高級組件
    try:
        from utils.unified_performance_monitor import get_performance_monitor
        HAS_PERFORMANCE_MONITOR = True
    except ImportError:
        HAS_PERFORMANCE_MONITOR = False

from log_utils.factory import get_logger

logger = get_logger(__name__)


class CubeMapper:
    """
    立方體貼圖處理器 (CubeMapper)

    一個多功能的工具類別，專門用於處理立方體六個面的各種格式轉換、座標生成和資料操作。
    它被設計為高效且易於使用，是連接投影演算法和後續處理（如 AI 偵測、影像渲染）的關鍵橋樑。
    """

    # 定義面的標準名稱和索引映射，確保在整個系統中的一致性。
    FACE_NAMES: List[str] = ["F", "R", "B", "L", "U", "D"]  # 前、右、後、左、上、下
    FACE_INDICES: Dict[str, int] = {name: idx for idx, name in enumerate(FACE_NAMES)}

    # 骰子展開佈局的二維網格座標，用於生成和解析骰子格式的影像。
    # 視覺化佈局:
    #      ( , U, , )
    #      (L, F, R, B)
    #      ( , D, , )
    DICE_LAYOUT: Dict[str, tuple[int, int]] = {
        "U": (1, 0),  # 上面 (Up) 在第 0 行，第 1 列
        "L": (0, 1),  # 左面 (Left) 在第 1 行，第 0 列
        "F": (1, 1),  # 前面 (Front) 在第 1 行，第 1 列
        "R": (2, 1),  # 右面 (Right) 在第 1 行，第 2 列
        "B": (3, 1),  # 後面 (Back) 在第 1 行，第 3 列
        "D": (1, 2),  # 下面 (Down) 在第 2 行，第 2 列
    }

    def __init__(self, face_size: int):
        """
        初始化立方體貼圖處理器。

        :param face_size: 每個立方體面的邊長（像素），必須為正方形。
        :raises ValueError: 如果 `face_size` 不是正整數。
        """
        if not isinstance(face_size, int) or face_size <= 0:
            raise ValueError(f"face_size 必須是正整數，但收到了 {face_size}")
        self.face_size = face_size
        self._stats: Dict[str, Union[int, float]] = {
            "total_operations": 0,
            "total_processing_time": 0.0,
        }

        # 如果性能監控模組可用，則進行初始化。
        self.performance_monitor = get_performance_monitor() if HAS_PERFORMANCE_MONITOR else None
        logger.debug(f"CubeMapper 初始化成功 - 面尺寸: {self.face_size}")

    def _get_face_slice(self, face_idx: int) -> slice:
        """根據面的索引，計算其在水平排列圖中的水平切片範圍。"""
        start = face_idx * self.face_size
        return slice(start, start + self.face_size)

    def horizon_to_list(self, cube_h: np.ndarray) -> List[np.ndarray]:
        """
        將水平排列 (Horizon) 的立方體貼圖轉換為包含六個面的列表 (List)。

        :param cube_h: 一個形狀為 (H, H*6, C) 的 NumPy 陣列。
        :return: 一個包含六個 NumPy 陣列（每個代表一個面）的列表。
        :raises ValueError: 如果輸入陣列的尺寸不符。
        """
        if cube_h.shape[0] != self.face_size or cube_h.shape[1] != self.face_size * 6:
            raise ValueError(f"水平立方體圖的尺寸應為 ({self.face_size}, {self.face_size*6}), 但收到 {cube_h.shape}")
        return np.split(cube_h, 6, axis=1)

    def horizon_to_dict(self, cube_h: np.ndarray) -> Dict[str, np.ndarray]:
        """
        將水平排列 (Horizon) 的立方體貼圖轉換為以面名稱為鍵的字典 (Dict)。

        :param cube_h: 水平排列的立方體貼圖。
        :return: 一個鍵為面名稱（'F', 'R', ...），值為對應面陣列的字典。
        """
        faces_list = self.horizon_to_list(cube_h)
        return dict(zip(self.FACE_NAMES, faces_list))

    def list_to_horizon(self, faces_list: List[np.ndarray]) -> np.ndarray:
        """
        將包含六個面的列表 (List) 轉換為水平排列 (Horizon) 的立方體貼圖。

        :param faces_list: 一個包含六個 NumPy 陣列的列表。
        :return: 一個水平拼接的 NumPy 陣列。
        :raises ValueError: 如果列表長度不為 6 或各面尺寸不一致。
        """
        if len(faces_list) != 6:
            raise ValueError(f"列表必須包含 6 個面，但收到了 {len(faces_list)} 個")
        if not all(face.shape == (self.face_size, self.face_size, faces_list[0].shape[2]) for face in faces_list):
            shapes = [face.shape for face in faces_list]
            raise ValueError(f"所有面的尺寸必須與 ({self.face_size}, {self.face_size}, C) 一致: {shapes}")
        return np.concatenate(faces_list, axis=1)

    def dict_to_horizon(self, faces_dict: Dict[str, np.ndarray]) -> np.ndarray:
        """
        將以面名稱為鍵的字典 (Dict) 轉換為水平排列 (Horizon) 的立方體貼圖。

        :param faces_dict: 一個包含六個面陣列的字典。
        :return: 一個水平拼接的 NumPy 陣列。
        :raises ValueError: 如果字典中缺少必需的面。
        """
        try:
            # 確保按照標準順序排列
            faces_list = [faces_dict[name] for name in self.FACE_NAMES]
        except KeyError as e:
            raise ValueError(f"字典中缺少必需的面: {e}。需要所有鍵: {self.FACE_NAMES}")
        return self.list_to_horizon(faces_list)

    def horizon_to_dice(self, cube_h: np.ndarray) -> np.ndarray:
        """
        將水平排列 (Horizon) 的立方體貼圖轉換為骰子展開格式 (Dice)。

        :param cube_h: 水平排列的立方體貼圖。
        :return: 一個 3x4 網格佈局的骰子展開圖。
        """
        faces_dict = self.horizon_to_dict(cube_h)
        
        # 建立一個 3x4 的空白畫布來容納骰子展開圖
        dice_shape = (self.face_size * 3, self.face_size * 4) + cube_h.shape[2:]
        dice = np.zeros(dice_shape, dtype=cube_h.dtype)

        # 根據 DICE_LAYOUT 將每個面放置到畫布的正確位置
        for face_name, (col, row) in self.DICE_LAYOUT.items():
            y_start, y_end = row * self.face_size, (row + 1) * self.face_size
            x_start, x_end = col * self.face_size, (col + 1) * self.face_size
            dice[y_start:y_end, x_start:x_end] = faces_dict[face_name]

        return dice

    def dice_to_horizon(self, cube_dice: np.ndarray) -> np.ndarray:
        """
        將骰子展開格式 (Dice) 的立方體貼圖轉換為水平排列格式 (Horizon)。

        :param cube_dice: 骰子展開格式的影像。
        :return: 一個水平排列的立方體貼圖。
        :raises ValueError: 如果骰子圖的尺寸不正確。
        """
        h, w = cube_dice.shape[:2]
        if h != self.face_size * 3 or w != self.face_size * 4:
            raise ValueError(f"骰子圖的尺寸應為 ({self.face_size*3}, {self.face_size*4}), 但收到 {cube_dice.shape}")

        faces_dict = {}
        for face_name, (col, row) in self.DICE_LAYOUT.items():
            y_start, y_end = row * self.face_size, (row + 1) * self.face_size
            x_start, x_end = col * self.face_size, (col + 1) * self.face_size
            faces_dict[face_name] = cube_dice[y_start:y_end, x_start:x_end]
            
        return self.dict_to_horizon(faces_dict)

    def dice_to_list(self, cube_dice: np.ndarray) -> List[np.ndarray]:
        """將骰子展開格式 (Dice) 轉換為列表格式 (List)。"""
        horizon = self.dice_to_horizon(cube_dice)
        return self.horizon_to_list(horizon)

    def format_output(
        self, cube_h: np.ndarray, format_type: str, squeeze: bool = False
    ) -> Union[np.ndarray, List[np.ndarray], Dict[str, np.ndarray]]:
        """
        一個統一的格式化輸出函式，將水平排列的立方體貼圖轉換為任何支援的目標格式。

        :param cube_h: 原始的水平排列立方體貼圖。
        :param format_type: 目標格式，可選 "horizon", "list", "dict", "dice"。
        :param squeeze: 對於單通道影像，是否移除最後的通道維度。
        :return: 轉換後指定格式的立方體貼圖。
        :raises ValueError: 如果提供了不支援的格式類型。
        """
        start_time = time.time()
        self._stats["total_operations"] += 1

        try:
            result: Union[np.ndarray, List[np.ndarray], Dict[str, np.ndarray]]
            dispatch = {
                "horizon": lambda: cube_h,
                "list": lambda: self.horizon_to_list(cube_h),
                "dict": lambda: self.horizon_to_dict(cube_h),
                "dice": lambda: self.horizon_to_dice(cube_h),
            }
            if format_type not in dispatch:
                raise ValueError(f"不支援的輸出格式: {format_type}")
            
            result = dispatch[format_type]()

            if squeeze and isinstance(result, list) and result[0].shape[-1] == 1:
                result = [face.squeeze(axis=-1) for face in result]
            elif squeeze and isinstance(result, dict) and next(iter(result.values())).shape[-1] == 1:
                result = {k: v.squeeze(axis=-1) for k, v in result.items()}
            elif squeeze and isinstance(result, np.ndarray) and result.shape[-1] == 1:
                result = result.squeeze(axis=-1)

        except Exception as e:
            logger.error(f"在將立方體貼圖轉換為 '{format_type}' 格式時發生錯誤: {e}")
            raise
        finally:
            processing_time = time.time() - start_time
            self._stats["total_processing_time"] += processing_time
            if self.performance_monitor:
                self.performance_monitor.record_operation(f"cube_format_{format_type}", processing_time)

        return result

    @staticmethod
    def fast_face_extraction(cube_array: np.ndarray, face_idx: int) -> np.ndarray:
        """
        從水平排列的陣列中快速提取單個立方體面。

        :param cube_array: 水平排列的立方體陣列。
        :param face_idx: 欲提取的面的索引 (0-5)。
        :return: 一個包含指定面資料的新 NumPy 陣列副本。
        """
        face_w = cube_array.shape[0]
        start = face_idx * face_w
        end = start + face_w
        return cube_array[:, start:end].copy()

    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取並計算立方體貼圖處理器的性能統計資訊。

        :return: 一個包含總操作次數、平均處理時間等統計數據的字典。
        """
        stats = self._stats.copy()
        total_ops = stats.get("total_operations", 0)
        stats["average_processing_time_ms"] = (stats.get("total_processing_time", 0) * 1000) / total_ops if total_ops > 0 else 0
        stats["face_size"] = self.face_size
        stats["available_formats"] = ["horizon", "list", "dict", "dice"]
        return stats

    def reset_statistics(self):
        """重置所有性能統計資訊為零。"""
        self._stats = {
            "total_operations": 0,
            "total_processing_time": 0.0,
        }
        logger.debug("CubeMapper 統計數據已重置。")


def main():
    """當此模組作為主程式執行時的範例函式，用於演示和測試。"""
    print("=== 立方體貼圖處理模組 (CubeMapper) 使用範例 ===")
    print("\n功能說明：")
    print("1. 在四種立方體格式間自由轉換：水平 (horizon)、列表 (list)、字典 (dict)、骰子 (dice)。")
    print("2. 高效能的陣列操作，部分核心功能由 Numba 加速。")
    print("3. 提供統一的格式化輸出介面。")

    print("\n格式視覺化說明：")
    print("- 水平 (horizon): [ F | R | B | L | U | D ]")
    print("- 列表 (list): [ array_F, array_R, ... ]")
    print("- 字典 (dict): { 'F': array_F, 'R': array_R, ... }")
    print("- 骰子 (dice):\n     [   | U |   |   ]\n     [ L | F | R | B ]\n     [   | D |   |   ]")

    print("\n程式碼使用示例：")
    print("```python")
    print("from core.cube_mapping import CubeMapper")
    print("import numpy as np")
    print("\n# 1. 建立一個面尺寸為 256x256 的映射器")
    print("mapper = CubeMapper(face_size=256)")
    print("\n# 2. 創建一個模擬的水平排列立方體貼圖")
    print("cube_horizon = np.random.randint(0, 256, size=(256, 256 * 6, 3), dtype=np.uint8)")
    print("\n# 3. 將水平圖轉換為字典格式")
    print("cube_dict = mapper.horizon_to_dict(cube_horizon)")
    print("front_face = cube_dict['F']")
    # print(f"前方面尺寸: {front_face.shape}")
    print("\n# 4. 將字典格式轉換為骰子展開格式")
    print("cube_dice = mapper.format_output(cube_horizon, format_type='dice')")
    # print(f"骰子圖尺寸: {cube_dice.shape}")
    print("```")

    # 執行一個簡單的轉換循環測試
    print("\n執行轉換測試：")
    try:
        face_size = 64
        mapper = CubeMapper(face_size)
        test_horizon = np.random.rand(face_size, face_size * 6, 3)
        print(f"原始水平格式 (Horizon): {test_horizon.shape}")

        test_list = mapper.horizon_to_list(test_horizon)
        print(f"-> 轉換為列表 (List): 成功，包含 {len(test_list)} 個面，每個面尺寸 {test_list[0].shape}")

        re_horizon_from_list = mapper.list_to_horizon(test_list)
        np.testing.assert_array_equal(test_horizon, re_horizon_from_list)
        print(f"<- 從列表轉換回水平: 成功，尺寸 {re_horizon_from_list.shape}")

        test_dict = mapper.horizon_to_dict(re_horizon_from_list)
        print(f"-> 轉換為字典 (Dict): 成功，包含鍵 {list(test_dict.keys())}")
        
        re_horizon_from_dict = mapper.dict_to_horizon(test_dict)
        np.testing.assert_array_equal(test_horizon, re_horizon_from_dict)
        print(f"<- 從字典轉換回水平: 成功，尺寸 {re_horizon_from_dict.shape}")

        test_dice = mapper.horizon_to_dice(re_horizon_from_dict)
        print(f"-> 轉換為骰子 (Dice): 成功，尺寸 {test_dice.shape}")

        re_horizon_from_dice = mapper.dice_to_horizon(test_dice)
        np.testing.assert_array_equal(test_horizon, re_horizon_from_dice)
        print(f"<- 從骰子轉換回水平: 成功，尺寸 {re_horizon_from_dice.shape}")
        
        print("\n所有格式轉換循環測試通過！")

    except Exception as e:
        print(f"\n測試失敗: {e}")


if __name__ == "__main__":
    main()