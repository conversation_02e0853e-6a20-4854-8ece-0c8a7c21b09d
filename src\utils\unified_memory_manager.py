#!/usr/bin/env python3
"""
統一記憶體管理器 - 整合所有記憶體管理功能
"""
import gc
import logging
import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable

import numpy as np
import psutil
import torch

from .core.config import MemoryConfig
from .core.thresholds import SystemThresholds, DEFAULT_THRESHOLDS

logger = logging.getLogger(__name__)


class MemoryStrategy(Enum):
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"
    SMART = "smart"


class MemoryProvider(ABC):
    @abstractmethod
    def get_info(self) -> dict:
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        pass


class SystemMemoryProvider(MemoryProvider):
    def get_info(self) -> dict:
        mem = psutil.virtual_memory()
        return {
            "total_mb": mem.total / (1024**2),
            "available_mb": mem.available / (1024**2),
            "percent": mem.percent,
            "used_mb": mem.used / (1024**2),
        }

    def cleanup(self) -> bool:
        gc.collect()
        return True

    def check_memory_status(self, thresholds: SystemThresholds | None = None) -> tuple[str, str]:
        thresholds = thresholds or DEFAULT_THRESHOLDS
        info = self.get_info()
        percent = info.get("percent", 0)
        if percent >= thresholds.memory_critical:
            return "CRITICAL", f"記憶體使用率 {percent:.1f}% 已達到危險水平"
        elif percent >= thresholds.memory_warning:
            return "WARNING", f"記憶體使用率 {percent:.1f}% 較高"
        else:
            return "OK", f"記憶體使用率 {percent:.1f}% 正常"

    def estimate_requirement(self, image_shape: tuple, dtype, factor: float) -> int:
        try:
            single_size = np.prod(image_shape) * np.dtype(dtype).itemsize
            estimated_size = single_size * factor
            return int(estimated_size / (1024 * 1024))
        except Exception:
            return 0


class CudaMemoryProvider(MemoryProvider):
    def __init__(self, device_id: int = 0):
        self.device_id = device_id
        self.cuda_available = torch.cuda.is_available()
        if self.cuda_available:
            self.device = torch.device(f'cuda:{self.device_id}')
            props = torch.cuda.get_device_properties(self.device_id)
            self.total_memory = props.total_memory
        else:
            self.total_memory = 0

    def get_info(self) -> dict:
        if not self.cuda_available:
            return {}
        free, total = torch.cuda.mem_get_info(self.device_id)
        return {
            "total_mb": total / (1024**2),
            "free_mb": free / (1024**2),
            "used_mb": (total - free) / (1024**2),
            "percent": ((total - free) / total * 100) if total > 0 else 0,
        }

    def cleanup(self) -> bool:
        if self.cuda_available:
            torch.cuda.empty_cache()
            return True
        return False

    def check_memory_available(self, required_mb: int, reserve_percent: float = 20.0) -> bool:
        if not self.cuda_available:
            return False
        info = self.get_info()
        free_mb = info.get("free_mb", 0)
        reserve_mb = self.total_memory / (1024**2) * (reserve_percent / 100)
        return (free_mb - reserve_mb) >= required_mb


class UnifiedMemoryManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, config: MemoryConfig | None = None):
        if hasattr(self, "_initialized"):
            return
        self.config = config or MemoryConfig()
        self.system_provider = SystemMemoryProvider()
        self.cuda_provider = CudaMemoryProvider() if torch.cuda.is_available() else None
        self._initialized = True

    def get_system_memory_info(self) -> dict:
        return self.system_provider.get_info()

    def get_gpu_memory_info(self) -> dict:
        if self.cuda_provider:
            return self.cuda_provider.get_info()
        return {}
    
    def check_system_memory_status(self) -> tuple[str, str]:
        return self.system_provider.check_memory_status(
            self.config.warning_threshold, self.config.critical_threshold
        )

    def check_gpu_memory_available(self, required_mb: int) -> bool:
        if self.cuda_provider:
            return self.cuda_provider.check_memory_available(required_mb)
        return False

    def estimate_memory_requirement(self, image_shape: tuple, dtype=np.float32, factor: float = 3.0) -> int:
        return self.system_provider.estimate_requirement(image_shape, dtype, factor)

    def cleanup_all(self):
        self.system_provider.cleanup()
        if self.cuda_provider:
            self.cuda_provider.cleanup()