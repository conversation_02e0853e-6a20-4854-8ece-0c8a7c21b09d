# image_processor.py
import os
import time
from numpy.typing import NDArray
import numpy as np
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Pool
import argparse
import matplotlib.pyplot as plt
from typing import Any, Literal, Optional, TypeVar, Literal, Union, overload, List, Tuple, Dict
from scipy.ndimage import map_coordinates
from 舊版.utils import *


CubeFormat = Literal["horizon", "list", "dict", "dice"]
InterpolationMode = Literal[
    "nearest",
    "linear",
    "bilinear",
    "biquadratic",
    "quadratic",
    "quad",
    "bicubic",
    "cubic",
    "biquartic",
    "quartic",
    "biquintic",
    "quintic",
]

_mode_to_order = {
    "nearest": 0,
    "linear": 1,
    "bilinear": 1,
    "biquadratic": 2,
    "quadratic": 2,
    "quad": 2,
    "bicubic": 3,
    "cubic": 3,
    "biquartic": 4,
    "quartic": 4,
    "biquintic": 5,
    "quintic": 5,
}

def mode_to_order(mode: InterpolationMode) -> int:
    """Convert a human-friendly interpolation string to integer equivalent.

    Parameters
    ----------
    mode: str
        Human-friendly interpolation string.

    Returns
    -------
    The order of the spline interpolation
    """
    try:
        return _mode_to_order[mode.lower()]
    except KeyError:
        raise ValueError(f'Unknown mode "{mode}".') from None
    
# Placeholder for Projection, EquirecSampler, etc. (assuming they are defined elsewhere)
class Projection:
    def __init__(self, height: int, width: int, mode: str = "bilinear"):
        self.height = height
        self.width = width
        self.face_width = width // 4
        self.order = mode_to_order(mode)
        self._E2Csampler = EquirecSampler.from_cubemap(self.face_width, height, width, self.order)
        self._C2Esampler = CubeFaceSampler.from_equirec(self.face_width, height, width, self.order)

    def equirec_to_cubemap(self, image: np.ndarray, cube_format: str = "horizon") -> np.ndarray:
        return self._E2Csampler(image)

    def cubemap_to_equirec(self, cubemap: np.ndarray, height: int, width: int) -> np.ndarray:
        return self._C2Esampler(cubemap)

class ImageProcessor:
    """Class to manage image loading, projection, and conversion."""
    def __init__(self, input_folder: str, start_idx: int = 0, end_idx: int = 9):
        """
        Initialize the ImageProcessor.

        Args:
            input_folder: str, folder containing input images.
            start_idx: int, starting index of images to process (default: 0).
            end_idx: int, ending index of images to process (default: 9).
        """
        self.input_folder = input_folder
        self.start_idx = start_idx
        self.end_idx = end_idx
        self.image_ids = self._get_image_ids()
        self.reference_height, self.reference_width = self._get_reference_size()
        self.projection = Projection(self.reference_height, self.reference_width)

    def _get_image_ids(self) -> List[str]:
        """Retrieve list of image filenames from the input folder."""
        image_files = [f for f in os.listdir(self.input_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        if not image_files:
            logger.warning(f"No valid images found in {self.input_folder}")
            return []
        # Adjust end_idx to avoid exceeding list length
        end_idx = min(self.end_idx, len(image_files))
        return image_files[self.start_idx:end_idx]

    def _get_reference_size(self) -> Tuple[int, int]:
        """Determine reference image size from the first valid image."""
        if not self.image_ids:
            raise ValueError("No images available to determine reference size")
        first_image_path = os.path.join(self.input_folder, self.image_ids[0])
        image = cv_imread(first_image_path)
        if image is None:
            raise ValueError(f"Cannot load reference image {first_image_path}")
        height, width, _ = image.shape
        logger.info(f"Reference image size: {height}x{width}")
        return height, width

    def load_images(self) -> Dict[str, np.ndarray]:
        """Load images in parallel using ThreadPoolExecutor."""
        def load_image(image_id: str) -> Tuple[str, Optional[np.ndarray]]:
            image_path = os.path.join(self.input_folder, image_id)
            image = cv_imread(image_path)
            return image_id, image

        start_time = time.time()
        valid_images = {}
        with ThreadPoolExecutor(max_workers=get_optimal_workers(len(self.image_ids))) as executor:
            futures = [executor.submit(load_image, img_id) for img_id in self.image_ids]
            for future in futures:
                try:
                    img_id, img = future.result()
                    if img is None:
                        logger.warning(f"Failed to load image: {img_id}")
                        continue
                    height, width, _ = img.shape
                    if height != self.reference_height or width != self.reference_width:
                        logger.error(f"Image {img_id} size mismatch: ({height}, {width}) vs ({self.reference_height}, {self.reference_width})")
                        continue
                    valid_images[img_id] = img
                except Exception as e:
                    logger.error(f"Error processing image {img_id}: {e}")
        logger.info(f"Image loading completed in {time.time() - start_time:.2f} seconds")
        return valid_images

    def process_cubemaps(self, images: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """Convert images to cubemaps in parallel using multiprocessing."""
        def process_image(args: Tuple[str, np.ndarray]) -> Tuple[str, np.ndarray]:
            img_id, img = args
            try:
                return img_id, self.projection.equirec_to_cubemap(img, cube_format="horizon")
            except Exception as e:
                logger.error(f"Error converting {img_id} to cubemap: {e}")
                return img_id, None

        start_time = time.time()
        with Pool(processes=get_optimal_workers(len(images)) // 2) as pool:
            results = pool.map(process_image, images.items())
        cubemaps = {img_id: cube for img_id, cube in results if cube is not None}
        logger.info(f"Cubemap conversion completed in {time.time() - start_time:.2f} seconds")
        return cubemaps

    def process_equirec(self, cubemaps: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """Convert cubemaps back to equirectangular images in parallel."""
        def process_cubemap(args: Tuple[str, np.ndarray]) -> Tuple[str, np.ndarray]:
            img_id, cube = args
            try:
                return img_id, self.projection.cubemap_to_equirec(cube, self.reference_height, self.reference_width)
            except Exception as e:
                logger.error(f"Error converting {img_id} to equirectangular: {e}")
                return img_id, None

        start_time = time.time()
        with Pool(processes=get_optimal_workers(len(cubemaps)) // 2) as pool:
            results = pool.map(process_cubemap, cubemaps.items())
        equirec_images = {img_id: e_img for img_id, e_img in results if e_img is not None}
        logger.info(f"Equirectangular conversion completed in {time.time() - start_time:.2f} seconds")
        return equirec_images

    def display_images(self, images: Dict[str, np.ndarray], title: str) -> None:
        """Display images using matplotlib."""
        for img_id, img in images.items():
            plt.figure(figsize=(10, 5))
            plt.imshow(img)
            plt.title(f"{title}: {img_id}")
            plt.axis('off')
            plt.show()
            plt.close()

def main():
    """Main function to process images, supporting both command-line and default execution."""
    # Define default values for testing
    DEFAULT_INPUT_FOLDER = r"D:\街景影像處理\街景mask\範例"
    DEFAULT_START = 0
    DEFAULT_END = 9

    parser = argparse.ArgumentParser(description="Process equirectangular images to cubemaps and back.")
    parser.add_argument("--input", default=DEFAULT_INPUT_FOLDER, help="Input image folder")
    parser.add_argument("--start", type=int, default=DEFAULT_START, help="Start index of images to process")
    parser.add_argument("--end", type=int, default=DEFAULT_END, help="End index of images to process")
    
    # Parse arguments, allowing defaults if no args provided
    args = parser.parse_args() if any(arg in os.sys.argv for arg in ['--input', '--start', '--end']) else parser.parse_args([])

    start_time = time.time()
    processor = ImageProcessor(args.input, args.start, args.end)
    
    # Load images
    images = processor.load_images()
    if not images:
        logger.error("No valid images loaded. Exiting.")
        return

    # Convert to cubemaps
    cubemaps = processor.process_cubemaps(images)
    if not cubemaps:
        logger.error("No cubemaps generated. Exiting.")
        return

    # Convert back to equirectangular
    equirec_images = processor.process_equirec(cubemaps)
    
    # Display results (optional, can be commented out for batch processing)
    processor.display_images(cubemaps, "Cubemap")
    processor.display_images(equirec_images, "Equirectangular")

    logger.info(f"Total processing time: {time.time() - start_time:.2f} seconds")
    logger.info(f"Processed {len(images)} images")

if __name__ == "__main__":
    main()