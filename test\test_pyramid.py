#!/usr/bin/env python3
"""
金字塔瓦片生成功能測試腳本
Test script for pyramid tile generation functionality
"""

import os
import sys
import numpy as np
import cv2

# 添加 src 路徑
sys.path.insert(0, 'src')

from processing.pyramid_generator import PyramidGenerator, PyramidConfig, PyramidQuality

def create_test_cube_faces():
    """創建測試用的立方體面圖像"""
    faces = {}
    face_names = ['F', 'R', 'B', 'L', 'U', 'D']
    
    for i, name in enumerate(face_names):
        # 創建 2048x2048 的測試圖像
        image = np.zeros((2048, 2048, 3), dtype=np.uint8)
        
        # 為每個面使用不同的顏色
        colors = [
            (255, 0, 0),    # F - 紅色
            (0, 255, 0),    # R - 綠色  
            (0, 0, 255),    # B - 藍色
            (255, 255, 0),  # L - 黃色
            (255, 0, 255),  # U - 紫色
            (0, 255, 255),  # D - 青色
        ]
        
        image[:] = colors[i]
        
        # 在圖像上添加文字標識
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(image, f"Face {name}", (800, 1000), font, 8, (255, 255, 255), 12)
        cv2.putText(image, f"({i})", (900, 1200), font, 6, (255, 255, 255), 8)
        
        faces[name] = image
    
    return faces

def test_pyramid_generation():
    """測試金字塔生成功能"""
    print("開始測試金字塔瓦片生成功能...")
    
    try:
        # 1. 創建測試數據
        print("創建測試數據...")
        cube_faces = create_test_cube_faces()
        thumbnail = cv2.resize(cube_faces['F'], (512, 512))
        
        # 2. 配置金字塔生成器
        print("配置金字塔生成器...")
        config = PyramidConfig(
            max_levels=3,
            tile_size=512,
            quality=PyramidQuality.HIGH,
            pyramid_levels=[611, 1222, 2445]
        )
        
        generator = PyramidGenerator(config)
        
        # 3. 創建輸出目錄
        output_dir = "test_output"
        scene_name = "test_scene"
        os.makedirs(output_dir, exist_ok=True)
        
        # 4. 生成金字塔
        print("開始生成金字塔瓦片...")
        success = generator.generate_pyramid(
            cube_faces=cube_faces,
            thumbnail=thumbnail,
            output_dir=output_dir,
            scene_name=scene_name
        )
        
        if success:
            print("金字塔生成成功！")
            
            # 5. 驗證輸出結構
            print("驗證輸出結構...")
            scene_path = os.path.join(output_dir, scene_name)
            
            # 檢查 html5 目錄
            html5_path = os.path.join(scene_path, "html5")
            if os.path.exists(html5_path):
                print("html5 目錄創建成功")
                
                # 檢查基礎立方體面
                for i in range(6):
                    face_file = os.path.join(html5_path, f"{i}.jpg")
                    if os.path.exists(face_file):
                        print(f"立方體面 {i}.jpg 存在")
                    else:
                        print(f"立方體面 {i}.jpg 缺失")
            else:
                print("html5 目錄創建失敗")
            
            # 檢查金字塔瓦片目錄
            for face_id in range(6):
                face_dir = os.path.join(scene_path, str(face_id))
                if os.path.exists(face_dir):
                    print(f"✅ 面 {face_id} 金字塔目錄存在")
                    
                    # 檢查每個層級
                    for level in range(3):
                        level_dir = os.path.join(face_dir, str(level))
                        if os.path.exists(level_dir):
                            # 計算該層級的瓦片數量
                            tiles = [f for f in os.listdir(level_dir) if f.endswith('.jpg')]
                            print(f"  ✅ 層級 {level}: {len(tiles)} 個瓦片")
                        else:
                            print(f"  ❌ 層級 {level} 目錄缺失")
                else:
                    print(f"❌ 面 {face_id} 金字塔目錄缺失")
            
            # 檢查縮略圖
            thumbnail_path = os.path.join(scene_path, "thumbnail.jpg")
            if os.path.exists(thumbnail_path):
                print("✅ 縮略圖存在")
            else:
                print("❌ 縮略圖缺失")
                
            print(f"\n📁 完整輸出結構已生成到: {scene_path}")
            print("🎉 測試完成！")
            
        else:
            print("❌ 金字塔生成失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def analyze_output_structure():
    """分析輸出結構"""
    print("\n📊 分析輸出結構...")
    scene_path = "test_output/test_scene"
    
    if not os.path.exists(scene_path):
        print("❌ 場景目錄不存在")
        return
    
    total_files = 0
    total_size = 0
    
    for root, dirs, files in os.walk(scene_path):
        for file in files:
            if file.endswith('.jpg'):
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                total_files += 1
                total_size += file_size
                
                # 顯示相對路徑
                rel_path = os.path.relpath(file_path, scene_path)
                print(f"  📄 {rel_path} ({file_size:,} bytes)")
    
    print(f"\n📈 統計結果:")
    print(f"  🗂️ 總檔案數: {total_files}")
    print(f"  💾 總大小: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")

if __name__ == "__main__":
    print("🚀 金字塔瓦片生成測試開始")
    print("=" * 50)
    
    success = test_pyramid_generation()
    
    if success:
        analyze_output_structure()
        print("\n🎯 測試總結: 所有功能正常工作！")
    else:
        print("\n⚠️ 測試總結: 發現問題，請檢查日誌")
    
    print("=" * 50)