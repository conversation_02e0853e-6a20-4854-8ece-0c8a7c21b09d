#!/usr/bin/env python3
"""
測試執行腳本
Test Runner Script

使用方法 / Usage:
python test/run_tests.py [options]

選項 / Options:
--module <module_name>  : 只執行特定模組的測試 / Run tests for specific module only
--coverage              : 啟用覆蓋率報告 / Enable coverage reporting
--verbose              : 詳細輸出 / Verbose output
--parallel             : 並行執行測試 / Run tests in parallel
--html                 : 生成HTML報告 / Generate HTML report
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path

# project_root for cwd only
project_root = Path(__file__).parent.parent


def run_tests(module=None, coverage=False, verbose=False, parallel=False, html=False):
    """
    執行測試

    Args:
        module: 指定測試模組
        coverage: 是否啟用覆蓋率
        verbose: 是否詳細輸出
        parallel: 是否並行執行
        html: 是否生成HTML報告
    """
    cmd = ["python", "-m", "pytest"]

    # 基本設定
    cmd.extend(["-v" if verbose else "-q"])

    # 指定測試目錄或模組
    if module:
        # Handle special module names
        if module == "performance":
            test_path = "test/test_performance/"
        elif module == "module_integration":
            test_path = "test/test_module_integration.py"
        elif module in ["utils_re", "processing_re"]:
            test_path = f"test/test_{module}/"
        else:
            test_path = f"test/test_{module}"
        
        if not os.path.exists(test_path):
            print(f"錯誤: 測試模組 {module} 不存在 (路徑: {test_path})")
            return False
        cmd.append(test_path)
    else:
        cmd.append("test/")

    # 覆蓋率設定
    if coverage:
        cmd.extend(["--cov=.", "--cov-report=term-missing"])
        if html:
            cmd.append("--cov-report=html")

    # 並行執行
    if parallel:
        try:
            import pytest_xdist

            cmd.extend(["-n", "auto"])
        except ImportError:
            print("警告: pytest-xdist 未安裝，無法並行執行")

    # HTML報告
    if html and not coverage:
        cmd.extend(["--html=test_report.html", "--self-contained-html"])

    # 額外設定
    cmd.extend(
        [
            "--tb=short",  # 簡短錯誤回溯
            "--strict-markers",  # 嚴格標記模式
            "--disable-warnings",  # 禁用警告（可選）
        ]
    )

    print(f"執行命令: {' '.join(cmd)}")
    print(f"工作目錄: {os.getcwd()}")
    print("-" * 50)

    try:
        result = subprocess.run(cmd, cwd=project_root)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\n測試被用戶中斷")
        return False
    except Exception as e:
        print(f"執行測試時發生錯誤: {e}")
        return False


def main():
    """主函數"""
    parser = argparse.ArgumentParser(
        description="全景影像處理系統測試執行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    parser.add_argument(
        "--module",
        "-m",
        help="只執行特定模組的測試 (config, core, detection, processing, utils, utils_re, processing_re, performance)",
        choices=["config", "core", "detection", "log_utils", "processing", "utils", "utils_re", "processing_re", "performance", "module_integration"],
    )

    parser.add_argument("--coverage", "-c", action="store_true", help="啟用覆蓋率報告")

    parser.add_argument("--verbose", "-v", action="store_true", help="詳細輸出")

    parser.add_argument(
        "--parallel", "-p", action="store_true", help="並行執行測試 (需要 pytest-xdist)"
    )

    parser.add_argument("--html", action="store_true", help="生成HTML報告")

    parser.add_argument("--install-deps", action="store_true", help="安裝測試依賴")

    args = parser.parse_args()

    # 安裝測試依賴
    if args.install_deps:
        print("安裝測試依賴...")
        deps = [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-html>=3.0.0",
            "pytest-xdist>=3.0.0",
            "pytest-mock>=3.0.0",
        ]
        for dep in deps:
            subprocess.run([sys.executable, "-m", "pip", "install", dep])
        print("依賴安裝完成")
        return

    # 檢查pytest是否安裝
    try:
        import pytest
    except ImportError:
        print("錯誤: pytest 未安裝")
        print("請執行: pip install pytest")
        print("或使用: python test/run_tests.py --install-deps")
        sys.exit(1)

    # 執行測試
    success = run_tests(
        module=args.module,
        coverage=args.coverage,
        verbose=args.verbose,
        parallel=args.parallel,
        html=args.html,
    )

    if success:
        print("\n✅ 所有測試通過!")
    else:
        print("\n❌ 部分測試失敗")
        sys.exit(1)


if __name__ == "__main__":
    main()
