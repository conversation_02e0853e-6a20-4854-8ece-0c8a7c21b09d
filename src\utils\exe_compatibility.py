#!/usr/bin/env python3
"""
EXE兼容性模块 - 专门解决PyInstaller打包后的导入和路径问题
EXE Compatibility Module - Specifically handles import and path issues after PyInstaller packaging

解决的问题：
1. EXE中utils不是package的问题
2. 动态导入失败的问题  
3. 路径设置问题
4. 模块可用性检测

Author: Claude Code Assistant
Date: 2025-06-24 (EXE Compatibility Fix)
"""

import os
import sys
import warnings
from pathlib import Path
from typing import Any, Callable

def is_exe_environment() -> bool:
    """检测是否在EXE环境中运行"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

def get_exe_base_path() -> Path:
    """获取EXE的基础路径"""
    if is_exe_environment():
        # PyInstaller创建的临时目录
        return Path(getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__))))
    else:
        # 正常Python环境
        return Path(__file__).parent.parent

def setup_exe_paths():
    """设置EXE环境的路径"""
    if is_exe_environment():
        base_path = get_exe_base_path()
        if str(base_path) not in sys.path:
            sys.path.insert(0, str(base_path))
        print(f"EXE环境检测: 添加路径 {base_path}")

def safe_import(module_name: str, package: str | None = None) -> Any | None:
    """安全导入模块，适用于EXE环境"""
    try:
        if package:
            module = __import__(f"{package}.{module_name}", fromlist=[module_name])
        else:
            module = __import__(module_name)
        return module
    except ImportError as e:
        if is_exe_environment():
            print(f"EXE环境导入失败: {module_name} - {e}")
        return None

def get_safe_function(module_name: str, function_name: str, fallback: Callable | None = None) -> Callable:
    """安全获取函数，提供fallback"""
    module = safe_import(module_name)
    if module and hasattr(module, function_name):
        return getattr(module, function_name)
    elif fallback:
        return fallback
    else:
        def dummy_function(*args, **kwargs):
            if is_exe_environment():
                print(f"EXE环境: 使用空函数代替 {module_name}.{function_name}")
            pass
        return dummy_function

def setup_matplotlib_fallback():
    """设置matplotlib的EXE兼容配置"""
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except Exception as e:
        if is_exe_environment():
            print(f"EXE环境: matplotlib配置失败 - {e}")
        return False

def create_exe_compatible_config():
    """创建EXE兼容的配置对象"""
    class ExeCompatibleConfig:
        def __init__(self):
            self.is_exe = is_exe_environment()
            self.base_path = get_exe_base_path()
            
        def safe_import_module(self, module_name: str):
            return safe_import(module_name)
            
        def get_resource_path(self, relative_path: str) -> str:
            """获取资源文件的正确路径"""
            if self.is_exe:
                return os.path.join(self.base_path, relative_path)
            else:
                return relative_path
    
    return ExeCompatibleConfig()

# 全局EXE兼容配置
exe_config = create_exe_compatible_config()

def init_exe_environment():
    """初始化EXE环境"""
    if is_exe_environment():
        print("🔧 EXE环境检测到，正在初始化兼容模式...")
        setup_exe_paths()
        setup_matplotlib_fallback()
        
        # 抑制一些EXE环境中的警告
        warnings.filterwarnings('ignore', category=UserWarning)
        warnings.filterwarnings('ignore', category=FutureWarning)
        
        print("✅ EXE兼容模式初始化完成")
        return True
    return False

# 模块级初始化
_exe_initialized = init_exe_environment()