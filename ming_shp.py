import pandas as pd
from pathlib import Path

# 這支檔案內就定義了 FacilityChecker


def load_csv(path: str | Path) -> pd.DataFrame:
    """
    小工具：若檔案存在就讀 CSV，否則回傳空 DataFrame
    （免得某類別還沒準備好資料）
    """
    p = Path(path)
    return pd.read_csv(p, encoding="utf-8") if p.is_file() else pd.DataFrame()


class FacilityChecker:
    """
    設施資料檢查器
    dfs : dict[str, pd.DataFrame]  各設施類別對應的資料表
    """

    def __init__(self, dfs: dict[str, pd.DataFrame]):
        self.dfs = dfs

    # ──────────── 固定出入口設施 ────────────
    def _check_fixed_entrance(self) -> bool:
        df = self.dfs.get("固定出入口設施", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("固定出入口設施：資料表為空，無法檢查")
        else:
            miss = [c for c in ("Name", "SWW_WTH", "U_WIDTH")
                    if c not in df.columns]
            if miss:
                errors.append(f"固定出入口設施：缺少欄位 {miss}")
            else:
                allowed_names = [
                    "天橋出入口", "地下道出入口", "捷運站出入口",
                    "捷運站附屬設施", "地下設施出入口",
                ]
                bad_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not bad_names.empty:
                    errors.append("固定出入口設施：下列 Name 不在允許清單 → "
                                  + ", ".join(bad_names.unique()))

                mask = df["SWW_WTH"] < 0.9
                vio = df[mask & (df["U_WIDTH"] < df["SWW_WTH"])]
                if not vio.empty:
                    errors.append("固定出入口設施：偵測到 U_WIDTH 小於對應 SWW_WTH (<0.9) 的資料\n"
                                  + vio.to_string(index=False))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 固定其他設施 ────────────
    def _check_fixed_other(self) -> bool:
        df = self.dfs.get("固定其他設施", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("固定其他設施：資料表為空，無法檢查")
        else:
            miss = [c for c in ("Name", "SWW_WTH", "U_WIDTH")
                    if c not in df.columns]
            if miss:
                errors.append(f"固定其他設施：缺少欄位 {miss}")
            else:
                allowed_names = [
                    "墩柱", "資源回收箱", "佈告欄", "樓梯", "斜坡", "垃圾桶",
                    "固定式廣告牌", "電池交換站", "欄杆", "電力設備",
                    "自來水設備", "其他設備", "消防設備", "通風設備",
                    "護欄", "其他",
                ]
                bad_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not bad_names.empty:
                    errors.append("固定其他設施：下列 Name 不在允許清單 → "
                                  + ", ".join(bad_names.unique()))

                mask = df["SWW_WTH"] < 0.9
                vio = df[mask & (df["U_WIDTH"] < df["SWW_WTH"])]
                if not vio.empty:
                    errors.append("固定其他設施：偵測到 U_WIDTH 小於對應 SWW_WTH (<0.9) 的資料\n"
                                  + vio.to_string(index=False))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ─────────────── 固定桿類設施 ───────────────
    def _check_fixed_pole(self) -> bool:
        df = self.dfs.get("固定桿類設施", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("固定桿類設施：資料表為空，無法檢查")
        else:
            miss = [c for c in ("Name", "SWW_WTH", "U_WIDTH", "桿件類型")
                    if c not in df.columns]
            if miss:
                errors.append(f"固定桿類設施：缺少欄位 {miss}")
            else:
                allowed_names = [
                    "燈桿", "電力桿", "電信桿", "標誌", "標示牌", "立式消防栓",
                    "號誌桿", "郵筒", "站牌", "車阻", "廢棄桿", "反光鏡",
                    "監測系統", "停車計時器", "YouBike自動服務機", "其他",
                ]
                bad_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not bad_names.empty:
                    errors.append("固定桿類設施：下列 Name 不在允許清單 → "
                                  + ", ".join(bad_names.unique()))

                allowed_types = ["雙頭柱桿", "N型柱桿", "直立桿", "其他"]
                bad_types = df.loc[~df["桿件類型"].isin(allowed_types), "桿件類型"]
                if not bad_types.empty:
                    errors.append("固定桿類設施：下列『桿件類型』不在允許清單 → "
                                  + ", ".join(bad_types.unique()))

                mask = df["SWW_WTH"] < 0.9
                vio = df[mask & (df["U_WIDTH"] < df["SWW_WTH"])]
                if not vio.empty:
                    errors.append("固定桿類設施：偵測到 U_WIDTH 小於對應 SWW_WTH (<0.9) 的資料\n"
                                  + vio.to_string(index=False))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 固定箱類設施 ────────────
    def _check_fixed_box(self) -> bool:
        df = self.dfs.get("固定箱類設施", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("固定箱類設施：資料表為空，無法檢查")
        else:
            missing = [c for c in (
                "Name", "SWW_WTH", "U_WIDTH") if c not in df.columns]
            if missing:
                errors.append(f"固定箱類設施：缺少欄位 {missing}")
            else:
                allowed_names = [
                    "電信箱", "號誌箱", "路燈箱", "變電箱",
                    "廢棄墩座", "大型桿件基座", "其他",
                ]
                bad_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not bad_names.empty:
                    errors.append("固定箱類設施：下列 Name 不在允許清單 → "
                                  + ", ".join(bad_names.unique()))

                mask = df["SWW_WTH"] < 0.9
                vio = df[mask & (df["U_WIDTH"] < df["SWW_WTH"])]
                if not vio.empty:
                    errors.append("固定箱類設施：偵測到 U_WIDTH 小於對應 SWW_WTH (<0.9) 的資料\n"
                                  + vio.to_string(index=False))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 斜坡道 ────────────
    def _check_ramp(self) -> bool:
        df = self.dfs.get("斜坡道", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("斜坡道：資料表為空，無法檢查")
        else:
            miss = [c for c in ("Name", "Slopetype") if c not in df.columns]
            if miss:
                errors.append(f"斜坡道：缺少欄位 {miss}")
            else:
                allowed_names = ["橫越人行道之穿越道", "人行道路緣斜坡"]
                bad_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not bad_names.empty:
                    errors.append("斜坡道：下列 Name 不在允許清單 → "
                                  + ", ".join(bad_names.unique()))

                allowed_type = ["通行無礙"]
                bad_types = df.loc[~df["Slopetype"].isin(
                    allowed_type), "Slopetype"]
                if not bad_types.empty:
                    errors.append("斜坡道：下列 Slopetype 不在允許清單 → "
                                  + ", ".join(bad_types.unique()))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 樹穴 ────────────
    def _check_tree_pit(self) -> bool:
        df = self.dfs.get("樹穴", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("樹穴：資料表為空，無法檢查")
        else:
            miss = [c for c in ("Name", "SWW_WTH", "U_WIDTH")
                    if c not in df.columns]
            if miss:
                errors.append(f"樹穴：缺少欄位 {miss}")
            else:
                allowed_names = ["樹穴", "花圃"]
                bad_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not bad_names.empty:
                    errors.append("樹穴：下列 Name 不在允許清單 → "
                                  + ", ".join(bad_names.unique()))

                mask = df["SWW_WTH"] < 0.9
                vio = df[mask & (df["U_WIDTH"] < df["SWW_WTH"])]
                if not vio.empty:
                    errors.append("樹穴：偵測到 U_WIDTH 小於對應 SWW_WTH (<0.9) 的資料\n"
                                  + vio.to_string(index=False))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 自行車專用道 ────────────
    def _check_bike_lane(self) -> bool:
        df = self.dfs.get("自行車專用道", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("自行車專用道：資料表為空，無法檢查")
        else:
            if "SWD_WTH" not in df.columns:
                errors.append("自行車專用道：缺少欄位 SWD_WTH")
            else:
                bad = df["SWD_WTH"].isna() | (
                    df["SWD_WTH"].astype(str).str.strip() == "")
                if bad.any():
                    errors.append("自行車專用道：SWD_WTH 欄位包含空值或空字串\n"
                                  + df.loc[bad].to_string(index=False))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 行人穿越道 ────────────
    def _check_crosswalk(self) -> bool:
        df = self.dfs.get("行人穿越道", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("行人穿越道：資料表為空，無法檢查")
        else:
            if "Name" not in df.columns:
                errors.append("行人穿越道：缺少欄位 Name")
            else:
                allowed_names = [
                    "枕木紋行人穿越道", "斑馬紋行人穿越道", "對角線行人穿越道"
                ]
                bad_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not bad_names.empty:
                    errors.append("行人穿越道：下列 Name 不在允許清單 → "
                                  + ", ".join(bad_names.unique()))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 人手孔類設施 ────────────
    def _check_manhole(self) -> bool:
        df = self.dfs.get("人手孔類設施", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("人手孔類設施：資料表為空，無法檢查")
        else:
            # 檢查必要欄位是否存在
            required_columns = ["Name", "X", "Y"]
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"人手孔類設施：缺少必要欄位 {missing_columns}")
            else:
                # 檢查 Name 欄位的允許值
                allowed_names = [
                    "一般電信孔", "軍訓孔類", "警訊孔類", "有線電視孔", "交通號誌孔",
                    "電力孔類", "自來水類", "地下消防栓", "制水閥", "雨水孔類",
                    "污水孔類", "排水格柵板", "瓦斯孔類", "水利類", "輸油管線類",
                    "共同管道類", "寬頻管道類", "纜線管溝", "其他或不明"
                ]
                invalid_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not invalid_names.empty:
                    errors.append("人手孔類設施：下列 Name 不在允許清單 → "
                                  + ", ".join(invalid_names.unique()))

                # 檢查座標值是否合理 (X: 121-122, Y: 24-25 大致是臺北市範圍)
                invalid_x = df[(df["X"] < 121) | (df["X"] > 122)]
                invalid_y = df[(df["Y"] < 24) | (df["Y"] > 25)]
                if not invalid_x.empty:
                    errors.append("人手孔類設施：X 座標值超出合理範圍 (應在121-122之間)\n"
                                  + invalid_x[["Name", "X"]].head().to_string(index=False))
                if not invalid_y.empty:
                    errors.append("人手孔類設施：Y 座標值超出合理範圍 (應在24-25之間)\n"
                                  + invalid_y[["Name", "Y"]].head().to_string(index=False))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 人行道主體範圍 ────────────
    def _check_pavement(self) -> bool:
        df = self.dfs.get("人行道主體範圍", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("人行道主體範圍：資料表為空，無法檢查")
        else:
            # 檢查必要欄位是否存在
            required_columns = [
                "Name", "PSTART", "PEND", "SW_DIRECT", "LENGTH", "WIDTH_U",
                "WIDTH_C", "SW_LENG", "SW_WTH", "SW_PAVE", "SHAPE_AR",
                "SW_AREA", "SW_PAVE_C"
            ]
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"人行道主體範圍：缺少必要欄位 {missing_columns}")

            # 檢查不可為空的文字欄位
            text_columns = ["Name", "PSTART", "PEND",
                            "SW_DIRECT", "SW_PAVE", "SW_PAVE_C"]
            for col in text_columns:
                if col in df.columns:
                    empty_values = df[df[col].isnull() | (df[col] == "")]
                    if not empty_values.empty:
                        errors.append(f"人行道主體範圍：{col} 欄位不得為空")

            # 檢查不可為0的數值欄位
            numeric_columns = ["LENGTH", "WIDTH_U", "WIDTH_C",
                               "SW_LENG", "SW_WTH", "SHAPE_AR", "SW_AREA"]
            for col in numeric_columns:
                if col in df.columns:
                    zero_values = df[df[col] == 0]
                    if not zero_values.empty:
                        errors.append(f"人行道主體範圍：{col} 欄位不得為0")

            # 檢查 SWT_WTH = SW_WTH - SWD_WTH
            if all(col in df.columns for col in ["SWT_WTH", "SW_WTH", "SWD_WTH"]):
                calc_errors = df[abs(
                    df["SWT_WTH"] - (df["SW_WTH"] - df["SWD_WTH"])) > 0.001]
                if not calc_errors.empty:
                    errors.append("人行道主體範圍：SWT_WTH 應等於 SW_WTH - SWD_WTH\n"
                                  + calc_errors[["SWT_WTH", "SW_WTH", "SWD_WTH"]].head().to_string(index=False))

            # 檢查 SWW_WTH <= SWT_WTH
            if all(col in df.columns for col in ["SWW_WTH", "SWT_WTH"]):
                width_errors = df[df["SWW_WTH"] > df["SWT_WTH"]]
                if not width_errors.empty:
                    errors.append("人行道主體範圍：SWW_WTH 應小於等於 SWT_WTH\n"
                                  + width_errors[["SWW_WTH", "SWT_WTH"]].head().to_string(index=False))

            # 檢查 U_OFFICE 為"是"時，U_WIDTH 不得為0
            if all(col in df.columns for col in ["U_OFFICE", "U_WIDTH"]):
                office_errors = df[(df["U_OFFICE"] == "是")
                                   & (df["U_WIDTH"] == 0)]
                if not office_errors.empty:
                    errors.append("人行道主體範圍：U_OFFICE 為「是」時，U_WIDTH 不得為0\n"
                                  + office_errors[["U_OFFICE", "U_WIDTH"]].head().to_string(index=False))

            # 檢查 ARCADE 和 U_OFFICE 的值是否為"是"或"否"
            for col in ["ARCADE", "U_OFFICE"]:
                if col in df.columns:
                    invalid_values = df[~df[col].isin(
                        ["是", "否"]) & ~df[col].isnull()]
                    if not invalid_values.empty:
                        errors.append(f"人行道主體範圍：{col} 欄位值應為「是」或「否」\n"
                                      + invalid_values[[col]].head().to_string(index=False))

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 人行道緣石高度 ────────────
    def _check_curb_height(self) -> bool:
        df = self.dfs.get("人行道緣石高度", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("人行道緣石高度：資料表為空，無法檢查")
        else:
            # 檢查必要欄位是否存在
            required_columns = ["TYPE"]
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"人行道緣石高度：缺少必要欄位 {missing_columns}")
            else:
                # 檢查 TYPE 欄位的允許值
                allowed_types = ["最高段", "漸變段", "最低段", "施工中"]
                invalid_types = df.loc[~df["TYPE"].isin(allowed_types), "TYPE"]
                if not invalid_types.empty:
                    errors.append("人行道緣石高度：下列 TYPE 不在允許清單 → "
                                  + ", ".join(invalid_types.unique()))

                # 檢查 TYPE 欄位是否為空
                empty_types = df[df["TYPE"].isnull() | (df["TYPE"] == "")]
                if not empty_types.empty:
                    errors.append("人行道緣石高度：TYPE 欄位不得為空")

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 人行道缺失 ────────────
    def _check_missing_pavement(self) -> bool:
        df = self.dfs.get("人行道缺失", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("人行道缺失：資料表為空，無法檢查")
        else:
            # 檢查必要欄位是否存在
            required_columns = ["TYPE", "X", "Y"]
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"人行道缺失：缺少必要欄位 {missing_columns}")
            else:
                # 檢查 TYPE 欄位的允許值
                allowed_types = [
                    "行穿線未對路緣斜坡",
                    "行穿線對路緣斜坡長度少於1.2公尺",
                    "無行人空間",
                    "人行道與騎樓、退縮地高低差",
                    "車阻距離少於1.5公尺"
                ]
                invalid_types = df.loc[~df["TYPE"].isin(allowed_types), "TYPE"]
                if not invalid_types.empty:
                    errors.append("人行道缺失：下列 TYPE 不在允許清單 → "
                                  + ", ".join(invalid_types.unique()))

                # 檢查 TYPE 欄位是否為空
                empty_types = df[df["TYPE"].isnull() | (df["TYPE"] == "")]
                if not empty_types.empty:
                    errors.append("人行道缺失：TYPE 欄位不得為空")

                # 檢查座標值是否合理 (X: 121-122, Y: 24-25 大致是臺北市範圍)
                if "X" in df.columns and "Y" in df.columns:
                    invalid_x = df[(df["X"] < 121) | (df["X"] > 122)]
                    invalid_y = df[(df["Y"] < 24) | (df["Y"] > 25)]
                    if not invalid_x.empty:
                        errors.append("人行道缺失：X 座標值超出合理範圍 (應在121-122之間)\n"
                                      + invalid_x[["TYPE", "X"]].head().to_string(index=False))
                    if not invalid_y.empty:
                        errors.append("人行道缺失：Y 座標值超出合理範圍 (應在24-25之間)\n"
                                      + invalid_y[["TYPE", "Y"]].head().to_string(index=False))

                # 檢查 note 欄位的特殊條件
                if "note" in df.columns:
                    # 條件1: TYPE為「無行人空間」時，note應為空或有替代通路說明
                    no_space = df[df["TYPE"] == "無行人空間"]
                    if not no_space.empty:
                        invalid_notes = no_space[~no_space["note"].isin(
                            ["", "有替代通路"]) & ~no_space["note"].isnull()]
                        if not invalid_notes.empty:
                            errors.append("人行道缺失：TYPE為「無行人空間」時，note應留空或填「有替代通路」\n"
                                          + invalid_notes[["TYPE", "note"]].head().to_string(index=False))

                    # 條件2: TYPE為「車阻距離少於1.5公尺」時，note應填間距長度(M)
                    barrier = df[df["TYPE"] == "車阻距離少於1.5公尺"]
                    if not barrier.empty:
                        missing_length = barrier[barrier["note"].isnull() | (
                            barrier["note"] == "")]
                        if not missing_length.empty:
                            errors.append("人行道缺失：TYPE為「車阻距離少於1.5公尺」時，note應填間距長度(M)\n"
                                          + missing_length[["TYPE", "note"]].head().to_string(index=False))
                        else:
                            # 檢查note是否為有效的數字加"M"
                            invalid_format = barrier[~barrier["note"].str.contains(
                                r'^\d+(\.\d+)?M$', na=True)]
                            if not invalid_format.empty:
                                errors.append("人行道缺失：車阻間距格式錯誤，應為數字加M（如1.2M）\n"
                                              + invalid_format[["TYPE", "note"]].head().to_string(index=False))

        # 忽略不檢查的欄位：ID, road, VILL_NAME, COUNTY_NAM
        # 這些欄位不需要特別檢查，如規格所述

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 人行道連接騎樓高度 ────────────
    def _check_sidewalk_bridge_height(self) -> bool:
        df = self.dfs.get("人行道連接騎樓高度", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("人行道連接騎樓高度：資料表為空，無法檢查")
        else:
            # 檢查必要欄位是否存在
            required_columns = ["TYPE"]
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"人行道連接騎樓高度：缺少必要欄位 {missing_columns}")
            else:
                # 檢查 TYPE 欄位的允許值
                allowed_types = ["有緩坡", "無緩坡"]
                invalid_types = df.loc[~df["TYPE"].isin(allowed_types), "TYPE"]
                if not invalid_types.empty:
                    errors.append("人行道連接騎樓高度：下列 TYPE 不在允許清單 → "
                                  + ", ".join(invalid_types.unique()))

                # 檢查 TYPE 欄位是否為空
                empty_types = df[df["TYPE"].isnull() | (df["TYPE"] == "")]
                if not empty_types.empty:
                    errors.append("人行道連接騎樓高度：TYPE 欄位不得為空")

        # 忽略不檢查的欄位：ID, VILL_NAME, COUNTY_NAM, 路名
        # 這些欄位不需要特別檢查，如規格所述

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 停車設施 ────────────
    def _check_parking_facilities(self) -> bool:
        df = self.dfs.get("停車設施", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("停車設施：資料表為空，無法檢查")
        else:
            # 檢查必要欄位是否存在
            required_columns = ["Name", "SWW_WTH", "U_WIDTH"]
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"停車設施：缺少必要欄位 {missing_columns}")
            else:
                # 檢查 Name 欄位的允許值
                allowed_names = [
                    "機車停車區",
                    "停車彎",
                    "YouBike自行車架",
                    "YouBike自行車停車區",
                    "自行車停車區",
                    "自行車架"
                ]
                invalid_names = df.loc[~df["Name"].isin(allowed_names), "Name"]
                if not invalid_names.empty:
                    errors.append("停車設施：下列 Name 不在允許清單 → "
                                  + ", ".join(invalid_names.unique()))

                # 檢查 Name 欄位是否為空
                empty_names = df[df["Name"].isnull() | (df["Name"] == "")]
                if not empty_names.empty:
                    errors.append("停車設施：NAME 欄位不得為空")

                # 檢查 U_WIDTH 和 SWW_WTH 的關係
                if all(col in df.columns for col in ["U_WIDTH", "SWW_WTH"]):
                    # 當 SWW_WTH < 0.9 時，U_WIDTH 不得小於 SWW_WTH
                    width_errors = df[(df["SWW_WTH"] < 0.9) & (
                        df["U_WIDTH"] < df["SWW_WTH"])]
                    if not width_errors.empty:
                        errors.append("停車設施：當 SWW_WTH < 0.9 時，U_WIDTH 不得小於 SWW_WTH\n"
                                      + width_errors[["Name", "SWW_WTH", "U_WIDTH"]].head().to_string(index=False))

        # 忽略不檢查的欄位：ID, Area, VILL_NAME, COUNTY_NAM, 路名
        # 這些欄位不需要特別檢查，如規格所述

        if errors:
            raise ValueError("\n\n".join(errors))
        return True

    # ──────────── 公共設施帶 ────────────
    def _check_public_utility_zone(self) -> bool:
        df = self.dfs.get("公共設施帶", pd.DataFrame())
        errors: list[str] = []

        if df.empty:
            errors.append("公共設施帶：資料表為空，無法檢查")
        else:
            # 檢查必要欄位是否存在
            required_columns = ["SWD_WTH"]
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"公共設施帶：缺少必要欄位 {missing_columns}")
            else:
                # 檢查 SWD_WTH 欄位是否為空
                empty_swd_wth = df[df["SWD_WTH"].isnull()]
                if not empty_swd_wth.empty:
                    errors.append("公共設施帶：SWD_WTH 欄位不得為空")

                # 檢查 SWD_WTH 是否為有效數值（非負數）
                if "SWD_WTH" in df.columns:
                    invalid_swd_wth = df[df["SWD_WTH"] < 0]
                    if not invalid_swd_wth.empty:
                        errors.append("公共設施帶：SWD_WTH 不得為負數\n"
                                      + invalid_swd_wth[["SWD_WTH"]].head().to_string(index=False))

        # 忽略不檢查的欄位：ID, Area, VILL_NAME, COUNTY_NAM, 路名
        # 這些欄位不需要特別檢查，如規格所述

        if errors:
            raise ValueError("\n\n".join(errors))
        return True


def run_test(title: str, df_key: str, func_name: str, data: dict):
    """
    title     : 測試標題
    df_key    : 放進 dfs 的字典 key（例如 "固定箱類設施"）
    func_name : FacilityChecker 內要呼叫的函式名稱字串（例如 "_check_fixed_box"）
    data      : dict → 轉成 DataFrame 當作測試資料
    """
    print(f"\n===== {title} =====")
    df = pd.DataFrame(data)
    dfs = {df_key: df}
    checker = FacilityChecker(dfs)

    try:
        ok = getattr(checker, func_name)()   # 反射呼叫對應檢查函式
        print(f"{title} → 檢查通過，回傳 {ok}")
    except ValueError as e:
        print(f"{title} → 檢查失敗，錯誤訊息：\n{e}")


if __name__ == "__main__":
    print("=" * 60)
    print("開始進行 FacilityChecker 完整測試")
    print("=" * 60)

    # ─────────────────── ① 固定出入口設施 ───────────────────
    entrance_valid = {
        "Name":    ["天橋出入口", "地下道出入口", "捷運站出入口"],
        "SWW_WTH": [1.0,         0.95,         1.2],
        "U_WIDTH": [1.2,         1.1,          1.5],
    }
    entrance_violation = {
        "Name":    ["天橋出入口", "未知出入口", "地下道出入口"],
        "SWW_WTH": [0.8,         0.85,        0.7],
        "U_WIDTH": [0.5,         0.9,         0.6],  # 第一筆和第三筆寬度違規，第二筆名稱違規
    }

    run_test("固定出入口設施／合法", "固定出入口設施", "_check_fixed_entrance", entrance_valid)
    run_test("固定出入口設施／違規", "固定出入口設施",
             "_check_fixed_entrance", entrance_violation)

    # ─────────────────── ② 固定其他設施 ───────────────────
    other_valid = {
        "Name":    ["墩柱", "資源回收箱", "佈告欄", "樓梯"],
        "SWW_WTH": [1.0,    0.95,        1.1,     0.9],
        "U_WIDTH": [1.2,    1.1,         1.3,     1.0],
    }
    other_violation = {
        "Name":    ["墩柱", "未知設施", "垃圾桶"],
        "SWW_WTH": [0.8,    0.85,      0.7],
        "U_WIDTH": [0.5,    0.9,       0.6],  # 第一筆和第三筆寬度違規，第二筆名稱違規
    }

    run_test("固定其他設施／合法", "固定其他設施", "_check_fixed_other", other_valid)
    run_test("固定其他設施／違規", "固定其他設施", "_check_fixed_other", other_violation)

    # ─────────────────── ③ 固定桿類設施 ───────────────────
    pole_valid = {
        "Name":      ["燈桿",  "號誌桿",  "電力桿"],
        "SWW_WTH":   [1.0,     0.95,      1.1],
        "U_WIDTH":   [1.2,     1.1,       1.3],
        "桿件類型":   ["直立桿", "雙頭柱桿", "N型柱桿"],
    }
    pole_violation = {
        "Name":      ["燈桿",  "未知桿",  "號誌桿"],
        "SWW_WTH":   [0.75,    0.85,      0.6],
        "U_WIDTH":   [0.5,     0.9,       0.4],     # 第一筆和第三筆寬度違規，第二筆名稱違規
        "桿件類型":   ["直立桿", "外星桿",  "其他"],   # 第二筆桿件類型也違規
    }

    run_test("固定桿類設施／合法", "固定桿類設施", "_check_fixed_pole", pole_valid)
    run_test("固定桿類設施／違規", "固定桿類設施", "_check_fixed_pole", pole_violation)

    # ─────────────────── ④ 固定箱類設施 ───────────────────
    box_valid = {
        "Name":    ["電信箱", "號誌箱", "路燈箱"],
        "SWW_WTH": [1.0,     0.95,     1.1],
        "U_WIDTH": [1.2,     1.1,      1.3],
    }
    box_violation = {
        "Name":    ["電信箱", "不存在的箱體", "號誌箱"],
        "SWW_WTH": [0.8,      0.85,         0.7],
        "U_WIDTH": [0.5,      0.9,          0.6],   # 第一筆和第三筆寬度違規，第二筆名稱違規
    }

    run_test("固定箱類設施／合法", "固定箱類設施", "_check_fixed_box", box_valid)
    run_test("固定箱類設施／違規", "固定箱類設施", "_check_fixed_box", box_violation)

    # ─────────────────── ⑤ 斜坡道 ───────────────────
    ramp_valid = {
        "Name":      ["橫越人行道之穿越道", "人行道路緣斜坡"],
        "Slopetype": ["通行無礙",          "通行無礙"],
    }
    ramp_violation = {
        "Name":      ["橫越人行道之穿越道", "未知斜坡道"],
        "Slopetype": ["通行無礙",          "不通行"],     # 第二筆名稱和類型都違規
    }

    run_test("斜坡道／合法", "斜坡道", "_check_ramp", ramp_valid)
    run_test("斜坡道／違規", "斜坡道", "_check_ramp", ramp_violation)

    # ─────────────────── ⑥ 樹穴 ───────────────────
    tree_valid = {
        "Name":    ["樹穴", "花圃"],
        "SWW_WTH": [1.0,    0.95],
        "U_WIDTH": [1.2,    1.1],
    }
    tree_violation = {
        "Name":    ["樹穴", "未知植栽"],
        "SWW_WTH": [0.8,    0.85],
        "U_WIDTH": [0.5,    0.9],     # 第一筆寬度違規，第二筆名稱違規
    }

    run_test("樹穴／合法", "樹穴", "_check_tree_pit", tree_valid)
    run_test("樹穴／違規", "樹穴", "_check_tree_pit", tree_violation)

    # ─────────────────── ⑦ 自行車專用道 ───────────────────
    bike_valid = {
        "SWD_WTH": [1.5, 2.0, 1.8],
        "其他欄位": ["A", "B", "C"],  # 可以有其他欄位
    }
    bike_violation = {
        "SWD_WTH": [1.5, None, ""],   # 第二筆空值，第三筆空字串
        "其他欄位": ["A", "B", "C"],
    }

    run_test("自行車專用道／合法", "自行車專用道", "_check_bike_lane", bike_valid)
    run_test("自行車專用道／違規", "自行車專用道", "_check_bike_lane", bike_violation)

    # ─────────────────── ⑧ 行人穿越道 ───────────────────
    crosswalk_valid = {
        "Name": ["枕木紋行人穿越道", "斑馬紋行人穿越道", "對角線行人穿越道"],
        "其他欄位": ["A", "B", "C"],
    }
    crosswalk_violation = {
        "Name": ["枕木紋行人穿越道", "未知穿越道"],
        "其他欄位": ["A", "B"],
    }

    run_test("行人穿越道／合法", "行人穿越道", "_check_crosswalk", crosswalk_valid)
    run_test("行人穿越道／違規", "行人穿越道", "_check_crosswalk", crosswalk_violation)

    # ─────────────────── ⑨ 測試空資料表 ───────────────────
    print("\n" + "=" * 40)
    print("測試空資料表情況")
    print("=" * 40)

    empty_data = {}

    run_test("固定出入口設施／空資料表", "固定出入口設施", "_check_fixed_entrance", empty_data)
    run_test("固定其他設施／空資料表", "固定其他設施", "_check_fixed_other", empty_data)
    run_test("固定桿類設施／空資料表", "固定桿類設施", "_check_fixed_pole", empty_data)
    run_test("固定箱類設施／空資料表", "固定箱類設施", "_check_fixed_box", empty_data)
    run_test("斜坡道／空資料表", "斜坡道", "_check_ramp", empty_data)
    run_test("樹穴／空資料表", "樹穴", "_check_tree_pit", empty_data)
    run_test("自行車專用道／空資料表", "自行車專用道", "_check_bike_lane", empty_data)
    run_test("行人穿越道／空資料表", "行人穿越道", "_check_crosswalk", empty_data)

    print("\n" + "=" * 60)
    print("測試完成！")
    print("=" * 60)
