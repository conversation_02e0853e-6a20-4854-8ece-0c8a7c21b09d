"""
重構後日誌模組測試

驗證重構後的日誌模組功能完整性，確保所有原有功能正常工作。
"""

import logging
import os
import sys
from pathlib import Path

# Import project modules directly (using pip install -e .)


def test_import_compatibility():
    """測試導入兼容性"""
    print("=== 測試導入兼容性 ===")

    try:
        # 測試新的模組化導入
        from log_utils import (ColoredFormatter, LogConfig, LogManager,
                               create_console_handler, create_file_handler,
                               get_global_log_manager, get_logger,
                               setup_logger)

        print("✅ 新的模組化導入成功")

        # 測試舊的向後兼容導入
        from log_utils import create_tool_logger, setup_basic_logger

        print("✅ 向後兼容導入成功")

        # 測試標準庫重新導出
        from log_utils import CRITICAL, DEBUG, ERROR, INFO, WARNING, getLogger

        print("✅ 標準庫重新導出成功")

        return True

    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False


def test_basic_functionality():
    """測試基本功能"""
    print("\n=== 測試基本功能 ===")

    try:
        from log_utils import get_logger, setup_logger

        # 創建測試日誌器
        logger = setup_logger(
            name="test_logger",
            level=logging.DEBUG,
            console_output=True,
            file_output=False,  # 避免創建文件
            simple_console=False,
            use_colors=True,
        )

        # 測試日誌輸出
        logger.debug("這是DEBUG消息")
        logger.info("這是INFO消息")
        logger.warning("這是WARNING消息")
        logger.error("這是ERROR消息")
        logger.critical("這是CRITICAL消息")

        # 測試獲取日誌器
        retrieved_logger = get_logger("test_logger")
        if retrieved_logger is logger:
            print("✅ 日誌器獲取成功")
        else:
            print("❌ 日誌器獲取失敗")
            return False

        print("✅ 基本功能測試成功")
        return True

    except Exception as e:
        print(f"❌ 基本功能測試失敗: {e}")
        return False


def test_config_system():
    """測試配置系統"""
    print("\n=== 測試配置系統 ===")

    try:
        from log_utils.core.config import (LogConfig, LogConfigPresets,
                                           create_config)

        # 測試預設配置
        dev_config = LogConfigPresets.development()
        prod_config = LogConfigPresets.production()

        print(f"✅ 開發配置: {dev_config.name}, 級別: {dev_config.level}")
        print(f"✅ 生產配置: {prod_config.name}, 級別: {prod_config.level}")

        # 測試配置驗證
        errors = dev_config.validate()
        if not errors:
            print("✅ 配置驗證通過")
        else:
            print(f"❌ 配置驗證失敗: {errors}")
            return False

        # 測試配置創建
        custom_config = create_config("debug", name="custom_test")
        print(f"✅ 自定義配置: {custom_config.name}")

        return True

    except Exception as e:
        print(f"❌ 配置系統測試失敗: {e}")
        return False


def test_formatters():
    """測試格式器"""
    print("\n=== 測試格式器 ===")

    try:
        from log_utils.formatters import (ColoredFormatter, CompactFormatter,
                                          JSONFormatter, create_formatter,
                                          get_format_string)

        # 測試彩色格式器
        colored_formatter = ColoredFormatter("%(levelname)s - %(message)s")
        print("✅ 彩色格式器創建成功")

        # 測試JSON格式器
        json_formatter = JSONFormatter()
        print("✅ JSON格式器創建成功")

        # 測試緊湊格式器
        compact_formatter = CompactFormatter()
        print("✅ 緊湊格式器創建成功")

        # 測試格式器工廠
        factory_formatter = create_formatter("colored", fmt="%(message)s")
        print("✅ 格式器工廠測試成功")

        # 測試格式字符串
        format_str = get_format_string("detailed")
        print(f"✅ 格式字符串: {format_str}")

        return True

    except Exception as e:
        print(f"❌ 格式器測試失敗: {e}")
        return False


def test_handlers():
    """測試處理器"""
    print("\n=== 測試處理器 ===")

    try:
        from log_utils.handlers import (HANDLER_PRESETS,
                                        create_console_handler,
                                        create_file_handler, create_handler)

        # 測試控制台處理器
        console_handler = create_console_handler(
            level=logging.INFO, use_colors=True, simple_format=False
        )
        print("✅ 控制台處理器創建成功")

        # 測試處理器預設
        print(f"✅ 處理器預設: {list(HANDLER_PRESETS.keys())}")

        return True

    except Exception as e:
        print(f"❌ 處理器測試失敗: {e}")
        return False


def test_backward_compatibility():
    """測試向後兼容性"""
    print("\n=== 測試向後兼容性 ===")

    try:
        from log_utils import (create_tool_logger, setup_basic_logger,
                               setup_simple_logger)

        # 測試基本日誌器
        basic_logger = setup_basic_logger("basic_test")
        basic_logger.info("基本日誌器測試")
        print("✅ 基本日誌器測試成功")

        # 測試工具日誌器
        tool_logger = create_tool_logger("tool_test")
        tool_logger.info("工具日誌器測試")
        print("✅ 工具日誌器測試成功")

        # 測試簡單日誌器
        simple_logger = setup_simple_logger("simple_test")
        simple_logger.info("簡單日誌器測試")
        print("✅ 簡單日誌器測試成功")

        return True

    except Exception as e:
        print(f"❌ 向後兼容性測試失敗: {e}")
        return False


def test_advanced_features():
    """測試高級功能"""
    print("\n=== 測試高級功能 ===")

    try:
        from log_utils.factory import (create_logger_from_preset,
                                       get_logging_statistics, list_loggers,
                                       set_logger_level)

        # 測試從預設創建日誌器
        preset_logger = create_logger_from_preset("preset_test", "development")
        preset_logger.info("預設日誌器測試")
        print("✅ 預設日誌器測試成功")

        # 測試統計信息
        stats = get_logging_statistics()
        print(f"✅ 統計信息: {stats['total_loggers']} 個日誌器")

        # 測試日誌器列表
        loggers = list_loggers()
        print(f"✅ 日誌器列表: {loggers}")

        # 測試設置級別
        success = set_logger_level("preset_test", logging.WARNING)
        print(f"✅ 設置級別: {'成功' if success else '失敗'}")

        return True

    except Exception as e:
        print(f"❌ 高級功能測試失敗: {e}")
        return False


def test_module_info():
    """測試模組信息"""
    print("\n=== 測試模組信息 ===")

    try:
        from log_utils import __version__, check_compatibility, get_module_info

        # 測試模組信息
        info = get_module_info()
        print(f"✅ 模組版本: {info['version']}")
        print(f"✅ 架構: {info['architecture']}")
        print(f"✅ 功能: {info['features']}")

        # 測試兼容性檢查
        compat = check_compatibility()
        print(f"✅ 兼容性: {'通過' if compat else '失敗'}")

        print(f"✅ 版本信息: {__version__}")

        return True

    except Exception as e:
        print(f"❌ 模組信息測試失敗: {e}")
        return False


def run_all_tests():
    """運行所有測試"""
    print("🚀 開始重構後日誌模組測試")
    print("=" * 50)

    tests = [
        test_import_compatibility,
        test_basic_functionality,
        test_config_system,
        test_formatters,
        test_handlers,
        test_backward_compatibility,
        test_advanced_features,
        test_module_info,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 測試異常: {e}")
            failed += 1

    print("\n" + "=" * 50)
    print(f"🎯 測試結果: {passed} 通過, {failed} 失敗")

    if failed == 0:
        print("🎉 所有測試通過！重構成功！")
    else:
        print("⚠️  部分測試失敗，需要檢查")

    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
