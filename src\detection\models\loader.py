"""
模型載入器

處理 YOLO 模型的載入和清理，並進行適當的資源管理。
"""

import os
from pathlib import Path
from typing import Any, Optional

# 延遲載入重型庫
def _import_torch():
    try:
        import torch
        return torch
    except ImportError:
        return None

def _import_ultralytics():
    try:
        from ultralytics import YOLO
        return YOLO
    except ImportError:
        return None

# 使用延遲載入來避免長時間的 import
try:
    from log_utils.factory import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)


class ModelLoader:
    """處理偵測模型的載入與清理"""

    def __init__(self):
        self._loaded_models: dict[str, Any] = {}
        self._model_paths: dict[str, str] = {}

    def load(self, model_path: str, device: str = "cpu") -> Any:
        """載入一個 YOLO 模型

        :param model_path: 模型檔案路徑
        :param device: 載入模型的裝置
        :return: 載入的模型實例
        :raises FileNotFoundError: 如果模型檔案不存在
        :raises ImportError: 如果 ultralytics 未安裝
        :raises Exception: 如果模型載入失敗
        """
        # 延遲載入 ultralytics
        YOLO = _import_ultralytics()
        if YOLO is None:
            raise ImportError("需要 ultralytics 套件才能載入模型")

        if not os.path.exists(model_path):
            raise FileNotFoundError(f"找不到模型檔案: {model_path}")

        # 為此模型+裝置組合建立一個唯一的鍵
        model_key = f"{model_path}@{device}"

        # 如果已載入，則回傳快取的模型
        if model_key in self._loaded_models:
            logger.debug(f"回傳快取的模型: {model_key}")
            return self._loaded_models[model_key]

        try:
            logger.info(f"正在載入模型: {model_path} 到 {device}")
            model = YOLO(model_path)
            model.to(device)

            # 快取載入的模型
            self._loaded_models[model_key] = model
            self._model_paths[model_key] = model_path

            logger.info(f"模型載入成功: {model_path}")
            return model

        except Exception as e:
            logger.error(f"載入模型 {model_path} 失敗: {e}")
            raise

    def load_if_exists(self, model_path: str, device: str = "cpu") -> Optional[Any]:
        """如果檔案存在則載入模型，否則回傳 None

        :param model_path: 模型檔案路徑
        :param device: 載入模型的裝置
        :return: 載入的模型，如果檔案不存在則為 None
        """
        if not model_path or not os.path.exists(model_path):
            logger.debug(f"找不到模型檔案，跳過: {model_path}")
            return None

        try:
            return self.load(model_path, device)
        except Exception as e:
            logger.warning(f"載入可選模型 {model_path} 失敗: {e}")
            return None

    def unload(self, model_path: str, device: str = "cpu"):
        """卸載指定的模型

        :param model_path: 模型檔案路徑
        :param device: 模型載入時所用的裝置
        """
        model_key = f"{model_path}@{device}"

        if model_key in self._loaded_models:
            try:
                model = self._loaded_models[model_key]

                # 刪除前移至 CPU 以釋放 GPU 記憶體
                if hasattr(model, "to"):
                    model.to("cpu")

                # 從快取中移除
                del self._loaded_models[model_key]
                del self._model_paths[model_key]

                logger.debug(f"已卸載模型: {model_key}")

            except Exception as e:
                logger.warning(f"卸載模型 {model_key} 時發生錯誤: {e}")

    def cleanup_model(self, model: Any):
        """清理一個模型實例

        :param model: 要清理的模型實例
        """
        try:
            if model is not None and hasattr(model, "to"):
                model.to("cpu")
            logger.debug("模型已清理")
        except Exception as e:
            logger.debug(f"模型清理錯誤: {e}")

    def cleanup_all(self):
        """清理所有已載入的模型"""
        logger.info("正在清理所有已載入的模型")

        for model_key, model in list(self._loaded_models.items()):
            try:
                # 先移至 CPU
                if hasattr(model, "to"):
                    model.to("cpu")

                # 刪除參考
                del self._loaded_models[model_key]

            except Exception as e:
                logger.warning(f"清理模型 {model_key} 時發生錯誤: {e}")

        # 清除所有快取
        self._loaded_models.clear()
        self._model_paths.clear()

        # 強制清理 GPU 記憶體
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.debug("GPU 快取已清除")
        except Exception as e:
            logger.debug(f"GPU 快取清理錯誤: {e}")

    def get_loaded_models(self) -> dict[str, str]:
        """取得已載入模型的資訊

        :return: 將模型鍵對應到路徑的字典
        """
        return self._model_paths.copy()

    def is_loaded(self, model_path: str, device: str = "cpu") -> bool:
        """檢查模型是否已載入

        :param model_path: 模型檔案路徑
        :param device: 要檢查的裝置
        :return: 如果模型已載入則為 True
        """
        model_key = f"{model_path}@{device}"
        return model_key in self._loaded_models

    def get_model_info(self, model_path: str) -> dict[str, Any]:
        """取得模型檔案的資訊

        :param model_path: 模型檔案路徑
        :return: 模型資訊字典
        """
        info = {
            "path": model_path,
            "exists": os.path.exists(model_path),
            "size_mb": 0,
            "loaded_devices": [],
        }

        if info["exists"]:
            try:
                file_size = os.path.getsize(model_path)
                info["size_mb"] = file_size / (1024 * 1024)
            except Exception:
                pass

        # 檢查此模型在哪個裝置上被載入
        for model_key, path in self._model_paths.items():
            if path == model_path:
                device = model_key.split("@")[1]
                info["loaded_devices"].append(device)

        return info

    def __del__(self):
        """解構子 - 清理所有模型"""
        try:
            self.cleanup_all()
        except Exception:
            pass
