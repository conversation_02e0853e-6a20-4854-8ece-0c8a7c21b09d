"""
依賴注入容器 - 使用 dependency-injector
整合核心模組，提供完整的服務配置
"""
from dependency_injector import containers, providers

from .core.config import UtilsConfig
from .core.thresholds import SystemThresholds, DEFAULT_THRESHOLDS
from .core.metric_collector import MetricRegistry
from .core.alert_system import AlertSystem
from .unified_memory_manager import UnifiedMemoryManager
from .unified_performance_monitor import UnifiedPerformanceMonitor
from .unified_distributed_processor import UnifiedDistributedProcessor


class CoreContainer(containers.DeclarativeContainer):
    """核心服務容器"""

    # 配置提供者 - 使用默認配置
    config = providers.Singleton(UtilsConfig)
    
    # 閾值配置 - 使用默認值
    thresholds = providers.Singleton(SystemThresholds)
    
    # 指標注册表
    metric_registry = providers.Singleton(MetricRegistry)
    
    # 警報系統
    alert_system = providers.Singleton(
        AlertSystem,
        thresholds=thresholds
    )


class ManagerContainer(containers.DeclarativeContainer):
    """統一管理器容器"""

    core = providers.Container(CoreContainer)

    # 實現單例模式 - 比 SingletonMeta 更優雅
    memory_manager = providers.Singleton(
        UnifiedMemoryManager,
        config=core.config.provided.memory
    )

    performance_monitor = providers.Singleton(
        UnifiedPerformanceMonitor,
        config=core.config.provided.performance
    )

    distributed_processor = providers.Singleton(
        UnifiedDistributedProcessor,
        backend=core.config.provided.processing.backend
    )


# 便捷的工廠函數
def create_utils_environment(config_file: str | None = None) -> dict:
    """
    創建完整的工具環境
    
    Args:
        config_file: 配置文件路徑 (可選)
        
    Returns:
        包含所有管理器的字典
    """
    container = ManagerContainer()
    
    if config_file:
        container.core.config.from_yaml(config_file)
    
    return {
        'memory': container.memory_manager(),
        'performance': container.performance_monitor(),
        'distributed': container.distributed_processor(),
        'core': {
            'thresholds': container.core.thresholds(),
            'metric_registry': container.core.metric_registry(),
            'alert_system': container.core.alert_system(),
        }
    }
