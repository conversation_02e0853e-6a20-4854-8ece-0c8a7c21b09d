"""
Processing Module - Enterprise-grade Image Processing Engine

This module contains complete image processing workflows, from input analysis to output
generation, supporting batch processing, progress management, HTML5 processing and other
enterprise-level functions.

Main components:
- factory: ProcessingFactory for dependency injection architecture
- pipeline: ProcessingPipeline for coordinated processing steps
- panorama_processor: Panoramic image processor (refactored coordinator)
- batch_processor: Batch processor (large-scale processing)
- scene_processor: Scene processor (unified processing interface)
- progress_manager: Progress manager (interruption recovery)
- input_analyzer: Input analyzer (intelligent directory scanning)
- pyramid_reporter: Report generator (multi-format reports)
- compatibility: Backward compatibility layer for legacy APIs
- components/: Service components (DetectionService, CubeService, etc.)
"""

# 為 PyInstaller 提供明確的套件標識
__package__ = "processing"
__version__ = "1.0.0"

# 處理模組和主要類別列表
__all__ = [
    # 子模組
    "batch_processor",
    "progress_manager", 
    "input_analyzer",
    "scene_processor",
    "pyramid_reporter",
    "panorama_processor",
    "pyramid_generator",
    "pyramid_config",
    "factory",
    "pipeline",
    "compatibility",
    "components",
    # 主要類別 (將在下方動態添加)
]

# 確保此模組被正確識別為套件
def _ensure_package():
    """Ensure this module is correctly identified as a package"""
    return True

_package_initialized = _ensure_package()

# 延遲載入的處理系統元件匯入與匯出
# 使用延遲載入來避免長時間的 import 過程

# 延遲載入標記
_batch_processor_loaded = False
_input_analyzer_loaded = False
_panorama_processor_loaded = False
_progress_manager_loaded = False
_pyramid_loaded = False
_scene_processor_loaded = False
_factory_loaded = False
_pipeline_loaded = False
_compatibility_loaded = False
_components_loaded = False

def _load_batch_processor():
    """延遲載入批次處理器"""
    global _batch_processor_loaded
    if not _batch_processor_loaded:
        try:
            from .batch_processor import AdvancedBatchProcessor
            globals()['AdvancedBatchProcessor'] = AdvancedBatchProcessor
            _batch_processor_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"批次處理器載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_input_analyzer():
    """延遲載入輸入分析器"""
    global _input_analyzer_loaded
    if not _input_analyzer_loaded:
        try:
            from .input_analyzer import InputAnalyzer
            globals()['InputAnalyzer'] = InputAnalyzer
            _input_analyzer_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"輸入分析器載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_panorama_processor():
    """延遲載入全景處理器"""
    global _panorama_processor_loaded
    if not _panorama_processor_loaded:
        try:
            from .panorama_processor import PanoramaProcessor
            globals()['PanoramaProcessor'] = PanoramaProcessor
            _panorama_processor_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"全景處理器載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_progress_manager():
    """延遲載入進度管理器"""
    global _progress_manager_loaded
    if not _progress_manager_loaded:
        try:
            from .progress_manager import ProgressManager, TemporalProgressManager
            globals()['ProgressManager'] = ProgressManager
            globals()['TemporalProgressManager'] = TemporalProgressManager
            _progress_manager_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"進度管理器載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_pyramid():
    """延遲載入金字塔相關元件"""
    global _pyramid_loaded
    if not _pyramid_loaded:
        try:
            from .pyramid_reporter import PyramidReporter, ProcessingStats, SceneReport
            from .pyramid_generator import PyramidGenerator, PyramidTileGenerator
            from .pyramid_config import PyramidConfig, PyramidQuality, TileFormat

            globals()['PyramidReporter'] = PyramidReporter
            globals()['ProcessingStats'] = ProcessingStats
            globals()['SceneReport'] = SceneReport
            globals()['PyramidGenerator'] = PyramidGenerator
            globals()['PyramidTileGenerator'] = PyramidTileGenerator
            globals()['PyramidConfig'] = PyramidConfig
            globals()['PyramidQuality'] = PyramidQuality
            globals()['TileFormat'] = TileFormat

            _pyramid_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"金字塔元件載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_scene_processor():
    """延遲載入場景處理器"""
    global _scene_processor_loaded
    if not _scene_processor_loaded:
        try:
            from .scene_processor import SceneProcessor
            globals()['SceneProcessor'] = SceneProcessor
            _scene_processor_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"場景處理器載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_factory():
    """延遲載入工廠"""
    global _factory_loaded
    if not _factory_loaded:
        try:
            from .factory import ProcessingFactory
            globals()['ProcessingFactory'] = ProcessingFactory
            _factory_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"工廠載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_pipeline():
    """延遲載入管線"""
    global _pipeline_loaded
    if not _pipeline_loaded:
        try:
            from .pipeline import (
                ProcessingPipeline,
                ProcessingContext,
                ProcessingResult,
                ProcessingStep,
                LoadImageStep,
                ConvertToCubeStep,
                DetectObjectsStep,
                ApplyBlurStep,
                SaveResultsStep,
            )

            globals()['ProcessingPipeline'] = ProcessingPipeline
            globals()['ProcessingContext'] = ProcessingContext
            globals()['ProcessingResult'] = ProcessingResult
            globals()['ProcessingStep'] = ProcessingStep
            globals()['LoadImageStep'] = LoadImageStep
            globals()['ConvertToCubeStep'] = ConvertToCubeStep
            globals()['DetectObjectsStep'] = DetectObjectsStep
            globals()['ApplyBlurStep'] = ApplyBlurStep
            globals()['SaveResultsStep'] = SaveResultsStep

            _pipeline_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"管線載入失敗: {e}", ImportWarning)
            return False
    return True

def _load_compatibility():
    """延遲載入相容性層"""
    global _compatibility_loaded
    if not _compatibility_loaded:
        try:
            from .compatibility import LegacyPanoramaProcessor, LegacyBatchProcessor
            globals()['LegacyPanoramaProcessor'] = LegacyPanoramaProcessor
            globals()['LegacyBatchProcessor'] = LegacyBatchProcessor
            _compatibility_loaded = True
            return True
        except ImportError as e:
            import warnings
            warnings.warn(f"相容性層載入失敗: {e}", ImportWarning)
            return False
    return True

# 將主要類別添加到 __all__
__all__.extend([
    # 主要處理器
    "AdvancedBatchProcessor",
    "PanoramaProcessor",
    "SceneProcessor",
    "InputAnalyzer",
    # 進度管理
    "ProgressManager",
    "TemporalProgressManager",
    # 金字塔相關
    "PyramidGenerator",
    "PyramidTileGenerator",
    "PyramidReporter",
    "PyramidConfig",
    "PyramidQuality",
    "TileFormat",
    "ProcessingStats",
    "SceneReport",
    # 工廠和管線
    "ProcessingFactory",
    "ProcessingPipeline",
    "ProcessingContext",
    "ProcessingResult",
    "ProcessingStep",
    # 管線步驟
    "LoadImageStep",
    "ConvertToCubeStep",
    "DetectObjectsStep",
    "ApplyBlurStep",
    "SaveResultsStep",
    # 向後兼容
    "LegacyPanoramaProcessor",
    "LegacyBatchProcessor",
])

# 定義 __getattr__ 來實現延遲載入
def __getattr__(name: str):
    """實現延遲載入的 __getattr__ 方法"""
    # 批次處理器
    if name == "AdvancedBatchProcessor":
        if _load_batch_processor():
            return globals().get(name)
        raise AttributeError(f"無法載入批次處理器 '{name}'")

    # 輸入分析器
    elif name == "InputAnalyzer":
        if _load_input_analyzer():
            return globals().get(name)
        raise AttributeError(f"無法載入輸入分析器 '{name}'")

    # 全景處理器
    elif name == "PanoramaProcessor":
        if _load_panorama_processor():
            return globals().get(name)
        raise AttributeError(f"無法載入全景處理器 '{name}'")

    # 進度管理器
    elif name in ["ProgressManager", "TemporalProgressManager"]:
        if _load_progress_manager():
            return globals().get(name)
        raise AttributeError(f"無法載入進度管理器 '{name}'")

    # 金字塔相關
    elif name in ["PyramidReporter", "ProcessingStats", "SceneReport", "PyramidGenerator", "PyramidTileGenerator", "PyramidConfig", "PyramidQuality", "TileFormat"]:
        if _load_pyramid():
            return globals().get(name)
        raise AttributeError(f"無法載入金字塔元件 '{name}'")

    # 場景處理器
    elif name == "SceneProcessor":
        if _load_scene_processor():
            return globals().get(name)
        raise AttributeError(f"無法載入場景處理器 '{name}'")

    # 工廠
    elif name == "ProcessingFactory":
        if _load_factory():
            return globals().get(name)
        raise AttributeError(f"無法載入工廠 '{name}'")

    # 管線相關
    elif name in ["ProcessingPipeline", "ProcessingContext", "ProcessingResult", "ProcessingStep", "LoadImageStep", "ConvertToCubeStep", "DetectObjectsStep", "ApplyBlurStep", "SaveResultsStep"]:
        if _load_pipeline():
            return globals().get(name)
        raise AttributeError(f"無法載入管線元件 '{name}'")

    # 相容性層
    elif name in ["LegacyPanoramaProcessor", "LegacyBatchProcessor"]:
        if _load_compatibility():
            return globals().get(name)
        raise AttributeError(f"無法載入相容性層 '{name}'")

    # 如果不是已知的延遲載入元件，拋出標準錯誤
    raise AttributeError(f"模組 '{__name__}' 沒有屬性 '{name}'")
