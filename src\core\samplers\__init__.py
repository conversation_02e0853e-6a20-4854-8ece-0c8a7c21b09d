"""
影像採樣器模組 (Sam<PERSON>s Module)

本模組提供了專門用於從來源影像（全景圖或立方體圖）中提取像素值的採樣器類別。
它作為 `core.interpolation` 和 `core.projection` 之間的橋樑，處理了複雜的邊界條件
（如全景圖的環繞邊界和立方體面的鄰接邊界），確保在進行投影轉換時能夠準確、
無縫地進行像素採樣。

主要匯出項目:
- `EnhancedEquirectSampler`: 專用於全景圖的採樣器。
- `EnhancedCubeFaceSampler`: 專用於立方體圖的採樣器。
- `create_equirect_sampler`: 創建全景圖採樣器的工廠函數。
- `create_cube_face_sampler`: 創建立方體圖採樣器的工廠函數。
"""

# 使用延遲載入來避免長時間的 import
def _import_core():
    """延遲載入核心模組"""
    try:
        from .core import (EnhancedCubeFaceSampler, EnhancedEquirectSampler,
                           create_cube_face_sampler, create_equirect_sampler)
        return EnhancedCubeFaceSampler, EnhancedEquirectSampler, create_cube_face_sampler, create_equirect_sampler
    except ImportError as e:
        import warnings
        warnings.warn(f"samplers.core 模組載入失敗: {e}", ImportWarning)
        return None, None, None, None

# 延遲載入標記
_core_loaded = False
EnhancedCubeFaceSampler = None
EnhancedEquirectSampler = None
create_cube_face_sampler = None
create_equirect_sampler = None

def __getattr__(name):
    """實現延遲載入的 __getattr__ 方法"""
    global _core_loaded, EnhancedCubeFaceSampler, EnhancedEquirectSampler, create_cube_face_sampler, create_equirect_sampler

    if name in ["EnhancedCubeFaceSampler", "EnhancedEquirectSampler", "create_cube_face_sampler", "create_equirect_sampler"]:
        if not _core_loaded:
            EnhancedCubeFaceSampler, EnhancedEquirectSampler, create_cube_face_sampler, create_equirect_sampler = _import_core()
            _core_loaded = True

        if name == "EnhancedCubeFaceSampler":
            return EnhancedCubeFaceSampler
        elif name == "EnhancedEquirectSampler":
            return EnhancedEquirectSampler
        elif name == "create_cube_face_sampler":
            return create_cube_face_sampler
        elif name == "create_equirect_sampler":
            return create_equirect_sampler

    raise AttributeError(f"模組 '{__name__}' 沒有屬性 '{name}'")

# 定義公開 API，明確指出可從外部匯入的類別與函數。
__all__ = [
    "EnhancedEquirectSampler",
    "EnhancedCubeFaceSampler",
    "create_equirect_sampler",
    "create_cube_face_sampler",
]
