#!/usr/bin/env python3
"""
測試 default 目錄創建和恢復功能
Test script for default directory creation and resume functionality
"""

import os
import sys
import tempfile
import yaml

# Add src path
sys.path.insert(0, 'src')

def test_progress_manager_with_default():
    """測試進度管理器處理 default 目錄的能力"""
    print("=== 測試進度管理器 default 目錄處理 ===")
    
    try:
        from processing.progress_manager import ProgressManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"臨時目錄: {temp_dir}")
            
            # 創建測試輸出結構
            # 模擬已經完成的場景
            scene1_dir = os.path.join(temp_dir, "scene1")
            os.makedirs(scene1_dir)
            
            # 創建金字塔結構
            html5_dir = os.path.join(scene1_dir, "html5")
            os.makedirs(html5_dir)
            
            # 創建基本檔案
            for i in range(6):
                with open(os.path.join(html5_dir, f"{i}.jpg"), 'w') as f:
                    f.write("test")
            
            with open(os.path.join(scene1_dir, "thumbnail.jpg"), 'w') as f:
                f.write("test")
            
            # 創建進度管理器並測試
            pm = ProgressManager(output_path=temp_dir)
            
            # 保存進度記錄
            pm.save_progress("default", "scene1", "完成", 1.5)
            pm.save_progress("default", "scene2", "失敗", 0.8)
            
            # 測試驗證功能
            print("測試場景驗證...")
            result1 = pm._verify_scene_output_exists("default/scene1")
            result2 = pm._verify_scene_output_exists("default/scene2")
            
            print(f"scene1 (有效結構) 驗證結果: {result1}")
            print(f"scene2 (無結構) 驗證結果: {result2}")
            
            # 測試加載已完成場景
            completed = pm.load_completed_scenes(verify_output=True)
            print(f"已完成場景: {completed}")
            
            print("✅ 進度管理器 default 測試完成")
            return True
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_organize_by_district_config():
    """測試 organize_by_district 配置選項"""
    print("\n=== 測試目錄組織配置 ===")
    
    # 測試配置選項
    configs = [
        {"organize_by_district": True, "expected": "default/scene"},
        {"organize_by_district": False, "expected": "scene"},
        {}, # 預設值測試
    ]
    
    for config in configs:
        organize = config.get("organize_by_district", True)  # 預設為 True
        expected_pattern = config.get("expected", "default/scene")
        
        print(f"配置 organize_by_district={organize} -> 結構: {expected_pattern}")
        
        # 模擬路徑創建邏輯
        output_path = "/test/output"
        scene_name = "test_scene"
        
        if organize:
            scene_output_dir = os.path.join(output_path, "default", scene_name)
            actual_output_path = os.path.join(output_path, "default")
        else:
            actual_output_path = output_path
            scene_output_dir = os.path.join(output_path, scene_name)
        
        print(f"  -> scene_output_dir: {scene_output_dir}")
        print(f"  -> actual_output_path: {actual_output_path}")
    
    print("✅ 目錄組織配置測試完成")

def test_config_creation():
    """創建測試配置檔案"""
    print("\n=== 創建測試配置檔案 ===")
    
    # 創建支援 default 目錄的配置
    config_with_default = {
        "mode": "pano_to_cube",
        "input_path": "./test_input",
        "primary_output_path": "./test_output",
        "organize_by_district": True,  # 啟用 default 目錄
        "display_mode": "verbose"
    }
    
    config_without_default = {
        "mode": "pano_to_cube", 
        "input_path": "./test_input",
        "primary_output_path": "./test_output",
        "organize_by_district": False,  # 直接在根目錄
        "display_mode": "quiet"
    }
    
    # 保存配置檔案
    with open("config_with_default.yaml", 'w', encoding='utf-8') as f:
        yaml.dump(config_with_default, f, default_flow_style=False, allow_unicode=True)
    
    with open("config_without_default.yaml", 'w', encoding='utf-8') as f:
        yaml.dump(config_without_default, f, default_flow_style=False, allow_unicode=True)
    
    print("創建的配置檔案:")
    print("1. config_with_default.yaml - 創建 default 目錄")
    print("2. config_without_default.yaml - 直接在根目錄")
    
    print("✅ 配置檔案創建完成")

def cleanup():
    """清理測試檔案"""
    test_files = ["config_with_default.yaml", "config_without_default.yaml"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"已清理: {file}")

if __name__ == "__main__":
    print("🚀 default 目錄測試開始")
    print("=" * 40)
    
    # 執行所有測試
    pm_success = test_progress_manager_with_default()
    
    test_organize_by_district_config()
    
    test_config_creation()
    
    print("\n" + "=" * 40)
    if pm_success:
        print("🎉 所有測試完成！")
        print("\n📋 使用說明:")
        print("1. 要創建 default 目錄: organize_by_district: true (預設)")
        print("2. 直接在根目錄: organize_by_district: false")
    else:
        print("⚠️ 部分測試失敗")
    
    print("=" * 40)
    
    # 提供清理選項
    response = input("\n是否清理測試配置檔案? (y/n): ")
    if response.lower() == 'y':
        cleanup()