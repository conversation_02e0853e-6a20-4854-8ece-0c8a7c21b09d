"""
模型管理器

高階模型管理，具備自動載入、快取和清理功能。
"""

from typing import Any, Optional

# 使用延遲載入來避免長時間的 import
try:
    from log_utils.factory import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

from .loader import ModelLoader

logger = get_logger(__name__)


class ModelManager:
    """用於偵測的高階模型管理"""

    def __init__(self):
        self.loader = ModelLoader()
        self._model_registry: dict[str, Any] = {}
        self._device = "cpu"

    def set_device(self, device: str):
        """設定模型載入的預設裝置

        :param device: 裝置字串 (例如 'cpu', 'cuda', 'cuda:0')
        """
        self._device = device
        logger.info(f"模型管理器裝置已設為: {device}")

    def register_model(
        self, model_id: str, model_path: str, required: bool = True
    ) -> bool:
        """註冊一個模型以供載入

        :param model_id: 模型的唯一識別碼
        :param model_path: 模型檔案的路徑
        :param required: 此模型是否為必需
        :return: 如果註冊成功則為 True
        """
        try:
            if required:
                model = self.loader.load(model_path, self._device)
            else:
                model = self.loader.load_if_exists(model_path, self._device)

            if model is not None:
                self._model_registry[model_id] = model
                logger.info(f"已註冊模型 '{model_id}': {model_path}")
                return True
            else:
                logger.warning(
                    f"註冊可選模型 '{model_id}' 失敗: {model_path}"
                )
                return False

        except Exception as e:
            if required:
                logger.error(f"註冊必要模型 '{model_id}' 失敗: {e}")
                raise
            else:
                logger.warning(f"註冊可選模型 '{model_id}' 失敗: {e}")
                return False

    def get_model(self, model_id: str) -> Optional[Any]:
        """透過 ID 取得已註冊的模型

        :param model_id: 模型識別碼
        :return: 模型實例，如果找不到則為 None
        """
        return self._model_registry.get(model_id)

    def get_models(self) -> dict[str, Any]:
        """取得所有已註冊的模型

        :return: model_id -> 模型實例的字典
        """
        return self._model_registry.copy()

    def has_model(self, model_id: str) -> bool:
        """檢查模型是否已註冊

        :param model_id: 模型識別碼
        :return: 如果模型存在則為 True
        """
        return model_id in self._model_registry

    def unregister_model(self, model_id: str):
        """取消註冊一個模型

        :param model_id: 模型識別碼
        """
        if model_id in self._model_registry:
            model = self._model_registry[model_id]
            self.loader.cleanup_model(model)
            del self._model_registry[model_id]
            logger.info(f"已取消註冊模型: {model_id}")

    def get_registered_models(self) -> list[str]:
        """取得已註冊模型的 ID 列表

        :return: 模型 ID 列表
        """
        return list(self._model_registry.keys())

    def setup_standard_models(
        self, primary_path: str, secondary_path: Optional[str] = None
    ):
        """設定標準的主要和次要模型

        :param primary_path: 主要模型的路徑 (必要)
        :param secondary_path: 次要模型的路徑 (可選)
        """
        logger.info("正在設定標準偵測模型")

        # 註冊主要模型 (必要)
        self.register_model("primary", primary_path, required=True)

        # 註冊次要模型 (可選)
        if secondary_path:
            self.register_model("secondary", secondary_path, required=False)

        logger.info(
            f"標準模型設定完成: 已載入 {len(self._model_registry)} 個模型"
        )

    def get_model_summary(self) -> dict[str, Any]:
        """取得模型管理器狀態的摘要

        :return: 摘要字典
        """
        summary = {
            "device": self._device,
            "registered_models": len(self._model_registry),
            "model_ids": list(self._model_registry.keys()),
            "loader_info": self.loader.get_loaded_models(),
        }

        return summary

    def validate_models(self) -> dict[str, bool]:
        """驗證所有已註冊的模型

        :return: model_id -> is_valid 的字典
        """
        validation_results = {}

        for model_id, model in self._model_registry.items():
            try:
                # 基本驗證 - 檢查模型是否可呼叫
                is_valid = model is not None and hasattr(model, "predict")
                validation_results[model_id] = is_valid

                if not is_valid:
                    logger.warning(f"模型 '{model_id}' 驗證失敗")

            except Exception as e:
                logger.error(f"驗證模型 '{model_id}' 時發生錯誤: {e}")
                validation_results[model_id] = False

        return validation_results

    def cleanup(self):
        """清理所有模型和資源"""
        logger.info("正在清理模型管理器")

        # 清除註冊表
        for model_id in list(self._model_registry.keys()):
            self.unregister_model(model_id)

        # 清理載入器
        self.loader.cleanup_all()

        logger.info("模型管理器清理完成")

    def reset_cache(self):
        """重設模型快取，同時保留已註冊的模型"""
        try:
            # 強制清理 GPU 快取
            import torch

            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.debug("模型管理器快取已重設")
        except Exception as e:
            logger.debug(f"快取重設錯誤: {e}")

    def __del__(self):
        """解構子 - 清理資源"""
        try:
            self.cleanup()
        except Exception:
            pass
