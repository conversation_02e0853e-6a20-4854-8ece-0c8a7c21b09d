#!/usr/bin/env python3
"""
打包 generate_pyramid.py 為 exe 檔案的腳本
使用 PyInstaller 進行打包
"""

import subprocess
import sys
import os
from pathlib import Path

def install_pyinstaller():
    """安裝 PyInstaller"""
    print("正在安裝 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller 安裝成功")
    except subprocess.CalledProcessError as e:
        print(f"PyInstaller 安裝失敗: {e}")
        return False
    return True

def build_exe():
    """使用 PyInstaller 打包程式"""
    script_dir = Path(__file__).parent
    main_script = script_dir / "generate_pyramid.py"
    
    if not main_script.exists():
        print(f"找不到主要腳本: {main_script}")
        return False
    
    # PyInstaller 命令參數
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成單一檔案
        "--console",                    # 保留控制台窗口
        "--name", "generate_pyramid",   # 指定輸出檔案名稱
        "--distpath", str(script_dir / "dist"),  # 指定輸出目錄
        "--workpath", str(script_dir / "build"), # 指定工作目錄
        "--specpath", str(script_dir),  # 指定 spec 檔案位置
        # 使用者需要在執行時提供 config.yaml 路徑
        "--hidden-import", "torch",
        "--hidden-import", "ultralytics",
        "--hidden-import", "cv2",
        "--hidden-import", "numpy",
        "--hidden-import", "scipy",
        "--hidden-import", "yaml",
        "--hidden-import", "pandas",
        "--hidden-import", "PIL",
        "--collect-data", "ultralytics",
        "--collect-data", "torch",
        str(main_script)
    ]
    
    print("開始打包...")
    print(f"執行命令: {' '.join(cmd)}")
    
    try:
        # 變更到腳本目錄
        os.chdir(script_dir)
        subprocess.check_call(cmd)
        print("打包成功!")
        
        exe_path = script_dir / "dist" / "generate_pyramid.exe"
        if exe_path.exists():
            print(f"EXE 檔案位置: {exe_path}")
            print(f"檔案大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"打包失敗: {e}")
        return False

def main():
    """主要函數"""
    print("=== generate_pyramid.py EXE 打包工具 ===")
    print()
    
    # 檢查是否已安裝 PyInstaller
    try:
        import PyInstaller
        print("PyInstaller 已安裝")
    except ImportError:
        if not install_pyinstaller():
            return
    
    # 開始打包
    if build_exe():
        print()
        print("打包完成!")
        print("請檢查 dist 資料夾中的 generate_pyramid.exe")
        print()
        print("使用說明:")
        print("1. 準備 config.yaml 配置檔案")
        print("2. 執行 generate_pyramid.exe")
        print("3. 按照提示輸入 config.yaml 檔案路徑")
    else:
        print("打包失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    main()