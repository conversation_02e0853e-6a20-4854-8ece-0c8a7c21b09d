import pytest
import sys
from pathlib import Path
import numpy as np

# Import project modules directly (using pip install -e .)

from utils.unified_memory_manager import UnifiedMemoryManager

def test_singleton_instance(memory_manager: UnifiedMemoryManager):
    """測試記憶體管理器是否為單例"""
    another_manager = UnifiedMemoryManager()
    assert memory_manager is another_manager

def test_get_system_memory_info(memory_manager: UnifiedMemoryManager):
    """測試獲取系統記憶體信息"""
    info = memory_manager.get_system_memory_info()
    assert isinstance(info, dict)
    assert 'total_mb' in info
    assert 'available_mb' in info
    assert 'percent' in info
    assert info['total_mb'] > 0
    assert 0 <= info['percent'] <= 100

def test_get_gpu_memory_info(memory_manager: UnifiedMemoryManager):
    """測試獲取GPU記憶體信息"""
    # 這個測試在沒有GPU的環境下也能安全運行
    info = memory_manager.get_gpu_memory_info()
    assert isinstance(info, dict)
    if memory_manager.cuda_provider:
        assert 'total_mb' in info
        assert 'free_mb' in info
        assert info['total_mb'] > 0
    else:
        assert not info  # 如果沒有GPU，應返回空字典

def test_estimate_memory_requirement(memory_manager: UnifiedMemoryManager):
    """測試記憶體需求估算"""
    shape = (1024, 1024, 3)
    dtype = np.uint8
    # 1024 * 1024 * 3 * 1 (byte) * 3 (factor) / (1024*1024) = 9 MB
    requirement = memory_manager.estimate_memory_requirement(shape, dtype=dtype, factor=3.0)
    assert isinstance(requirement, int)
    assert requirement > 0
    # 預期值約為 9MB
    assert 8 <= requirement <= 10

def test_cleanup_all(memory_manager: UnifiedMemoryManager):
    """測試清理所有記憶體"""
    # 這個測試主要確保調用不會引發異常
    try:
        memory_manager.cleanup_all()
        assert True
    except Exception as e:
        pytest.fail(f"cleanup_all() 引發了異常: {e}")