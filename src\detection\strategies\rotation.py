"""
旋轉偵測策略

為面 5 (地面) 實作基於旋轉的偵測。
包含 90° 和 270° 旋轉偵測與座標轉換。
"""

from typing import Any

import cv2
import numpy as np
import torch

from log_utils import get_logger

from ..core.data_structures import DetectionBox
from .base import DetectionStrategy

logger = get_logger(__name__)


class RotationStrategy(DetectionStrategy):
    """面 5 (地面) 的旋轉偵測策略"""

    def detect(
        self, image: np.ndarray, models: dict[str, Any], config: Any
    ) -> list[DetectionBox]:
        """執行旋轉偵測：原始 + 90° + 270° 旋轉

        :param image: 輸入影像
        :param models: 可用模型的字典
        :param config: 偵測設定
        :return: 來自所有旋轉角度的偵測框列表
        """
        all_detections = []
        h, w = image.shape[:2]

        primary_model = models.get("primary")
        if not primary_model:
            logger.warning("旋轉偵測無主要模型可用")
            return []

        # 對於地面偵測使用 face5_test_conf
        face5_conf = getattr(config, "face5_test_conf", config.conf_threshold)

        # 原始影像偵測
        original_detections = self._detect_with_model(
            image, primary_model, face5_conf, config
        )
        all_detections.extend(original_detections)

        # 90° 旋轉偵測
        try:
            rotated_90 = cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
            rotated_90_detections = self._detect_with_model(
                rotated_90, primary_model, face5_conf, config
            )

            # 將座標轉換回原始方向
            for det in rotated_90_detections:
                new_x1 = det.y1
                new_y1 = w - det.x2
                new_x2 = det.y2
                new_y2 = w - det.x1

                all_detections.append(
                    DetectionBox(
                        x1=int(new_x1),
                        y1=int(new_y1),
                        x2=int(new_x2),
                        y2=int(new_y2),
                        confidence=det.confidence,
                        class_id=det.class_id,
                    )
                )
        except Exception as e:
            logger.error(f"90° 旋轉偵測失敗: {e}")

        # 270° 旋轉偵測
        try:
            rotated_270 = cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
            rotated_270_detections = self._detect_with_model(
                rotated_270, primary_model, face5_conf, config
            )

            # 將座標轉換回原始方向
            for det in rotated_270_detections:
                new_x1 = h - det.y2
                new_y1 = det.x1
                new_x2 = h - det.y1
                new_y2 = det.x2

                all_detections.append(
                    DetectionBox(
                        x1=int(new_x1),
                        y1=int(new_y1),
                        x2=int(new_x2),
                        y2=int(new_y2),
                        confidence=det.confidence,
                        class_id=det.class_id,
                    )
                )
        except Exception as e:
            logger.error(f"270° 旋轉偵測失敗: {e}")

        # 在原始影像上進行次要模型偵測 (如果可用)
        secondary_model = models.get("secondary")
        if secondary_model:
            secondary_detections = self._detect_with_model(
                image, secondary_model, face5_conf, config
            )
            all_detections.extend(secondary_detections)

        return all_detections

    def _detect_with_model(
        self, image: np.ndarray, model: Any, conf_threshold: float, config: Any
    ) -> list[DetectionBox]:
        """使用單一模型執行偵測

        :param image: 輸入影像
        :param model: YOLO 模型實例
        :param conf_threshold: 信賴度閾值
        :param config: 偵測設定
        :return: 偵測框列表
        """
        try:
            # 如果 CUDA 可用，則使用 AMP
            use_amp = (
                "cuda" in str(config.device) if hasattr(config, "device") else False
            )

            if use_amp:
                with torch.amp.autocast(device_type="cuda"):
                    results = model.predict(
                        image,
                        conf=conf_threshold,
                        iou=config.iou_threshold,
                        verbose=False,
                        device=config.device,
                    )
            else:
                results = model.predict(
                    image,
                    conf=conf_threshold,
                    iou=config.iou_threshold,
                    verbose=False,
                    device=config.device,
                )

            detections = []
            if results and len(results) > 0:
                for box in results[0].boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    conf_val = box.conf[0].cpu().item()
                    cls = int(box.cls[0].cpu().item())

                    detections.append(
                        DetectionBox(
                            x1=int(x1),
                            y1=int(y1),
                            x2=int(x2),
                            y2=int(y2),
                            confidence=conf_val,
                            class_id=cls,
                        )
                    )

            return detections

        except Exception as e:
            logger.error(f"旋轉偵測失敗: {e}")
            return []

    def get_name(self) -> str:
        """取得策略名稱"""
        return "rotation"

    def can_handle_face(self, face_id: int) -> bool:
        """旋轉策略只處理面 5 (地面)"""
        return face_id == 5

    def get_description(self) -> str:
        """取得策略描述"""
        return "用於地面的旋轉偵測 (0°, 90°, 270°) 並進行座標轉換"
