"""
matplotlib 字體配置模塊
統一設置中文字體和負號顯示問題
"""

try:
    import matplotlib.pyplot as plt
    import matplotlib
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    
import logging
# 已使用現代 type hints

logger = logging.getLogger(__name__)

def setup_chinese_font(
    fonts: list[str] | None = None,
    auto_detect: bool = True,
    verbose: bool = False
) -> bool:
    """
    設置 matplotlib 中文字體配置
    
    Args:
        fonts: 字體列表，預設為 ['Microsoft YaHei', 'SimHei', 'Times New Roman']
        auto_detect: 是否自動檢測可用字體
        verbose: 是否顯示詳細資訊
        
    Returns:
        bool: 設置是否成功
    """
    if not HAS_MATPLOTLIB:
        if verbose:
            logger.warning("matplotlib 不可用，跳過字體設置")
        return False
        
    if fonts is None:
        fonts = ['Microsoft YaHei', 'SimHei', 'Deja<PERSON>u Sans', 'Times New Roman']
    
    try:
        if auto_detect:
            # 檢測可用字體
            available_fonts = set([f.name for f in matplotlib.font_manager.fontManager.ttflist])
            
            # 找到第一個可用的字體
            selected_fonts = []
            for font in fonts:
                if font in available_fonts:
                    selected_fonts.append(font)
                    if verbose:
                        logger.info(f"找到可用字體: {font}")
                elif verbose:
                    logger.warning(f"字體不可用: {font}")
            
            if selected_fonts:
                plt.rcParams['font.sans-serif'] = selected_fonts
            else:
                logger.warning("未找到指定字體，使用預設字體")
                plt.rcParams['font.sans-serif'] = fonts
        else:
            plt.rcParams['font.sans-serif'] = fonts
        
        # 解決負號顯示問題
        plt.rcParams['axes.unicode_minus'] = False
        
        if verbose:
            logger.info(f"字體設置完成: {plt.rcParams['font.sans-serif']}")
            logger.info("負號顯示問題已修復")
        
        return True
        
    except Exception as e:
        logger.error(f"字體設置失敗: {e}")
        return False

def reset_font_config():
    """重置字體配置為預設值"""
    if not HAS_MATPLOTLIB:
        logger.warning("matplotlib 不可用，跳過字體重置")
        return
    plt.rcParams.update(plt.rcParamsDefault)
    logger.info("字體配置已重置為預設值")

def get_available_chinese_fonts() -> list[str]:
    """
    獲取系統中可用的中文字體
    
    Returns:
        List[str]: 可用的中文字體列表
    """
    if not HAS_MATPLOTLIB:
        return []
        
    chinese_fonts = []
    chinese_keywords = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 
                       'FangSong', 'NSimSun', 'Microsoft JhengHei']
    
    available_fonts = [f.name for f in matplotlib.font_manager.fontManager.ttflist]
    
    for font in available_fonts:
        if any(keyword in font for keyword in chinese_keywords):
            chinese_fonts.append(font)
    
    return sorted(list(set(chinese_fonts)))

def test_font_display():
    """測試字體顯示效果"""
    if not HAS_MATPLOTLIB:
        print("matplotlib 不可用，無法測試字體顯示")
        return
        
    import numpy as np
    
    plt.figure(figsize=(10, 6))
    
    # 測試中文顯示
    x = np.linspace(0, 2*np.pi, 100)
    y = np.sin(x)
    
    plt.plot(x, y, label='正弦函數')
    plt.plot(x, -y, label='負正弦函數')  # 測試負號
    
    plt.title('中文字體測試圖表')
    plt.xlabel('X軸 (弧度)')
    plt.ylabel('Y軸 (振幅)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 添加一些中文註釋
    plt.text(np.pi, 0.5, '這是中文測試文字', 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    plt.tight_layout()
    plt.show()

# 自動初始化（當模塊被導入時）
def auto_setup():
    """自動設置字體配置"""
    return setup_chinese_font(verbose=False)

# 模塊級別的便捷函數
def quick_setup():
    """快速設置（最常用的配置）- 英文優先設置避免中文顯示問題"""
    if not HAS_MATPLOTLIB:
        return
    # 設置繪圖樣式為英文，避免中文顯示問題
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'Times New Roman']
    plt.rcParams['axes.unicode_minus'] = False

# 當模塊被導入時自動執行
if __name__ != "__main__":
    # 只有在作為模塊導入時才自動設置
    quick_setup()

if __name__ == "__main__":
    # 如果直接運行此腳本，則執行測試
    print("=== matplotlib 中文字體配置模塊 ===")
    print(f"可用的中文字體: {get_available_chinese_fonts()}")
    
    success = setup_chinese_font(verbose=True)
    if success:
        print("字體配置成功！")
        test_font_display()
    else:
        print("字體配置失敗！")
