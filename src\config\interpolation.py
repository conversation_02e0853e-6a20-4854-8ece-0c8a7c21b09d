"""
插值設定與演算法枚舉模組

本模組定義了系統中所有與影像插值相關的設定類別、演算法枚舉和預設配置。
它提供了一個豐富的插值方法庫，並允許透過配置類別進行精細控制，以平衡影像品質與處理性能。
"""

from dataclasses import dataclass
from enum import Enum
from typing import Optional


class InterpolationMethod(Enum):
    """
    影像插值方法枚舉。

    涵蓋了從基礎到高級的超過38種插值演算法，每種演算法在速度、品質、銳利度、平滑度等方面有不同特性。
    """

    # --- 基本方法 (速度快，品質尚可) ---
    NEAREST = "nearest"  # 最近鄰插值：速度最快，但會產生明顯的鋸齒效應。
    LINEAR = "linear"  # 線性插值：速度快，效果比最近鄰平滑。
    BILINEAR = "bilinear"  # 雙線性插值：在2D影像中常用，是LINEAR的擴展，效果平滑。
    BICUBIC = "bicubic"  # 雙三次插值：在保持細節和平滑之間取得了良好平衡，是常用的高品質選項。

    # --- 高階樣條 (更平滑的曲線) ---
    QUADRATIC = "quadratic"  # 二次樣條插值
    CUBIC = "cubic"  # 三次樣條插值
    QUARTIC = "quartic"  # 四次樣條插值
    QUINTIC = "quintic"  # 五次樣條插值
    BSPLINE = "bspline"  # B-樣條插值：產生非常平滑的結果。

    # --- Lanczos 系列 (高品質，適合縮小影像) ---
    LANCZOS2 = "lanczos2"  # Lanczos-2：品質較高，能較好地保留邊緣細節。
    LANCZOS3 = "lanczos3"  # Lanczos-3：常用的高品質插值，銳利度與細節保留俱佳。
    LANCZOS4 = "lanczos4"  # Lanczos-4：更高品質，計算成本也更高。
    LANCZOS8 = "lanczos8"  # Lanczos-8：極高品質，通常用於專業級影像處理。

    # --- 現代濾波器 (在銳利度與振鈴效應間取捨) ---
    MITCHELL = "mitchell"  # Mitchell-Netravali 濾波器：在模糊與鋸齒之間取得良好平衡。
    CATMULL_ROM = "catmull_rom"  # Catmull-Rom 樣條：能產生比BICUBIC更銳利的結果。
    GAUSSIAN = "gaussian"  # 高斯濾波器：結果非常平滑，常用於去噪。
    SINC = "sinc"  # Sinc 濾波器：理論上是理想的重建濾波器，但計算成本高。

    # --- 窗函數 (用於訊號處理，也可用於插值) ---
    KAISER = "kaiser"  # Kaiser 窗函數
    BLACKMAN = "blackman"  # Blackman 窗函數
    HAMMING = "hamming"  # Hamming 窗函數
    HANN = "hann"  # Hann 窗函數

    # --- 其他高級方法 ---
    HERMITE = "hermite"  # Hermite 插值
    TRIANGULAR = "triangular"  # 三角濾波器，效果類似線性插值。
    BOX = "box"  # 盒狀濾波器，效果類似最近鄰插值。
    WELCH = "welch"  # Welch 窗函數
    BARTLETT = "bartlett"  # Bartlett 窗函數
    TUKEY = "tukey"  # Tukey 窗函數
    BLACKMAN_Harris = "blackman_harris"  # Blackman-Harris 窗函數
    NUTTALL = "nuttall"  # Nuttall 窗函數
    SPLINE36 = "spline36"  # 36-tap 樣條濾波器
    SPLINE64 = "spline64"  # 64-tap 樣條濾波器
    ANISOTROPIC = "anisotropic"  # 非等向性濾波，能更好地保護邊緣。


class MultipleInterpolationStrategy(Enum):
    """
    當需要組合多種插值方法時，所採用的策略枚舉。
    """
    SEQUENTIAL = "sequential"  # 按通道輪流使用不同方法 (例如 R用A法, G用B法, B用C法)。
    ALTERNATING = "alternating"  # 奇偶像素行/列交替使用不同方法。
    DOUBLE_PASS = "double_pass"  # 雙重處理，例如先用一種方法放大，再用另一種方法銳化。
    MIXED = "mixed"  # 在圖像的不同區域隨機混合使用不同方法。
    WEIGHTED = "weighted"  # 將多種方法的結果進行加權平均組合。
    ADAPTIVE = "adaptive"  # 根據圖像內容（如邊緣、平坦區域）自適應地選擇最佳方法。
    PARALLEL = "parallel"  # 並行處理不同方法後，將結果融合。


@dataclass
class InterpolationConfig:
    """
    單一插值方法的詳細設定資料類別。
    """
    method: InterpolationMethod | str  # 使用的插值方法。
    use_gpu: bool = True  # 是否優先使用GPU進行加速，若可用。
    cache_enabled: bool = True  # 是否啟用插值結果快取。
    parallel_threads: int = 0  # 平行處理執行緒數 (0 表示自動偵測最佳數量)。
    memory_limit_mb: int = 2048  # 此操作的記憶體使用上限（MB）。
    precision: str = "float32"  # 計算精度，可選 'float16', 'float32', 'float64'。
    enable_streaming: bool = True  # 對於大圖像，是否啟用流式處理以節省記憶體。
    auto_optimize: bool = True  # 是否允許系統根據硬體自動優化演算法選擇。

    # --- 特定插值方法的專用參數 ---
    lanczos_a: float = 3.0  # Lanczos 濾波器的 'a' 參數，控制窗口大小。
    gaussian_sigma: float = 1.0  # 高斯濾波器的標準差 (sigma)。
    kaiser_beta: float = 8.0  # Kaiser 窗的 'beta' 參數。
    mitchell_b: float = 1 / 3  # Mitchell 濾波器的 'B' 參數。
    mitchell_c: float = 1 / 3  # Mitchell 濾波器的 'C' 參數。

    # --- 性能優化相關參數 ---
    chunk_size_mb: int = 256  # 流式處理時，每個處理塊的大小（MB）。
    prefetch_enabled: bool = True  # 是否啟用資料預取以優化IO性能。
    adaptive_quality: bool = True  # 是否啟用自適應品質調整，在處理速度和品質間動態平衡。
    benchmark_enabled: bool = True  # 是否啟用性能基準測試來評估不同設定的效率。


@dataclass
class MultipleInterpolationConfig:
    """
    多重插值策略的設定資料類別。
    """
    strategy: MultipleInterpolationStrategy  # 採用的組合策略。
    methods: list[InterpolationMethod | str]  # 要組合的多種插值方法列表。
    weights: Optional[list[float]] = None  # 當策略為 'WEIGHTED' 時，對應每種方法的權重。
    blend_mode: str = "linear"  # 結果的混合模式，可選 'linear', 'gaussian', 'adaptive'。


# 提供一個預設的、平衡的單一插值設定。
DEFAULT_INTERPOLATION_CONFIG = InterpolationConfig(
    method=InterpolationMethod.BICUBIC,  # 預設使用高品質且常用的BICUBIC方法。
    use_gpu=True,
    cache_enabled=True,
    parallel_threads=0,
    memory_limit_mb=2048,
    precision="float32",
    enable_streaming=True,
    auto_optimize=True,
)

# 提供一個預設的多重插值設定範例。
DEFAULT_MULTIPLE_INTERPOLATION_CONFIG = MultipleInterpolationConfig(
    strategy=MultipleInterpolationStrategy.WEIGHTED,  # 預設使用加權策略。
    methods=[InterpolationMethod.BICUBIC, InterpolationMethod.LANCZOS3],  # 混合兩種高品質方法。
    weights=[0.7, 0.3],  # BICUBIC佔70%權重，LANCZOS3佔30%。
    blend_mode="linear",
)


def main():
    """
    測試 interpolation.py 模組的主要功能
    """
    print("=" * 60)
    print("測試 config.interpolation 模組")
    print("=" * 60)

    # 測試插值方法枚舉
    print("\n1. 測試插值方法枚舉:")
    print(f"   基本方法:")
    print(f"     NEAREST = {InterpolationMethod.NEAREST.value}")
    print(f"     BILINEAR = {InterpolationMethod.BILINEAR.value}")
    print(f"     BICUBIC = {InterpolationMethod.BICUBIC.value}")

    print(f"   高階方法:")
    print(f"     LANCZOS3 = {InterpolationMethod.LANCZOS3.value}")
    print(f"     MITCHELL = {InterpolationMethod.MITCHELL.value}")
    print(f"     GAUSSIAN = {InterpolationMethod.GAUSSIAN.value}")

    # 測試插值配置
    print("\n2. 測試插值配置:")
    config = InterpolationConfig(method=InterpolationMethod.BICUBIC)
    print(f"   測試配置:")
    print(f"     method = {config.method}")
    print(f"     use_gpu = {config.use_gpu}")
    print(f"     cache_enabled = {config.cache_enabled}")
    print(f"     precision = {config.precision}")

    # 測試多重插值策略
    print("\n3. 測試多重插值策略:")
    print(f"   WEIGHTED = {MultipleInterpolationStrategy.WEIGHTED.value}")
    print(f"   SEQUENTIAL = {MultipleInterpolationStrategy.SEQUENTIAL.value}")
    print(f"   ADAPTIVE = {MultipleInterpolationStrategy.ADAPTIVE.value}")

    # 測試多重插值配置
    print("\n4. 測試多重插值配置:")
    multi_config = MultipleInterpolationConfig(
        strategy=MultipleInterpolationStrategy.WEIGHTED,
        methods=[InterpolationMethod.BICUBIC, InterpolationMethod.LANCZOS3]
    )
    print(f"   測試多重配置:")
    print(f"     strategy = {multi_config.strategy}")
    print(f"     methods = {[m.value for m in multi_config.methods]}")
    print(f"     weights = {multi_config.weights}")

    # 測試預設配置
    print("\n5. 測試預設配置:")
    print(f"   預設插值配置: {DEFAULT_INTERPOLATION_CONFIG.method.value}")
    print(f"   預設多重插值配置: {DEFAULT_MULTIPLE_INTERPOLATION_CONFIG.strategy.value}")

    # 測試所有插值方法
    print("\n6. 所有可用的插值方法:")
    methods = list(InterpolationMethod)
    print(f"   總共 {len(methods)} 種方法:")
    for i, method in enumerate(methods, 1):
        print(f"     {i:2d}. {method.value}")

    print("\n✅ config.interpolation 模組測試完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
