"""
座標轉換核心模組 (core.coordinate.core)

提供一個高效能的座標轉換器 `CoordinateTransformer`，用於在不同座標系統之間進行轉換。
支援的座標系統包括：
- 全景圖像素座標 (u, v)
- 球面座標 (經度 longitude, 緯度 latitude)
- 3D 笛卡爾座標 (x, y, z)
- 立方體面座標 (face_name, face_x, face_y)

此模組利用 `functools.lru_cache` 對計算成本高的格點生成函式進行快取，
並呼叫 `numba_kernels` 中使用 Numba JIT 編譯的函式來加速核心計算，以達到最佳性能。
"""

import os
# 只在需要時設置 Numba 禁用
if os.environ.get("DISABLE_NUMBA") != "0":
    os.environ["DISABLE_NUMBA"] = "1"

from functools import lru_cache
from typing import Any, Optional

import numpy as np
from numpy.typing import NDArray

from config.constants import _CACHE_SIZE

# 使用延遲載入來避免循環依賴
def _get_logger():
    """延遲載入 logger"""
    try:
        from log_utils.factory import get_logger
        return get_logger(__name__)
    except ImportError:
        import logging
        return logging.getLogger(__name__)

# 延遲初始化 logger
logger = None

import os
# 只在需要時設置 Numba 禁用
if os.environ.get("DISABLE_NUMBA") != "0":
    os.environ["DISABLE_NUMBA"] = "1"

from .numba_kernels import (_apply_top_bottom_masks,
                            _create_initial_face_pattern, _fill_cube_faces,
                            compute_cube_coords_fast, coor_to_uv, uv_to_coor,
                            uv_to_xyz, xyz_to_uv)
# 在測試環境中禁用性能監控以避免導入問題
if os.environ.get("TESTING") == "1":
    HAS_PERFORMANCE_MONITOR = False
else:
    # 嘗試匯入性能監控工具
    try:
        from utils.unified_performance_monitor import UnifiedPerformanceMonitor
        HAS_PERFORMANCE_MONITOR = True
    except ImportError:
        HAS_PERFORMANCE_MONITOR = False

# logger 將在需要時初始化


class CoordinateTransformer:
    """
    座標轉換器類別

    封裝了所有與座標系統轉換相關的邏輯。
    提供一個統一的介面來處理從 2D 全景圖到 3D 空間，再到立方體面的複雜轉換。
    內建性能統計和可選的性能監控功能。
    """

    def __init__(self):
        """
        初始化座標轉換器。
        - 設定立方體面名稱的標準順序。
        - 初始化用於追蹤性能的統計字典。
        - 如果 `UnifiedPerformanceMonitor` 可用，則建立一個實例以進行性能監控。
        """
        self.face_names = ["F", "R", "B", "L", "U", "D"]
        self._stats = {
            "coordinate_transforms": 0,
            "cache_hits": 0,
            "total_processing_time": 0.0,
        }
        self.performance_monitor = (
            UnifiedPerformanceMonitor("CoordinateTransformer") if HAS_PERFORMANCE_MONITOR else None
        )

    @staticmethod
    @lru_cache(maxsize=_CACHE_SIZE)
    def generate_cube_coordinates(face_w: int) -> NDArray:
        """
        生成水平排列的立方體面 3D 座標陣列。

        此函式會預先計算所有六個面的 3D 座標，並將其排列在一個單一的 Numpy 陣列中。
        使用 `@lru_cache` 裝飾器快取結果，以避免對相同 `face_w` 的重複密集計算。

        :param face_w: 每個立方體面的寬度（像素）。
        :return: 一個形狀為 `(face_w, face_w * 6, 3)` 的唯讀 Numpy 陣列，包含了所有立方體面的 3D 座標。
        :raises ValueError: 如果 `face_w` 不是正整數。
        """
        if face_w <= 0:
            raise ValueError("face_w 必須為正整數")
        
        out = np.empty((face_w, face_w * 6, 3), np.float32)
        rng = np.linspace(-0.5, 0.5, num=face_w, dtype=np.float32)
        x, y = np.meshgrid(rng, -rng)
        
        # 呼叫 Numba 加速的核心函式來高效填充立方體面的座標
        _fill_cube_faces(out, x, y, np.flip(x, 1), np.flip(y, 0), face_w)
        
        out.setflags(write=False)  # 將陣列設為唯讀以防止後續意外修改
        return out

    @staticmethod
    @lru_cache(maxsize=_CACHE_SIZE)
    def generate_equirect_grid(h: int, w: int) -> tuple[NDArray, NDArray]:
        """
        生成全景圖的經緯度格點（UV 格點）。

        :param h: 全景圖的高度。
        :param w: 全景圖的寬度。
        :return: 一個包含經度（uu）和緯度（vv）格點的元組，均為唯讀 Numpy 陣列。
        """
        u = np.linspace(-np.pi, np.pi, num=w, dtype=np.float32)
        v = np.linspace(np.pi / 2, -np.pi / 2, num=h, dtype=np.float32)
        uu, vv = np.meshgrid(u, v)
        uu.setflags(write=False)
        vv.setflags(write=False)
        return uu, vv

    @staticmethod
    @lru_cache(maxsize=_CACHE_SIZE)
    def compute_face_type(h: int, w: int) -> NDArray:
        """
        計算全景圖中每個像素所對應的立方體面類型。

        :param h: 全景圖的高度。
        :param w: 全景圖的寬度，必須是 4 的倍數以確保幾何正確性。
        :return: 一個形狀為 `(h, w)` 的唯讀 Numpy 陣列，其值對應立方體的面索引（0-5）。
        :raises ValueError: 如果寬度不是 4 的倍數。
        """
        if w % 4:
            raise ValueError(f"寬度必須是 4 的倍數，當前為 {w}")
        
        face_type = _create_initial_face_pattern(h, w)
        _apply_top_bottom_masks(face_type, h, w)
        face_type.setflags(write=False)
        return face_type

    @staticmethod
    def compute_cube_coordinates(
        u: NDArray, v: NDArray, face_type: NDArray, face_w: int
    ) -> tuple[NDArray, NDArray]:
        """
        根據球面座標 (u, v) 和面類型，快速計算在立方體面上的 2D 座標。

        :param u: 經度陣列。
        :param v: 緯度陣列。
        :param face_type: 面類型陣列。
        :param face_w: 立方體面的寬度。
        :return: 一個包含立方體面 x, y 座標的元組。
        """
        return compute_cube_coords_fast(u, v, face_type, face_w)

    def panorama_to_sphere(
        self, pano_u: float, pano_v: float, pano_width: int, pano_height: int
    ) -> tuple[float, float]:
        """將單一全景圖像素座標 (pano_u, pano_v) 轉換為球面座標（經度, 緯度）。"""
        u_arr, v_arr = coor_to_uv(np.array([pano_u]), np.array([pano_v]), pano_height, pano_width)
        return u_arr[0], v_arr[0]

    def sphere_to_panorama(
        self, longitude: float, latitude: float, pano_width: int, pano_height: int
    ) -> tuple[float, float]:
        """將單一球面座標（經度, 緯度）轉換為全景圖像素座標。"""
        x_arr, y_arr = uv_to_coor(np.array([longitude]), np.array([latitude]), pano_height, pano_width)
        return x_arr[0], y_arr[0]

    def sphere_to_cartesian(
        self, longitude: float, latitude: float
    ) -> tuple[float, float, float]:
        """將單一球面座標（經度, 緯度）轉換為 3D 笛卡爾座標。"""
        xyz_arr = uv_to_xyz(np.array([[longitude]], dtype=np.float32), np.array([[latitude]], dtype=np.float32))
        return xyz_arr[0, 0, 0], xyz_arr[0, 0, 1], xyz_arr[0, 0, 2]

    def cartesian_to_sphere(self, x: float, y: float, z: float) -> tuple[float, float]:
        """將單一 3D 笛卡爾座標轉換為球面座標（經度, 緯度）。"""
        u_arr, v_arr = xyz_to_uv(np.array([[[x, y, z]]], dtype=np.float32))
        return u_arr[0, 0], v_arr[0, 0]

    def cartesian_to_cube_face(
        self, x: float, y: float, z: float, cube_size: int
    ) -> tuple[Optional[str], float, float]:
        """將 3D 笛卡爾座標轉換為其所在的立方體面及該面上的 2D 座標。"""
        abs_x, abs_y, abs_z = abs(x), abs(y), abs(z)
        
        # 根據絕對值最大的分量來確定點在哪一個面上
        if abs_x >= abs_y and abs_x >= abs_z:
            face_name, face_x, face_y = ("R", -z / x, y / x) if x > 0 else ("L", z / x, y / x)
        elif abs_y >= abs_x and abs_y >= abs_z:
            face_name, face_x, face_y = ("U", x / y, -z / y) if y > 0 else ("D", x / -y, z / -y)
        else:  # abs_z is the largest
            face_name, face_x, face_y = ("F", x / z, y / z) if z > 0 else ("B", -x / z, y / z)

        # 將 [-1, 1] 範圍的標準化座標轉換為 [0, cube_size] 的像素座標
        pixel_x = (face_x + 1) / 2 * cube_size
        pixel_y = (face_y + 1) / 2 * cube_size
        
        # 確保計算出的點在立方體面邊界內
        if 0 <= pixel_x < cube_size and 0 <= pixel_y < cube_size:
            return face_name, pixel_x, pixel_y
        return None, 0.0, 0.0

    def cube_face_to_cartesian(
        self, face_name: str, face_x: float, face_y: float, cube_size: int
    ) -> tuple[float, float, float]:
        """將立方體面上的 2D 像素座標轉換回 3D 笛卡爾座標。"""
        # 將像素座標轉換回 [-1, 1] 的標準化範圍
        norm_x = (face_x / cube_size) * 2 - 1
        norm_y = (face_y / cube_size) * 2 - 1
        
        dispatch = {
            "F": (norm_x, norm_y, 1),
            "B": (-norm_x, norm_y, -1),
            "R": (1, norm_y, -norm_x),
            "L": (-1, norm_y, norm_x),
            "U": (norm_x, 1, -norm_y),
            "D": (norm_x, -1, norm_y),
        }
        
        x, y, z = dispatch.get(face_name, (0, 0, 0))
        if (x, y, z) == (0, 0, 0):
            raise ValueError(f"未知的面名稱: {face_name}")

        # 將向量歸一化，使其長度為 1，成為單位球體上的一個點
        norm = np.sqrt(x * x + y * y + z * z)
        return (x / norm, y / norm, z / norm) if norm != 0 else (0.0, 0.0, 0.0)

    def panorama_point_to_cube_face(
        self, pano_u: float, pano_v: float, pano_width: int, pano_height: int, cube_size: int
    ) -> tuple[Optional[str], float, float]:
        """
        端到端轉換：將全景圖上的一個點直接轉換到其對應的立方體面上的點。
        這是一個複合轉換，結合了 panorama → sphere → cartesian → cube_face。
        """
        longitude, latitude = self.panorama_to_sphere(pano_u, pano_v, pano_width, pano_height)
        x, y, z = self.sphere_to_cartesian(longitude, latitude)
        return self.cartesian_to_cube_face(x, y, z, cube_size)

    def cube_face_point_to_panorama(
        self, face_name: str, face_x: float, face_y: float, cube_size: int, pano_width: int, pano_height: int
    ) -> tuple[float, float]:
        """
        端到端轉換：將立方體面上的一個點直接轉換回其在全景圖上的對應點。
        這是一個複合轉換，結合了 cube_face → cartesian → sphere → panorama。
        """
        x, y, z = self.cube_face_to_cartesian(face_name, face_x, face_y, cube_size)
        longitude, latitude = self.cartesian_to_sphere(x, y, z)
        return self.sphere_to_panorama(longitude, latitude, pano_width, pano_height)

    def get_statistics(self) -> dict[str, Any]:
        """獲取座標轉換的性能統計數據。"""
        stats = self._stats.copy()
        total_ops = stats["coordinate_transforms"]
        stats["average_processing_time_ms"] = (
            (stats["total_processing_time"] * 1000) / total_ops if total_ops > 0 else 0
        )
        return stats

    def reset_statistics(self):
        """重置性能統計數據。"""
        self._stats = {
            "coordinate_transforms": 0,
            "cache_hits": 0,
            "total_processing_time": 0.0,
        }
