"""
插值核函數套件 (Interpolation Kernels Package)

此 `__init__.py` 檔案的目的是作為插值核函數的統一匯出點。
它從 `basic` 和 `advanced` 子模組中匯集了所有 CPU 版本的插值核函數，
為上層的 `interpolator` 模組提供一個清晰、單一的介面來存取這些底層演算法。
這種設計模式有助於模組化管理，並簡化了依賴關係。
"""

# 從 .advanced 模組匯入所有高級插值核函數。
# 這些核函數通常提供更高的影像品質，但計算成本也相對較高。
from .advanced import (_bartlett_kernel, _bartlett_kernel_2,
                       _blackman_harris_kernel, _blackman_harris_kernel_2,
                       _catmull_rom_kernel, _gaussian_kernel,
                       _gaussian_kernel_default, _hermite_kernel,
                       _kaiser_kernel, _kaiser_kernel_default, _lanczos_kernel,
                       _lanczos_kernel_2, _lanczos_kernel_3, _lanczos_kernel_4,
                       _lanczos_kernel_8, _mitchell_kernel,
                       _mitchell_kernel_default, _nuttall_kernel,
                       _nuttall_kernel_2, _sinc_kernel, _spline36_kernel,
                       _spline64_kernel, _tukey_kernel, _tukey_kernel_default,
                       _welch_kernel, _welch_kernel_2)

# 從 .basic 模組匯入所有基礎插值核函數。
# 這些核函數速度較快，適用於對性能要求較高的場景。
from .basic import (_bilinear_core, _box_kernel, _cubic_convolution,
                    _triangular_kernel)

# 備註：`experimental.py` 檔案被保留用於未來開發實驗性的新核函數。
# 由於目前為空，因此在此處不進行任何匯出操作。

# 定義公開 API
__all__ = [
    # 高級插值核函數
    "_bartlett_kernel",
    "_bartlett_kernel_2", 
    "_blackman_harris_kernel",
    "_blackman_harris_kernel_2",
    "_catmull_rom_kernel",
    "_gaussian_kernel",
    "_gaussian_kernel_default",
    "_hermite_kernel",
    "_kaiser_kernel",
    "_kaiser_kernel_default",
    "_lanczos_kernel",
    "_lanczos_kernel_2",
    "_lanczos_kernel_3",
    "_lanczos_kernel_4",
    "_lanczos_kernel_8",
    "_mitchell_kernel",
    "_mitchell_kernel_default",
    "_nuttall_kernel",
    "_nuttall_kernel_2",
    "_sinc_kernel",
    "_spline36_kernel",
    "_spline64_kernel",
    "_tukey_kernel",
    "_tukey_kernel_default",
    "_welch_kernel",
    "_welch_kernel_2",
    # 基礎插值核函數
    "_bilinear_core",
    "_box_kernel", 
    "_cubic_convolution",
    "_triangular_kernel",
]
