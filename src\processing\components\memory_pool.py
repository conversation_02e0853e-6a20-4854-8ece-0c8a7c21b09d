#!/usr/bin/env python3
"""
記憶體池管理模組
Memory Pool Management Module

提供統一記憶體池管理，避免OOM錯誤，實現智能記憶體分配和清理機制。
Provides unified memory pool management to avoid OOM errors and implement intelligent memory allocation and cleanup mechanisms.

Author: <PERSON> Code Assistant
Date: 2025-01-19
"""

# 1. 標準庫
import gc
import logging
import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass, field
from typing import Any, ContextManager, Generator

# 2. 第三方庫
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False


@dataclass
class MemoryUsage:
    """記憶體使用統計"""
    allocated_mb: float = 0.0
    peak_mb: float = 0.0
    available_mb: float = 0.0
    total_mb: float = 0.0
    usage_percent: float = 0.0


@dataclass
class AllocationRecord:
    """記憶體分配記錄"""
    operation_name: str
    size_mb: float
    allocated_time: float = field(default_factory=time.time)
    is_active: bool = True


class MemoryPool:
    """
    統一記憶體池管理，避免 OOM。
    
    特性:
    - 智能記憶體分配
    - 自動垃圾回收
    - 記憶體使用監控
    - 分配記錄追踪
    """
    
    def __init__(self, max_memory_percent: float = 0.8):
        """
        初始化記憶體池
        
        Args:
            max_memory_percent: 最大記憶體使用百分比（0.0-1.0）
        """
        self.max_memory_percent = max_memory_percent
        self._get_system_memory()
        
        self.allocated_memory = 0.0
        self._allocations: dict[str, AllocationRecord] = {}
        self._lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # 記憶體使用統計
        self._usage_history: list[MemoryUsage] = []
        self._max_history_length = 100
        
    def _get_system_memory(self):
        """獲取系統記憶體信息"""
        if HAS_PSUTIL:
            memory = psutil.virtual_memory()
            self.total_memory_mb = memory.total / (1024 ** 2)
            self.max_memory_mb = self.total_memory_mb * self.max_memory_percent
        else:
            # 後備方案：假設 8GB 系統記憶體
            self.total_memory_mb = 8 * 1024
            self.max_memory_mb = self.total_memory_mb * self.max_memory_percent
            self.logger.warning("psutil 不可用，使用默認記憶體值")
            
    @contextmanager
    def allocate(self, size_mb: float, operation_name: str) -> ContextManager[bool]:
        """
        安全的記憶體分配上下文管理器
        
        Args:
            size_mb: 請求分配的記憶體大小（MB）
            operation_name: 操作名稱，用於追踪
            
        Yields:
            bool: 是否成功分配記憶體
        """
        allocated = False
        
        with self._lock:
            # 檢查是否有足夠記憶體
            if self._can_allocate(size_mb):
                allocated = True
                self.allocated_memory += size_mb
                
                # 記錄分配
                allocation_id = f"{operation_name}_{int(time.time() * 1000)}"
                self._allocations[allocation_id] = AllocationRecord(
                    operation_name=operation_name,
                    size_mb=size_mb
                )
                
                self.logger.debug(f"分配記憶體: {size_mb:.1f}MB 給 {operation_name}")
                
        try:
            if allocated:
                # 更新使用統計
                self._update_usage_stats()
                yield True
            else:
                self.logger.warning(f"記憶體分配失敗: {operation_name} 需要 {size_mb:.1f}MB")
                # 嘗試清理記憶體
                self._cleanup_memory()
                
                # 再次檢查
                with self._lock:
                    if self._can_allocate(size_mb):
                        allocated = True
                        self.allocated_memory += size_mb
                        allocation_id = f"{operation_name}_{int(time.time() * 1000)}"
                        self._allocations[allocation_id] = AllocationRecord(
                            operation_name=operation_name,
                            size_mb=size_mb
                        )
                        self.logger.info(f"清理後成功分配: {size_mb:.1f}MB 給 {operation_name}")
                        yield True
                    else:
                        yield False
                        
        finally:
            if allocated:
                with self._lock:
                    self.allocated_memory -= size_mb
                    # 標記分配記錄為非活動狀態
                    for alloc_id, record in self._allocations.items():
                        if (record.operation_name == operation_name and 
                            record.size_mb == size_mb and record.is_active):
                            record.is_active = False
                            break
                            
                self.logger.debug(f"釋放記憶體: {size_mb:.1f}MB 從 {operation_name}")
    
    def _can_allocate(self, size_mb: float) -> bool:
        """檢查是否可以分配指定大小的記憶體"""
        return (self.allocated_memory + size_mb) <= self.max_memory_mb
    
    def _cleanup_memory(self):
        """觸發記憶體清理"""
        self.logger.info("觸發記憶體清理...")
        
        # 強制垃圾回收
        gc.collect()
        
        # 清理舊的分配記錄
        current_time = time.time()
        expired_records = []
        
        for alloc_id, record in self._allocations.items():
            if (not record.is_active and 
                current_time - record.allocated_time > 300):  # 5分鐘後清理
                expired_records.append(alloc_id)
        
        for alloc_id in expired_records:
            del self._allocations[alloc_id]
            
        self.logger.info(f"清理完成，移除了 {len(expired_records)} 個過期記錄")
    
    def _update_usage_stats(self):
        """更新記憶體使用統計"""
        current_usage = self.get_current_usage()
        
        # 限制歷史記錄長度
        if len(self._usage_history) >= self._max_history_length:
            self._usage_history.pop(0)
            
        self._usage_history.append(current_usage)
    
    def get_current_usage(self) -> MemoryUsage:
        """獲取當前記憶體使用情況"""
        usage = MemoryUsage()
        usage.allocated_mb = self.allocated_memory
        
        if HAS_PSUTIL:
            memory = psutil.virtual_memory()
            usage.available_mb = memory.available / (1024 ** 2)
            usage.total_mb = memory.total / (1024 ** 2)
            usage.usage_percent = memory.percent
        else:
            usage.available_mb = self.max_memory_mb - self.allocated_memory
            usage.total_mb = self.total_memory_mb
            usage.usage_percent = (self.allocated_memory / self.total_memory_mb) * 100
        
        # 計算峰值
        if self._usage_history:
            usage.peak_mb = max(u.allocated_mb for u in self._usage_history)
        else:
            usage.peak_mb = usage.allocated_mb
            
        return usage
    
    def get_allocation_summary(self) -> dict[str, Any]:
        """獲取分配摘要"""
        active_allocations = [r for r in self._allocations.values() if r.is_active]
        
        # 按操作名稱分組統計
        operation_stats: dict[str, dict[str, Any]] = {}
        for record in active_allocations:
            op_name = record.operation_name
            if op_name not in operation_stats:
                operation_stats[op_name] = {
                    "count": 0,
                    "total_mb": 0.0,
                    "avg_mb": 0.0
                }
            
            operation_stats[op_name]["count"] += 1
            operation_stats[op_name]["total_mb"] += record.size_mb
        
        # 計算平均值
        for stats in operation_stats.values():
            if stats["count"] > 0:
                stats["avg_mb"] = stats["total_mb"] / stats["count"]
        
        return {
            "total_active_allocations": len(active_allocations),
            "total_allocated_mb": sum(r.size_mb for r in active_allocations),
            "operation_breakdown": operation_stats,
            "pool_utilization": (self.allocated_memory / self.max_memory_mb) * 100
        }
    
    def force_cleanup(self):
        """強制清理所有資源"""
        with self._lock:
            self.allocated_memory = 0.0
            self._allocations.clear()
            
        gc.collect()
        self.logger.info("強制清理記憶體池完成")


# 全局記憶體池實例
_global_memory_pool: MemoryPool | None = None
_pool_lock = threading.Lock()


def get_global_memory_pool() -> MemoryPool:
    """獲取全局記憶體池實例（單例模式）"""
    global _global_memory_pool
    
    if _global_memory_pool is None:
        with _pool_lock:
            if _global_memory_pool is None:
                _global_memory_pool = MemoryPool()
                
    return _global_memory_pool


def allocate_memory(size_mb: float, operation_name: str) -> ContextManager[bool]:
    """
    便捷的記憶體分配函數
    
    Args:
        size_mb: 記憶體大小（MB）
        operation_name: 操作名稱
        
    Returns:
        記憶體分配上下文管理器
    """
    pool = get_global_memory_pool()
    return pool.allocate(size_mb, operation_name)