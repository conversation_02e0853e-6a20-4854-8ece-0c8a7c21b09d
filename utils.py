# utils.py
import os
import cv2
import numpy as np
from numpy.typing import NDArray
from functools import lru_cache
import logging
from typing import Optional, Tuple, TypeVar, Literal, Any
# from typing import Any, Literal, Optional, TypeVar, Union, Literal, Union, overload
from enum import IntEnum
# from typing import Any, Literal, Optional, TypeVar, Literal, Union, overload, List, Tuple, Dict
from collections.abc import Sequence


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


DType = TypeVar("DType", bound=np.generic, covariant=True)
_CACHE_SIZE = 8

class Dim(IntEnum):
    X = 0
    Y = 1
    Z = 2
    
class Face(IntEnum):
    """Face type indexing for numpy vectorization."""

    FRONT = 0
    RIGHT = 1
    BACK = 2
    LEFT = 3
    UP = 4
    DOWN = 5



CubeFormat = Literal["horizon", "list", "dict", "dice"]
InterpolationMode = Literal[
    "nearest",
    "linear",
    "bilinear",
    "biquadratic",
    "quadratic",
    "quad",
    "bicubic",
    "cubic",
    "biquartic",
    "quartic",
    "biquintic",
    "quintic",
]

_mode_to_order = {
    "nearest": 0,
    "linear": 1,
    "bilinear": 1,
    "biquadratic": 2,
    "quadratic": 2,
    "quad": 2,
    "bicubic": 3,
    "cubic": 3,
    "biquartic": 4,
    "quartic": 4,
    "biquintic": 5,
    "quintic": 5,
}

def mode_to_order(mode: InterpolationMode) -> int:
    """Convert a human-friendly interpolation string to integer equivalent.

    Parameters
    ----------
    mode: str
        Human-friendly interpolation string.

    Returns
    -------
    The order of the spline interpolation
    """
    try:
        return _mode_to_order[mode.lower()]
    except KeyError:
        raise ValueError(f'Unknown mode "{mode}".') from None

def cube_h2list(cube_h: NDArray[DType]) -> list[NDArray[DType]]:
    """Split an image into a list of 6 faces."""
    if cube_h.shape[0] * 6 != cube_h.shape[1]:
        raise ValueError("Cubemap's width must by 6x its height.")
    return np.split(cube_h, 6, axis=1)

def cube_h2dict(cube_h: NDArray[DType]) -> dict[str, NDArray[DType]]:
    return dict(zip("FRBLUD", cube_h2list(cube_h)))

def slice_chunk(index: int, width: int, offset=0):
    start = index * width + offset
    return slice(start, start + width)

def cube_h2dice(cube_h: NDArray[DType]) -> NDArray[DType]:
    if cube_h.shape[0] * 6 != cube_h.shape[1]:
        raise ValueError("Cubemap's width must by 6x its height.")
    w = cube_h.shape[0]
    cube_dice = np.zeros((w * 3, w * 4, cube_h.shape[2]), dtype=cube_h.dtype)
    cube_list = cube_h2list(cube_h)
    # Order: F R B L U D
    #        ┌────┐
    #        │ U  │
    #   ┌────┼────┼────┬────┐
    #   │ L  │ F  │ R  │ B  │
    #   └────┼────┼────┴────┘
    #        │ D  │
    #        └────┘
    sxy = [(1, 1), (2, 1), (3, 1), (0, 1), (1, 0), (1, 2)]
    for (sx, sy), face in zip(sxy, cube_list):
        cube_dice[slice_chunk(sy, w), slice_chunk(sx, w)] = face
    return cube_dice

@lru_cache(_CACHE_SIZE)
def equirect_uvgrid(h: int, w: int) -> tuple[NDArray[np.float32], NDArray[np.float32]]:
    u = np.linspace(-np.pi, np.pi, num=w, dtype=np.float32)
    v = np.linspace(np.pi / 2, -np.pi / 2, num=h, dtype=np.float32)
    uu, vv = np.meshgrid(u, v)
    # Since we are using lru_cache, we want the return value to be immutable.
    uu.setflags(write=False)
    vv.setflags(write=False)
    return uu, vv  # pyright: ignore[reportReturnType]


def create_directory(directory: str) -> None:
    """Create a directory if it does not exist."""
    try:
        if not os.path.exists(directory):
            os.makedirs(directory)
    except OSError as e:
        logger.error(f"Failed to create directory {directory}: {e}")
        raise

def cv_imread(file_path: str) -> Optional[np.ndarray]:
    """Read an image from a file path using OpenCV."""
    try:
        img = cv2.imdecode(np.fromfile(file_path, dtype=np.uint8), -1)
        if img is None:
            raise ValueError(f"Image decoding failed for {file_path}")
        return img
    except Exception as e:
        logger.error(f"Error reading image {file_path}: {e}")
        return None

def get_optimal_workers(task_count: int) -> int:
    """Calculate optimal number of workers based on CPU cores and task count."""
    cpu_cores = os.cpu_count() or 1
    return min(cpu_cores * 2, task_count)  # I/O tasks can use more threads


def uv2coor(u: NDArray[DType], v: NDArray[DType], h: int, w: int) -> tuple[NDArray[DType], NDArray[DType]]:
    """Transform spherical(r, u, v) into equirectangular(x, y).

    Assume that u has range 2pi and v has range pi.
    The coordinate of the equirectangular is from (0.5, 0.5) to (h-0.5, w-0.5).

    Parameters
    ----------
    uv: ndarray
        An array object in shape of [..., 2].
    h: int
        Height of the equirectangular image.
    w: int
        Width of the equirectangular image.

    Returns
    -------
    out: ndarray
        An array object in shape of [..., 2].

    Notes
    -----
    In this project, e2c calls utils.uv2coor(uv, h, w) where:

        * uv is in [-pi, pi] x [-pi/2, pi/2]
        * coor_x is in [-0.5, w-0.5]
        * coor_y is in [-0.5, h-0.5]
    """
    coor_x = (u / (2 * np.pi) + 0.5) * w - 0.5  # pyright: ignore[reportOperatorIssue]
    coor_y = (-v / np.pi + 0.5) * h - 0.5  # pyright: ignore[reportOperatorIssue]
    return coor_x, coor_y


def xyz2uv(xyz: NDArray[DType]) -> tuple[NDArray[DType], NDArray[DType]]:
    """Transform cartesian (x,y,z) to spherical(r, u, v), and only outputs (u, v).

    Parameters
    ----------
    xyz: ndarray
        An array object in shape of [..., 3].

    Returns
    -------
    out: ndarray
        An array object in shape of [..., 2],
        any point i of this array is in [-pi, pi].

    Notes
    -----
    In this project, e2c calls utils.xyz2uv(xyz) where:

        * xyz is in [-0.5, 0.5] x [-0.5, 0.5] x [-0.5, 0.5]
        * u is in [-pi, pi]
        * v is in [-pi/2, pi/2]
        * any point i of output array is in [-pi, pi] x [-pi/2, pi/2].
    """
    if not np.all((xyz >= -0.5) & (xyz <= 0.5)):
        raise ValueError("xyz 應在 [-0.5, 0.5] 範圍內")
    
    x = xyz[..., 0:1]  # Keep dimensions but avoid copy
    y = xyz[..., 1:2]
    z = xyz[..., 2:3]
    u = np.arctan2(x, z)
    c = np.hypot(x, z)
    v = np.arctan2(y, c)
    return u, v


@lru_cache(_CACHE_SIZE)
def xyzcube(face_w: int) -> NDArray[np.float32]:
    """
    Return the xyz coordinates of the unit cube in [F R B L U D] format.

    Parameters
    ----------
    face_w: int
        Specify the length of each face of the cubemap.

    Returns
    -------
    out: ndarray
        An array object with dimension (face_w, face_w * 6, 3)
        which store the each face of numalized cube coordinates.
        The cube is centered at the origin so that each face k
        in out has range [-0.5, 0.5] x [-0.5, 0.5].
    """
    if face_w <= 0:
        raise ValueError("face_w 必須為正整數")
    
    out = np.empty((face_w, face_w * 6, 3), np.float32)

    # Create coordinates once and reuse
    rng = np.linspace(-0.5, 0.5, num=face_w, dtype=np.float32)
    x, y = np.meshgrid(rng, -rng)

    # Pre-compute flips
    x_flip = np.flip(x, 1)
    y_flip = np.flip(y, 0)

    def face_slice(index):
        return slice_chunk(index, face_w)

    # Front face (z = 0.5)
    out[:, face_slice(Face.FRONT), Dim.X] = x
    out[:, face_slice(Face.FRONT), Dim.Y] = y
    out[:, face_slice(Face.FRONT), Dim.Z] = 0.5

    # Right face (x = 0.5)
    out[:, face_slice(Face.RIGHT), Dim.X] = 0.5
    out[:, face_slice(Face.RIGHT), Dim.Y] = y
    out[:, face_slice(Face.RIGHT), Dim.Z] = x_flip

    # Back face (z = -0.5)
    out[:, face_slice(Face.BACK), Dim.X] = x_flip
    out[:, face_slice(Face.BACK), Dim.Y] = y
    out[:, face_slice(Face.BACK), Dim.Z] = -0.5

    # Left face (x = -0.5)
    out[:, face_slice(Face.LEFT), Dim.X] = -0.5
    out[:, face_slice(Face.LEFT), Dim.Y] = y
    out[:, face_slice(Face.LEFT), Dim.Z] = x

    # Up face (y = 0.5)
    out[:, face_slice(Face.UP), Dim.X] = x
    out[:, face_slice(Face.UP), Dim.Y] = 0.5
    out[:, face_slice(Face.UP), Dim.Z] = y_flip

    # Down face (y = -0.5)
    out[:, face_slice(Face.DOWN), Dim.X] = x
    out[:, face_slice(Face.DOWN), Dim.Y] = -0.5
    out[:, face_slice(Face.DOWN), Dim.Z] = y

    # Since we are using lru_cache, we want the return value to be immutable.
    out.setflags(write=False)
    return out



class EquirecSampler:
    def __init__(
        self,
        coor_x: NDArray,
        coor_y: NDArray,
        order: int,
    ):
        # Add 1 to the coordinates to compensate for the 1 pixel upper padding.
        coor_y = coor_y + 1  # Not done inplace on purpose.
        if cv2 and order in (0, 1, 3):
            self._use_cv2 = True
            if order == 0:
                self._order = cv2.INTER_NEAREST
                nninterpolation = True
            elif order == 1:
                self._order = cv2.INTER_LINEAR
                nninterpolation = False
            elif order == 3:
                self._order = cv2.INTER_CUBIC
                nninterpolation = False
            else:
                raise NotImplementedError

            self._coor_x, self._coor_y = cv2.convertMaps(
                coor_x,
                coor_y,
                cv2.CV_16SC2,
                nninterpolation=nninterpolation,
            )
        else:
            self._use_cv2 = False
            self._coor_x = coor_x
            self._coor_y = coor_y
            self._order = order

    def __call__(self, img: NDArray[DType]) -> NDArray[DType]:
        if img.dtype == np.float16:
            source_dtype = np.float16
        else:
            source_dtype = None

        if source_dtype:
            img = img.astype(np.float32)  # pyright: ignore

        padded = self._pad(img)
        if self._use_cv2:
            # cv2.remap can handle uint8, float32, float64
            out = cv2.remap(padded, self._coor_x, self._coor_y, interpolation=self._order)  # pyright: ignore
        else:
            # map_coordinates can handle uint8, float32, float64
            out = map_coordinates(
                padded,
                (self._coor_y, self._coor_x),
                order=self._order,
            )[..., 0]

        if source_dtype:
            out = out.astype(source_dtype)

        return out  # pyright: ignore[reportReturnType]

    def _pad(self, img: NDArray[DType]) -> NDArray[DType]:
        """Adds 1 pixel of padding above/below image."""
        w = img.shape[1]
        padded = np.pad(img, ((1, 1), (0, 0)), mode="empty")
        padded[0, :] = np.roll(img[[0]], w // 2, 1)
        padded[-1, :] = np.roll(img[[-1]], w // 2, 1)
        return padded

    @classmethod
    @lru_cache(_CACHE_SIZE)
    def from_cubemap(cls, face_w: int, h: int, w: int, order: int):
        """Construct a EquirecSampler from cubemap specs.

        Parameters
        ----------
        face_w: int
            Length of each face of the output cubemap.
        h: int
            Height of input equirec image.
        w: int
            Width of input equirec image.
        order: int
            The order of the spline interpolation. See ``scipy.ndimage.map_coordinates``.
        """
        xyz = xyzcube(face_w)
        u, v = xyz2uv(xyz)
        coor_x, coor_y = uv2coor(u, v, h, w)
        return cls(coor_x, coor_y, order=order)



class CubeFaceSampler:
    """作為一個類別排列，以便在多次影像插值中重複使用座標計算。"""

    def __init__(
        self,
        tp: NDArray,
        coor_x: NDArray,
        coor_y: NDArray,
        order: int,
        h: int,
        w: int,
    ):
        """初始化取樣器並執行預計算。

        參數
        ----------
        tp: numpy.ndarray
            (H, W) 從 ``equirect_facetype`` 生成的面類型影像
        coor_x: numpy.ndarray
            (H, W) 要取樣的 X 座標
        coor_y: numpy.ndarray
            (H, W) 要取樣的 Y 座標
        order: int
            樣條插值的階數。請參閱 ``scipy.ndimage.map_coordinates``
        h: int
            預期的輸入影像高度
        w: int
            預期的輸入影像寬度
        """
        # 增加 1 以補償 1 像素的周圍填充
        coor_x = coor_x + 1  # 故意不進行原地操作
        coor_y = coor_y + 1  # 故意不進行原地操作

        self._tp = tp
        self._h = h
        self._w = w
        if cv2 and order in (0, 1, 3):
            self._use_cv2 = True
            if order == 0:
                self._order = cv2.INTER_NEAREST
                nninterpolation = True
            elif order == 1:
                self._order = cv2.INTER_LINEAR
                nninterpolation = False
            elif order == 3:
                self._order = cv2.INTER_CUBIC
                nninterpolation = False
            else:
                raise NotImplementedError

            # +2 是來自 self._pad 的填充
            coor_y += np.multiply(tp, h + 2, dtype=np.float32)
            self._coor_x, self._coor_y = cv2.convertMaps(
                coor_x,
                coor_y,
                cv2.CV_16SC2,
                nninterpolation=nninterpolation,
            )
        else:
            self._use_cv2 = False
            self._coor_x = coor_x
            self._coor_y = coor_y
            self._order = order

    def __call__(self, cube_faces: NDArray[DType]) -> NDArray[DType]:
        """取樣立方體面。

        參數
        ----------
        cube_faces: numpy.ndarray
            (6, S, S) 立方體面

        返回
        -------
        numpy.ndarray
            (H, W) 取樣後的影像
        """
        h, w = cube_faces.shape[-2:]
        if h != self._h:
            raise ValueError(f"輸入高度 {h} 與預期高度 {self._h} 不符。")
        if w != self._w:
            raise ValueError(f"輸入寬度 {w} 與預期寬度 {self._w} 不符。")

        if cube_faces.dtype == np.float16:
            source_dtype = np.float16
        else:
            source_dtype = None

        if source_dtype:
            cube_faces = cube_faces.astype(np.float32)  # pyright: ignore

        padded = self._pad(cube_faces)
        if self._use_cv2:
            w = padded.shape[-1]
            v_img = padded.reshape(-1, w)

            # cv2.remap 可以處理 uint8, float32, float64
            out = cv2.remap(v_img, self._coor_x, self._coor_y, interpolation=self._order)  # pyright: ignore
        else:
            # map_coordinates 可以處理 uint8, float32, float64
            out = map_coordinates(padded, (self._tp, self._coor_y, self._coor_x), order=self._order)

        if source_dtype:
            out = out.astype(source_dtype)

        return out  # pyright: ignore[reportReturnType]

    def _pad(self, cube_faces: NDArray[DType]) -> NDArray[DType]:
        """為每個立方體面添加 1 像素的填充。"""
        ABOVE = (0, slice(None))
        BELOW = (-1, slice(None))
        LEFT = (slice(None), 0)
        RIGHT = (slice(None), -1)
        padded = np.pad(cube_faces, ((0, 0), (1, 1), (1, 1)), mode="empty")

        # 填充上方/下方
        padded[Face.FRONT][ABOVE] = padded[Face.UP, -2, :]
        padded[Face.FRONT][BELOW] = padded[Face.DOWN, 1, :]
        padded[Face.RIGHT][ABOVE] = padded[Face.UP, ::-1, -2]
        padded[Face.RIGHT][BELOW] = padded[Face.DOWN, :, -2]
        padded[Face.BACK][ABOVE] = padded[Face.UP, 1, ::-1]
        padded[Face.BACK][BELOW] = padded[Face.DOWN, -2, ::-1]
        padded[Face.LEFT][ABOVE] = padded[Face.UP, :, 1]
        padded[Face.LEFT][BELOW] = padded[Face.DOWN, ::-1, 1]
        padded[Face.UP][ABOVE] = padded[Face.BACK, 1, ::-1]
        padded[Face.UP][BELOW] = padded[Face.FRONT, 1, :]
        padded[Face.DOWN][ABOVE] = padded[Face.FRONT, -2, :]
        padded[Face.DOWN][BELOW] = padded[Face.BACK, -2, ::-1]

        # 填充左邊/右邊
        padded[Face.FRONT][LEFT] = padded[Face.LEFT, :, -2]
        padded[Face.FRONT][RIGHT] = padded[Face.RIGHT, :, 1]
        padded[Face.RIGHT][LEFT] = padded[Face.FRONT, :, -2]
        padded[Face.RIGHT][RIGHT] = padded[Face.BACK, :, 1]
        padded[Face.BACK][LEFT] = padded[Face.RIGHT, :, -2]
        padded[Face.BACK][RIGHT] = padded[Face.LEFT, :, 1]
        padded[Face.LEFT][LEFT] = padded[Face.BACK, :, -2]
        padded[Face.LEFT][RIGHT] = padded[Face.FRONT, :, 1]
        padded[Face.UP][LEFT] = padded[Face.LEFT, 1, :]
        padded[Face.UP][RIGHT] = padded[Face.RIGHT, 1, ::-1]
        padded[Face.DOWN][LEFT] = padded[Face.LEFT, -2, ::-1]
        padded[Face.DOWN][RIGHT] = padded[Face.RIGHT, -2, :]

        return padded

    @classmethod
    @lru_cache(_CACHE_SIZE)
    def from_equirec(cls, face_w: int, h: int, w: int, order: int):
        """從等距矩形規格構建 CubemapSampler。

        參數
        ----------
        face_w: int
            輸入立方圖每個面的長度
        h: int
            輸出等距矩形影像高度
        w: int
            輸出等距矩形影像寬度
        order: int
            樣條插值的階數。請參閱 ``scipy.ndimage.map_coordinates``
        """
        u, v = equirect_uvgrid(h, w)

        # 獲取每個像素的面 ID：0F 1R 2B 3L 4U 5D
        tp = equirect_facetype(h, w)

        coor_x = np.empty((h, w), dtype=np.float32)
        coor_y = np.empty((h, w), dtype=np.float32)
        face_w2 = face_w / 2

        # 中間帶（前/右/後/左）
        mask = tp < Face.UP
        angles = u[mask] - (np.pi / 2 * tp[mask])
        tan_angles = np.tan(angles)
        cos_angles = np.cos(angles)
        tan_v = np.tan(v[mask])

        coor_x[mask] = face_w2 * tan_angles
        coor_y[mask] = -face_w2 * tan_v / cos_angles

        mask = tp == Face.UP
        c = face_w2 * np.tan(np.pi / 2 - v[mask])
        coor_x[mask] = c * np.sin(u[mask])
        coor_y[mask] = c * np.cos(u[mask])

        mask = tp == Face.DOWN
        c = face_w2 * np.tan(np.pi / 2 - np.abs(v[mask]))
        coor_x[mask] = c * np.sin(u[mask])
        coor_y[mask] = -c * np.cos(u[mask])

        # 最終正規化
        coor_x += face_w2
        coor_y += face_w2
        coor_x.clip(0, face_w, out=coor_x)
        coor_y.clip(0, face_w, out=coor_y)

        return cls(tp, coor_x, coor_y, order, face_w, face_w)

def cube_h2list(cube_h: NDArray[DType]) -> list[NDArray[DType]]:
    """將影像分割成 6 個面的列表。"""
    if cube_h.shape[0] * 6 != cube_h.shape[1]:
        raise ValueError("立方圖的寬度必須是其高度的 6 倍。")
    return np.split(cube_h, 6, axis=1)


def cube_list2h(cube_list: list[NDArray[DType]]) -> NDArray[DType]:
    """將 6 個面影像列表並排拼接。"""
    if len(cube_list) != 6:
        raise ValueError(f"必須提供 6 個元素來構建立方體；得到了 {len(cube_list)} 個。")
    for i, face in enumerate(cube_list):
        if face.shape != cube_list[0].shape:
            raise ValueError(
                f"面 {i} 的形狀 {face.shape} 與第一個面的形狀 {cube_list[0].shape} 不符。"
            )
        if face.dtype != cube_list[0].dtype:
            raise ValueError(
                f"面 {i} 的數據類型 {face.dtype} 與第一個面的數據類型 {cube_list[0].dtype} 不符。"
            )

    return np.concatenate(cube_list, axis=1, dtype=cube_list[0].dtype)


def cube_h2dict(cube_h: NDArray[DType]) -> dict[str, NDArray[DType]]:
    """將立方圖轉換為字典格式。"""
    return dict(zip("FRBLUD", cube_h2list(cube_h)))


def cube_dict2list(cube_dict: dict[Any, NDArray[DType]], face_k: Optional[Sequence] = None) -> list[NDArray[DType]]:
    """將字典格式的立方圖轉換為列表。"""
    face_k = face_k or "FRBLUD"
    if len(face_k) != 6:
        raise ValueError(f"必須提供 6 個 face_k 鍵來構建立方體；得到了 {len(face_k)} 個。")
    return [cube_dict[k] for k in face_k]


def cube_dict2h(cube_dict: dict[Any, NDArray[DType]], face_k: Optional[Sequence] = None) -> NDArray[DType]:
    """將字典格式的立方圖轉換為並排拼接的影像。"""
    return cube_list2h(cube_dict2list(cube_dict, face_k))


def cube_h2dice(cube_h: NDArray[DType]) -> NDArray[DType]:
    """將立方圖轉換為骰子展開圖格式。"""
    if cube_h.shape[0] * 6 != cube_h.shape[1]:
        raise ValueError("立方圖的寬度必須是其高度的 6 倍。")
    w = cube_h.shape[0]
    cube_dice = np.zeros((w * 3, w * 4, cube_h.shape[2]), dtype=cube_h.dtype)
    cube_list = cube_h2list(cube_h)
    # 順序：F R B L U D
    #        ┌────┐
    #        │ U  │
    #   ┌────┼────┼────┬────┐
    #   │ L  │ F  │ R  │ B  │
    #   └────┼────┼────┴────┘
    #        │ D  │
    #        └────┘
    sxy = [(1, 1), (2, 1), (3, 1), (0, 1), (1, 0), (1, 2)]
    for (sx, sy), face in zip(sxy, cube_list):
        cube_dice[slice_chunk(sy, w), slice_chunk(sx, w)] = face
    return cube_dice


def cube_dice2list(cube_dice: NDArray[DType]) -> list[NDArray[DType]]:
    """將骰子展開圖轉換為 6 個面的列表。"""
    if cube_dice.shape[0] % 3 != 0:
        raise ValueError("骰子影像高度必須是 3 的倍數。")
    w = cube_dice.shape[0] // 3
    if cube_dice.shape[1] != w * 4:
        raise ValueError(f"骰子寬度必須是 4 個“面”（4x{w}={4*w}）寬。")
    # 順序：F R B L U D
    #        ┌────┐
    #        │ U  │
    #   ┌────┼────┼────┬────┐
    #   │ L  │ F  │ R  │ B  │
    #   └────┼────┼────┴────┘
    #        │ D  │
    #        └────┘
    out = []
    sxy = [(1, 1), (2, 1), (3, 1), (0, 1), (1, 0), (1, 2)]
    for sx, sy in sxy:
        out.append(cube_dice[slice_chunk(sy, w), slice_chunk(sx, w)])
    return out


def cube_dice2h(cube_dice: NDArray[DType]) -> NDArray[DType]:
    """將骰子展開圖轉換為並排拼接的立方圖。"""
    if cube_dice.shape[0] % 3 != 0:
        raise ValueError("骰子影像高度必須是 3 的倍數。")
    w = cube_dice.shape[0] // 3
    if cube_dice.shape[1] != w * 4:
        raise ValueError(f"骰子寬度必須是 4 個“面”（4x{w}={4*w}）寬。")
    cube_h = np.zeros((w, w * 6, cube_dice.shape[2]), dtype=cube_dice.dtype)
    # 順序：F R B L U D
    #        ┌────┐
    #        │ U  │
    #   ┌────┼────┼────┬────┐
    #   │ L  │ F  │ R  │ B  │
    #   └────┼────┼────┴────┘
    #        │ D  │
    #        └────┘
    sxy = [(1, 1), (2, 1), (3, 1), (0, 1), (1, 0), (1, 2)]
    for i, (sx, sy) in enumerate(sxy):
        cube_h[:, slice_chunk(i, w)] = cube_dice[slice_chunk(sy, w), slice_chunk(sx, w)]
    return cube_h


@lru_cache(_CACHE_SIZE)
def equirect_uvgrid(h: int, w: int) -> tuple[NDArray[np.float32], NDArray[np.float32]]:
    u = np.linspace(-np.pi, np.pi, num=w, dtype=np.float32)
    v = np.linspace(np.pi / 2, -np.pi / 2, num=h, dtype=np.float32)
    uu, vv = np.meshgrid(u, v)
    # Since we are using lru_cache, we want the return value to be immutable.
    uu.setflags(write=False)
    vv.setflags(write=False)
    return uu, vv  # pyright: ignore[reportReturnType]


@lru_cache(_CACHE_SIZE)
def equirect_facetype(h: int, w: int) -> NDArray[np.int32]:
    """Generate a 2D equirectangular segmentation image for each facetype.

    The generated segmentation image has lookup:

    * 0 - front
    * 1 - right
    * 2 - back
    * 3 - left
    * 4 - up
    * 5 - down

    See ``Face``.

    Example:

        >>> equirect_facetype(8, 12)
            array([[4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4],
                   [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4],
                   [2, 3, 3, 3, 0, 0, 0, 1, 1, 1, 2, 2],
                   [2, 3, 3, 3, 0, 0, 0, 1, 1, 1, 2, 2],
                   [2, 3, 3, 3, 0, 0, 0, 1, 1, 1, 2, 2],
                   [2, 3, 3, 3, 0, 0, 0, 1, 1, 1, 2, 2],
                   [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
                   [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], dtype=int32)

    Parameters
    ----------
    h: int
        Desired output height.
    w: int
        Desired output width. Must be a multiple of 4.

    Returns
    -------
    ndarray
        2D numpy equirectangular segmentation image for the 6 face types.
    """
    if w % 4:
        raise ValueError(f"w must be a multiple of 4. Got {w}.")

    # Create the pattern [2,3,3,0,0,1,1,2]
    w4 = w // 4
    w8 = w // 8
    h3 = h // 3
    tp = np.empty((h, w), dtype=np.int32)
    tp[:, :w8] = 2
    tp[:, w8 : w8 + w4] = 3
    tp[:, w8 + w4 : w8 + 2 * w4] = 0
    tp[:, w8 + 2 * w4 : w8 + 3 * w4] = 1
    tp[:, w8 + 3 * w4 :] = 2

    # Prepare ceil mask
    idx = np.linspace(-np.pi, np.pi, w4) / 4
    idx = np.round(h / 2 - np.arctan(np.cos(idx)) * h / np.pi).astype(np.int32)
    # It'll never go past a third of the image, so only process that for optimization
    mask = np.empty((h3, w4), np.bool_)
    row_idx = np.arange(h3, dtype=np.int32)[:, None]
    np.less(row_idx, idx[None], out=mask)

    flip_mask = np.flip(mask, 0)
    tp[:h3, :w8][mask[:, w8:]] = Face.UP
    tp[-h3:, :w8][flip_mask[:, w8:]] = Face.DOWN
    for i in range(3):
        s = slice_chunk(i, w4, w8)
        tp[:h3, s][mask] = Face.UP
        tp[-h3:, s][flip_mask] = Face.DOWN
    remainder = w - s.stop  # pyright: ignore[reportPossiblyUnboundVariable]
    tp[:h3, s.stop :][mask[:, :remainder]] = Face.UP  # pyright: ignore[reportPossiblyUnboundVariable]
    tp[-h3:, s.stop :][flip_mask[:, :remainder]] = Face.DOWN  # pyright: ignore[reportPossiblyUnboundVariable]

    # Since we are using lru_cache, we want the return value to be immutable.
    tp.setflags(write=False)

    return tp

