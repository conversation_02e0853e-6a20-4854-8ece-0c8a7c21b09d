#!/usr/bin/env python3
"""
統一分散式處理器 - 整合所有分散式處理功能
"""
import logging
import threading
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor, Future
from enum import Enum
from typing import Any, Callable
from concurrent.futures import ProcessPoolExecutor, Future
import asyncio

from .core.config import ProcessingConfig

logger = logging.getLogger(__name__)


class ProcessingBackend(Enum):
    THREADING = "threading"
    MULTIPROCESSING = "multiprocessing"
    ASYNCIO = "asyncio"


class BackendStrategy(ABC):
    @abstractmethod
    def submit(self, fn, *args, **kwargs) -> Future:
        pass

    @abstractmethod
    async def submit_async(self, coro, *args, **kwargs) -> Any:
        pass

    @abstractmethod
    def shutdown(self):
        pass


class ThreadingBackend(BackendStrategy):
    def __init__(self, max_workers: int | None = None):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    def submit(self, fn, *args, **kwargs) -> Future:
        return self.executor.submit(fn, *args, **kwargs)

    async def submit_async(self, coro, *args, **kwargs) -> Any:
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(self.executor, lambda: asyncio.run(coro(*args, **kwargs)))

    def shutdown(self):
        self.executor.shutdown(wait=True)

class MultiprocessingBackend(BackendStrategy):
    def __init__(self, max_workers: int | None = None):
        self.executor = ProcessPoolExecutor(max_workers=max_workers)

    def submit(self, fn, *args, **kwargs) -> Future:
        return self.executor.submit(fn, *args, **kwargs)
    
    async def submit_async(self, coro, *args, **kwargs) -> Any:
        # ProcessPoolExecutor 不能很好地直接支援非同步任務
        # 這是一種簡化且可能低效的方式
        # 對於真正的非同步處理，使用 asyncio 或 Ray 等其他後端會更好
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(self.executor, lambda: asyncio.run(coro(*args, **kwargs)))

    def shutdown(self):
        self.executor.shutdown(wait=True)


class UnifiedDistributedProcessor:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, backend: str = "THREADING", max_workers: int | None = None):
        if hasattr(self, "_initialized"):
            return
            
        self.config = ProcessingConfig(backend=backend, max_workers=max_workers)
        self.backend = self._create_backend()
        self._initialized = True
        logger.info(f"UnifiedDistributedProcessor initialized with {self.config.backend} backend.")

    def _create_backend(self) -> BackendStrategy:
        if self.config.backend == ProcessingBackend.THREADING.value:
            return ThreadingBackend(self.config.max_workers)
        if self.config.backend == ProcessingBackend.MULTIPROCESSING.value:
            return MultiprocessingBackend(self.config.max_workers)
        raise NotImplementedError(f"Backend '{self.config.backend}' is not implemented.")

    def submit(self, fn: Callable, *args, **kwargs) -> Future:
        """提交同步任務"""
        return self.backend.submit(fn, *args, **kwargs)

    async def submit_async(self, coro: Callable, *args, **kwargs) -> Any:
        """提交異步任務"""
        return await self.backend.submit_async(coro, *args, **kwargs)

    def map(self, fn: Callable, iterable: list) -> list:
        """並行映射操作"""
        futures = [self.submit(fn, item) for item in iterable]
        return [f.result() for f in futures]

    def shutdown(self):
        self.backend.shutdown()