"""
舊版日誌管理模組 (相容性層) - 已棄用

本檔案包含 `log_utils` 模組在重構前的原始實作。它被保留下來的核心目的是為了
**維持對舊有程式碼的完全向後相容性**。

**重要警告：**
- **請絕對不要在此檔案中加入任何新功能或進行修改。**
- 本檔案中的所有類別和函式都已被新的模組化架構（位於 `core`, `factory`, `formatters`, `handlers`）所取代。
- 任何對 `log_utils.logger` 的直接匯入都應被視為技術債，並應盡快遷移至新的 API。
- 新的開發工作應優先使用 `log_utils.factory` 中的便利函式，例如 `create_logger_from_preset`。

此檔案的存在是一個過渡性措施，旨在確保系統在逐步重構的過程中能夠穩定運作。
"""
import warnings

# 在模組被匯入時立即發出一個明確的棄用警告
warnings.warn(
    "`log_utils.logger` 模組已被棄用，其功能已由新的模組化架構取代。"
    "請更新您的程式碼，改用 `from log_utils.factory import ...`。"
    "此相容性層將在未來的版本中被移除。",
    DeprecationWarning,
    stacklevel=2,
)

import logging
import os
import sys
import threading
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

# 嘗試導入 logging.handlers，如果失敗則使用基本功能
try:
    import logging.handlers
    HAS_HANDLERS = True
except ImportError:
    HAS_HANDLERS = False


class ColoredFormatter(logging.Formatter):
    """
    【已棄用】彩色日誌格式器。

    此類別已被 `log_utils.formatters.ColoredFormatter` 取代。
    """
    COLORS = {
        "DEBUG": "\033[36m",
        "INFO": "\033[32m",
        "WARNING": "\033[33m",
        "ERROR": "\033[31m",
        "CRITICAL": "\033[35m",
        "RESET": "\033[0m",
    }
    def __init__(self, fmt: str, use_colors: bool = True):
        super().__init__(fmt)
        self.use_colors = use_colors and self._supports_color()

    def _supports_color(self) -> bool:
        if os.name == "nt":
            try:
                import ctypes
                kernel32 = ctypes.windll.kernel32
                kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
                return True
            except:
                return False
        return hasattr(sys.stderr, "isatty") and sys.stderr.isatty()

    def format(self, record: logging.LogRecord) -> str:
        formatted = super().format(record)
        if self.use_colors and record.levelname in self.COLORS:
            color = self.COLORS[record.levelname]
            reset = self.COLORS["RESET"]
            formatted = formatted.replace(
                record.levelname, f"{color}{record.levelname}{reset}"
            )
        return formatted


class LogManager:
    """
    【已棄用】日誌管理器。

    此類別已被 `log_utils.core.manager.LogManager` 取代。
    新的管理器透過 `LogConfig` 物件進行設定，提供了更強大、更靈活的組態方式。
    """
    def __init__(self, base_output_path: Optional[str] = None):
        self.base_output_path = base_output_path
        self.loggers: Dict[str, logging.Logger] = {}
        self.log_files: Dict[str, str] = {}
        self._lock = threading.Lock()

        if base_output_path:
            self.log_dir = os.path.join(base_output_path, "logs")
        else:
            self.log_dir = os.path.join(os.getcwd(), "logs")
        os.makedirs(self.log_dir, exist_ok=True)

        self.detailed_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        self.simple_format = "%(levelname)s: %(message)s"
        self.file_format = "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"

    def setup_logger(
        self,
        name: Optional[str] = None,
        level: int = logging.INFO,
        simple_console: bool = False,
        enable_file_logging: bool = True,
        max_file_size: int = 10 * 1024 * 1024,
        backup_count: int = 5,
    ) -> logging.Logger:
        with self._lock:
            if name is None:
                name = f"panorama_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if name in self.loggers:
                return self.loggers[name]

            logger = logging.getLogger(name)
            logger.setLevel(level)
            logger.handlers.clear()

            console_handler = logging.StreamHandler(sys.stdout)
            if simple_console:
                console_formatter = logging.Formatter(self.simple_format)
                console_handler.setLevel(logging.INFO)
            else:
                console_formatter = ColoredFormatter(self.detailed_format)
                console_handler.setLevel(level)
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)

            if enable_file_logging:
                log_filename = f"{name}.log"
                log_filepath = os.path.join(self.log_dir, log_filename)
                if HAS_HANDLERS:
                    file_handler: logging.Handler = (
                        logging.handlers.RotatingFileHandler(
                            log_filepath,
                            maxBytes=max_file_size,
                            backupCount=backup_count,
                            encoding="utf-8",
                        )
                    )
                else:
                    file_handler = logging.FileHandler(log_filepath, encoding="utf-8")
                file_formatter = logging.Formatter(self.file_format)
                file_handler.setFormatter(file_formatter)
                file_handler.setLevel(logging.DEBUG)
                logger.addHandler(file_handler)
                self.log_files[name] = log_filepath

            logger.propagate = False
            self.loggers[name] = logger
            logger.info(f"舊版日誌管理器已初始化 - 名稱: {name}")
            if enable_file_logging:
                logger.info(f"日誌文件保存路徑: {self.log_files[name]}")
            return logger

    def get_logger(self, name: str) -> Optional[logging.Logger]:
        return self.loggers.get(name)

    def get_log_file(self, name: Optional[str] = None) -> Optional[str]:
        if name is None:
            if self.log_files:
                return list(self.log_files.values())[-1]
            return None
        return self.log_files.get(name)

    def set_level(self, name: str, level: int):
        if name in self.loggers:
            self.loggers[name].setLevel(level)
            for handler in self.loggers[name].handlers:
                if not isinstance(handler, logging.FileHandler):
                    handler.setLevel(level)

    def cleanup_old_logs(self, days: int = 30):
        import time
        current_time = time.time()
        cutoff_time = current_time - (days * 24 * 60 * 60)
        cleaned_count = 0
        try:
            for filename in os.listdir(self.log_dir):
                if filename.endswith(".log"):
                    filepath = os.path.join(self.log_dir, filename)
                    file_time = os.path.getmtime(filepath)
                    if file_time < cutoff_time:
                        try:
                            os.remove(filepath)
                            cleaned_count += 1
                        except OSError:
                            pass
            if cleaned_count > 0 and self.loggers:
                list(self.loggers.values())[-1].info(f"清理了 {cleaned_count} 個舊日誌文件")
        except Exception as e:
            if self.loggers:
                list(self.loggers.values())[-1].error(f"清理日誌文件時發生錯誤: {e}")

    def create_progress_logger(self, name: str) -> logging.Logger:
        progress_logger = self.setup_logger(
            name=f"{name}_progress", simple_console=True, enable_file_logging=True
        )
        progress_format = "%(asctime)s - PROGRESS - %(message)s"
        for handler in progress_logger.handlers:
            if isinstance(handler, logging.FileHandler):
                handler.setFormatter(logging.Formatter(progress_format))
        return progress_logger

    def shutdown(self):
        with self._lock:
            for logger in self.loggers.values():
                for handler in logger.handlers:
                    handler.close()
                    logger.removeHandler(handler)
            self.loggers.clear()
            self.log_files.clear()


_global_log_manager: Optional[LogManager] = None

def get_global_log_manager(base_output_path: Optional[str] = None) -> LogManager:
    """
    【已棄用】獲取全域日誌管理器實例。

    請改用 `log_utils.factory.get_global_log_manager`。
    """
    global _global_log_manager
    if _global_log_manager is None:
        _global_log_manager = LogManager(base_output_path)
    return _global_log_manager


def setup_basic_logger(name: str = "panorama", level: int = logging.INFO) -> logging.Logger:
    """
    【已棄用】快速設定基本日誌器的便利函式。

    請改用 `log_utils.factory.setup_basic_logger` 或 `create_logger_from_preset`。
    """
    log_manager = get_global_log_manager()
    return log_manager.setup_logger(name, level)


def get_logger(name: str) -> logging.Logger:
    """
    【已棄用】獲取或建立日誌器的便利函式。

    請改用 `log_utils.factory.get_logger`。
    """
    log_manager = get_global_log_manager()
    logger = log_manager.get_logger(name)
    if logger is None:
        logger = log_manager.setup_logger(name)
    return logger


def setup_simple_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    【已棄用】設定簡單日誌器的便利函式。

    請改用 `log_utils.factory.setup_simple_logger`。
    """
    is_exe_mode = getattr(sys, "frozen", False)
    use_simple = (level >= logging.WARNING) or is_exe_mode
    log_manager = LogManager()
    logger = log_manager.setup_logger(
        name=name,
        level=level,
        simple_console=use_simple,
        enable_file_logging=False,
    )
    return logger


def create_tool_logger(name: str, verbose: bool = False, quiet: bool = False) -> logging.Logger:
    """
    【已棄用】為工具程式建立專用日誌器。

    請改用 `log_utils.factory.create_tool_logger`。
    """
    if quiet:
        level = logging.WARNING
    elif verbose:
        level = logging.DEBUG
    else:
        level = logging.INFO
    return setup_simple_logger(name, level)


def main():
    """
    舊版日誌模組的使用範例和自我測試。
    主要用於證明其功能，但在新程式碼中不應被呼叫。
    """
    print("=== 舊版日誌管理模組 (相容性層) 測試 ===")
    print("警告：您正在執行一個已棄用的模組。請考慮遷移至新的 API。")

    print("\n--- 實際功能測試 ---")
    test_log_manager = LogManager()
    
    print("\n1. 詳細模式日誌器:")
    detailed_logger = test_log_manager.setup_logger("test_detailed_legacy")
    detailed_logger.debug("舊版偵錯訊息 (應不可見)")
    detailed_logger.info("舊版一般訊息")
    detailed_logger.warning("舊版警告訊息")

    print("\n2. 簡化模式日誌器:")
    simple_logger = test_log_manager.setup_logger("test_simple_legacy", simple_console=True)
    simple_logger.info("舊版簡化模式訊息")
    
    log_file = test_log_manager.get_log_file("test_detailed_legacy")
    if log_file and os.path.exists(log_file):
        print(f"\n日誌檔案已成功建立於: {log_file}")
    else:
        print("\n日誌檔案建立失敗。")

    test_log_manager.shutdown()
    print("\n測試完成。")


if __name__ == "__main__":
    main()
