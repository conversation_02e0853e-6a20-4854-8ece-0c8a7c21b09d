#!/usr/bin/env python3
"""
Integration test runner for the refactored processing and utils modules.

This script runs comprehensive integration tests to validate the interaction between
the refactored modules and ensures the processing pipeline works end-to-end.
"""
import sys
import os
from pathlib import Path
import pytest
import argparse
import logging

# project_root for test directory paths
project_root = Path(__file__).parent.parent

# Setup logging with UTF-8 compatible format
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)


def setup_test_environment():
    """Setup test environment and dependencies."""
    # Setup test environment variables
    os.environ["TESTING"] = "1"
    os.environ["DISABLE_GPU"] = "1" 
    os.environ["DISABLE_NUMBA"] = "1"
    os.environ["LOG_LEVEL"] = "ERROR"
    
    # Ensure test directories exist
    test_dirs = [
        project_root / "test" / "test_processing",
        project_root / "test" / "test_utils",
    ]
    
    for test_dir in test_dirs:
        test_dir.mkdir(parents=True, exist_ok=True)
    
    # Check for required modules - skip core in testing environment
    try:
        import processing
        import utils
        import detection
        # Skip core import in test environment due to Numba issues
        logger.info("All required modules are importable")
        return True
    except ImportError as e:
        logger.error(f"Failed to import required modules: {e}")
        return False


def run_processing_tests(verbose=False, specific_test=None):
    """Run processing integration tests."""
    logger.info("Running processing integration tests...")
    
    test_args = [
        str(project_root / "test" / "test_processing"),
        "-v" if verbose else "",
        "--tb=short",
        "--no-header",
        "-x",  # Stop on first failure for faster feedback
    ]
    
    if specific_test:
        test_args.append(f"-k {specific_test}")
    
    # Filter out empty strings
    test_args = [arg for arg in test_args if arg]
    
    try:
        exit_code = pytest.main(test_args)
        if exit_code == 0:
            logger.info("processing integration tests passed")
        else:
            logger.error("processing integration tests failed")
        return exit_code
    except Exception as e:
        logger.error(f"Error running processing tests: {e}")
        return 1


def run_utils_tests(verbose=False, specific_test=None):
    """Run utils integration tests."""
    logger.info("?�� Running utils integration tests...")
    
    test_args = [
        str(project_root / "test" / "test_utils"),
        "-v" if verbose else "",
        "--tb=short", 
        "--no-header",
        "-x",
    ]
    
    if specific_test:
        test_args.append(f"-k {specific_test}")
    
    test_args = [arg for arg in test_args if arg]
    
    try:
        exit_code = pytest.main(test_args)
        if exit_code == 0:
            logger.info("??utils integration tests passed")
        else:
            logger.error("??utils integration tests failed")
        return exit_code
    except Exception as e:
        logger.error(f"??Error running utils tests: {e}")
        return 1


def run_cross_module_tests(verbose=False):
    """Run cross-module integration tests."""
    logger.info("?�� Running cross-module integration tests...")
    
    test_args = [
        str(project_root / "test" / "test_processing" / "test_processing_integration.py::TestEndToEndProcessingIntegration"),
        "-v" if verbose else "",
        "--tb=short",
        "--no-header",
    ]
    
    test_args = [arg for arg in test_args if arg]
    
    try:
        exit_code = pytest.main(test_args)
        if exit_code == 0:
            logger.info("??Cross-module integration tests passed")
        else:
            logger.error("??Cross-module integration tests failed")
        return exit_code
    except Exception as e:
        logger.error(f"??Error running cross-module tests: {e}")
        return 1


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Run integration tests for refactored modules",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_integration_tests.py                    # Run all tests
  python run_integration_tests.py --processing       # Only processing tests  
  python run_integration_tests.py --utils           # Only utils tests
  python run_integration_tests.py --cross-module    # Only cross-module tests
  python run_integration_tests.py -v --test factory # Verbose, filter by 'factory'
        """
    )
    
    parser.add_argument("-v", "--verbose", action="store_true",
                       help="Enable verbose test output")
    parser.add_argument("--processing", action="store_true", 
                       help="Run only processing tests")
    parser.add_argument("--utils", action="store_true",
                       help="Run only utils tests") 
    parser.add_argument("--cross-module", action="store_true",
                       help="Run only cross-module integration tests")
    parser.add_argument("--test", type=str,
                       help="Filter tests by name/keyword")
    parser.add_argument("--setup-only", action="store_true",
                       help="Only setup environment, don't run tests")
    
    args = parser.parse_args()
    
    logger.info("?? Starting integration test runner...")
    
    # Setup test environment
    if not setup_test_environment():
        logger.error("??Failed to setup test environment")
        return 1
    
    if args.setup_only:
        logger.info("??Environment setup completed")
        return 0
    
    exit_codes = []
    
    # Run selected test suites
    if args.processing or not any([args.processing, args.utils, args.cross_module]):
        exit_codes.append(run_processing_tests(args.verbose, args.test))
    
    if args.utils or not any([args.processing, args.utils, args.cross_module]):
        exit_codes.append(run_utils_tests(args.verbose, args.test))
    
    if args.cross_module or not any([args.processing, args.utils, args.cross_module]):
        exit_codes.append(run_cross_module_tests(args.verbose))
    
    # Summary
    total_failures = sum(1 for code in exit_codes if code != 0)
    
    if total_failures == 0:
        logger.info("?? All integration tests passed successfully!")
        return 0
    else:
        logger.error(f"?�� {total_failures} test suite(s) failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())

