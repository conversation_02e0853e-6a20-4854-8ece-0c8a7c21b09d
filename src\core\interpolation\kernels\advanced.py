"""
高級插值核函數模組

本模組定義了一系列高級插值演算法所需的核心數學函數，即「核函數」(Kernel)。
這些函數描述了在進行卷積插值時，周圍像素點對目標像素的權重貢獻。
使用純 NumPy 實現，專為計算效率而優化。
"""

import numpy as np


def _lanczos_kernel(x: float, a: float = 3.0) -> float:
    """
    Lanczos 核函數，是一種基於 Sinc 函數的加窗實現。
    它能在保留影像細節和抑制振鈴效應（Ringing artifacts）之間取得良好平衡，
    是高品質影像縮放的常用選擇。參數 `a` 控制窗口大小（影響範圍）。
    """
    if abs(x) >= a:
        return 0.0
    if abs(x) < 1e-8:  # 避免除以零
        return 1.0
    pi_x = np.pi * x
    return a * np.sin(pi_x) * np.sin(pi_x / a) / (pi_x * pi_x)


def _mitchell_kernel(x: float, b: float = 1 / 3, c: float = 1 / 3) -> float:
    """
    Mitchell-Netravali 核函數，是一種三次卷積濾波器。
    它透過參數 B 和 C 在模糊（blurring）和鋸齒（aliasing）之間進行權衡，
    通常能產生視覺上較為舒適的結果。
    """
    x = abs(x)
    if x < 1:
        return ((12 - 9 * b - 6 * c) * x**3 + (-18 + 12 * b + 6 * c) * x**2 + (6 - 2 * b)) / 6
    elif x < 2:
        return ((-b - 6 * c) * x**3 + (6 * b + 30 * c) * x**2 + (-12 * b - 48 * c) * x + (8 * b + 24 * c)) / 6
    return 0.0


def _catmull_rom_kernel(x: float) -> float:
    """
    Catmull-Rom 核函數，是 Mitchell-Netravali 核的一種特例 (B=0, C=0.5)。
    它能產生比標準雙三次插值更銳利的影像，但可能伴隨輕微的振鈴效應。
    """
    x = abs(x)
    if x < 1:
        return 1.5 * x**3 - 2.5 * x**2 + 1
    elif x < 2:
        return -0.5 * x**3 + 2.5 * x**2 - 4 * x + 2
    return 0.0


def _gaussian_kernel(x: float, sigma: float = 1.0) -> float:
    """
    高斯核函數。其效果類似於高斯模糊，能產生非常平滑的插值結果，
    但會損失較多的高頻細節（銳利度）。
    """
    return np.exp(-(x**2) / (2 * sigma**2)) / (sigma * np.sqrt(2 * np.pi))


def _kaiser_kernel(x: float, beta: float = 8.0, support: float = 3.0) -> float:
    """
    Kaiser 窗函數，一種靈活的窗函數，可透過 `beta` 參數調整主瓣寬度和旁瓣衰減。
    """
    if abs(x) > support:
        return 0.0
    x_norm = x / support
    return np.i0(beta * np.sqrt(1 - x_norm**2)) / np.i0(beta)


def _sinc_kernel(x: float) -> float:
    """
    Sinc 核函數，是理想的低通濾波器。在理論上能完美重建訊號，
    但在實際應用中，由於其無限支撐（infinite support），通常需要加窗（如Lanczos）。
    """
    if abs(x) < 1e-8:
        return 1.0
    pi_x = np.pi * x
    return np.sin(pi_x) / pi_x


def _hermite_kernel(x: float) -> float:
    """Hermite 三次樣條插值核函數。"""
    x = abs(x)
    if x > 2.0:
        return 0.0
    if x <= 1.0:
        return 2 * x**3 - 3 * x**2 + 1
    return -x**3 + 5 * x**2 - 8 * x + 4


def _welch_kernel(x: float, support: float = 2.0) -> float:
    """Welch 窗函數，是一種拋物線窗。"""
    x = abs(x)
    if x >= support:
        return 0.0
    x_norm = x / support
    return 1 - x_norm**2


def _bartlett_kernel(x: float, support: float = 2.0) -> float:
    """Bartlett 窗函數，與三角窗類似。"""
    x = abs(x)
    if x >= support:
        return 0.0
    return 1 - x / support


def _tukey_kernel(x: float, alpha: float = 0.5, support: float = 2.0) -> float:
    """Tukey 窗函數，也稱為餘弦錐形窗。"""
    x = abs(x)
    if x >= support:
        return 0.0
    x_norm = x / support
    if x_norm <= alpha / 2:
        return 1.0
    if x_norm <= 1:
        return 0.5 * (1 + np.cos(np.pi * (x_norm - alpha / 2) / (1 - alpha / 2)))
    return 0.0


def _blackman_harris_kernel(x: float, support: float = 2.0) -> float:
    """Blackman-Harris 窗函數。"""
    x = abs(x)
    if x >= support:
        return 0.0
    x_norm = x / support
    a0, a1, a2, a3 = 0.35875, 0.48829, 0.14128, 0.01168
    phase = 2 * np.pi * x_norm
    return a0 - a1 * np.cos(phase) + a2 * np.cos(2 * phase) - a3 * np.cos(3 * phase)


def _nuttall_kernel(x: float, support: float = 2.0) -> float:
    """Nuttall 窗函數。"""
    x = abs(x)
    if x >= support:
        return 0.0
    x_norm = x / support
    a0, a1, a2, a3 = 0.3635819, 0.4891775, 0.1365995, 0.0106411
    phase = 2 * np.pi * x_norm
    return a0 - a1 * np.cos(phase) + a2 * np.cos(2 * phase) - a3 * np.cos(3 * phase)


def _spline36_kernel(x: float) -> float:
    """Spline36 核函數，一種高階樣條濾波器。"""
    x = abs(x)
    if x >= 3:
        return 0.0
    if x < 1:
        return ((13/11)*x**2 - (453/209))*x**2 + 1
    if x < 2:
        return (((-6/11)*x + (270/209))*x**2 + ((-156/209)*x + (26/11)))*x - (1/11)
    return (((1/11)*x - (45/209))*x**2 + ((26/209)*x - (13/11)))*x + (1/11)


def _spline64_kernel(x: float) -> float:
    """Spline64 核函數，比 Spline36 更高階，更平滑。"""
    x = abs(x)
    if x >= 4:
        return 0.0
    if x < 1:
        return ((49/41)*x**2 - (6387/2911))*x**2 + 1
    if x < 2:
        return (((-24/41)*x + (4032/2911))*x**2 + ((-2328/2911)*x + (162/41)))*x - (3/41)
    if x < 3:
        return (((6/41)*x - (1008/2911))*x**2 + ((582/2911)*x - (63/41)))*x + (9/41)
    return (((-1/41)*x + (168/2911))*x**2 + ((-97/2911)*x + (21/41)))*x - (1/41)


# --- 為需要參數的核函數創建帶有預設參數的專門版本 ---
# 這樣做可以讓上層模組直接調用，而無需關心具體的參數設定。

def _welch_kernel_2(x: float) -> float:
    """Welch 核函數，支撐範圍為 2。"""
    return _welch_kernel(x, support=2.0)


def _bartlett_kernel_2(x: float) -> float:
    """Bartlett 核函數，支撐範圍為 2。"""
    return _bartlett_kernel(x, support=2.0)


def _tukey_kernel_default(x: float) -> float:
    """Tukey 核函數，使用預設參數 alpha=0.5, support=2.0。"""
    return _tukey_kernel(x, alpha=0.5, support=2.0)


def _blackman_harris_kernel_2(x: float) -> float:
    """Blackman-Harris 核函數，支撐範圍為 2。"""
    return _blackman_harris_kernel(x, support=2.0)


def _nuttall_kernel_2(x: float) -> float:
    """Nuttall 核函數，支撐範圍為 2。"""
    return _nuttall_kernel(x, support=2.0)


def _mitchell_kernel_default(x: float) -> float:
    """Mitchell 核函數，使用推薦的預設參數 B=1/3, C=1/3。"""
    return _mitchell_kernel(x, b=1/3, c=1/3)


def _lanczos_kernel_2(x: float) -> float:
    """Lanczos-2 核函數 (a=2)。"""
    return _lanczos_kernel(x, a=2.0)


def _lanczos_kernel_3(x: float) -> float:
    """Lanczos-3 核函數 (a=3)，最常用的高品質選項。"""
    return _lanczos_kernel(x, a=3.0)


def _lanczos_kernel_4(x: float) -> float:
    """Lanczos-4 核函數 (a=4)。"""
    return _lanczos_kernel(x, a=4.0)


def _lanczos_kernel_8(x: float) -> float:
    """Lanczos-8 核函數 (a=8)，用於極高品質的場景。"""
    return _lanczos_kernel(x, a=8.0)


def _gaussian_kernel_default(x: float) -> float:
    """高斯核函數，使用預設 sigma=1.0。"""
    return _gaussian_kernel(x, sigma=1.0)


def _kaiser_kernel_default(x: float) -> float:
    """Kaiser 核函數，使用預設參數 beta=8.0, support=3.0。"""
    return _kaiser_kernel(x, beta=8.0, support=3.0)