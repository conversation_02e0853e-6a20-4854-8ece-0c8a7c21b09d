#!/usr/bin/env python3
"""
統一模糊處理服務模組
Unified Blurring Service Module

提供標準化的圖像模糊功能，特別是針對檢測到的區域進行處理。
Provides standardized image blurring functions, especially for processing detected regions.

Author: Roo
Date: 2025-07-19
"""

# 1. 標準庫
import logging
from typing import Any

# 2. 第三方庫
import cv2
import numpy as np

# 3. 本地模組
try:
    from detection.core.data_structures import DetectionBox
    from utils.image_utils import ImageUtils
    HAS_DEPS = True
except ImportError:
    HAS_DEPS = False
    # 佔位符
    class DetectionBox: pass
    class ImageUtils:
        @staticmethod
        def apply_blur(image, region, blur_type='gaussian', kernel_size=51):
            return image

class BlurService:
    """
    統一的模糊處理服務。

    職責:
    - 根據檢測到的區域對圖像進行模糊處理。
    - 支持多種模糊類型。
    - 提供批次處理能力。
    """

    def __init__(self, config: Any):
        """
        初始化模糊服務

        Args:
            config: 應用程序配置對象
        """
        if not HAS_DEPS:
            raise ImportError("Required dependencies for BlurService are not available.")
            
        self.config = config.processing
        self.logger = logging.getLogger(__name__)

    def _apply_blur_to_regions(self, image: np.ndarray, regions: list[DetectionBox]) -> np.ndarray:
        """對單張圖像的多個區域應用模糊"""
        blurred_image = image.copy()
        for region in regions:
            try:
                blurred_image = ImageUtils.apply_blur(
                    blurred_image,
                    region.bbox,
                    self.config.blur_type,
                    self.config.blur_kernel_size
                )
            except Exception as e:
                self.logger.warning(f"應用模糊到區域 {region.bbox} 時失敗: {e}")
        return blurred_image

    def batch_blur_regions(
        self,
        images: dict[int, np.ndarray],
        detection_results: dict[int, list[DetectionBox]]
    ) -> dict[int, np.ndarray]:
        """
        批次模糊多張圖像的指定區域。

        Args:
            images: 一個字典，鍵為 face_id，值為原始圖像。
            detection_results: 一個字典，鍵為 face_id，值為檢測到的區域列表。

        Returns:
            一個字典，鍵為 face_id，值為模糊處理後的圖像。
        """
        blurred_images = {}
        for face_id, image in images.items():
            regions = detection_results.get(face_id, [])
            if regions:
                blurred_images[face_id] = self._apply_blur_to_regions(image, regions)
            else:
                # 如果沒有檢測到區域，則返回原始圖像
                blurred_images[face_id] = image
        
        self.logger.info(f"批次模糊處理完成，共處理 {len(images)} 張圖像。")
        return blurred_images
