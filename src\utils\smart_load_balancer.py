#!/usr/bin/env python3
"""
智能負載均衡器 - 方案一智能負載均衡
Smart Load Balancer - Plan 1 Intelligent Load Balancing

實現自適應負載均衡、資源調度和任務分配優化
Implements adaptive load balancing, resource scheduling and task allocation optimization

Author: Claude Code Assistant  
Date: 2025-01-16
"""

import os
import sys
import time
import threading
import queue
import warnings
from typing import Any, Callable
from dataclasses import dataclass, field, asdict
from enum import Enum
from pathlib import Path
import logging
import statistics
from collections import defaultdict, deque
import hashlib
import uuid
import heapq

import numpy as np

# Import project modules directly (using pip install -e .)

# 導入性能監控和GPU管理
try:
    from utils.performance_monitor import get_performance_monitor, PerformanceMetric
    from utils.gpu_manager import get_gpu_manager, DeviceType, DeviceInfo
    HAS_MONITORING = True
except ImportError:
    HAS_MONITORING = False

logger = logging.getLogger('smart_load_balancer')

class LoadBalanceStrategy(Enum):
    """負載均衡策略"""
    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_RESPONSE_TIME = "least_response_time"
    RESOURCE_BASED = "resource_based"
    ADAPTIVE = "adaptive"
    MACHINE_LEARNING = "ml_based"

class TaskPriority(Enum):
    """任務優先級"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5

class ResourceType(Enum):
    """資源類型"""
    CPU = "cpu"
    MEMORY = "memory"
    GPU = "gpu"
    NETWORK = "network"
    STORAGE = "storage"

@dataclass
class WorkerNode:
    """工作節點"""
    node_id: str
    host: str
    port: int
    capabilities: list[str]
    resources: dict[ResourceType, float]  # 最大資源容量
    current_load: dict[ResourceType, float]  # 當前負載
    weight: float = 1.0
    active_tasks: int = 0
    max_tasks: int = 10
    response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    last_heartbeat: float = field(default_factory=time.time)
    status: str = "online"
    performance_score: float = 1.0
    reliability_score: float = 1.0

@dataclass
class Task:
    """任務定義"""
    task_id: str
    task_type: str
    priority: TaskPriority
    estimated_resources: dict[ResourceType, float]
    estimated_time: float
    data: Any
    dependencies: list[str] = field(default_factory=list)
    constraints: dict[str, Any] = field(default_factory=dict)
    created_time: float = field(default_factory=time.time)
    deadline: float | None = None
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class TaskResult:
    """任務結果"""
    task_id: str
    node_id: str
    success: bool
    execution_time: float
    resource_usage: dict[ResourceType, float]
    error_message: str | None = None
    completed_time: float = field(default_factory=time.time)

class SmartLoadBalancer:
    """智能負載均衡器"""
    
    def __init__(self, 
                 strategy: LoadBalanceStrategy = LoadBalanceStrategy.ADAPTIVE,
                 enable_ml_optimization: bool = True,
                 health_check_interval: float = 10.0):
        """
        初始化智能負載均衡器
        
        Args:
            strategy: 負載均衡策略
            enable_ml_optimization: 啟用機器學習優化
            health_check_interval: 健康檢查間隔（秒）
        """
        self.strategy = strategy
        self.enable_ml_optimization = enable_ml_optimization
        self.health_check_interval = health_check_interval
        
        # 節點管理
        self.workers: dict[str, WorkerNode] = {}
        self.worker_round_robin_index = 0
        
        # 任務管理
        self.task_queue = queue.PriorityQueue()
        self.pending_tasks: dict[str, Task] = {}
        self.running_tasks: dict[str, tuple[Task, WorkerNode]] = {}
        self.completed_tasks: dict[str, TaskResult] = {}
        
        # 統計信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'average_response_time': 0.0,
            'total_throughput': 0.0,
            'resource_utilization': {}
        }
        
        # 性能歷史
        self.performance_history: dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # 機器學習組件
        self.ml_model = None
        if enable_ml_optimization:
            self._initialize_ml_model()
        
        # 控制狀態
        self.running = False
        self.scheduler_thread = None
        self.health_checker_thread = None
        
        # 性能監控器
        self.performance_monitor = None
        if HAS_MONITORING:
            self.performance_monitor = get_performance_monitor()
        
        logger.info(f"智能負載均衡器初始化完成 - 策略: {strategy.value}")
    
    def _initialize_ml_model(self):
        """初始化機器學習模型"""
        try:
            # 這裡可以集成scikit-learn或其他ML庫
            # 用於預測任務執行時間和最佳節點選擇
            self.ml_features = [
                'node_cpu_usage', 'node_memory_usage', 'node_gpu_usage',
                'task_estimated_time', 'task_priority', 'node_performance_score',
                'node_reliability_score', 'current_queue_length'
            ]
            logger.info("機器學習模型初始化完成")
        except Exception as e:
            logger.warning(f"機器學習模型初始化失敗: {e}")
            self.enable_ml_optimization = False
    
    def register_worker(self, node_id: str, host: str, port: int,
                       capabilities: list[str], resources: dict[ResourceType, float]) -> bool:
        """
        註冊工作節點
        
        Args:
            node_id: 節點ID
            host: 主機地址
            port: 端口
            capabilities: 能力列表
            resources: 資源容量
            
        Returns:
            註冊是否成功
        """
        try:
            worker = WorkerNode(
                node_id=node_id,
                host=host,
                port=port,
                capabilities=capabilities,
                resources=resources,
                current_load={rt: 0.0 for rt in ResourceType}
            )
            
            self.workers[node_id] = worker
            logger.info(f"註冊工作節點: {node_id} ({host}:{port})")
            return True
            
        except Exception as e:
            logger.error(f"工作節點註冊失敗: {e}")
            return False
    
    def unregister_worker(self, node_id: str) -> bool:
        """取消註冊工作節點"""
        if node_id in self.workers:
            del self.workers[node_id]
            logger.info(f"取消註冊工作節點: {node_id}")
            return True
        return False
    
    def submit_task(self, task_type: str, data: Any,
                   priority: TaskPriority = TaskPriority.NORMAL,
                   estimated_resources: dict[ResourceType, float] | None = None,
                   estimated_time: float = 60.0,
                   deadline: float | None = None) -> str:
        """
        提交任務
        
        Args:
            task_type: 任務類型
            data: 任務數據
            priority: 優先級
            estimated_resources: 預估資源需求
            estimated_time: 預估執行時間
            deadline: 截止時間
            
        Returns:
            任務ID
        """
        task_id = str(uuid.uuid4())
        
        if estimated_resources is None:
            estimated_resources = {
                ResourceType.CPU: 1.0,
                ResourceType.MEMORY: 100.0,  # MB
                ResourceType.GPU: 0.0
            }
        
        task = Task(
            task_id=task_id,
            task_type=task_type,
            priority=priority,
            estimated_resources=estimated_resources,
            estimated_time=estimated_time,
            data=data,
            deadline=deadline
        )
        
        # 添加到任務隊列（優先級越高，數值越小）
        priority_value = -priority.value  # 負數實現高優先級
        self.task_queue.put((priority_value, time.time(), task))
        self.pending_tasks[task_id] = task
        
        self.stats['total_tasks'] += 1
        
        logger.info(f"提交任務 {task_id}: {task_type} (優先級: {priority.name})")
        return task_id
    
    def start(self):
        """啟動負載均衡器"""
        if self.running:
            logger.warning("負載均衡器已在運行")
            return
        
        self.running = True
        
        # 啟動任務調度線程
        self.scheduler_thread = threading.Thread(target=self._task_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        # 啟動健康檢查線程
        self.health_checker_thread = threading.Thread(target=self._health_checker, daemon=True)
        self.health_checker_thread.start()
        
        logger.info("智能負載均衡器已啟動")
    
    def stop(self):
        """停止負載均衡器"""
        self.running = False
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5.0)
        
        if self.health_checker_thread:
            self.health_checker_thread.join(timeout=5.0)
        
        logger.info("智能負載均衡器已停止")
    
    def _task_scheduler(self):
        """任務調度主循環"""
        while self.running:
            try:
                # 獲取任務
                try:
                    priority, submit_time, task = self.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 選擇最佳節點
                best_worker = self._select_best_worker(task)
                
                if best_worker:
                    # 分配任務
                    self._assign_task(task, best_worker)
                else:
                    # 沒有可用節點，重新排隊
                    self.task_queue.put((priority, submit_time, task))
                    time.sleep(1.0)
                
            except Exception as e:
                logger.error(f"任務調度錯誤: {e}")
    
    def _select_best_worker(self, task: Task) -> WorkerNode | None:
        """選擇最佳工作節點"""
        suitable_workers = []
        
        # 篩選合適的節點
        for worker in self.workers.values():
            if (worker.status == "online" and
                task.task_type in worker.capabilities and
                worker.active_tasks < worker.max_tasks and
                self._can_handle_resources(worker, task)):
                suitable_workers.append(worker)
        
        if not suitable_workers:
            return None
        
        # 根據策略選擇節點
        if self.strategy == LoadBalanceStrategy.ROUND_ROBIN:
            return self._round_robin_select(suitable_workers)
        elif self.strategy == LoadBalanceStrategy.LEAST_CONNECTIONS:
            return min(suitable_workers, key=lambda w: w.active_tasks)
        elif self.strategy == LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_select(suitable_workers)
        elif self.strategy == LoadBalanceStrategy.LEAST_RESPONSE_TIME:
            return self._least_response_time_select(suitable_workers)
        elif self.strategy == LoadBalanceStrategy.RESOURCE_BASED:
            return self._resource_based_select(suitable_workers, task)
        elif self.strategy == LoadBalanceStrategy.ADAPTIVE:
            return self._adaptive_select(suitable_workers, task)
        elif self.strategy == LoadBalanceStrategy.MACHINE_LEARNING:
            return self._ml_based_select(suitable_workers, task)
        else:
            return suitable_workers[0]  # 默認選擇第一個
    
    def _can_handle_resources(self, worker: WorkerNode, task: Task) -> bool:
        """檢查節點是否能處理任務資源需求"""
        for resource_type, required in task.estimated_resources.items():
            if resource_type in worker.resources:
                available = worker.resources[resource_type] - worker.current_load[resource_type]
                if available < required:
                    return False
        return True
    
    def _round_robin_select(self, workers: list[WorkerNode]) -> WorkerNode:
        """輪詢選擇"""
        self.worker_round_robin_index = (self.worker_round_robin_index + 1) % len(workers)
        return workers[self.worker_round_robin_index]
    
    def _weighted_round_robin_select(self, workers: list[WorkerNode]) -> WorkerNode:
        """加權輪詢選擇"""
        total_weight = sum(w.weight for w in workers)
        if total_weight == 0:
            return workers[0]
        
        # 基於權重選擇
        target = np.random.random() * total_weight
        current_weight = 0
        
        for worker in workers:
            current_weight += worker.weight
            if current_weight >= target:
                return worker
        
        return workers[-1]
    
    def _least_response_time_select(self, workers: list[WorkerNode]) -> WorkerNode:
        """最少響應時間選擇"""
        def avg_response_time(worker):
            if worker.response_times:
                return statistics.mean(worker.response_times)
            return 0.0
        
        return min(workers, key=avg_response_time)
    
    def _resource_based_select(self, workers: list[WorkerNode], task: Task) -> WorkerNode:
        """基於資源的選擇"""
        def resource_score(worker):
            score = 0.0
            for resource_type, required in task.estimated_resources.items():
                if resource_type in worker.resources:
                    available = worker.resources[resource_type] - worker.current_load[resource_type]
                    utilization = worker.current_load[resource_type] / worker.resources[resource_type]
                    # 偏好資源充足且利用率適中的節點
                    score += available * (1.0 - utilization)
            return score
        
        return max(workers, key=resource_score)
    
    def _adaptive_select(self, workers: list[WorkerNode], task: Task) -> WorkerNode:
        """自適應選擇"""
        def adaptive_score(worker):
            # 綜合多個因素
            load_score = 1.0 - (worker.active_tasks / worker.max_tasks)
            performance_score = worker.performance_score
            reliability_score = worker.reliability_score
            
            # 資源可用性
            resource_score = 0.0
            for resource_type, required in task.estimated_resources.items():
                if resource_type in worker.resources:
                    available = worker.resources[resource_type] - worker.current_load[resource_type]
                    if available >= required:
                        resource_score += 1.0
            resource_score /= len(task.estimated_resources)
            
            # 響應時間因素
            response_factor = 1.0
            if worker.response_times:
                avg_time = statistics.mean(worker.response_times)
                response_factor = 1.0 / (1.0 + avg_time / 60.0)  # 正規化到分鐘
            
            # 綜合分數
            total_score = (load_score * 0.3 + 
                          performance_score * 0.25 + 
                          reliability_score * 0.2 + 
                          resource_score * 0.15 + 
                          response_factor * 0.1)
            
            return total_score
        
        return max(workers, key=adaptive_score)
    
    def _ml_based_select(self, workers: list[WorkerNode], task: Task) -> WorkerNode:
        """基於機器學習的選擇"""
        if not self.enable_ml_optimization or not self.ml_model:
            return self._adaptive_select(workers, task)
        
        # 這裡可以實現ML模型預測
        # 目前使用自適應選擇作為後備
        return self._adaptive_select(workers, task)
    
    def _assign_task(self, task: Task, worker: WorkerNode):
        """分配任務到工作節點"""
        try:
            # 更新節點狀態
            worker.active_tasks += 1
            for resource_type, amount in task.estimated_resources.items():
                if resource_type in worker.current_load:
                    worker.current_load[resource_type] += amount
            
            # 移動任務狀態
            self.running_tasks[task.task_id] = (task, worker)
            if task.task_id in self.pending_tasks:
                del self.pending_tasks[task.task_id]
            
            # 模擬任務執行（實際應該發送到工作節點）
            self._simulate_task_execution(task, worker)
            
            logger.debug(f"分配任務 {task.task_id} 到節點 {worker.node_id}")
            
        except Exception as e:
            logger.error(f"任務分配失敗: {e}")
    
    def _simulate_task_execution(self, task: Task, worker: WorkerNode):
        """模擬任務執行（實際應該是網路通信）"""
        def execute_task():
            try:
                start_time = time.time()
                
                # 模擬執行時間
                execution_time = task.estimated_time + np.random.normal(0, task.estimated_time * 0.1)
                execution_time = max(0.1, execution_time)  # 最少0.1秒
                
                time.sleep(min(execution_time, 5.0))  # 最多模擬5秒
                
                actual_time = time.time() - start_time
                
                # 模擬成功率（基於節點可靠性）
                success = np.random.random() < worker.reliability_score
                
                # 創建任務結果
                result = TaskResult(
                    task_id=task.task_id,
                    node_id=worker.node_id,
                    success=success,
                    execution_time=actual_time,
                    resource_usage=task.estimated_resources,
                    error_message=None if success else "模擬執行失敗"
                )
                
                self._handle_task_completion(result)
                
            except Exception as e:
                # 處理執行錯誤
                error_result = TaskResult(
                    task_id=task.task_id,
                    node_id=worker.node_id,
                    success=False,
                    execution_time=0.0,
                    resource_usage={},
                    error_message=str(e)
                )
                self._handle_task_completion(error_result)
        
        # 在新線程中執行任務
        task_thread = threading.Thread(target=execute_task, daemon=True)
        task_thread.start()
    
    def _handle_task_completion(self, result: TaskResult):
        """處理任務完成"""
        try:
            task_id = result.task_id
            
            if task_id in self.running_tasks:
                task, worker = self.running_tasks[task_id]
                
                # 更新節點狀態
                worker.active_tasks -= 1
                for resource_type, amount in result.resource_usage.items():
                    if resource_type in worker.current_load:
                        worker.current_load[resource_type] = max(0, 
                            worker.current_load[resource_type] - amount)
                
                # 更新響應時間歷史
                worker.response_times.append(result.execution_time)
                
                # 更新性能分數
                if result.success:
                    worker.performance_score = min(1.0, worker.performance_score + 0.01)
                    worker.reliability_score = min(1.0, worker.reliability_score + 0.005)
                else:
                    worker.performance_score = max(0.1, worker.performance_score - 0.02)
                    worker.reliability_score = max(0.1, worker.reliability_score - 0.01)
                
                # 移動任務狀態
                del self.running_tasks[task_id]
                self.completed_tasks[task_id] = result
                
                # 更新統計
                if result.success:
                    self.stats['completed_tasks'] += 1
                else:
                    self.stats['failed_tasks'] += 1
                
                logger.debug(f"任務完成 {task_id}: {'成功' if result.success else '失敗'}")
                
        except Exception as e:
            logger.error(f"任務完成處理錯誤: {e}")
    
    def _health_checker(self):
        """健康檢查主循環"""
        while self.running:
            try:
                current_time = time.time()
                
                for worker in self.workers.values():
                    # 檢查心跳超時
                    if current_time - worker.last_heartbeat > 30.0:  # 30秒超時
                        if worker.status == "online":
                            worker.status = "offline"
                            logger.warning(f"節點 {worker.node_id} 離線")
                    
                    # 更新性能監控數據
                    if self.performance_monitor:
                        self.performance_monitor._add_metric(
                            f"worker_{worker.node_id}_load",
                            worker.active_tasks / worker.max_tasks * 100,
                            "%"
                        )
                
                time.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"健康檢查錯誤: {e}")
    
    def update_worker_heartbeat(self, node_id: str,
                              load_info: dict[ResourceType, float] | None = None):
        """更新工作節點心跳"""
        if node_id in self.workers:
            worker = self.workers[node_id]
            worker.last_heartbeat = time.time()
            worker.status = "online"
            
            if load_info:
                worker.current_load.update(load_info)
    
    def get_cluster_status(self) -> dict[str, Any]:
        """獲取集群狀態"""
        total_workers = len(self.workers)
        online_workers = len([w for w in self.workers.values() if w.status == "online"])
        
        # 計算平均響應時間
        all_response_times = []
        for worker in self.workers.values():
            all_response_times.extend(worker.response_times)
        
        avg_response_time = statistics.mean(all_response_times) if all_response_times else 0.0
        
        # 更新統計
        self.stats['average_response_time'] = avg_response_time
        
        status = {
            'strategy': self.strategy.value,
            'total_workers': total_workers,
            'online_workers': online_workers,
            'pending_tasks': len(self.pending_tasks),
            'running_tasks': len(self.running_tasks),
            'completed_tasks': len(self.completed_tasks),
            'statistics': self.stats.copy(),
            'workers': []
        }
        
        # 工作節點詳情
        for worker in self.workers.values():
            worker_info = {
                'node_id': worker.node_id,
                'status': worker.status,
                'active_tasks': worker.active_tasks,
                'max_tasks': worker.max_tasks,
                'performance_score': worker.performance_score,
                'reliability_score': worker.reliability_score,
                'resource_usage': {}
            }
            
            # 資源使用率
            for resource_type in ResourceType:
                if resource_type in worker.resources:
                    usage_percent = (worker.current_load[resource_type] / 
                                   worker.resources[resource_type]) * 100
                    worker_info['resource_usage'][resource_type.value] = usage_percent
            
            status['workers'].append(worker_info)
        
        return status
    
    def print_cluster_status(self):
        """打印集群狀態"""
        status = self.get_cluster_status()
        
        print("=== 智能負載均衡器狀態 ===")
        print(f"策略: {status['strategy']}")
        print(f"節點: {status['online_workers']}/{status['total_workers']} (在線/總計)")
        print(f"任務: 待處理 {status['pending_tasks']}, 運行中 {status['running_tasks']}, 已完成 {status['completed_tasks']}")
        print(f"平均響應時間: {status['statistics']['average_response_time']:.2f}秒")
        print(f"成功率: {status['statistics']['completed_tasks'] / max(1, status['statistics']['total_tasks']) * 100:.1f}%")
        
        print("\n工作節點:")
        for worker_info in status['workers']:
            print(f"  {worker_info['node_id']}: {worker_info['status']}")
            print(f"    任務: {worker_info['active_tasks']}/{worker_info['max_tasks']}")
            print(f"    性能分數: {worker_info['performance_score']:.2f}")
            print(f"    可靠性: {worker_info['reliability_score']:.2f}")
            
            if worker_info['resource_usage']:
                print("    資源使用率:")
                for resource, usage in worker_info['resource_usage'].items():
                    print(f"      {resource}: {usage:.1f}%")

def create_smart_load_balancer(strategy: LoadBalanceStrategy = LoadBalanceStrategy.ADAPTIVE) -> SmartLoadBalancer:
    """創建智能負載均衡器實例"""
    return SmartLoadBalancer(strategy=strategy)

if __name__ == "__main__":
    # 測試智能負載均衡器
    balancer = SmartLoadBalancer(strategy=LoadBalanceStrategy.ADAPTIVE)
    
    # 註冊測試節點
    balancer.register_worker(
        "worker1", "localhost", 8001, 
        ["panorama_processing", "image_resize"],
        {ResourceType.CPU: 4.0, ResourceType.MEMORY: 8192.0, ResourceType.GPU: 1.0}
    )
    
    balancer.register_worker(
        "worker2", "localhost", 8002,
        ["panorama_processing", "object_detection"],
        {ResourceType.CPU: 8.0, ResourceType.MEMORY: 16384.0, ResourceType.GPU: 2.0}
    )
    
    # 啟動負載均衡器
    balancer.start()
    
    try:
        # 提交測試任務
        for i in range(10):
            task_id = balancer.submit_task(
                task_type="panorama_processing",
                data=f"test_data_{i}",
                priority=TaskPriority.NORMAL,
                estimated_time=2.0 + np.random.random() * 3.0
            )
            print(f"提交任務: {task_id}")
        
        # 等待任務完成
        print("等待任務完成...")
        time.sleep(15)
        
        # 打印狀態
        balancer.print_cluster_status()
        
    except KeyboardInterrupt:
        print("收到中斷信號")
    finally:
        balancer.stop()
        print("負載均衡器已停止")