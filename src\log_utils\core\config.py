"""
日誌配置管理模組

提供日誌配置的結構化管理，從原始的參數傳遞改為配置對象。
"""

import logging
import os
import sys
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional


@dataclass
class LogConfig:
    """日誌配置類

    使用dataclass提供結構化的日誌配置管理。
    """

    # 基本配置
    name: str = "default"
    level: int = logging.INFO

    # 輸出配置
    console_output: bool = True
    file_output: bool = True

    # 檔案配置
    base_output_path: Optional[str] = None
    log_filename: Optional[str] = None

    # 格式配置
    simple_console: bool = False
    use_colors: bool = True
    color_theme: str = "default"

    # 檔案輪轉配置
    max_bytes: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5

    # 高級配置
    exe_mode: bool = False
    thread_safe: bool = True

    # 額外處理器
    additional_handlers: List[Dict[str, Any]] = field(default_factory=list)

    # 格式字符串
    console_format: Optional[str] = None
    file_format: Optional[str] = None

    def __post_init__(self):
        """後初始化處理"""
        # 檢測exe模式
        if self.exe_mode is None:
            self.exe_mode = self._detect_exe_mode()

        # 設置預設輸出路徑
        if self.base_output_path is None:
            self.base_output_path = self._get_default_output_path()

        # 設置預設日誌檔案名
        if self.log_filename is None:
            self.log_filename = f"{self.name}.log"

    def _detect_exe_mode(self) -> bool:
        """檢測是否執行在可執行檔案模式"""
        return (
            hasattr(sys, "frozen")  # PyInstaller
            or hasattr(sys, "_MEIPASS")  # PyInstaller
            or "python" not in sys.executable.lower()  # 其他打包方式
        )

    def _get_default_output_path(self) -> str:
        """獲取預設輸出路徑"""
        if self.exe_mode:
            # exe模式下，使用exe所在目錄
            return os.path.dirname(os.path.abspath(sys.executable))
        else:
            # 開發模式下，使用當前工作目錄
            return os.getcwd()

    @property
    def log_dir(self) -> Path:
        """獲取日誌目錄路徑"""
        assert self.base_output_path is not None, "base_output_path 不應為 None"
        return Path(self.base_output_path) / "logs"

    @property
    def log_file_path(self) -> Path:
        """獲取日誌檔案完整路徑"""
        assert self.log_filename is not None, "log_filename 不應為 None"
        return self.log_dir / self.log_filename

    def get_console_format(self) -> str:
        """獲取控制台格式字符串"""
        if self.console_format:
            return self.console_format

        if self.simple_console:
            return "%(levelname)s - %(message)s"
        else:
            return "%(asctime)s - %(levelname)s - %(message)s"

    def get_file_format(self) -> str:
        """獲取檔案格式字符串"""
        if self.file_format:
            return self.file_format

        return "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"

    def validate(self) -> List[str]:
        """驗證配置有效性"""
        errors = []

        # 檢查名稱
        if not self.name or not isinstance(self.name, str):
            errors.append("name 必須是非空字符串")

        # 檢查級別
        if self.level not in [
            logging.DEBUG,
            logging.INFO,
            logging.WARNING,
            logging.ERROR,
            logging.CRITICAL,
        ]:
            errors.append("level 必須是有效的日誌級別")

        # 檢查輸出配置
        if not self.console_output and not self.file_output:
            errors.append("至少需要啟用一種輸出方式")

        # 檢查路徑
        if self.file_output and not self.base_output_path:
            errors.append("啟用檔案輸出時需要指定 base_output_path")

        # 檢查檔案輪轉配置
        if self.max_bytes <= 0:
            errors.append("max_bytes 必須大於 0")

        if self.backup_count < 0:
            errors.append("backup_count 必須大於等於 0")

        return errors

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            "name": self.name,
            "level": self.level,
            "console_output": self.console_output,
            "file_output": self.file_output,
            "base_output_path": self.base_output_path,
            "log_filename": self.log_filename,
            "simple_console": self.simple_console,
            "use_colors": self.use_colors,
            "color_theme": self.color_theme,
            "max_bytes": self.max_bytes,
            "backup_count": self.backup_count,
            "exe_mode": self.exe_mode,
            "thread_safe": self.thread_safe,
            "console_format": self.console_format,
            "file_format": self.file_format,
            "additional_handlers": self.additional_handlers,
        }

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "LogConfig":
        """從字典創建配置"""
        return cls(**config_dict)

    @classmethod
    def from_file(cls, config_file: str) -> "LogConfig":
        """從配置檔案創建配置"""
        import json

        with open(config_file, "r", encoding="utf-8") as f:
            config_data = json.load(f)

        return cls.from_dict(config_data)

    def save_to_file(self, config_file: str):
        """保存配置到檔案"""
        import json

        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)

    def copy(self) -> "LogConfig":
        """創建配置副本"""
        return LogConfig(**self.to_dict())

    def update(self, **kwargs) -> "LogConfig":
        """更新配置並返回新實例"""
        config_dict = self.to_dict()
        config_dict.update(kwargs)
        return LogConfig.from_dict(config_dict)


# 預定義配置
class LogConfigPresets:
    """預定義的日誌配置"""

    @staticmethod
    def development() -> LogConfig:
        """開發環境配置"""
        return LogConfig(
            name="development",
            level=logging.DEBUG,
            console_output=True,
            file_output=True,
            simple_console=False,
            use_colors=True,
            max_bytes=10 * 1024 * 1024,
            backup_count=3,
        )

    @staticmethod
    def production() -> LogConfig:
        """生產環境配置"""
        return LogConfig(
            name="production",
            level=logging.INFO,
            console_output=True,
            file_output=True,
            simple_console=True,
            use_colors=False,
            max_bytes=50 * 1024 * 1024,
            backup_count=10,
        )

    @staticmethod
    def console_only() -> LogConfig:
        """僅控制台輸出配置"""
        return LogConfig(
            name="console_only",
            level=logging.INFO,
            console_output=True,
            file_output=False,
            simple_console=False,
            use_colors=True,
        )

    @staticmethod
    def file_only() -> LogConfig:
        """僅檔案輸出配置"""
        return LogConfig(
            name="file_only",
            level=logging.DEBUG,
            console_output=False,
            file_output=True,
            max_bytes=20 * 1024 * 1024,
            backup_count=5,
        )

    @staticmethod
    def debug() -> LogConfig:
        """調試配置"""
        return LogConfig(
            name="debug",
            level=logging.DEBUG,
            console_output=True,
            file_output=True,
            simple_console=False,
            use_colors=True,
            max_bytes=5 * 1024 * 1024,
            backup_count=2,
            file_format="%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s() - %(message)s",
        )

    @staticmethod
    def minimal() -> LogConfig:
        """最小化配置"""
        return LogConfig(
            name="minimal",
            level=logging.WARNING,
            console_output=True,
            file_output=False,
            simple_console=True,
            use_colors=False,
        )


def create_config(preset: str = "development", **overrides) -> LogConfig:
    """創建配置的便利函數

    :param preset: 預設名稱
    :param overrides: 覆蓋參數
    :return: 日誌配置
    """
    presets = {
        "development": LogConfigPresets.development,
        "production": LogConfigPresets.production,
        "console_only": LogConfigPresets.console_only,
        "file_only": LogConfigPresets.file_only,
        "debug": LogConfigPresets.debug,
        "minimal": LogConfigPresets.minimal,
    }

    if preset not in presets:
        raise ValueError(f"未知預設: {preset}")

    config = presets[preset]()

    # 應用覆蓋參數
    if overrides:
        config = config.update(**overrides)

    return config


def main():
    """
    測試 log_utils.core.config 模組的主要功能
    """
    print("=" * 60)
    print("測試 log_utils.core.config 模組")
    print("=" * 60)

    # 測試基本配置創建
    print("\n1. 測試基本配置創建:")
    try:
        config = LogConfig(name="test_config")
        print(f"   配置創建成功: {config.name}")
        print(f"   日誌級別: {logging.getLevelName(config.level)}")
        print(f"   控制台輸出: {config.console_output}")
        print(f"   檔案輸出: {config.file_output}")
        print(f"   日誌目錄: {config.log_dir}")
        print(f"   日誌檔案: {config.log_file_path}")
    except Exception as e:
        print(f"   基本配置測試失敗: {e}")

    # 測試預設配置
    print("\n2. 測試預設配置:")
    presets = ["development", "production", "console_only", "file_only", "debug", "minimal"]
    for preset in presets:
        try:
            config = create_config(preset)
            print(f"   {preset}: {config.name} (級別: {logging.getLevelName(config.level)})")
        except Exception as e:
            print(f"   {preset} 配置測試失敗: {e}")

    # 測試配置驗證
    print("\n3. 測試配置驗證:")
    try:
        # 有效配置
        valid_config = LogConfig(name="valid", level=logging.INFO)
        errors = valid_config.validate()
        print(f"   有效配置驗證: {'通過' if not errors else f'失敗 - {errors}'}")

        # 無效配置
        invalid_config = LogConfig(name="", level=999)
        errors = invalid_config.validate()
        print(f"   無效配置驗證: {'通過' if errors else '失敗 - 應該有錯誤'}")
        print(f"   錯誤列表: {errors}")
    except Exception as e:
        print(f"   配置驗證測試失敗: {e}")

    # 測試格式字符串
    print("\n4. 測試格式字符串:")
    try:
        config = LogConfig(name="format_test")
        console_format = config.get_console_format()
        file_format = config.get_file_format()
        print(f"   控制台格式: {console_format}")
        print(f"   檔案格式: {file_format}")

        # 測試簡單控制台格式
        simple_config = LogConfig(name="simple", simple_console=True)
        simple_format = simple_config.get_console_format()
        print(f"   簡單格式: {simple_format}")
    except Exception as e:
        print(f"   格式字符串測試失敗: {e}")

    # 測試配置序列化
    print("\n5. 測試配置序列化:")
    try:
        config = LogConfig(name="serialize_test", level=logging.DEBUG)

        # 轉換為字典
        config_dict = config.to_dict()
        print(f"   字典轉換成功: {len(config_dict)} 個屬性")

        # 從字典創建
        new_config = LogConfig.from_dict(config_dict)
        print(f"   從字典創建成功: {new_config.name}")

        # 測試複製
        copied_config = config.copy()
        print(f"   配置複製成功: {copied_config.name}")

        # 測試更新
        updated_config = config.update(level=logging.WARNING)
        print(f"   配置更新成功: {logging.getLevelName(updated_config.level)}")
    except Exception as e:
        print(f"   配置序列化測試失敗: {e}")

    # 測試檔案操作
    print("\n6. 測試檔案操作:")
    try:
        config = LogConfig(name="file_test")
        test_file = "test_log_config.json"

        # 保存到檔案
        config.save_to_file(test_file)
        print(f"   配置保存成功: {test_file}")

        # 從檔案載入
        loaded_config = LogConfig.from_file(test_file)
        print(f"   配置載入成功: {loaded_config.name}")

        # 清理測試檔案
        import os
        if os.path.exists(test_file):
            os.remove(test_file)
            print("   測試檔案已清理")
    except Exception as e:
        print(f"   檔案操作測試失敗: {e}")

    # 測試環境檢測
    print("\n7. 測試環境檢測:")
    try:
        config = LogConfig(name="env_test")
        print(f"   EXE 模式: {config.exe_mode}")
        print(f"   預設輸出路徑: {config.base_output_path}")
        print(f"   執行檔路徑: {sys.executable}")
    except Exception as e:
        print(f"   環境檢測測試失敗: {e}")

    print("\n✅ log_utils.core.config 模組測試完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
