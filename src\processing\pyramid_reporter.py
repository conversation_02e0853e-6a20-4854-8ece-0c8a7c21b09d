#!/usr/bin/env python3
"""
金字塔處理器報告生成模組
Pyramid Processor Report Generation Module

提供詳細的處理報告、統計分析、可視化生成等功能。
Provides detailed processing reports, statistical analysis, and visualization generation.

Author: <PERSON> Assistant
Date: 2025-01-11
"""

import csv
import datetime
import json
import os
import sys
import time
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any

# Import project modules directly (using pip install -e .)

# 可選依賴導入
try:
    import matplotlib.patches as patches
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_agg import FigureCanvasAgg

    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import numpy as np

    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False

# 導入相關模組
try:
    from utils.annotation_visualizer import AnnotationVisualizer

    HAS_VISUALIZER = True
except ImportError:
    HAS_VISUALIZER = False

try:
    from log_utils.logger import create_tool_logger

    HAS_LOGGER = True
except ImportError:
    HAS_LOGGER = False


@dataclass
class ProcessingStats:
    """處理統計數據類"""

    total_scenes: int = 0
    successful_scenes: int = 0
    failed_scenes: int = 0
    skipped_scenes: int = 0

    total_panoramas: int = 0
    panorama_to_cube_count: int = 0
    cube_processed_count: int = 0

    total_blur_detections: int = 0
    total_logo_applications: int = 0
    total_pyramid_tiles: int = 0

    processing_times: list[float] = field(default_factory=list)
    memory_usage: list[float] = field(default_factory=list)

    start_time: float = 0.0
    end_time: float = 0.0

    def __post_init__(self):
        if not self.start_time:
            self.start_time = time.time()

    @property
    def total_time(self) -> float:
        """總處理時間"""
        if self.end_time:
            return self.end_time - self.start_time
        return time.time() - self.start_time

    @property
    def average_time(self) -> float:
        """平均處理時間"""
        if self.processing_times:
            return sum(self.processing_times) / len(self.processing_times)
        return 0.0

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_scenes > 0:
            return self.successful_scenes / self.total_scenes
        return 0.0

    @property
    def peak_memory(self) -> float:
        """峰值記憶體使用"""
        if self.memory_usage:
            return max(self.memory_usage)
        return 0.0


@dataclass
class SceneReport:
    """場景處理報告"""

    district: str
    scene: str
    status: str  # 'success', 'failed', 'skipped'
    processing_time: float = 0.0
    error_message: str = ""

    # 統計信息
    panorama_count: int = 0
    cube_count: int = 0
    blur_detection_count: int = 0
    pyramid_tile_count: int = 0

    # 檔案信息
    input_files: list[str] = field(default_factory=list)
    output_files: list[str] = field(default_factory=list)

    # 模糊檢測詳情
    blur_details: dict[int, dict] = field(default_factory=dict)


class PyramidReporter:
    """
    金字塔處理器報告生成器

    負責生成詳細的處理報告、統計分析和可視化圖表。
    """

    def __init__(self, output_path: str, config=None, logger=None):
        """
        初始化報告生成器

        Args:
            output_path: 輸出目錄路徑
            config: 配置對象
            logger: 日誌器
        """
        self.output_path = output_path
        self.config = config
        self.logger = logger or self._setup_logger()

        # 統計數據
        self.stats = ProcessingStats()
        self.scene_reports: list[SceneReport] = []

        # 報告路徑
        self.report_dir = os.path.join(output_path, "reports")
        os.makedirs(self.report_dir, exist_ok=True)

        # 初始化可視化器
        if HAS_VISUALIZER:
            self.visualizer = AnnotationVisualizer()
        else:
            self.visualizer = None

    def _setup_logger(self):
        """設置日誌器"""
        if HAS_LOGGER:
            return create_tool_logger("pyramid_reporter", verbose=False, quiet=False)
        else:

            class SimpleLogger:
                def info(self, msg):
                    print(f"INFO: {msg}")

                def warning(self, msg):
                    print(f"WARNING: {msg}")

                def error(self, msg):
                    print(f"ERROR: {msg}")

                def debug(self, msg):
                    print(f"DEBUG: {msg}")

            return SimpleLogger()

    def start_processing(self):
        """開始處理統計"""
        self.stats.start_time = time.time()
        self.logger.info("開始統計處理時間")

    def finish_processing(self):
        """完成處理統計"""
        self.stats.end_time = time.time()
        self.logger.info(f"處理完成，總耗時: {self.stats.total_time:.2f}秒")

    def add_scene_report(self, report: SceneReport):
        """添加場景報告"""
        self.scene_reports.append(report)

        # 更新統計
        self.stats.total_scenes += 1
        if report.status == "success":
            self.stats.successful_scenes += 1
        elif report.status == "failed":
            self.stats.failed_scenes += 1
        elif report.status == "skipped":
            self.stats.skipped_scenes += 1

        self.stats.total_panoramas += report.panorama_count
        self.stats.total_blur_detections += report.blur_detection_count
        self.stats.total_pyramid_tiles += report.pyramid_tile_count

        if report.processing_time > 0:
            self.stats.processing_times.append(report.processing_time)

    def record_conversion(self, conversion_type: str):
        """記錄轉換操作"""
        if conversion_type == "panorama_to_cube":
            self.stats.panorama_to_cube_count += 1
        elif conversion_type == "cube_processed":
            self.stats.cube_processed_count += 1

    def record_blur_detection(self, face_id: int, blur_count: int, details: dict = None):
        """記錄模糊檢測"""
        self.stats.total_blur_detections += blur_count

        # 記錄到最新場景報告
        if self.scene_reports:
            latest_report = self.scene_reports[-1]
            if face_id not in latest_report.blur_details:
                latest_report.blur_details[face_id] = {"count": 0, "details": []}
            latest_report.blur_details[face_id]["count"] += blur_count
            if details:
                latest_report.blur_details[face_id]["details"].append(details)

    def record_logo_application(self):
        """記錄標誌應用"""
        self.stats.total_logo_applications += 1

    def record_memory_usage(self, memory_mb: float):
        """記錄記憶體使用"""
        self.stats.memory_usage.append(memory_mb)

    def generate_summary_report(self) -> str:
        """生成摘要報告"""
        report_lines = [
            "=" * 60,
            "金字塔處理器 - 處理摘要報告",
            "=" * 60,
            f"生成時間: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "== 基本統計 ==",
            f"總處理時間: {self.stats.total_time:.2f} 秒",
            f"處理場景數: {self.stats.total_scenes}",
            f"  - 成功: {self.stats.successful_scenes}",
            f"  - 失敗: {self.stats.failed_scenes}",
            f"  - 跳過: {self.stats.skipped_scenes}",
            f"成功率: {self.stats.success_rate:.1%}",
            "",
            "== 處理內容 ==",
            f"全景圖處理: {self.stats.total_panoramas} 張",
            f"全景→立方體轉換: {self.stats.panorama_to_cube_count} 次",
            f"立方體結構處理: {self.stats.cube_processed_count} 個",
            f"模糊區域檢測: {self.stats.total_blur_detections} 個",
            f"標誌應用: {self.stats.total_logo_applications} 次",
            f"金字塔瓦片: {self.stats.total_pyramid_tiles} 個",
            "",
            "== 性能指標 ==",
            f"平均處理時間: {self.stats.average_time:.2f} 秒/場景",
        ]

        if self.stats.memory_usage:
            report_lines.append(f"峰值記憶體使用: {self.stats.peak_memory:.1f} MB")

        # 處理時間分佈
        if self.stats.processing_times:
            times = self.stats.processing_times
            report_lines.extend(
                [
                    "",
                    "== 處理時間分佈 ==",
                    f"最快: {min(times):.2f} 秒",
                    f"最慢: {max(times):.2f} 秒",
                    f"中位數: {sorted(times)[len(times)//2]:.2f} 秒",
                ]
            )

        # 失敗場景分析
        failed_reports = [r for r in self.scene_reports if r.status == "failed"]
        if failed_reports:
            report_lines.extend(
                [
                    "",
                    "== 失敗場景分析 ==",
                ]
            )
            for report in failed_reports[:5]:  # 最多顯示5個失敗案例
                report_lines.append(f"  {report.district}/{report.scene}: {report.error_message}")
            if len(failed_reports) > 5:
                report_lines.append(f"  ... 還有 {len(failed_reports) - 5} 個失敗場景")

        report_lines.append("=" * 60)
        return "\n".join(report_lines)

    def save_summary_report(self, filename: str = None) -> str:
        """保存摘要報告"""
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"summary_report_{timestamp}.txt"

        report_path = os.path.join(self.report_dir, filename)
        report_content = self.generate_summary_report()

        try:
            with open(report_path, "w", encoding="utf-8") as f:
                f.write(report_content)
            self.logger.info(f"摘要報告已保存: {report_path}")
            return report_path
        except Exception as e:
            self.logger.error(f"保存摘要報告失敗: {e}")
            return ""

    def save_detailed_csv_report(self, filename: str = None) -> str:
        """保存詳細CSV報告"""
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"detailed_report_{timestamp}.csv"

        report_path = os.path.join(self.report_dir, filename)

        try:
            with open(report_path, "w", encoding="utf-8", newline="") as f:
                writer = csv.writer(f)

                # 寫入標頭
                writer.writerow(
                    [
                        "區域",
                        "場景",
                        "狀態",
                        "處理時間(秒)",
                        "全景圖數量",
                        "立方體數量",
                        "模糊檢測數",
                        "金字塔瓦片數",
                        "錯誤訊息",
                        "輸入檔案",
                        "輸出檔案",
                    ]
                )

                # 寫入場景數據
                for report in self.scene_reports:
                    writer.writerow(
                        [
                            report.district,
                            report.scene,
                            report.status,
                            f"{report.processing_time:.2f}",
                            report.panorama_count,
                            report.cube_count,
                            report.blur_detection_count,
                            report.pyramid_tile_count,
                            report.error_message,
                            "; ".join(report.input_files),
                            "; ".join(report.output_files),
                        ]
                    )

            self.logger.info(f"詳細CSV報告已保存: {report_path}")
            return report_path

        except Exception as e:
            self.logger.error(f"保存詳細CSV報告失敗: {e}")
            return ""

    def save_json_report(self, filename: str = None) -> str:
        """保存JSON格式報告"""
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"report_{timestamp}.json"

        report_path = os.path.join(self.report_dir, filename)

        # 準備JSON數據
        json_data = {
            "metadata": {
                "generated_at": datetime.datetime.now().isoformat(),
                "processor_version": "2.0.0",
                "config": self.config.to_dict() if self.config else None,
            },
            "summary": {
                "total_time": self.stats.total_time,
                "total_scenes": self.stats.total_scenes,
                "successful_scenes": self.stats.successful_scenes,
                "failed_scenes": self.stats.failed_scenes,
                "skipped_scenes": self.stats.skipped_scenes,
                "success_rate": self.stats.success_rate,
                "average_time": self.stats.average_time,
                "peak_memory_mb": self.stats.peak_memory,
            },
            "processing_stats": {
                "total_panoramas": self.stats.total_panoramas,
                "panorama_to_cube_count": self.stats.panorama_to_cube_count,
                "cube_processed_count": self.stats.cube_processed_count,
                "total_blur_detections": self.stats.total_blur_detections,
                "total_logo_applications": self.stats.total_logo_applications,
                "total_pyramid_tiles": self.stats.total_pyramid_tiles,
            },
            "scene_reports": [
                {
                    "district": r.district,
                    "scene": r.scene,
                    "status": r.status,
                    "processing_time": r.processing_time,
                    "panorama_count": r.panorama_count,
                    "cube_count": r.cube_count,
                    "blur_detection_count": r.blur_detection_count,
                    "pyramid_tile_count": r.pyramid_tile_count,
                    "error_message": r.error_message,
                    "blur_details": r.blur_details,
                }
                for r in self.scene_reports
            ],
        }

        try:
            with open(report_path, "w", encoding="utf-8") as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"JSON報告已保存: {report_path}")
            return report_path
        except Exception as e:
            self.logger.error(f"保存JSON報告失敗: {e}")
            return ""

    def generate_performance_chart(self, filename: str = None) -> str:
        """生成性能分析圖表"""
        if not HAS_MATPLOTLIB:
            self.logger.warning("Matplotlib未安裝，無法生成圖表")
            return ""

        if not self.stats.processing_times:
            self.logger.warning("無處理時間數據，無法生成圖表")
            return ""

        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_chart_{timestamp}.png"

        chart_path = os.path.join(self.report_dir, filename)

        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle("金字塔處理器性能分析", fontsize=16)

            # 1. 處理時間分佈直方圖
            ax1.hist(
                self.stats.processing_times,
                bins=20,
                alpha=0.7,
                color="skyblue",
                edgecolor="black",
            )
            ax1.set_title("處理時間分佈")
            ax1.set_xlabel("處理時間 (秒)")
            ax1.set_ylabel("頻次")
            ax1.grid(True, alpha=0.3)

            # 2. 成功率餅圖
            sizes = [
                self.stats.successful_scenes,
                self.stats.failed_scenes,
                self.stats.skipped_scenes,
            ]
            labels = ["成功", "失敗", "跳過"]
            colors = ["lightgreen", "lightcoral", "lightyellow"]
            ax2.pie(sizes, labels=labels, colors=colors, autopct="%1.1f%%", startangle=90)
            ax2.set_title("處理結果分佈")

            # 3. 處理時間趨勢線圖
            if len(self.stats.processing_times) > 1:
                ax3.plot(
                    range(len(self.stats.processing_times)),
                    self.stats.processing_times,
                    "b-",
                    alpha=0.7,
                    linewidth=1,
                )
                ax3.set_title("處理時間趨勢")
                ax3.set_xlabel("場景序號")
                ax3.set_ylabel("處理時間 (秒)")
                ax3.grid(True, alpha=0.3)

            # 4. 記憶體使用趨勢（如果有數據）
            if self.stats.memory_usage:
                ax4.plot(
                    range(len(self.stats.memory_usage)),
                    self.stats.memory_usage,
                    "r-",
                    alpha=0.7,
                    linewidth=1,
                )
                ax4.set_title("記憶體使用趨勢")
                ax4.set_xlabel("時間點")
                ax4.set_ylabel("記憶體使用 (MB)")
                ax4.grid(True, alpha=0.3)
            else:
                # 顯示統計條形圖
                categories = ["全景圖", "立方體轉換", "模糊檢測", "標誌應用"]
                values = [
                    self.stats.total_panoramas,
                    self.stats.panorama_to_cube_count,
                    self.stats.total_blur_detections,
                    self.stats.total_logo_applications,
                ]
                ax4.bar(
                    categories,
                    values,
                    color=["blue", "green", "orange", "purple"],
                    alpha=0.7,
                )
                ax4.set_title("處理內容統計")
                ax4.set_ylabel("數量")
                plt.setp(ax4.xaxis.get_majorticklabels(), rotation=45)

            plt.tight_layout()
            plt.savefig(chart_path, dpi=150, bbox_inches="tight")
            plt.close()

            self.logger.info(f"性能分析圖表已保存: {chart_path}")
            return chart_path

        except Exception as e:
            self.logger.error(f"生成性能圖表失敗: {e}")
            return ""

    def generate_all_reports(self) -> dict[str, str]:
        """生成所有報告"""
        self.finish_processing()

        report_paths = {}

        # 生成摘要報告
        summary_path = self.save_summary_report()
        if summary_path:
            report_paths["summary"] = summary_path

        # 生成詳細CSV報告
        csv_path = self.save_detailed_csv_report()
        if csv_path:
            report_paths["csv"] = csv_path

        # 生成JSON報告
        json_path = self.save_json_report()
        if json_path:
            report_paths["json"] = json_path

        # 生成性能圖表
        chart_path = self.generate_performance_chart()
        if chart_path:
            report_paths["chart"] = chart_path

        # 生成報告索引
        index_path = self._generate_report_index(report_paths)
        if index_path:
            report_paths["index"] = index_path

        self.logger.info(f"所有報告生成完成，共 {len(report_paths)} 個檔案")
        return report_paths

    def _generate_report_index(self, report_paths: dict[str, str]) -> str:
        """生成報告索引檔案"""
        index_path = os.path.join(self.report_dir, "report_index.html")

        try:
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金字塔處理器報告索引</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .stats {{ display: flex; justify-content: space-around; margin: 20px 0; }}
        .stat-item {{ text-align: center; }}
        .stat-value {{ font-size: 2em; font-weight: bold; color: #2c3e50; }}
        .report-list {{ margin: 20px 0; }}
        .report-item {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .success {{ color: #27ae60; }}
        .failed {{ color: #e74c3c; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>金字塔處理器報告索引</h1>
        <p>生成時間: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <div class="stat-value">{self.stats.total_scenes}</div>
            <div>總場景數</div>
        </div>
        <div class="stat-item">
            <div class="stat-value success">{self.stats.successful_scenes}</div>
            <div>成功</div>
        </div>
        <div class="stat-item">
            <div class="stat-value failed">{self.stats.failed_scenes}</div>
            <div>失敗</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{self.stats.success_rate:.1%}</div>
            <div>成功率</div>
        </div>
    </div>
    
    <div class="report-list">
        <h2>可用報告</h2>
"""

            # 添加報告連結
            for report_type, path in report_paths.items():
                if report_type != "index":
                    filename = os.path.basename(path)
                    html_content += f"""
        <div class="report-item">
            <strong>{report_type.upper()}:</strong> 
            <a href="{filename}" target="_blank">{filename}</a>
        </div>
"""

            html_content += """
    </div>
</body>
</html>
"""

            with open(index_path, "w", encoding="utf-8") as f:
                f.write(html_content)

            self.logger.info(f"報告索引已生成: {index_path}")
            return index_path

        except Exception as e:
            self.logger.error(f"生成報告索引失敗: {e}")
            return ""


def create_pyramid_reporter(output_path: str, config=None, logger=None) -> PyramidReporter:
    """
    工廠函數：創建金字塔報告生成器

    Args:
        output_path: 輸出路徑
        config: 配置對象
        logger: 日誌器

    Returns:
        PyramidReporter實例
    """
    return PyramidReporter(output_path, config, logger)


if __name__ == "__main__":
    # 使用示例和測試
    print("=== 金字塔處理器報告生成模組 ===\n")

    # 創建測試報告器
    test_output = "./test_reports"
    os.makedirs(test_output, exist_ok=True)

    reporter = create_pyramid_reporter(test_output)
    reporter.start_processing()

    # 模擬添加一些測試數據
    for i in range(5):
        report = SceneReport(
            district=f"區域{i//2 + 1}",
            scene=f"場景{i + 1}",
            status="success" if i < 4 else "failed",
            processing_time=2.5 + i * 0.5,
            panorama_count=1,
            blur_detection_count=i * 2,
            pyramid_tile_count=100 + i * 50,
            error_message="測試錯誤" if i >= 4 else "",
        )
        reporter.add_scene_report(report)

        # 記錄一些轉換
        reporter.record_conversion("panorama_to_cube")
        reporter.record_blur_detection(0, i * 2)
        if i % 2 == 0:
            reporter.record_logo_application()

    # 生成所有報告
    print("生成測試報告...")
    report_paths = reporter.generate_all_reports()

    print(f"報告生成完成，共 {len(report_paths)} 個檔案:")
    for report_type, path in report_paths.items():
        print(f"  {report_type}: {path}")

    # 顯示摘要
    print("\n" + reporter.generate_summary_report())
