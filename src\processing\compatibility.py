#!/usr/bin/env python3
"""
向後兼容性支援層
Backward Compatibility Support Layer

確保舊版API繼續工作，提供平滑的遷移路徑。
Ensures legacy APIs continue to work and provides smooth migration path.

Author: <PERSON> Assistant
Date: 2025-01-19
"""

# 1. 標準庫
import logging
import warnings
from typing import Any

# 2. 第三方庫
import numpy as np

# 3. 本地模組
try:
    from config.settings import get_config, Config
    HAS_CONFIG = True
except ImportError:
    HAS_CONFIG = False
    Config = None

from .factory import ProcessingFactory
from .pipeline import ProcessingResult


class LegacyPanoramaProcessor:
    """
    兼容舊版API的包裝器
    
    提供與舊版PanoramaProcessor相同的接口，但內部使用新的依賴注入架構。
    """
    
    def __init__(self, detector=None, **kwargs):
        """
        初始化兼容處理器
        
        Args:
            detector: 舊版檢測器實例（已廢棄，將被忽略）
            **kwargs: 其他參數（主要用於兼容性）
        """
        if detector is not None:
            warnings.warn(
                "直接傳遞detector參數已廢棄。請使用ProcessingFactory創建處理器。",
                DeprecationWarning,
                stacklevel=2
            )
        
        self.logger = logging.getLogger(__name__)
        
        # 使用新的工廠創建管線
        try:
            config = get_config() if HAS_CONFIG else None
            self.pipeline = ProcessingFactory.create_pipeline(config)
            self._processor = ProcessingFactory.create_panorama_processor(config)
        except Exception as e:
            self.logger.error(f"初始化處理器失敗: {e}")
            self.pipeline = None
            self._processor = None
    
    def process_single_panorama(
        self, 
        img_path: str, 
        output_folder: str, 
        **kwargs
    ) -> tuple[bool, dict]:
        """
        處理單張全景圖（兼容舊版接口）
        
        Args:
            img_path: 圖像路徑
            output_folder: 輸出文件夾
            **kwargs: 其他參數（用於兼容性）
            
        Returns:
            tuple: (是否成功, 統計信息字典)
        """
        if self._processor is None:
            self.logger.error("處理器未初始化")
            return False, {"error": "處理器未初始化"}
        
        try:
            # 使用新的處理器
            success, stats = self._processor.process_single_panorama(img_path, output_folder)
            return success, stats
            
        except Exception as e:
            self.logger.error(f"處理失敗: {e}")
            return False, {"error": str(e)}
    
    def process_cube_images(
        self, 
        cube_folder: str, 
        output_folder: str, 
        **kwargs
    ) -> tuple[bool, dict]:
        """
        處理立方體圖像（兼容舊版接口）
        
        Args:
            cube_folder: 立方體文件夾
            output_folder: 輸出文件夾
            **kwargs: 其他參數（用於兼容性）
            
        Returns:
            tuple: (是否成功, 統計信息字典)
        """
        if self.pipeline is None:
            self.logger.error("處理管線未初始化")
            return False, {"error": "處理管線未初始化"}
        
        try:
            # 使用新的管線
            result = self.pipeline.process_cube_faces(cube_folder, output_folder)
            return result.success, result.stats
            
        except Exception as e:
            self.logger.error(f"處理失敗: {e}")
            return False, {"error": str(e)}


class LegacyBatchProcessor:
    """
    兼容舊版BatchProcessor的包裝器
    """
    
    def __init__(self, detector=None, **kwargs):
        """
        初始化兼容批處理器
        
        Args:
            detector: 舊版檢測器實例（已廢棄）
            **kwargs: 其他參數
        """
        if detector is not None:
            warnings.warn(
                "直接傳遞detector參數已廢棄。請使用ProcessingFactory創建處理器。",
                DeprecationWarning,
                stacklevel=2
            )
        
        self.logger = logging.getLogger(__name__)
        
        try:
            config = get_config() if HAS_CONFIG else None
            self._processor = ProcessingFactory.create_batch_processor(config)
        except Exception as e:
            self.logger.error(f"初始化批處理器失敗: {e}")
            self._processor = None
    
    def process_folder(
        self, 
        input_folder: str, 
        output_folder: str, 
        **kwargs
    ) -> tuple[bool, dict]:
        """
        處理文件夾（兼容舊版接口）
        
        Args:
            input_folder: 輸入文件夾
            output_folder: 輸出文件夾
            **kwargs: 其他參數
            
        Returns:
            tuple: (是否成功, 統計信息字典)
        """
        if self._processor is None:
            self.logger.error("批處理器未初始化")
            return False, {"error": "批處理器未初始化"}
        
        try:
            # 委派給新的批處理器
            # 注意：這裡假設新的批處理器有類似的方法
            if hasattr(self._processor, 'process_folder'):
                return self._processor.process_folder(input_folder, output_folder, **kwargs)
            else:
                self.logger.error("新批處理器缺少process_folder方法")
                return False, {"error": "方法不可用"}
                
        except Exception as e:
            self.logger.error(f"批處理失敗: {e}")
            return False, {"error": str(e)}


# 便捷函數提供舊版風格的創建方式
def create_legacy_processor(detector=None, **kwargs) -> LegacyPanoramaProcessor:
    """
    創建兼容舊版API的處理器
    
    Args:
        detector: 舊版檢測器（已廢棄）
        **kwargs: 其他參數
        
    Returns:
        LegacyPanoramaProcessor實例
    """
    return LegacyPanoramaProcessor(detector=detector, **kwargs)


def create_legacy_batch_processor(detector=None, **kwargs) -> LegacyBatchProcessor:
    """
    創建兼容舊版API的批處理器
    
    Args:
        detector: 舊版檢測器（已廢棄）
        **kwargs: 其他參數
        
    Returns:
        LegacyBatchProcessor實例
    """
    return LegacyBatchProcessor(detector=detector, **kwargs)


# 為了完全向後兼容，提供舊的類名作為別名
PanoramaProcessor = LegacyPanoramaProcessor
BatchProcessor = LegacyBatchProcessor


# 遷移助手函數
def migrate_to_new_api(legacy_processor) -> dict[str, Any]:
    """
    提供遷移指南，幫助用戶遷移到新API
    
    Args:
        legacy_processor: 舊版處理器實例
        
    Returns:
        dict: 遷移指南信息
    """
    migration_guide = {
        "current_api": "legacy",
        "recommended_api": "factory_based",
        "migration_steps": [
            "1. 導入ProcessingFactory: from processing.factory import ProcessingFactory",
            "2. 使用工廠創建處理器: processor = ProcessingFactory.create_panorama_processor()",
            "3. 或創建完整管線: pipeline = ProcessingFactory.create_pipeline()",
            "4. 使用新的處理方法進行處理"
        ],
        "benefits": [
            "更好的依賴管理",
            "更強的可測試性", 
            "更靈活的配置",
            "更好的性能"
        ],
        "breaking_changes": [
            "直接傳遞detector參數已廢棄",
            "某些內部方法可能不再可用",
            "配置方式有所變化"
        ]
    }
    
    return migration_guide


def check_api_compatibility(processor_instance) -> dict[str, Any]:
    """
    檢查API兼容性
    
    Args:
        processor_instance: 處理器實例
        
    Returns:
        dict: 兼容性檢查結果
    """
    compatibility = {
        "is_legacy": isinstance(processor_instance, (LegacyPanoramaProcessor, LegacyBatchProcessor)),
        "api_version": "legacy" if isinstance(processor_instance, (LegacyPanoramaProcessor, LegacyBatchProcessor)) else "modern",
        "recommendations": []
    }
    
    if compatibility["is_legacy"]:
        compatibility["recommendations"].append("考慮遷移到新的ProcessingFactory API")
        compatibility["recommendations"].append("新API提供更好的性能和可測試性")
    
    return compatibility