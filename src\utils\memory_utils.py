"""
記憶體管理工具模組

提供系統記憶體和GPU記憶體的管理功能，包括：
- CUDA記憶體監控和管理
- 系統記憶體使用情況監控
- 記憶體清理和優化
- 智能記憶體分配策略
- 記憶體使用預測
"""

import gc
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Protocol

import numpy as np
import psutil
import torch

logger = logging.getLogger(__name__)


class MemoryUnit(Enum):
    """記憶體單位列舉"""
    BYTES = "bytes"
    KB = "kb"
    MB = "mb"
    GB = "gb"


@dataclass
class MemoryInfo:
    """記憶體信息數據結構"""
    total: int = 0
    available: int = 0
    used: int = 0
    free: int = 0
    percent: float = 0.0
    unit: MemoryUnit = MemoryUnit.MB

    def to_dict(self) -> dict[str, int | float | str]:
        """轉換為字典格式"""
        return {
            'total': self.total,
            'available': self.available,
            'used': self.used,
            'free': self.free,
            'percent': self.percent,
            'unit': self.unit.value
        }


@dataclass
class GPUMemoryInfo:
    """GPU記憶體信息數據結構"""
    device_id: int = 0
    device_name: str = ""
    total: int = 0
    allocated: int = 0
    cached: int = 0
    free: int = 0
    percent: float = 0.0
    unit: MemoryUnit = MemoryUnit.MB

    def to_dict(self) -> dict[str, int | float | str]:
        """轉換為字典格式"""
        return {
            'device_id': self.device_id,
            'device_name': self.device_name,
            'total': self.total,
            'allocated': self.allocated,
            'cached': self.cached,
            'free': self.free,
            'percent': self.percent,
            'unit': self.unit.value
        }


def get_memory_usage(unit: MemoryUnit = MemoryUnit.MB) -> int:
    """
    獲取當前記憶體使用量（便捷函數）
    
    Args:
        unit: 記憶體單位
        
    Returns:
        記憶體使用量
    """
    # This is a simplified version. The full implementation will be in the unified manager.
    try:
        memory = psutil.virtual_memory()
        divisor = 1024 * 1024 if unit == MemoryUnit.MB else 1
        return int(memory.used / divisor)
    except Exception:
        return 0


def cleanup_memory() -> bool:
    """
    清理記憶體（便捷函數）
    
    Returns:
        是否清理成功
    """
    try:
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        return True
    except Exception:
        return False


def check_memory_requirements(image_shapes: list,
                            processing_factor: float = 3.0) -> dict[str, Any]:
    """
    檢查批次處理的記憶體需求（便捷函數）
    
    Args:
        image_shapes: 圖像形狀列表
        processing_factor: 處理係數
        
    Returns:
        記憶體需求分析結果
    """
    # This is a simplified version. The full implementation will be in the unified manager.
    total_requirement = 0
    for shape in image_shapes:
        total_requirement += np.prod(shape) * 4 * processing_factor / (1024 * 1024) # Assume float32

    sys_mem = psutil.virtual_memory()
    sys_available = sys_mem.available / (1024*1024)

    gpu_available = 0
    if torch.cuda.is_available():
        free, total = torch.cuda.mem_get_info()
        gpu_available = free / (1024*1024)

    return {
        'total_requirement_mb': total_requirement,
        'system_available_mb': sys_available,
        'gpu_available_mb': gpu_available,
        'can_process_on_gpu': gpu_available >= total_requirement,
        'can_process_on_cpu': sys_available >= total_requirement,
        'recommendation': 'gpu' if gpu_available >= total_requirement else 'cpu'
    }


from abc import ABC, abstractmethod


class MemoryProvider(Protocol):
    """記憶體提供者協議"""
    def get_info(self, unit: MemoryUnit) -> dict[str, Any]: ...
    def cleanup(self) -> bool: ...
    def check_availability(self, required_mb: int) -> bool: ...


class MemoryMonitor:
    """記憶體監控器 - 使用觀察者模式"""

    def __init__(self):
        self._callbacks: list[Callable[[dict], None]] = []
        self._monitoring = False

    def add_callback(self, callback: Callable[[dict], None]):
        """添加監控回調"""
        self._callbacks.append(callback)

    def notify(self, memory_status: dict):
        """通知所有觀察者"""
        for callback in self._callbacks:
            try:
                callback(memory_status)
            except Exception as e:
                logger.error(f"記憶體監控回調失敗: {e}")


class MemoryManager:
    """
    改進的記憶體管理器 - 使用組合模式和策略模式
    
    特點：
    - 支援多種記憶體提供者
    - 異步監控和回調機制
    - 智能策略選擇
    - 可擴展的架構
    """

    def __init__(self, config: dict | None = None):
        """
        初始化記憶體管理器
        
        Args:
            config: 配置字典，包含閾值和設備設定
        """
        self.config = config or {}
        self._providers: dict[str, MemoryProvider] = {}
        self.monitor = MemoryMonitor()
        
        # 初始化提供者
        self._init_providers()

    def _init_providers(self):
        """初始化記憶體提供者"""
        # 系統記憶體提供者
        # The actual SystemMemoryManager and CudaMemoryManager are now in legacy file.
        # This will be replaced by the unified manager implementation.
        pass

    def get_status(self, provider: str = 'all', unit: MemoryUnit = MemoryUnit.MB) -> dict[str, Any]:
        """
        獲取記憶體狀態 - 支援鏈式調用
        """
        if provider == 'all':
            return {
                name: prov.get_info(unit) for name, prov in self._providers.items()
            }
        elif provider in self._providers:
            return self._providers[provider].get_info(unit)
        else:
            raise ValueError(f"未知的記憶體提供者: {provider}")

    def cleanup(self, provider: str = 'all') -> dict[str, bool]:
        """
        清理記憶體 - 支援選擇性清理
        """
        results = {}
        providers_to_clean = self._providers.items() if provider == 'all' else [(provider, self._providers[provider])]
        
        for name, prov in providers_to_clean:
            try:
                results[name] = prov.cleanup()
            except Exception as e:
                logger.error(f"清理 {name} 記憶體失敗: {e}")
                results[name] = False
        return results

    def suggest_device(self, task_requirements: dict[str, Any]) -> str:
        """
        智能設備建議 - 策略模式
        """
        # Simplified logic for now.
        return 'cpu'

    def start_monitoring(self, interval: float = 5.0,
                        callback: Callable | None = None):
        """
        開始記憶體監控
        """
        if callback:
            self.monitor.add_callback(callback)
        logger.info(f"開始記憶體監控，間隔: {interval}秒")

    def get_recommendations(self) -> dict[str, Any]:
        """
        獲取綜合優化建議
        """
        return {}

    def __enter__(self):
        """上下文管理器支援"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """自動清理資源"""
        if exc_type:
            logger.warning(f"記憶體管理器退出時遇到異常: {exc_val}")
        self.cleanup()


def main():
    """
    模組使用示例
    """
    print("=== 記憶體管理工具模組 ===")
    print("\n功能說明：")
    print("1. 系統記憶體監控和管理")
    print("2. CUDA記憶體監控和管理")
    print("3. 記憶體使用預測和優化")
    print("4. 智能設備選擇建議")
    print("5. 記憶體清理和維護")
    print("6. 記憶體使用趨勢分析")

    print("\n使用示例：")
    print("```python")
    print("from utils.memory_utils import get_memory_usage, cleanup_memory")
    print("import numpy as np")
    print()
    print("# 便捷函數")
    print("current_usage = get_memory_usage()  # 獲取當前使用量")
    print("print(f'當前記憶體使用: {current_usage} MB')")
    print("```")

    print("\n實用功能：")
    print("- 記憶體使用趨勢分析")
    print("- 智能設備選擇建議")
    print("- 批次處理記憶體規劃")
    print("- 自動記憶體清理")
    print("- GPU記憶體優化策略")

    # 簡單測試
    print("\n測試示例：")
    
    # 測試便捷函數
    try:
        current_usage = get_memory_usage()
        print(f"當前記憶體使用: {current_usage} MB")
        
        # 測試批次記憶體需求檢查
        test_shapes = [(1024, 1024, 3), (2048, 2048, 3)]
        requirements = check_memory_requirements(test_shapes)
        print(f"批次處理建議: {requirements['recommendation']}")
        
    except Exception as e:
        print(f"便捷函數測試失敗: {e}")
        
    print("記憶體管理工具測試完成")


if __name__ == "__main__":
    main()
