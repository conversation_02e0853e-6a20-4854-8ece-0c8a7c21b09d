import numpy as np
from numpy.typing import NDArray
from enum import IntEnum
from functools import lru_cache
from typing import Any, Literal, Optional, TypeVar, Union, Literal, Union, overload,List
from scipy.ndimage import map_coordinates
import cv2
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from multiprocessing import Pool
import os
from collections.abc import Sequence
import matplotlib.pyplot as plt
from PIL import Image
import logging

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 輸出到控制台
        logging.FileHandler('image_processing.log')  # 輸出到檔案
    ]
)
logger = logging.getLogger(__name__)

# 假設 img_id 是檔名或資料夾名
exts = ['.jpg', '.png', '.jpeg', '.bmp', '.tif', '.tiff']

DType = TypeVar("DType", bound=np.generic, covariant=True)
_CACHE_SIZE = 8

class Dim(IntEnum):
    X = 0
    Y = 1
    Z = 2
    
class Face(IntEnum):
    """用於 NumPy 向量化的面類型索引。"""

    FRONT = 0
    RIGHT = 1
    BACK = 2
    LEFT = 3
    UP = 4
    DOWN = 5

# 縮放因子
# PYRAMID_SCALE_FACTORS: List[float] = [0.29, 0.597, 1.194 ]
const = 0.3184
PYRAMID_SCALE_FACTORS: List[float] = [ 0.3184 , 0.6368  , 1.2735]
PYRAMID_FACTORS: List[float] = [ 611 , 1222  , 2445]
# 小: 512 + 99 ; 中: 512 *2 + 198 ; 大: 512 *4 +397
# 小: 611 ; 中: 1222 ; 大: 2445
# 記得這邊的大小會影像到base image轉換大小，理論上base一開始會是 1920，但如果已經把cubemap調過就會變2048影響倍率



# 標誌疊加預設值
DEFAULT_LOGO_SCALE = 0.741


def uv2coor(u: NDArray[DType], v: NDArray[DType], h: int, w: int) -> tuple[NDArray[DType], NDArray[DType]]:
    """將球面座標 (r, u, v) 轉換為全景圖座標 (x, y)。

    假設 u 的範圍為 2π，v 的範圍為 π。
    全景圖座標的範圍從 (0.5, 0.5) 到 (h-0.5, w-0.5)。

    參數
    ----------
    uv: ndarray
        形狀為 [..., 2] 的陣列物件。
    h: int
        全景圖影像的高度。
    w: int
        全景圖影像的寬度。

    返回值
    -------
    out: ndarray
        形狀為 [..., 2] 的陣列物件。

    備註
    -----
    在此專案中，e2c 調用 utils.uv2coor(uv, h, w)，其中：

        * uv 在 [-π, π] x [-π/2, π/2] 範圍內
        * coor_x 在 [-0.5, w-0.5] 範圍內
        * coor_y 在 [-0.5, h-0.5] 範圍內
    """
    coor_x = (u / (2 * np.pi) + 0.5) * w - 0.5  # pyright: ignore[reportOperatorIssue]
    coor_y = (-v / np.pi + 0.5) * h - 0.5  # pyright: ignore[reportOperatorIssue]
    return coor_x, coor_y


def xyz2uv(xyz: NDArray[DType]) -> tuple[NDArray[DType], NDArray[DType]]:
    """將笛卡爾座標 (x, y, z) 轉換為球面座標 (r, u, v)，僅輸出 (u, v)。

    參數
    ----------
    xyz: ndarray
        形狀為 [..., 3] 的陣列物件。

    返回值
    -------
    out: ndarray
        形狀為 [..., 2] 的陣列物件，
        陣列中的每個點 i 在 [-π, π] 範圍內。

    備註
    -----
    在此專案中，e2c 調用 utils.xyz2uv(xyz)，其中：

        * xyz 在 [-0.5, 0.5] x [-0.5, 0.5] x [-0.5, 0.5] 範圍內
        * u 在 [-π, π] 範圍內
        * v 在 [-π/2, π/2] 範圍內
        * 輸出陣列的每個點 i 在 [-π, π] x [-π/2, π/2] 範圍內。
    """
    if not np.all((xyz >= -0.5) & (xyz <= 0.5)):
        raise ValueError("xyz 應在 [-0.5, 0.5] 範圍內")
    
    x = xyz[..., 0:1]  # 保持維度但避免複製
    y = xyz[..., 1:2]
    z = xyz[..., 2:3]
    u = np.arctan2(x, z)
    c = np.hypot(x, z)
    v = np.arctan2(y, c)
    return u, v


@lru_cache(_CACHE_SIZE)
def xyzcube(face_w: int) -> NDArray[np.float32]:
    """
    返回單位立方體的 xyz 座標，格式為 [F R B L U D]。

    參數
    ----------
    face_w: int
        指定立方體映射每個面的長度。

    返回值
    -------
    out: ndarray
        維度為 (face_w, face_w * 6, 3) 的陣列物件，
        儲存每個面的標準化立方體座標。
        立方體以原點為中心，因此每個面 k 在 out 中的範圍為 [-0.5, 0.5] x [-0.5, 0.5]。
    """
    if face_w <= 0:
        raise ValueError("face_w 必須為正整數")
    
    out = np.empty((face_w, face_w * 6, 3), np.float32)

    # 一次性創建座標並重複使用
    rng = np.linspace(-0.5, 0.5, num=face_w, dtype=np.float32)
    x, y = np.meshgrid(rng, -rng)

    # 預計算翻轉
    x_flip = np.flip(x, 1)
    y_flip = np.flip(y, 0)

    def face_slice(index):
        return slice_chunk(index, face_w)

    # 前方面 (z = 0.5)
    out[:, face_slice(Face.FRONT), Dim.X] = x
    out[:, face_slice(Face.FRONT), Dim.Y] = y
    out[:, face_slice(Face.FRONT), Dim.Z] = 0.5

    # 右方面 (x = 0.5)
    out[:, face_slice(Face.RIGHT), Dim.X] = 0.5
    out[:, face_slice(Face.RIGHT), Dim.Y] = y
    out[:, face_slice(Face.RIGHT), Dim.Z] = x_flip

    # 後方面 (z = -0.5)
    out[:, face_slice(Face.BACK), Dim.X] = x_flip
    out[:, face_slice(Face.BACK), Dim.Y] = y
    out[:, face_slice(Face.BACK), Dim.Z] = -0.5

    # 左方面 (x = -0.5)
    out[:, face_slice(Face.LEFT), Dim.X] = -0.5
    out[:, face_slice(Face.LEFT), Dim.Y] = y
    out[:, face_slice(Face.LEFT), Dim.Z] = x

    # 上方面 (y = 0.5)
    out[:, face_slice(Face.UP), Dim.X] = x
    out[:, face_slice(Face.UP), Dim.Y] = 0.5
    out[:, face_slice(Face.UP), Dim.Z] = y_flip

    # 下方面 (y = -0.5)
    out[:, face_slice(Face.DOWN), Dim.X] = x
    out[:, face_slice(Face.DOWN), Dim.Y] = -0.5
    out[:, face_slice(Face.DOWN), Dim.Z] = y

    # 由於使用 lru_cache，我們希望返回值是不可變的。
    out.setflags(write=False)
    return out


class EquirecSampler:
    def __init__(
        self,
        coor_x: NDArray,
        coor_y: NDArray,
        order: int,
    ):
        # 將座標加 1 以補償 1 像素的上方填充。
        coor_y = coor_y + 1  # 故意不進行原地操作。
        if cv2 and order in (0, 1, 3):
            self._use_cv2 = True
            if order == 0:
                self._order = cv2.INTER_NEAREST
                nninterpolation = True
            elif order == 1:
                self._order = cv2.INTER_LINEAR
                nninterpolation = False
            elif order == 3:
                self._order = cv2.INTER_CUBIC
                nninterpolation = False
            else:
                raise NotImplementedError

            self._coor_x, self._coor_y = cv2.convertMaps(
                coor_x,
                coor_y,
                cv2.CV_16SC2,
                nninterpolation=nninterpolation,
            )
        else:
            self._use_cv2 = False
            self._coor_x = coor_x
            self._coor_y = coor_y
            self._order = order

    def __call__(self, img: NDArray[DType]) -> NDArray[DType]:
        if img.dtype == np.float16:
            source_dtype = np.float16
        else:
            source_dtype = None

        if source_dtype:
            img = img.astype(np.float32)  # pyright: ignore

        padded = self._pad(img)
        if self._use_cv2:
            # cv2.remap 可以處理 uint8, float32, float64
            out = cv2.remap(padded, self._coor_x, self._coor_y, interpolation=self._order)  # pyright: ignore
        else:
            # map_coordinates 可以處理 uint8, float32, float64
            out = map_coordinates(
                padded,
                (self._coor_y, self._coor_x),
                order=self._order,
            )[..., 0]

        if source_dtype:
            out = out.astype(source_dtype)

        return out  # pyright: ignore[reportReturnType]

    def _pad(self, img: NDArray[DType]) -> NDArray[DType]:
        """在影像上下各添加 1 像素的填充。"""
        w = img.shape[1]
        padded = np.pad(img, ((1, 1), (0, 0)), mode="empty")
        padded[0, :] = np.roll(img[[0]], w // 2, 1)
        padded[-1, :] = np.roll(img[[-1]], w // 2, 1)
        return padded

    @classmethod
    @lru_cache(_CACHE_SIZE)
    def from_cubemap(cls, face_w: int, h: int, w: int, order: int):
        """從立方體映射規格構建 EquirecSampler。

        參數
        ----------
        face_w: int
            輸出立方體映射每個面的長度。
        h: int
            輸入全景圖影像的高度。
        w: int
            輸入全景圖影像的寬度。
        order: int
            樣條插值的階數。參見 ``scipy.ndimage.map_coordinates``。
        """
        xyz = xyzcube(face_w)
        u, v = xyz2uv(xyz)
        coor_x, coor_y = uv2coor(u, v, h, w)
        return cls(coor_x, coor_y, order=order)


CubeFormat = Literal["horizon", "list", "dict", "dice"]
InterpolationMode = Literal[
    "nearest",
    "linear",
    "bilinear",
    "biquadratic",
    "quadratic",
    "quad",
    "bicubic",
    "cubic",
    "biquartic",
    "quartic",
    "biquintic",
    "quintic",
]

_mode_to_order = {
    "nearest": 0,
    "linear": 1,
    "bilinear": 1,
    "biquadratic": 2,
    "quadratic": 2,
    "quad": 2,
    "bicubic": 3,
    "cubic": 3,
    "biquartic": 4,
    "quartic": 4,
    "biquintic": 5,
    "quintic": 5,
}

def mode_to_order(mode: InterpolationMode) -> int:
    """將人類友好的插值字串轉換為整數等價物。

    參數
    ----------
    mode: str

    返回值
    -------
    樣條插值的階數
    """
    try:
        return _mode_to_order[mode.lower()]
    except KeyError:
        raise ValueError(f'未知的模式 "{mode}"。') from None

# def cube_h2list(cube_h: NDArray[DType]) -> list[NDArray[DType]]:
#     ""
#     "將影像分割為 6 個面的列表。"""
#     if cube_h.shape[0] * 6 != cube_h.shape[1]:
#         raise ValueError("立方體映射的寬度必須是其高度的 6 倍。")
#     return np.split(cube_h, 6, axis=1)

# def cube_h2dict(cube_h: NDArray[DType]) -> dict[str, NDArray[DType]]:
#     return dict(zip("FRBLUD", cube_h2list(cube_h)))

def slice_chunk(index: int, width: int, offset=0):
    start = index * width + offset
    return slice(start, start + width)

def cube_h2dice(cube_h: NDArray[DType]) -> NDArray[DType]:
    if cube_h.shape[0] * 6 != cube_h.shape[1]:
        raise ValueError("立方體映射的寬度必須是其高度的 6 倍。")
    w = cube_h.shape[0]
    cube_dice = np.zeros((w * 3, w * 4, cube_h.shape[2]), dtype=cube_h.dtype)
    cube_list = cube_h2list(cube_h)
    # 順序：F R B L U D
    #        ┌────┐
    #        │ U  │
    #   ┌────┼────┼────┬────┐
    #   │ L  │ F  │ R  │ B  │
    #   └────┼────┼────┴────┘
    #        │ D  │
    #        └────┘
    sxy = [(1, 1), (2, 1), (3, 1), (0, 1), (1, 0), (1, 2)]
    for (sx, sy), face in zip(sxy, cube_list):
        cube_dice[slice_chunk(sy, w), slice_chunk(sx, w)] = face
    return cube_dice

class CubeFaceSampler:
    """作為一個類別排列，以便在多次影像插值中重複使用座標計算。"""

    def __init__(
        self,
        tp: NDArray,
        coor_x: NDArray,
        coor_y: NDArray,
        order: int,
        h: int,
        w: int,
    ):
        """初始化取樣器並執行預計算。

        參數
        ----------
        tp: numpy.ndarray
            (H, W) 從 ``equirect_facetype`` 生成的面類型影像
        coor_x: numpy.ndarray
            (H, W) 要取樣的 X 座標
        coor_y: numpy.ndarray
            (H, W) 要取樣的 Y 座標
        order: int
            樣條插值的階數。請參閱 ``scipy.ndimage.map_coordinates``
        h: int
            預期的輸入影像高度
        w: int
            預期的輸入影像寬度
        """
        # 增加 1 以補償 1 像素的周圍填充
        coor_x = coor_x + 1  # 故意不進行原地操作
        coor_y = coor_y + 1  # 故意不進行原地操作

        self._tp = tp
        self._h = h
        self._w = w
        if cv2 and order in (0, 1, 3):
            self._use_cv2 = True
            if order == 0:
                self._order = cv2.INTER_NEAREST
                nninterpolation = True
            elif order == 1:
                self._order = cv2.INTER_LINEAR
                nninterpolation = False
            elif order == 3:
                self._order = cv2.INTER_CUBIC
                nninterpolation = False
            else:
                raise NotImplementedError

            # +2 是來自 self._pad 的填充
            coor_y += np.multiply(tp, h + 2, dtype=np.float32)
            self._coor_x, self._coor_y = cv2.convertMaps(
                coor_x,
                coor_y,
                cv2.CV_16SC2,
                nninterpolation=nninterpolation,
            )
        else:
            self._use_cv2 = False
            self._coor_x = coor_x
            self._coor_y = coor_y
            self._order = order

    def __call__(self, cube_faces: NDArray[DType]) -> NDArray[DType]:
        """取樣立方體面。

        參數
        ----------
        cube_faces: numpy.ndarray
            (6, S, S) 立方體面

        返回
        -------
        numpy.ndarray
            (H, W) 取樣後的影像
        """
        h, w = cube_faces.shape[-2:]
        if h != self._h:
            raise ValueError(f"輸入高度 {h} 與預期高度 {self._h} 不符。")
        if w != self._w:
            raise ValueError(f"輸入寬度 {w} 與預期寬度 {self._w} 不符。")

        if cube_faces.dtype == np.float16:
            source_dtype = np.float16
        else:
            source_dtype = None

        if source_dtype:
            cube_faces = cube_faces.astype(np.float32)  # pyright: ignore

        padded = self._pad(cube_faces)
        if self._use_cv2:
            w = padded.shape[-1]
            v_img = padded.reshape(-1, w)

            # cv2.remap 可以處理 uint8, float32, float64
            out = cv2.remap(v_img, self._coor_x, self._coor_y, interpolation=self._order)  # pyright: ignore
        else:
            # map_coordinates 可以處理 uint8, float32, float64
            out = map_coordinates(padded, (self._tp, self._coor_y, self._coor_x), order=self._order)

        if source_dtype:
            out = out.astype(source_dtype)

        return out  # pyright: ignore[reportReturnType]

    def _pad(self, cube_faces: NDArray[DType]) -> NDArray[DType]:
        """為每個立方體面添加 1 像素的填充。"""
        ABOVE = (0, slice(None))
        BELOW = (-1, slice(None))
        LEFT = (slice(None), 0)
        RIGHT = (slice(None), -1)
        padded = np.pad(cube_faces, ((0, 0), (1, 1), (1, 1)), mode="empty")

        # 填充上方/下方
        padded[Face.FRONT][ABOVE] = padded[Face.UP, -2, :]
        padded[Face.FRONT][BELOW] = padded[Face.DOWN, 1, :]
        padded[Face.RIGHT][ABOVE] = padded[Face.UP, ::-1, -2]
        padded[Face.RIGHT][BELOW] = padded[Face.DOWN, :, -2]
        padded[Face.BACK][ABOVE] = padded[Face.UP, 1, ::-1]
        padded[Face.BACK][BELOW] = padded[Face.DOWN, -2, ::-1]
        padded[Face.LEFT][ABOVE] = padded[Face.UP, :, 1]
        padded[Face.LEFT][BELOW] = padded[Face.DOWN, ::-1, 1]
        padded[Face.UP][ABOVE] = padded[Face.BACK, 1, ::-1]
        padded[Face.UP][BELOW] = padded[Face.FRONT, 1, :]
        padded[Face.DOWN][ABOVE] = padded[Face.FRONT, -2, :]
        padded[Face.DOWN][BELOW] = padded[Face.BACK, -2, ::-1]

        # 填充左邊/右邊
        padded[Face.FRONT][LEFT] = padded[Face.LEFT, :, -2]
        padded[Face.FRONT][RIGHT] = padded[Face.RIGHT, :, 1]
        padded[Face.RIGHT][LEFT] = padded[Face.FRONT, :, -2]
        padded[Face.RIGHT][RIGHT] = padded[Face.BACK, :, 1]
        padded[Face.BACK][LEFT] = padded[Face.RIGHT, :, -2]
        padded[Face.BACK][RIGHT] = padded[Face.LEFT, :, 1]
        padded[Face.LEFT][LEFT] = padded[Face.BACK, :, -2]
        padded[Face.LEFT][RIGHT] = padded[Face.FRONT, :, 1]
        padded[Face.UP][LEFT] = padded[Face.LEFT, 1, :]
        padded[Face.UP][RIGHT] = padded[Face.RIGHT, 1, ::-1]
        padded[Face.DOWN][LEFT] = padded[Face.LEFT, -2, ::-1]
        padded[Face.DOWN][RIGHT] = padded[Face.RIGHT, -2, :]

        return padded

    @classmethod
    @lru_cache(_CACHE_SIZE)
    def from_equirec(cls, face_w: int, h: int, w: int, order: int):
        """從等距矩形規格構建 CubemapSampler。

        參數
        ----------
        face_w: int
            輸入立方圖每個面的長度
        h: int
            輸出等距矩形影像高度
        w: int
            輸出等距矩形影像寬度
        order: int
            樣條插值的階數。請參閱 ``scipy.ndimage.map_coordinates``
        """
        u, v = equirect_uvgrid(h, w)

        # 獲取每個像素的面 ID：0F 1R 2B 3L 4U 5D
        tp = equirect_facetype(h, w)

        coor_x = np.empty((h, w), dtype=np.float32)
        coor_y = np.empty((h, w), dtype=np.float32)
        face_w2 = face_w / 2

        # 中間帶（前/右/後/左）
        mask = tp < Face.UP
        angles = u[mask] - (np.pi / 2 * tp[mask])
        tan_angles = np.tan(angles)
        cos_angles = np.cos(angles)
        tan_v = np.tan(v[mask])

        coor_x[mask] = face_w2 * tan_angles
        coor_y[mask] = -face_w2 * tan_v / cos_angles

        mask = tp == Face.UP
        c = face_w2 * np.tan(np.pi / 2 - v[mask])
        coor_x[mask] = c * np.sin(u[mask])
        coor_y[mask] = c * np.cos(u[mask])

        mask = tp == Face.DOWN
        c = face_w2 * np.tan(np.pi / 2 - np.abs(v[mask]))
        coor_x[mask] = c * np.sin(u[mask])
        coor_y[mask] = -c * np.cos(u[mask])

        # 最終正規化
        coor_x += face_w2
        coor_y += face_w2
        coor_x.clip(0, face_w, out=coor_x)
        coor_y.clip(0, face_w, out=coor_y)

        return cls(tp, coor_x, coor_y, order, face_w, face_w)

def cube_h2list(cube_h: NDArray[DType]) -> list[NDArray[DType]]:
    """將影像分割成 6 個面的列表。"""
    if cube_h.shape[0] * 6 != cube_h.shape[1]:
        raise ValueError("立方圖的寬度必須是其高度的 6 倍。")
    return np.split(cube_h, 6, axis=1)


def cube_list2h(cube_list: list[NDArray[DType]]) -> NDArray[DType]:
    """將 6 個面影像列表並排拼接。"""
    if len(cube_list) != 6:
        raise ValueError(f"必須提供 6 個元素來構建立方體；得到了 {len(cube_list)} 個。")
    for i, face in enumerate(cube_list):
        if face.shape != cube_list[0].shape:
            raise ValueError(
                f"面 {i} 的形狀 {face.shape} 與第一個面的形狀 {cube_list[0].shape} 不符。"
            )
        if face.dtype != cube_list[0].dtype:
            raise ValueError(
                f"面 {i} 的數據類型 {face.dtype} 與第一個面的數據類型 {cube_list[0].dtype} 不符。"
            )

    return np.concatenate(cube_list, axis=1, dtype=cube_list[0].dtype)


def cube_h2dict(cube_h: NDArray[DType]) -> dict[str, NDArray[DType]]:
    """將立方圖轉換為字典格式。"""
    return dict(zip("FRBLUD", cube_h2list(cube_h)))


def cube_dict2list(cube_dict: dict[Any, NDArray[DType]], face_k: Optional[Sequence] = None) -> list[NDArray[DType]]:
    """將字典格式的立方圖轉換為列表。"""
    face_k = face_k or "FRBLUD"
    if len(face_k) != 6:
        raise ValueError(f"必須提供 6 個 face_k 鍵來構建立方體；得到了 {len(face_k)} 個。")
    return [cube_dict[k] for k in face_k]


def cube_dict2h(cube_dict: dict[Any, NDArray[DType]], face_k: Optional[Sequence] = None) -> NDArray[DType]:
    """將字典格式的立方圖轉換為並排拼接的影像。"""
    return cube_list2h(cube_dict2list(cube_dict, face_k))


def cube_h2dice(cube_h: NDArray[DType]) -> NDArray[DType]:
    """將立方圖轉換為骰子展開圖格式。"""
    if cube_h.shape[0] * 6 != cube_h.shape[1]:
        raise ValueError("立方圖的寬度必須是其高度的 6 倍。")
    w = cube_h.shape[0]
    cube_dice = np.zeros((w * 3, w * 4, cube_h.shape[2]), dtype=cube_h.dtype)
    cube_list = cube_h2list(cube_h)
    # 順序：F R B L U D
    #        ┌────┐
    #        │ U  │
    #   ┌────┼────┼────┬────┐
    #   │ L  │ F  │ R  │ B  │
    #   └────┼────┼────┴────┘
    #        │ D  │
    #        └────┘
    sxy = [(1, 1), (2, 1), (3, 1), (0, 1), (1, 0), (1, 2)]
    for (sx, sy), face in zip(sxy, cube_list):
        cube_dice[slice_chunk(sy, w), slice_chunk(sx, w)] = face
    return cube_dice


def cube_dice2list(cube_dice: NDArray[DType]) -> list[NDArray[DType]]:
    """將骰子展開圖轉換為 6 個面的列表。"""
    if cube_dice.shape[0] % 3 != 0:
        raise ValueError("骰子影像高度必須是 3 的倍數。")
    w = cube_dice.shape[0] // 3
    if cube_dice.shape[1] != w * 4:
        raise ValueError(f"骰子寬度必須是 4 個“面”（4x{w}={4*w}）寬。")
    # 順序：F R B L U D
    #        ┌────┐
    #        │ U  │
    #   ┌────┼────┼────┬────┐
    #   │ L  │ F  │ R  │ B  │
    #   └────┼────┼────┴────┘
    #        │ D  │
    #        └────┘
    out = []
    sxy = [(1, 1), (2, 1), (3, 1), (0, 1), (1, 0), (1, 2)]
    for sx, sy in sxy:
        out.append(cube_dice[slice_chunk(sy, w), slice_chunk(sx, w)])
    return out


def cube_dice2h(cube_dice: NDArray[DType]) -> NDArray[DType]:
    """將骰子展開圖轉換為並排拼接的立方圖。"""
    if cube_dice.shape[0] % 3 != 0:
        raise ValueError("骰子影像高度必須是 3 的倍數。")
    w = cube_dice.shape[0] // 3
    if cube_dice.shape[1] != w * 4:
        raise ValueError(f"骰子寬度必須是 4 個“面”（4x{w}={4*w}）寬。")
    cube_h = np.zeros((w, w * 6, cube_dice.shape[2]), dtype=cube_dice.dtype)
    # 順序：F R B L U D
    #        ┌────┐
    #        │ U  │
    #   ┌────┼────┼────┬────┐
    #   │ L  │ F  │ R  │ B  │
    #   └────┼────┼────┴────┘
    #        │ D  │
    #        └────┘
    sxy = [(1, 1), (2, 1), (3, 1), (0, 1), (1, 0), (1, 2)]
    for i, (sx, sy) in enumerate(sxy):
        cube_h[:, slice_chunk(i, w)] = cube_dice[slice_chunk(sy, w), slice_chunk(sx, w)]
    return cube_h


@lru_cache(_CACHE_SIZE)
def equirect_uvgrid(h: int, w: int) -> tuple[NDArray[np.float32], NDArray[np.float32]]:
    u = np.linspace(-np.pi, np.pi, num=w, dtype=np.float32)
    v = np.linspace(np.pi / 2, -np.pi / 2, num=h, dtype=np.float32)
    uu, vv = np.meshgrid(u, v)
    # Since we are using lru_cache, we want the return value to be immutable.
    uu.setflags(write=False)
    vv.setflags(write=False)
    return uu, vv  # pyright: ignore[reportReturnType]




class Projection:
    """計算全景與立方體投影轉換公式"""
    def __init__(self, h: int, w: int, mode: InterpolationMode = "cubic",):
        
        self.h = h
        self.w = w
        self.face_w = w//4
        self.mode = mode
        self.order = mode_to_order(mode)
        
        # 延遲初始化
        self._E2Csampler = None  
        self._C2Esampler = None  

    def equirec_to_cubemap(self,
        e_img: NDArray[DType],
        cube_format: CubeFormat = "list",
    ) -> Union[NDArray[DType], list[NDArray[DType]], dict[str, NDArray[DType]]]:
        """將全景圖影像轉換為立方體映射。

        參數
        ----------
        e_img: ndarray
            全景圖影像，形狀為 [H, W] 或 [H, W, *]。
        cube_format: Literal["horizon", "list", "dict", "dice"]
            返回立方體映射的格式。
        返回值
        -------
        Union[NDArray, list[NDArray], dict[str, NDArray]]
            根據 `cube_format` 指定的格式返回立方體映射。
        """
        if e_img.ndim not in (2, 3):
            raise ValueError("e_img 必須具有 2 或 3 個維度。")
        if e_img.ndim == 2:
            e_img = e_img[..., None]
            squeeze = True
        else:
            squeeze = False
        
        if self._E2Csampler is None:
            self._E2Csampler = EquirecSampler.from_cubemap(self.face_w, self.h, self.w, self.order)
        
        cubemap = np.stack(
            [self._E2Csampler(e_img[..., i]) for i in range(e_img.shape[2])],
            axis=-1,
            dtype=e_img.dtype,
        )

        if cube_format == "horizon":
            if squeeze:
                cubemap = cubemap[..., 0]
        elif cube_format == "list":
            cubemap = cube_h2list(cubemap)
            if squeeze:
                cubemap = [x[..., 0] for x in cubemap]
        elif cube_format == "dict":
            cubemap = cube_h2dict(cubemap)
            if squeeze:
                cubemap = {k: v[..., 0] for k, v in cubemap.items()}
        elif cube_format == "dice":
            cubemap = cube_h2dice(cubemap)
            if squeeze:
                cubemap = cubemap[..., 0]
        else:
            raise NotImplementedError

        return cubemap

    def cubemap_to_equirec(self,
        cubemap: Union[NDArray[DType], list[NDArray[DType]], dict[str, NDArray[DType]]],
        h: int,
        w: int,
        mode: InterpolationMode = "cubic",
        cube_format: CubeFormat = "horizon",
    ) -> NDArray:
        """將立方體影像轉為全景。

        參數
         ----------
        立方體影像: Union[NDArray, list[NDArray], dict[str, NDArray]]
        h, w: 全景的長寬
         
        mode: Literal["bilinear", "nearest"]
            Interpolation mode.
        cube_format: Literal["horizon", "list", "dict", "dice"]
            立方體進來形式。

        返回值
        -------
        np.ndarray
            全景影像。
        """
        if self._C2Esampler is None:
            self._C2Esampler = CubeFaceSampler.from_equirec(self.face_w, self.h, self.w, self.order)  # 假設方法名
        
        if w % 8 != 0:
            raise ValueError("w must be a multiple of 8.")

        if cube_format == "horizon":
            if not isinstance(cubemap, np.ndarray):
                raise TypeError('cubemap must be a numpy array for cube_format="horizon"')
            if cubemap.ndim == 2:
                cubemap = cubemap[..., None]
                squeeze = True
            else:
                squeeze = False
            cube_faces = cube_h2list(cubemap)
        elif cube_format == "list":
            if not isinstance(cubemap, list):
                raise TypeError('cubemap must be a list for cube_format="list"')
            if len({x.shape for x in cubemap}) != 1:
                raise ValueError("All cubemap elements must have same shape")
            if cubemap[0].ndim == 2:
                cube_faces = [x[..., None] for x in cubemap]
                squeeze = True
            else:
                cube_faces = cubemap
                squeeze = False
        elif cube_format == "dict":
            if not isinstance(cubemap, dict):
                raise TypeError('cubemap must be a dict for cube_format="dict"')
            if len({x.shape for x in cubemap.values()}) != 1:
                raise ValueError("All cubemap elements must have same shape")
            if cubemap["F"].ndim == 2:
                cubemap = {k: v[..., None] for k, v in cubemap.items()}
                squeeze = True
            else:
                squeeze = False
            cube_faces = cube_dict2list(cubemap)
        elif cube_format == "dice":
            if not isinstance(cubemap, np.ndarray):
                raise TypeError('cubemap must be a numpy array for cube_format="dice"')
            if cubemap.ndim == 2:
                cubemap = cubemap[..., None]
                squeeze = True
            else:
                squeeze = False
            cube_faces = cube_dice2list(cubemap)
        else:
            raise ValueError('Unknown cube_format "{cube_format}".')

        cube_faces = np.stack(cube_faces)

        if cube_faces.shape[1] != cube_faces.shape[2]:
            raise ValueError("Cubemap faces must be square.")
        face_w = cube_faces.shape[2]

        # sampler = CubeFaceSampler.from_equirec(face_w, h, w, self.order)
        equirec = np.empty((h, w, cube_faces.shape[3]), dtype=cube_faces[0].dtype)
        for i in range(cube_faces.shape[3]):
            equirec[..., i] = self._C2Esampler(cube_faces[..., i])

        return equirec[..., 0] if squeeze else equirec


@lru_cache(_CACHE_SIZE)
def equirect_facetype(h: int, w: int) -> NDArray[np.int32]:
    """Generate a 2D equirectangular segmentation image for each facetype.

    The generated segmentation image has lookup:

    * 0 - front
    * 1 - right
    * 2 - back
    * 3 - left
    * 4 - up
    * 5 - down

    See ``Face``.

    Example:

        >>> equirect_facetype(8, 12)
            array([[4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4],
                   [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4],
                   [2, 3, 3, 3, 0, 0, 0, 1, 1, 1, 2, 2],
                   [2, 3, 3, 3, 0, 0, 0, 1, 1, 1, 2, 2],
                   [2, 3, 3, 3, 0, 0, 0, 1, 1, 1, 2, 2],
                   [2, 3, 3, 3, 0, 0, 0, 1, 1, 1, 2, 2],
                   [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
                   [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5]], dtype=int32)

    Parameters
    ----------
    h: int
        Desired output height.
    w: int
        Desired output width. Must be a multiple of 4.

    Returns
    -------
    ndarray
        2D numpy equirectangular segmentation image for the 6 face types.
    """
    if w % 4:
        raise ValueError(f"w must be a multiple of 4. Got {w}.")

    # Create the pattern [2,3,3,0,0,1,1,2]
    w4 = w // 4
    w8 = w // 8
    h3 = h // 3
    tp = np.empty((h, w), dtype=np.int32)
    tp[:, :w8] = 2
    tp[:, w8 : w8 + w4] = 3
    tp[:, w8 + w4 : w8 + 2 * w4] = 0
    tp[:, w8 + 2 * w4 : w8 + 3 * w4] = 1
    tp[:, w8 + 3 * w4 :] = 2

    # Prepare ceil mask
    idx = np.linspace(-np.pi, np.pi, w4) / 4
    idx = np.round(h / 2 - np.arctan(np.cos(idx)) * h / np.pi).astype(np.int32)
    # It'll never go past a third of the image, so only process that for optimization
    mask = np.empty((h3, w4), np.bool_)
    row_idx = np.arange(h3, dtype=np.int32)[:, None]
    np.less(row_idx, idx[None], out=mask)

    flip_mask = np.flip(mask, 0)
    tp[:h3, :w8][mask[:, w8:]] = Face.UP
    tp[-h3:, :w8][flip_mask[:, w8:]] = Face.DOWN
    for i in range(3):
        s = slice_chunk(i, w4, w8)
        tp[:h3, s][mask] = Face.UP
        tp[-h3:, s][flip_mask] = Face.DOWN
    remainder = w - s.stop  # pyright: ignore[reportPossiblyUnboundVariable]
    tp[:h3, s.stop :][mask[:, :remainder]] = Face.UP  # pyright: ignore[reportPossiblyUnboundVariable]
    tp[-h3:, s.stop :][flip_mask[:, :remainder]] = Face.DOWN  # pyright: ignore[reportPossiblyUnboundVariable]

    # Since we are using lru_cache, we want the return value to be immutable.
    tp.setflags(write=False)

    return tp

# 自定義的影像載入函數
def cv_imread(file_path):
    """從檔案路徑載入影像並解碼"""
    cv_img = cv2.imdecode(np.fromfile(file_path, dtype=np.uint8), -1)
    return cv_img

def load_image(img_id):
    """載入單張影像並返回影像與檔案名稱"""
    img_path = os.path.join(image_folder, img_id)
    return cv_imread(img_path), img_id


def pil_to_cv(pil_image):
        
    open_cv_image = np.array(pil_image)
    # 轉換 RGB 為 BGR
    open_cv_image = cv2.cvtColor(open_cv_image, cv2.COLOR_RGB2BGR)
    return open_cv_image


def cv_imwrite(file_path, image):
        
      # new_image = pil_to_cv(image)
      ext = os.path.splitext(file_path)[1]  # 獲取文件擴展名
      success, encoded_image = cv2.imencode(ext, image)
      if success:
            encoded_image.tofile(file_path)
            
      return success

class ImageCutter:
    
        def __init__(self, cube_dict, thumbnail, logo_img, output_scene_folder):
            
                # 映射表
                self.cube_index_map = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}
                # self.cubemaps = {self.cube_index_map[key]: value for key, value in cube_dict.items()}
                # 映射表
                self.cube_index_map = {"F": 0, "R": 1, "B": 2, "L": 3, "U": 4, "D": 5}
                
                self.logo_img = logo_img
                
                # 建立 cubemaps 並處理 logo
                self.cubemaps = {}
                cube_images= []
                for key, value in cube_dict.items():
                    cube_index = self.cube_index_map[key]
                    
                    # 如果是面5且有標誌，添加標誌
                    if cube_index == 5 and self.logo_img is not None:
                        value = self._overlay_logo(value, scale=0.741)
                    
                    # 如果要先resize的話
                    # new_cubmap =  cv2.resize(value, (2048, 2048), interpolation=cv2.INTER_CUBIC) 
                    self.cubemaps[cube_index] = value
                    cube_images.append(value)
                    
                self.output_scene_folder = output_scene_folder
                
                # self.preview = preview
                self.preview = np.hstack(cube_images)

                self.thumbnail = thumbnail
        
        def create_directory(self, directory):
            
            if not os.path.exists(directory):
                  os.makedirs(directory)
                  
        def create_folder_structure(self):
                """
                創建所需的資料夾結構，
                        # 順序：F R B L U D
                        #        ┌────┐
                        #        │ U  │
                        #   ┌────┼────┼────┬────┐
                        #   │ L  │ F  │ R  │ B  │
                        #   └────┼────┼────┴────┘
                        #        │ D  │
                        #        └────┘
                        
                        對應：
                        
                        
                        # 順序：F R B L U D
                        #        ┌────┐
                        #        │ 4  │
                        #   ┌────┼────┼────┬────┐
                        #   │ 3  │ 0  │ 1  │ 2  │
                        #   └────┼────┼────┴────┘
                        #        │ 5  │
                        #        └────┘
        
                """
                if not os.path.exists(self.output_scene_folder):
                        os.makedirs(self.output_scene_folder, exist_ok=True)
                        
                        for subfolder in ['0', '1', '2', '3', '4', '5', 'html5']:
                                os.makedirs(os.path.join(self.output_scene_folder, subfolder), exist_ok=True)
        
        
        def pyramid_image(self, img_id):
            
                # 讀取機底影像
                base_image = self.cubemaps[img_id]
                # 使用 ThreadPoolExecutor 同時進行三個縮放操作
                with ThreadPoolExecutor(max_workers=3) as executor:
                        large_future = executor.submit(self.resize_to_fixed_size, base_image, PYRAMID_FACTORS[2])
                        medium_future = executor.submit(self.resize_to_fixed_size, base_image, PYRAMID_FACTORS[1])
                        small_future = executor.submit(self.resize_to_fixed_size, base_image, PYRAMID_FACTORS[0])

                        large_image = large_future.result()
                        medium_image = medium_future.result()
                        small_image = small_future.result()

                # 保存影像切片
                base_dir = os.path.join(self.output_scene_folder, str(img_id))
                self.create_directory(base_dir)

                large_dir = os.path.join(base_dir, '2')
                medium_dir = os.path.join(base_dir, '1')
                small_dir = os.path.join(base_dir, '0')

                self.create_directory(large_dir)
                self.create_directory(medium_dir)
                self.create_directory(small_dir)

                self.save_image_slices(large_image, large_dir)
                self.save_image_slices(medium_image, medium_dir)
                self.save_image_slices(small_image, small_dir)

        def resize_image(self, base_image, scale_factor):
            new_width = int(base_image.shape[1] * scale_factor)
            new_height = int(base_image.shape[0] * scale_factor)
            resized_image = cv2.resize(base_image, (new_width, new_height))
            return resized_image
        
        def resize_to_fixed_size(self, image, size):
            
            # 使用 LANCZOS 重採樣以獲得更好的質量
            resized_image = cv2.resize(image, (size, size), interpolation=cv2.INTER_LANCZOS4)
    
            return resized_image

            
        def save_image_slices(self, image, folder):
            height, width, _ = image.shape
            slice_size = 512
            num_slices_x = (width + slice_size - 1) // slice_size
            num_slices_y = (height + slice_size - 1) // slice_size

            def save_slice(x, y):
                left = x * slice_size
                upper = y * slice_size
                right = min(left + slice_size, width)
                lower = min(upper + slice_size, height)

                slice_image = image[upper:lower, left:right]
                slice_path = os.path.join(folder, f'{y}_{x}.jpg')
                cv_imwrite(slice_path, slice_image)

            with ThreadPoolExecutor() as executor:
                  futures = [executor.submit(save_slice, x, y) for y in range(num_slices_y) for x in range(num_slices_x)]
                  for future in futures:
                        future.result()
                        
        def save_preview(self):
            
                # 因為本身讀檔有opencv跟PIL各自讀取，兩者通道順序不一樣需轉換
                preview_rgb = cv2.cvtColor(self.preview, cv2.COLOR_RGB2BGR)
                pil_preview = Image.fromarray(preview_rgb)
                pil_preview_resized = pil_preview.resize((1536, 256))
                pil_preview_resized.save(os.path.join(self.output_scene_folder, 'preview.jpg'))
        
        def save_thumbnail(self):
                  
                thumbnail_rgb = cv2.cvtColor(self.thumbnail , cv2.COLOR_RGB2BGR)
                pil_thumbnail = Image.fromarray(thumbnail_rgb)
                pil_thumbnail_resized = pil_thumbnail.resize((400, 200))
                pil_thumbnail_resized.save(os.path.join(self.output_scene_folder, 'thumbnail.jpg'))
                # cv_imwrite(os.path.join(self.output_scene_folder, 'thumbnail.jpg'),image_resized)
                
        def process_pyramid_image(self,i):
                
            self.pyramid_image(i)
            
        def save_image(self):
            
                # 存preview
                self.save_preview()
                
                # 存thumbnail_image
                self.save_thumbnail()
                
                # 存html
                html5_folder = os.path.join(self.output_scene_folder, 'html5')
                os.makedirs(html5_folder, exist_ok=True) # 確保輸出資料夾存在
                with ThreadPoolExecutor(max_workers=5) as executor:
                                futures = []
                                for i in range(6):
                                
                                        new_cubemap = self.cubemaps[i] # 如果在init就已經resize則不需要
                                        
                                        # resized_cubemap = cv2.resize(new_cubemap, (2048, 2048), interpolation=cv2.INTER_CUBIC)
                                        resized_cubemap = cv2.resize(new_cubemap, (2048, 2048), interpolation=cv2.INTER_LANCZOS4)

                                        
                                        # resized_new_cubemap = cv2.cvtColor(resized_cubemap, cv2.COLOR_BGR2RGB)
                                        output_path = os.path.join(html5_folder, f'{i}.jpg')
                                        
                                        future = executor.submit(cv_imwrite, output_path, resized_cubemap)
                                        futures.append(future)  
                                        
                                # 等待所有任务完成
                                for future in futures:
                                        future.result()
                

                with ThreadPoolExecutor(max_workers=6) as executor:
                        # 提交每個金字塔影像的處理任務
                        futures = [executor.submit(self.process_pyramid_image, i) for i in range(6)]
                        
                        # 等待所有任務完成
                        for future in futures:
                                future.result()
                                
        def _overlay_logo(self, face, scale=0.741):
            """
            將標誌疊加到面上

            Args:
                face: 需要添加標誌的面
                scale: 標誌相對於面寬度的比例，默認0.75

            Returns:
                添加標誌後的面
            """
            # 確保有標誌可以疊加
            if self.logo_img is None:
                return face

            # 複製面以避免修改原始數據
            face_copy = face.copy()

            # 如果面是灰度圖，轉換為彩色
            if len(face_copy.shape) == 2:
                face_copy = cv2.cvtColor(face_copy, cv2.COLOR_GRAY2BGR)

            # 獲取面的高和寬
            face_h, face_w = face_copy.shape[:2]

            # 調整標誌大小為面寬度的指定比例
            logo_width = int(face_w * scale)
            logo_height = int(
                self.logo_img.shape[0] * (logo_width / self.logo_img.shape[1]))
            resized_logo = cv2.resize(self.logo_img, (logo_width, logo_height))

            # 計算標誌應該放置的位置（居中）
            x_offset = (face_w - logo_width) // 2
            y_offset = (face_h - logo_height) // 2

            # 檢查標誌是否有Alpha通道
            if resized_logo.shape[2] == 4:
                # 有Alpha通道的疊加
                # 提取Alpha通道
                alpha = resized_logo[:, :, 3] / 255.0
                alpha = alpha[:, :, np.newaxis]  # 擴展維度以便可以直接做乘法

                roi = face_copy[y_offset:y_offset +
                                logo_height, x_offset:x_offset+logo_width]
                # 使用向量化操作替代循環
                roi[:] = (1 - alpha) * roi + alpha * resized_logo[:, :, :3]
            else:
                # 沒有Alpha通道，直接覆蓋
                face_copy[y_offset:y_offset+logo_height,
                        x_offset:x_offset+logo_width] = resized_logo[:, :, :3]

            return face_copy
    
import shutil
from ultralytics import YOLO
import torch

def load_models(model_path1, model_path2):
    """載入兩個YOLO模型"""
    try:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"使用設備: {device}")
        model1 = YOLO(model_path1)
        model2 = YOLO(model_path2)
        model1.to(device)
        model2.to(device)
        return model1, model2
    except Exception as e:
        logger.error(f"載入模型失敗: {e}")
        return None, None

def detect_and_blur_face(face_image, models, face_key, conf_threshold=0.25):
    """對單一立方體面進行偵測並模糊化"""
    # 如果是面 'U' (上)，則跳過偵測
    if face_key == 'U':
        return face_image

    face_copy = face_image.copy()
    face_h, face_w = face_copy.shape[:2]
    total_pixels = face_h * face_w
    pixel_threshold = total_pixels * 0.03

    all_boxes = []
    for model in models:
        results = model.predict(face_copy, conf=conf_threshold, verbose=False)
        for r in results:
            all_boxes.extend(r.boxes.xyxy)

    if not all_boxes:
        return face_copy

    for box in all_boxes:
        x1, y1, x2, y2 = map(int, box)
        box_w = x2 - x1
        box_h = y2 - y1
        box_pixels = box_w * box_h

        # 檢查框的像素是否大於閾值，如果是則捨棄這個框
        if box_pixels > pixel_threshold:
            continue
            
        roi = face_copy[y1:y2, x1:x2]
        if roi.size > 0:
            # 模糊核心的大小可以根據需求調整
            blurred_roi = cv2.GaussianBlur(roi, (99, 99), 30)
            face_copy[y1:y2, x1:x2] = blurred_roi
            
    return face_copy


if __name__ == "__main__":
    # 開始計時
    total_start_time = time.time()

    cpu_cores = os.cpu_count()
    logger.info(f"當前電腦 CPU 有 {cpu_cores} 個核心")

    # 設置並行參數
    io_workers = cpu_cores * 1  # I/O 密集型任務
    compute_processes = cpu_cores // 2  # 計算密集型任務
    logger.info(f"影像載入使用 {io_workers} 個線程")
    logger.info(f"投影轉換使用 {compute_processes} 個進程")


    image_folder = input("請輸入全景圖像路徑 : ")
    logo_path = input("請輸入Logo路徑 (可留空): ")
    output_path =  input("請輸入輸出路徑 : ")
    model1_path = input("請輸入模型1的路徑 (.pt file): ")
    model2_path = input("請輸入模型2的路徑 (.pt file): ")
    
    conf_input = 0.01
    try:
        conf_threshold = float(conf_input) if conf_input else 0.25
        if not (0.0 <= conf_threshold <= 1.0):
            logger.warning("輸入值無效，將使用預設值 0.25")
            conf_threshold = 0.25
    except ValueError:
        logger.info("未輸入或輸入格式錯誤，將使用預設值 0.25")
        conf_threshold = 0.25
    logger.info(f"將使用信心度閾值: {conf_threshold}")

    # 確保輸出目錄存在
    os.makedirs(output_path, exist_ok=True)
    logger.info(f"輸出目錄 '{output_path}' 已確認存在。")

    # 載入模型
    logger.info("正在載入YOLO模型...")
    models = load_models(model1_path, model2_path)
    if models[0] is None:
        logger.error("模型載入失敗，退出程式。")
        exit(1)
    logger.info("模型載入成功。")


    # 獲取原始圖像列表
    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    image_ids = [f for f in image_files if f.lower().endswith(tuple(exts))]
    total_images = len(image_ids)
    logger.info(f"找到 {total_images} 張影像待處理")

    # 從第一張影像獲取尺寸
    if not image_ids:
        logger.error("沒有找到任何影像，退出程式")
        exit(1)

    logo_img = None
    if logo_path and logo_path.strip():
        if os.path.exists(logo_path):
            logo_img = cv_imread(logo_path)
            if logo_img is not None:
                # 檢查是否有Alpha通道
                if logo_img.shape[2] < 4:
                    # 創建Alpha通道
                    alpha = np.ones(
                        logo_img.shape[:2], dtype=np.uint8) * 255
                    logo_img = cv2.merge([
                        logo_img[:, :, 0],
                        logo_img[:, :, 1],
                        logo_img[:, :, 2],
                        alpha
                    ])
            else:
                logger.warning(f"載入標誌失敗，請檢查檔案是否為正確的圖片格式: {logo_path}")
        else:
            logger.warning(f"提供的Logo路徑不存在: '{logo_path}'，將不疊加Logo。")
    else:
        logger.warning("警告：無Logo，程式將繼續使用")
                    
    
    image_path = os.path.join(image_folder, image_ids[0])
    image = cv_imread(image_path)
    if image is None:
        logger.error(f"無法載入第一張影像 {image_ids[0]}，退出程式")
        exit(1)
    h, w, _ = image.shape
    project_cube = Projection(h, w)

    # 批次大小
    batch_size = 10
    processed_count = 0  # 追蹤成功處理的張數

    # 分批處理所有圖像
    for batch_start in range(0, len(image_ids), batch_size):
        batch_end = min(batch_start + batch_size, len(image_ids))
        # logger.info(f"處理第 {batch_start} 到 {batch_end - 1} 張圖像...")

        # 並行載入影像
        
        with ThreadPoolExecutor(max_workers=io_workers) as executor:
                images_with_ids = list(executor.map(load_image, image_ids[batch_start:batch_end]))

        # 過濾和檢查影像並整理為字典
        valid_images = {}
        for img, img_id in images_with_ids:
            
            # 檢查輸出檔案是否已存在
            base_name, ext = os.path.splitext(img_id)
            output_file_path = os.path.join(output_path, f"{base_name}{ext}")
            if os.path.exists(output_file_path):
                continue

            if img is None:
                logger.warning(f"無法載入影像: {img_id}")
                continue
            
            curr_h, curr_w, _ = img.shape
            if curr_h != h or curr_w != w:
                logger.error(f"影像 {img_id} 尺寸不符: ({curr_h}, {curr_w}) vs ({h}, {w})")
                continue
            valid_images[img_id] = img

        # 處理並儲存影像
        for img_id, img in valid_images.items():
            img_start_time = time.time()

            # 1. 全景 -> 立方體
            original_cube_dict = project_cube.equirec_to_cubemap(img, cube_format="dict")

            # 2. 在每個面上進行偵測和模糊
            blurred_cube_dict = {}
            for face_key, face_img in original_cube_dict.items():
                blurred_face = detect_and_blur_face(face_img, models, face_key, conf_threshold)
                blurred_cube_dict[face_key] = blurred_face

            # 3. 模糊後的立方體 -> 模糊後的全景圖
            blurred_pano_img = project_cube.cubemap_to_equirec(
                blurred_cube_dict, h, w, cube_format="dict"
            )

            # 新增：儲存模糊後的全景圖
            base_name, ext = os.path.splitext(img_id)
            blurred_filename = f"{base_name}{ext}"
            blurred_output_path = os.path.join(output_path, blurred_filename)
            cv_imwrite(blurred_output_path, blurred_pano_img)

            # 生成金字塔功能已關閉
            
            processed_count += 1
            img_proc_time = time.time() - img_start_time
            print(f"{processed_count}/{total_images} {img_id} 處理完成 {img_proc_time:.2f}秒")


    # 計算總執行時間並輸出結果
    total_time = time.time() - total_start_time
    
    logger.info(f"處理共 {processed_count} 張影像")
    logger.info(f"執行時間: {total_time:.2f} 秒")