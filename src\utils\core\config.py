"""
統一工具配置模組 - 使用 Pydantic
"""
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class MemoryConfig(BaseSettings):
    """記憶體管理配置"""
    strategy: str = "BALANCED"
    cache_size_mb: int = 1024
    warning_threshold: float = Field(80.0, ge=0, le=100)
    critical_threshold: float = Field(90.0, ge=0, le=100)


class PerformanceConfig(BaseSettings):
    """性能監控配置"""
    level: str = "DETAILED"
    interval_sec: float = Field(1.0, gt=0)
    enable_gpu_monitoring: bool = True
    enable_predictive_analysis: bool = True


class ProcessingConfig(BaseSettings):
    """分散式處理配置"""
    backend: str = "threading"
    max_workers: int | None = None
    enable_load_balancing: bool = True


class ThresholdsConfig(BaseSettings):
    """系統閾值配置"""
    cpu_warning: float = Field(80.0, ge=0, le=100, description="CPU使用率警告閾值")
    cpu_critical: float = Field(90.0, ge=0, le=100, description="CPU使用率嚴重閾值")
    memory_warning: float = Field(80.0, ge=0, le=100, description="記憶體使用率警告閾值")
    memory_critical: float = Field(90.0, ge=0, le=100, description="記憶體使用率嚴重閾值")
    gpu_warning: float = Field(85.0, ge=0, le=100, description="GPU使用率警告閾值")
    gpu_critical: float = Field(95.0, ge=0, le=100, description="GPU使用率嚴重閾值")
    disk_warning: float = Field(85.0, ge=0, le=100, description="磁碟使用率警告閾值")
    disk_critical: float = Field(95.0, ge=0, le=100, description="磁碟使用率嚴重閾值")


class UtilsConfig(BaseSettings):
    """統一工具配置 - Pydantic 實現"""
    model_config = SettingsConfigDict(
        env_file='.env',
        env_nested_delimiter='__',  # 支援 MEMORY__STRATEGY 格式
        case_sensitive=False
    )

    memory: MemoryConfig = Field(default_factory=MemoryConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    processing: ProcessingConfig = Field(default_factory=ProcessingConfig)
    thresholds: ThresholdsConfig = Field(default_factory=ThresholdsConfig)
